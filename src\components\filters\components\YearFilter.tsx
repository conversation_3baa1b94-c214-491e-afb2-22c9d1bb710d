
import React from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import type { SelectedFilters } from "@/types/question";

interface YearFilterProps {
  selectedYears: string[];
  onToggleYear: (year: string) => void;
  questionCounts: {
    totalCounts: { [key: string]: number };
    filteredCounts: { [key: string]: number };
  };
  hasActiveFilters: boolean;
  selectedFilters: SelectedFilters;
  searchTerm?: string;
}

export const YearFilter = ({ 
  selectedYears, 
  onToggleYear,
  questionCounts,
  hasActiveFilters,
  selectedFilters,
  searchTerm = ""
}: YearFilterProps) => {
  // Generate years from current year back to 2010
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 2009 }, (_, i) => currentYear - i);

  // Filter years based on search term if provided
  const filteredYears = searchTerm 
    ? years.filter(year => year.toString().includes(searchTerm))
    : years;

  return (
    <ScrollArea className="h-72 pr-4" style={{ overflowY: 'auto' }}>
      <div className="space-y-2 pb-4">
        {filteredYears.map((year) => {
          const yearStr = year.toString();
          const isSelected = selectedYears.includes(yearStr);
          return (
            <div
              key={year}
              className={cn(
                'flex items-center justify-between p-2 rounded-lg transition-all duration-200 cursor-pointer',
                'hover:bg-[#FEF7CD]/50',
                isSelected && 'bg-[#FEF7CD]'
              )}
              onClick={() => onToggleYear(yearStr)}
            >
              <div className="flex items-center gap-2">
                <div 
                  className={cn(
                    "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center",
                    isSelected 
                      ? "bg-[#FF6B00] border-black text-white" 
                      : "border-black hover:border-[#FF6B00]",
                  )}
                >
                  {isSelected && (
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      className="w-3.5 h-3.5"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  )}
                </div>
                <span className={cn(
                  "text-sm transition-colors duration-200",
                  isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
                )}>
                  {year}
                </span>
              </div>
              
              <div 
                className={cn(
                  "min-w-[3rem] text-center px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200",
                  isSelected 
                    ? "bg-[#FF6B00] text-white" 
                    : "bg-gray-100 text-gray-600"
                )}
              >
                {/* Count could be added here if available */}
                {questionCounts.totalCounts[yearStr] || 0}
              </div>
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
};
