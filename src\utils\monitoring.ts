// =====================================================
// SISTEMA DE MONITORAMENTO PARA STUDYWISE
// =====================================================

import { supabase } from '@/integrations/supabase/client';

interface MonitoringMetrics {
  timestamp: Date;
  responseTime: number;
  status: 'healthy' | 'degraded' | 'down';
  endpoint: string;
  error?: string;
  userAgent: string;
  userId?: string;
}

interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  firstInputDelay?: number;
}

class Monitoring {
  private metricsBuffer: MonitoringMetrics[] = [];
  private performanceBuffer: PerformanceMetrics[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  // Iniciar monitoramento
  startMonitoring(intervalMs: number = 300000) { // 5 minutos
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Monitoramento periódico
    this.monitoringInterval = setInterval(() => {
      this.checkSystemHealth();
    }, intervalMs);

    // Monitoramento de performance da página
    this.setupPerformanceMonitoring();

    // Monitoramento de erros JavaScript
    this.setupErrorMonitoring();

    // Primeira verificação imediata
    this.checkSystemHealth();
  }

  // Parar monitoramento
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('🔍 Monitoring stopped');
  }

  // Verificar saúde do sistema
  private async checkSystemHealth() {
    const checks = [
      { name: 'database', check: () => this.checkDatabase() },
      { name: 'auth', check: () => this.checkAuth() },
      { name: 'storage', check: () => this.checkStorage() },
    ];

    for (const { name, check } of checks) {
      try {
        const startTime = performance.now();
        await check();
        const responseTime = performance.now() - startTime;

        this.recordMetric({
          timestamp: new Date(),
          responseTime,
          status: responseTime < 2000 ? 'healthy' : 'degraded',
          endpoint: name,
          userAgent: navigator.userAgent,
        });

        // Alertar se muito lento
        if (responseTime > 5000) {
          this.sendAlert(`${name} is very slow: ${responseTime.toFixed(0)}ms`);
        }
      } catch (error) {
        this.recordMetric({
          timestamp: new Date(),
          responseTime: 0,
          status: 'down',
          endpoint: name,
          error: error instanceof Error ? error.message : 'Unknown error',
          userAgent: navigator.userAgent,
        });

        this.sendAlert(`${name} is down: ${error}`);
      }
    }

    // Enviar métricas em lote
    await this.flushMetrics();
  }

  // Verificar banco de dados
  private async checkDatabase() {
    const { error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (error) throw error;
  }

  // Verificar autenticação
  private async checkAuth() {
    const { error } = await supabase.auth.getSession();
    if (error) throw error;
  }

  // Verificar storage
  private async checkStorage() {
    const { error } = await supabase.storage
      .from('sounds')
      .list('', { limit: 1 });

    if (error) throw error;
  }

  // Configurar monitoramento de performance
  private setupPerformanceMonitoring() {
    // Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordPerformanceMetric('lcp', lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.recordPerformanceMetric('fid', entry.processingStart - entry.startTime);
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordPerformanceMetric('cls', clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // Navigation Timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

        const metrics: PerformanceMetrics = {
          pageLoadTime: navigation.loadEventEnd - navigation.navigationStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        };

        this.performanceBuffer.push(metrics);
        this.flushPerformanceMetrics();
      }, 0);
    });
  }

  // Configurar monitoramento de erros
  private setupErrorMonitoring() {
    // Erros JavaScript
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      });
    });

    // Promises rejeitadas
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'unhandled_promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
      });
    });

    // Erros de recursos
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource_error',
          message: `Failed to load resource: ${(event.target as any)?.src || (event.target as any)?.href}`,
          element: (event.target as any)?.tagName,
        });
      }
    }, true);
  }

  // Registrar métrica
  private recordMetric(metric: MonitoringMetrics) {
    this.metricsBuffer.push(metric);
  }

  // Registrar métrica de performance
  private recordPerformanceMetric(type: string, value: number) {
    // Métrica registrada silenciosamente
  }

  // Registrar erro
  private recordError(error: any) {
    // Enviar para sistema de monitoramento externo
    if (import.meta.env.PROD) {
      this.sendToExternalMonitoring('error', error);
    }
  }

  // Enviar alerta
  private sendAlert(message: string) {
    // Em produção, enviar para webhook/email/Slack
    if (import.meta.env.PROD) {
      this.sendToExternalMonitoring('alert', { message, timestamp: new Date() });
    }
  }

  // Enviar para monitoramento externo
  private async sendToExternalMonitoring(type: string, data: any) {
    try {
      // Webhook para Slack, Discord, ou serviço de monitoramento
      const webhookUrl = import.meta.env.VITE_MONITORING_WEBHOOK;

      if (webhookUrl) {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type,
            data,
            timestamp: new Date().toISOString(),
            environment: import.meta.env.MODE,
            app: 'studywise-frontend',
          }),
        });
      }
    } catch (error) {
      // Error silently handled
    }
  }

  // Enviar métricas para Supabase
  private async flushMetrics() {
    if (this.metricsBuffer.length === 0) return;

    try {
      const metrics = [...this.metricsBuffer];
      this.metricsBuffer = [];

      // Só enviar em produção
      if (import.meta.env.PROD) {
        await supabase.from('monitoring_metrics').insert(
          metrics.map(metric => ({
            timestamp: metric.timestamp.toISOString(),
            response_time: metric.responseTime,
            status: metric.status,
            endpoint: metric.endpoint,
            error_message: metric.error,
            user_agent: metric.userAgent,
            user_id: metric.userId,
          }))
        );
      }

      // Metrics flushed successfully
    } catch (error) {
      // Failed to flush metrics - silent error
    }
  }

  // Enviar métricas de performance
  private async flushPerformanceMetrics() {
    if (this.performanceBuffer.length === 0) return;

    try {
      const metrics = [...this.performanceBuffer];
      this.performanceBuffer = [];

      // Performance metrics processed silently

      // Enviar para analytics
      if (import.meta.env.PROD && (window as any).gtag) {
        metrics.forEach(metric => {
          (window as any).gtag('event', 'page_performance', {
            page_load_time: metric.pageLoadTime,
            dom_content_loaded: metric.domContentLoaded,
          });
        });
      }
    } catch (error) {
      // Error silently handled
    }
  }

  // Obter status atual
  getStatus() {
    const recentMetrics = this.metricsBuffer.slice(-10);
    const healthyCount = recentMetrics.filter(m => m.status === 'healthy').length;
    const totalCount = recentMetrics.length;

    return {
      isHealthy: totalCount === 0 || healthyCount / totalCount > 0.8,
      recentMetrics: recentMetrics.slice(-5),
      isMonitoring: this.isMonitoring,
    };
  }
}

// Instância singleton
export const monitoring = new Monitoring();

// Iniciar automaticamente em produção - DESABILITADO
// if (import.meta.env.PROD) {
//   monitoring.startMonitoring(300000); // 5 minutos
// } else {
//   monitoring.startMonitoring(600000); // 10 minutos em desenvolvimento
// }

// Hook para usar monitoramento em componentes
export const useMonitoring = () => {
  return {
    getStatus: monitoring.getStatus.bind(monitoring),
    startMonitoring: monitoring.startMonitoring.bind(monitoring),
    stopMonitoring: monitoring.stopMonitoring.bind(monitoring),
  };
};
