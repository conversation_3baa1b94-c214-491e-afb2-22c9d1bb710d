import { useState, useCallback, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useDrWillHistory } from './useDrWillHistory';
import { drWillLogger } from '@/utils/logger';
import { drWillMonitor, measureOperation } from '@/utils/drWillMonitor';

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isStreaming?: boolean;
  isThinking?: boolean;
  needsFinalFormat?: boolean; // 🎯 Flag para formatação final após streaming
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

interface UseDrWillChatProps {
  currentThreadId: string | null;
  saveToHistory: (message: any, threadId?: string) => Promise<string | null>;
  createNewThread: (firstMessage?: string) => Promise<string | null>;
  getConversationHistory: (threadId?: string) => Promise<Array<{role: 'user' | 'assistant', content: string}>>;
}

export const useDrWillChat = (props?: UseDrWillChatProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 🎯 CORREÇÃO: Usar props quando disponíveis, senão usar hook interno
  const historyHook = useDrWillHistory();

  const currentThreadId = props?.currentThreadId ?? historyHook.currentThreadId;
  const saveToHistory = props?.saveToHistory ?? historyHook.saveMessage;
  const createNewThread = props?.createNewThread ?? historyHook.createNewThread;
  const getConversationHistory = props?.getConversationHistory ?? historyHook.getConversationHistory;

  // Manter outras funções do hook interno para compatibilidade
  const {
    deleteThread,
    threads,
    clearCurrentConversation,
    loadMessages: loadHistoryMessages,
    loadThreads,
    ...historyMethods
  } = historyHook;

  // Track if we're loading from history to avoid conflicts
  const [isLoadingFromHistory, setIsLoadingFromHistory] = useState(false);
  const [lastLoadedThreadId, setLastLoadedThreadId] = useState<string | null>(null);

  // Function to manually load messages from a thread (for history selection)
  const loadMessagesFromThread = useCallback(async (threadId: string) => {
    return await measureOperation(`loadMessagesFromThread_${threadId.slice(0, 8)}`, async () => {
      // 🎯 CORREÇÃO: Usar loadHistoryMessages que já define o currentThreadId corretamente
      const historyMessages = await loadHistoryMessages(threadId);

      if (historyMessages && historyMessages.length > 0) {
        // Convert to ChatMessage format
        const chatMessages = historyMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.isUser,
          timestamp: msg.timestamp,
          isStreaming: false
        }));

        setMessages(chatMessages);
        drWillLogger.threadLoaded(threadId, chatMessages.length);
        drWillMonitor.updateThreadState(threadId, chatMessages.length);
      } else {
        setMessages([]);

        drWillMonitor.updateThreadState(threadId, 0);
      }

      setLastLoadedThreadId(threadId);

      // 🎯 AGUARDAR: Pequeno delay para permitir que o currentThreadId seja atualizado
      await new Promise(resolve => setTimeout(resolve, 50));

      // Verificação silenciosa de sincronização
    });
  }, [loadHistoryMessages, currentThreadId]);

  // ❌ REMOVIDO: Auto-load que estava causando conflito
  // Agora só carregamos mensagens quando explicitamente solicitado via loadMessagesFromThread

  // ✅ REMOVIDO: getConversationHistoryFromDB - agora usamos o do useDrWillHistory que é mais confiável

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((messageId: string, updates: Partial<ChatMessage>) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    ));
  }, []);

  const sendMessage = useCallback(async (content: string, userId?: string, forceThreadId?: string | null, forceNewThreadDirect?: boolean) => {
    if (!content.trim() || isLoading) return;



    return await measureOperation('sendMessage', async () => {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      let threadId: string | null = null;

      if (forceNewThreadDirect === true) {
        threadId = null;
      } else {
        threadId = forceThreadId || currentThreadId;
      }

      if (!threadId) {
        threadId = await createNewThread(content.trim());
        if (!threadId) {
          drWillLogger.error('sendMessage', 'Failed to create new thread');
          return;
        }
      }

      // Add user message
      const now = new Date();
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        content: content.trim(),
        isUser: true,
        timestamp: now
      };



      addMessage(userMessage);

      // Save user message to history
      const savedMessageId = await saveToHistory({
        id: userMessage.id,
        content: userMessage.content,
        isUser: true,
        timestamp: userMessage.timestamp
      }, threadId);

      if (!savedMessageId) {
        drWillLogger.error('saving user message to history', 'Failed to save');
        setError('Erro ao salvar mensagem');
        setIsLoading(false);
        return;
      }

      // 🧠 OTIMIZAÇÃO: Buscar histórico em paralelo com o salvamento
      // Reduzir delay para melhorar performance
      await new Promise(resolve => setTimeout(resolve, 100));
      const conversationHistory = await getConversationHistory(threadId);

    setIsLoading(true);
    setIsStreaming(true);
    setError(null);

      // NOVA IMPLEMENTAÇÃO: Thinking + Dr. Will em sequência
      await processThinkingAndResponse(content, threadId, conversationHistory, userId);
    });
  }, [isLoading, addMessage, updateMessage]);

  // Função para dividir thinking em exatamente 2 etapas
  const parseThinkingSteps = (content: string): string[] => {
    // Log removido para limpeza

    // Limpar conteúdo e remover títulos Markdown isolados
    let cleanContent = content.trim();

    // Estratégia especial: Se tem títulos Markdown, tentar agrupar título + conteúdo
    const markdownSections = cleanContent.split(/\n\s*\n/).filter(section => section.trim());
    const meaningfulSections: string[] = [];

    for (let i = 0; i < markdownSections.length; i++) {
      const section = markdownSections[i].trim();

      // Se é um título Markdown isolado, tentar juntar com próxima seção
      if (section.match(/^\*\*[^*]+\*\*\s*$/) && i + 1 < markdownSections.length) {
        const nextSection = markdownSections[i + 1].trim();
        meaningfulSections.push(section + '\n\n' + nextSection);
        i++; // Pular próxima seção pois já foi processada
      } else if (section.length > 50) {
        meaningfulSections.push(section);
      }
    }

    if (meaningfulSections.length >= 2) {
      // Log removido para limpeza
      return meaningfulSections.slice(0, 2);
    }

    // Estratégia 1: Dividir por quebras de linha duplas (parágrafos)
    const paragraphs = cleanContent.split(/\n\s*\n/).filter(p => {
      const trimmed = p.trim();
      // Filtrar títulos muito pequenos (provavelmente só títulos Markdown)
      return trimmed.length > 50 && !trimmed.match(/^\*\*[^*]+\*\*\s*$/);
    });

    if (paragraphs.length >= 2) {
      const steps = paragraphs.slice(0, 2);
      console.log('✅ Using paragraph strategy (filtered):', {
        step1Length: steps[0].length,
        step2Length: steps[1].length,
        totalParagraphs: paragraphs.length
      });
      return steps;
    }

    // Estratégia 2: Dividir por emojis (cada emoji marca uma nova etapa)
    const emojiPattern = /([🔍💡🧠📋🎯⚡✅🎓📚❓👩‍⚕️🏗️])/;
    const emojiSplit = cleanContent.split(emojiPattern);

    const emojiSteps: string[] = [];
    for (let i = 1; i < emojiSplit.length; i += 2) {
      if (emojiSplit[i] && emojiSplit[i + 1]) {
        emojiSteps.push(emojiSplit[i] + emojiSplit[i + 1].trim());
      }
    }

    if (emojiSteps.length >= 2) {
      const steps = emojiSteps.slice(0, 2);
      console.log('✅ Using emoji strategy:', {
        step1Length: steps[0].length,
        step2Length: steps[1].length
      });
      return steps;
    }

    // Estratégia 3: Dividir por frases (2 frases por etapa = 4 frases total)
    const sentences = cleanContent.split(/[.!?]+/).filter(s => s.trim());

    if (sentences.length >= 4) {
      // 2 frases por etapa
      const steps = [
        sentences.slice(0, 2).join('. ') + '.',
        sentences.slice(2, 4).join('. ') + '.'
      ];
      console.log('✅ Using sentence strategy:', {
        step1Length: steps[0].length,
        step2Length: steps[1].length
      });
      return steps;
    }

    // Estratégia 4: Fallback - dividir por tamanho de caracteres
    if (cleanContent.length > 100) {
      const midPoint = Math.floor(cleanContent.length / 2);
      // Encontrar um ponto de quebra natural (espaço, ponto, quebra de linha)
      let splitPoint = midPoint;

      for (let i = midPoint; i < cleanContent.length && i < midPoint + 100; i++) {
        if (cleanContent[i] === '.' && cleanContent[i + 1] === ' ') {
          splitPoint = i + 1;
          break;
        } else if (cleanContent[i] === '\n') {
          splitPoint = i;
          break;
        } else if (cleanContent[i] === ' ' && i > midPoint + 20) {
          splitPoint = i;
          break;
        }
      }

      const result = [
        cleanContent.substring(0, splitPoint).trim(),
        cleanContent.substring(splitPoint).trim()
      ].filter(step => step.length > 20); // Filtrar steps muito pequenos

      console.log('✅ Using character-based fallback strategy:', {
        step1Length: result[0]?.length || 0,
        step2Length: result[1]?.length || 0,
        totalSteps: result.length,
        splitPoint
      });

      return result.length >= 2 ? result : [cleanContent];
    }

    // Se muito pequeno, retornar como único step
    console.log('⚠️ Content too small, using single step');
    return [cleanContent];
  };

  // Função para exibir thinking em etapas individuais
  const displayThinkingSteps = async (
    messageId: string,
    steps: string[],
    updateMessage: (id: string, updates: Partial<ChatMessage>) => void
  ) => {
    let allThinkingContent = '';


    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];


      // Limpar conteúdo anterior (cada etapa aparece sozinha)
      updateMessage(messageId, { content: '' });

      // Simular typing para esta etapa específica
      const words = step.split(' ');
      let currentStepContent = '';

      for (let j = 0; j < words.length; j++) {
        currentStepContent += (j > 0 ? ' ' : '') + words[j];

        // Processar Markdown básico no thinking (negrito)
        let processedContent = currentStepContent.replace(/\*\*(.*?)\*\*/g, '**$1**');

        updateMessage(messageId, { content: processedContent });

        // Delay entre palavras (40ms para ser mais rápido)
        await new Promise(resolve => setTimeout(resolve, 40));
      }



      // Guardar conteúdo completo para histórico
      allThinkingContent += step;
      if (i < steps.length - 1) {
        allThinkingContent += '\n\n';
      }

      // Delay após completar a etapa (0.5 segundos)

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Delay final antes de limpar tudo (0.5 segundos)

    await new Promise(resolve => setTimeout(resolve, 500));

    // LIMPAR TUDO - preparar para Dr. Will

    updateMessage(messageId, { content: '', isStreaming: true });


    return allThinkingContent; // Retornar conteúdo completo para salvar no histórico
  };

  // Função para aplicar streaming falso ao Dr. Will
  const applyFakeStreaming = async (
    messageId: string,
    content: string,
    updateMessage: (id: string, updates: Partial<ChatMessage>) => void
  ) => {
    // Dividir conteúdo em palavras
    const words = content.split(' ');
    let currentContent = '';

    // 🎯 CORREÇÃO: NÃO zerar o conteúdo, apenas mudar para resposta normal
    updateMessage(messageId, { isStreaming: true, isThinking: false });

    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i];
      updateMessage(messageId, { content: currentContent, isStreaming: true, isThinking: false });

      // Delay variável baseado no tamanho da palavra
      const word = words[i];
      let delay = 30; // Base delay

      // Delays especiais para pontuação
      if (word.endsWith('.') || word.endsWith('!') || word.endsWith('?')) {
        delay = 150; // Pausa maior após frases
      } else if (word.endsWith(',') || word.endsWith(';') || word.endsWith(':')) {
        delay = 80; // Pausa média após vírgulas
      } else if (word.length > 8) {
        delay = 50; // Palavras longas demoram mais
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Marcar como não streaming
    updateMessage(messageId, { content: currentContent, isStreaming: false, isThinking: false });
  };

  // Nova função para processar thinking + resposta completa (MODO INTEGRADO)
  const processThinkingAndResponse = async (
    content: string,
    threadId: string,
    conversationHistory: Array<{role: 'user' | 'assistant', content: string}>,
    userId?: string
  ) => {
    // Create AI response message (inicialmente para thinking)
    const aiMessageId = (Date.now() + 1).toString();
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      content: '',
      isUser: false,
      timestamp: new Date(),
      isStreaming: true,
      isThinking: true
    };

    addMessage(aiMessage);

    try {
      // Get Supabase URL and key for direct function call
      const supabaseUrl = supabase.supabaseUrl;
      const supabaseKey = supabase.supabaseKey;

      const requestBody = {
        message: content,
        userId: userId,
        conversationHistory: conversationHistory
      };



      // NOVA ESTRATÉGIA: Uma única chamada para dr-will-medevo com thinking integrado
      const drWillResponse = await fetch(`${supabaseUrl}/functions/v1/dr-will-medevo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current.signal
      });

      if (!drWillResponse.ok) {
        const errorText = await drWillResponse.text();
        throw new Error(`Dr. Will API error: ${drWillResponse.status} - ${errorText}`);
      }

      // Processar stream integrado (thinking + resposta final)
      const reader = drWillResponse.body?.getReader();
      if (!reader) {
        throw new Error('Dr. Will stream não disponível');
      }

      const decoder = new TextDecoder();
      let fullThinkingContent = '';
      let fullDrWillContent = '';
      let chunkCount = 0;
      let thinkingPhaseComplete = false;

      // Processar stream integrado
      while (true) {
        const { done, value } = await reader.read();
        chunkCount++;

        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (!line.startsWith('data: ')) continue;

          try {
            const jsonText = line.slice(6).trim();
            if (!jsonText) continue;

            const data = JSON.parse(jsonText);

            if (data.done) {
              break;
            }

            if (data.content) {
              if (data.isThinking === true) {
                // Conteúdo de thinking
                fullThinkingContent += data.content;
                // Log removido para limpeza

                // 🎯 CORREÇÃO: Exibir thinking em tempo real

                updateMessage(aiMessageId, {
                  content: fullThinkingContent,
                  isStreaming: true,
                  isThinking: true
                });
              } else {
                // Conteúdo da resposta final
                if (!thinkingPhaseComplete) {
                  // Primeira vez que recebemos resposta final, processar thinking
                  thinkingPhaseComplete = true;

                  if (fullThinkingContent) {
                    // Logs removidos para limpeza
                    const thinkingSteps = parseThinkingSteps(fullThinkingContent);
                    // await displayThinkingSteps(aiMessageId, thinkingSteps, updateMessage);
                  }
                }

                // Acumular resposta final
                fullDrWillContent += data.content;

                // Log removido para limpeza

                // Log removido para limpeza

                // Atualizar mensagem em tempo real com a resposta final
                updateMessage(aiMessageId, {
                  content: fullDrWillContent,
                  isStreaming: true,
                  isThinking: false
                });
              }
            }



            if (data.error) {
              throw new Error(data.error);
            }
          } catch (parseError) {
            // Continue processing other lines
          }
        }
      }

      // Finalizar streaming - log removido para evitar spam

      // Logs removidos para limpeza

      updateMessage(aiMessageId, {
        content: fullDrWillContent,
        isStreaming: false,
        isThinking: false
      });

      // Save AI response to history (APENAS resposta do Dr. Will)
      if (fullDrWillContent && threadId) {
        const savedAiMessageId = await saveToHistory({
          id: aiMessageId,
          content: fullDrWillContent,
          isUser: false,
          timestamp: new Date()
        }, threadId);

        if (!savedAiMessageId) {
          drWillLogger.error('saving AI response to history', 'Failed to save');
        }
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        // Request was cancelled, remove the incomplete AI message
        setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      } else {
        // Tratamento de erro mais específico
        let errorMessage = 'Desculpe, ocorreu um erro inesperado. Tente novamente.';

        if (error.message?.includes('API error')) {
          errorMessage = 'Erro de conexão com Dr. Will. Verifique sua internet e tente novamente.';
        } else if (error.message?.includes('timeout')) {
          errorMessage = 'Dr. Will está demorando para responder. Tente uma pergunta mais simples.';
        } else if (error.message?.includes('rate limit')) {
          errorMessage = 'Muitas perguntas simultâneas. Aguarde um momento antes de tentar novamente.';
        } else if (error.message?.includes('GEMINI_API_KEY')) {
          errorMessage = 'Dr. Will está temporariamente indisponível. Tente novamente mais tarde.';
        }

        updateMessage(aiMessageId, {
          content: `❌ **Erro**: ${errorMessage}

💡 **Dicas para tentar novamente:**
- Verifique sua conexão com a internet
- Tente uma pergunta mais específica
- Aguarde alguns segundos antes de tentar novamente

Se o problema persistir, entre em contato com o suporte.`,
          isStreaming: false
        });

        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
    // Don't call clearCurrentConversation here to avoid circular dependency
  }, []);

  const setMessagesDirectly = useCallback((newMessages: ChatMessage[]) => {
    setMessages(newMessages);
  }, []);

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Save chat session to database (optional feature)
  const saveChatSession = useCallback(async (userId: string) => {
    if (messages.length === 0) return null;

    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: userId,
          messages: messages,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      return null;
    }
  }, [messages]);

  // Load chat session from database (optional feature)
  const loadChatSession = useCallback(async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error) throw error;
      
      if (data && data.messages) {
        setMessages(data.messages);
      }
      
      return data;
    } catch (error) {
      return null;
    }
  }, []);

  return {
    messages,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    clearMessages,
    setMessages: setMessagesDirectly,
    cancelRequest,
    saveChatSession,
    loadChatSession,
    addMessage,
    updateMessage,

    // History methods
    currentThreadId,
    clearCurrentConversation,
    loadMessages: loadMessagesFromThread, // ✅ CORREÇÃO: Usar função manual
    createNewThread,
    deleteThread,
    threads,
    loadThreads
  };
};
