
import { Progress } from "@/components/ui/progress";
import { Trophy, BookOpen, GraduationCap } from 'lucide-react';
import { motion } from "framer-motion";

interface UserProgressHeaderProps {
  userProfile: {
    level: number;
    points: number;
  } | null;
}

export const UserProgressHeader = ({ userProfile }: UserProgressHeaderProps) => {
  if (!userProfile) {
    return (
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Seu Progresso</h1>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-gray-600">Carregando...</p>
          </div>
        </div>
      </div>
    );
  }

  const nextLevelThreshold = userProfile.level * 100;
  const progressToNextLevel = (userProfile.points % 100) / nextLevelThreshold * 100;

  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative rounded-xl overflow-hidden mb-8"
    >
      <div className="absolute inset-0 bg-black rounded-lg"></div>
      <div className="relative p-6">
        <div className="inline-block transform -rotate-2 mb-4">
          <div className="bg-hackathon-red border-2 border-white px-4 py-1 text-white font-bold tracking-wide text-sm shadow-card-sm">
            PROGRESSO DE ESTUDOS
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div>
            <h1 className="text-5xl font-black leading-none mb-2 text-white">
              <span className="inline-block bg-hackathon-yellow text-black px-4 py-2 transform -rotate-1 border-2 border-black">Seu</span>
              <span className="block text-hackathon-red mt-1">Progresso</span>
            </h1>
            <p className="text-xl text-white/90 max-w-lg">
              Acompanhe seu desenvolvimento e evolução nos <span className="underline decoration-hackathon-yellow decoration-4 font-medium">estudos</span>
            </p>
          </div>
          
          <div className="flex items-center gap-4 bg-hackathon-yellow p-3 rounded-xl border-2 border-black">
            <div>
              <p className="text-xs text-black/80 font-medium">Nível</p>
              <div className="flex items-center">
                <GraduationCap className="h-4 w-4 mr-1 text-black" />
                <p className="text-xl font-bold text-black">{userProfile.level}</p>
              </div>
            </div>
            <div className="flex flex-col gap-1 items-center">
              <div className="w-32 bg-black/20 h-2.5 rounded-full overflow-hidden border border-black/40">
                <div 
                  className="h-full bg-black rounded-full"
                  style={{ width: `${progressToNextLevel}%` }}
                ></div>
              </div>
              <p className="text-xs text-black/80 font-medium">{userProfile.points} pontos</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
