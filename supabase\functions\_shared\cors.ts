
// Configuração de CORS para produção e desenvolvimento
const getAllowedOrigins = () => {
  const isDevelopment = Deno.env.get("ENVIRONMENT") !== "production";

  if (isDevelopment) {
    return "*"; // Permitir qualquer origem em desenvolvimento
  }

  // Em produção, apenas domínios específicos
  return "https://medevo.com.br";
};

export const corsHeaders = {
  "Access-Control-Allow-Origin": getAllowedOrigins(),
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Credentials": "true",
};
