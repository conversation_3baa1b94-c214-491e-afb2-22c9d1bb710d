import { useEffect, useState, useRef, useCallback, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Brain, Eye, HelpCircle, Flag } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useAICommentary } from "@/hooks/useAICommentary";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Question, AICommentaryResponse } from "@/types/question";
import { supabase } from "@/integrations/supabase/client";

import { CheckCircle, XCircle } from "lucide-react";
import { QuestionAICommentaryFeedback } from './QuestionAICommentaryFeedback';

// 🔧 Função para limpar JSON mal formatado que foi salvo incorretamente
const cleanMalformedJson = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Verificar se o texto contém JSON mal formatado
  if (text.includes('```json') || (text.includes('"alternativas"') && text.includes('"comentario_final"'))) {
    try {
      // Tentar extrair o JSON do texto
      let jsonText = text;

      // Remover marcadores de código markdown
      jsonText = jsonText.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Remover tags HTML se existirem
      jsonText = jsonText.replace(/<[^>]*>/g, '');

      // Tentar parsear como JSON
      const parsed = JSON.parse(jsonText);

      // Se conseguiu parsear e tem a estrutura esperada, extrair o comentário final
      if (parsed.comentario_final && typeof parsed.comentario_final === 'string') {
        return parsed.comentario_final;
      }

      // Se não tem comentário final, tentar extrair de alternativas
      if (parsed.alternativas && Array.isArray(parsed.alternativas)) {
        const comments = parsed.alternativas
          .map((alt: any) => alt.comentario)
          .filter(Boolean)
          .join('<br><br>');
        return comments || 'Análise não disponível.';
      }

    } catch (e) {
      console.warn('Erro ao limpar JSON mal formatado:', e);

      // Fallback: tentar extrair texto útil usando regex
      const cleanText = text
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .replace(/<[^>]*>/g, '')
        .replace(/\{[\s\S]*"comentario_final":\s*"([^"]*)"[\s\S]*\}/g, '$1')
        .trim();

      return cleanText || 'Análise não disponível.';
    }
  }

  return text;
};



// 🛡️ COMPONENTE ESTÁVEL: Definido fora para evitar re-criação
const HelpDialog = () => (
  <Dialog>
    <DialogTrigger asChild>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
      >
        <HelpCircle className="h-4 w-4 text-gray-500" />
      </Button>
    </DialogTrigger>
    <DialogContent className="max-w-[90vw] max-h-[80vh] w-full sm:max-w-md rounded-lg overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Como funciona a Análise da IA
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-4 text-sm">
        <div>
          <h4 className="font-semibold mb-2">📝 Gerar Análise vs Ver Análise</h4>
          <p className="text-gray-600 leading-relaxed">
            <strong>"Gerar Análise"</strong> aparece quando a questão ainda não possui uma análise da IA.
            Ao clicar, nossa IA criará uma explicação detalhada para você.
          </p>
          <p className="text-gray-600 leading-relaxed mt-2">
            <strong>"Ver Análise"</strong> aparece quando a questão já possui uma análise pronta.
            Isso significa que outro usuário já gerou a análise anteriormente.
          </p>
        </div>

        <div>
          <h4 className="font-semibold mb-2">🤝 Análise Compartilhada</h4>
          <p className="text-gray-600 leading-relaxed">
            Quando você gera uma análise, ela fica disponível para todos os outros usuários.
            Isso economiza tempo e garante que todos tenham acesso às mesmas explicações de qualidade.
          </p>
        </div>

        <div>
          <h4 className="font-semibold mb-2">🔄 Melhoria Contínua</h4>
          <p className="text-gray-600 leading-relaxed">
            As análises são constantemente aprimoradas com base no feedback dos usuários.
            Se você encontrar algo incorreto ou pouco relevante, pode nos ajudar!
          </p>
        </div>

        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <h4 className="font-semibold mb-2 flex items-center gap-2 text-blue-800">
            <Flag className="h-4 w-4" />
            Como reportar problemas
          </h4>
          <p className="text-blue-700 text-sm leading-relaxed">
            Se uma análise não estiver correta ou relevante, clique no ícone de bandeira
            <Flag className="h-3 w-3 inline mx-1" /> no canto superior direito da análise
            para deixar seu feedback. Isso nos ajuda a melhorar continuamente.
          </p>
        </div>
      </div>
    </DialogContent>
  </Dialog>
);

interface QuestionAICommentaryProps {
  question: Question;
  onCommentaryGenerated: (commentary: AICommentaryResponse) => void;
  existingCommentary: AICommentaryResponse | null;
  sessionId: string;
}

export const QuestionAICommentary = ({
  question,
  onCommentaryGenerated,
  existingCommentary,
  sessionId
}: QuestionAICommentaryProps) => {
  // 🚀 HOOKS PRIMEIRO - SEMPRE na mesma ordem
  const { generateCommentary, isLoading, error, resetCommentary } = useAICommentary();
  const [savedCommentary, setSavedCommentary] = useState<AICommentaryResponse | null>(null);
  const currentQuestionRef = useRef<string>(question.id);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showCommentary, setShowCommentary] = useState(false);
  const [currentLoadingMessage, setCurrentLoadingMessage] = useState(0);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // 🛡️ DEBOUNCE: Evitar múltiplas chamadas de onCommentaryGenerated
  const lastCallbackCallRef = useRef<string>('');
  const callbackTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const hasValidAICommentary = useCallback((commentary: any): boolean => {
    if (!commentary) return false;
    if (typeof commentary !== 'object') return false;

    if (!commentary.alternativas || !Array.isArray(commentary.alternativas)) return false;
    if (commentary.alternativas.length === 0) return false;

    const firstAlt = commentary.alternativas[0];
    return typeof firstAlt === 'object' && 'texto' in firstAlt && 'correta' in firstAlt;
  }, []);

  // 🛡️ FUNÇÃO DEBOUNCED para evitar múltiplas chamadas
  const debouncedOnCommentaryGenerated = useCallback((commentary: AICommentaryResponse) => {
    const commentaryHash = JSON.stringify(commentary);
    const callKey = `${question.id}-${commentaryHash}`;

    // Se já chamamos com os mesmos dados, ignorar
    if (lastCallbackCallRef.current === callKey) {
      return;
    }

    // Cancelar timeout anterior se existir
    if (callbackTimeoutRef.current) {
      clearTimeout(callbackTimeoutRef.current);
    }

    // Agendar chamada com debounce de 50ms
    callbackTimeoutRef.current = setTimeout(() => {
      lastCallbackCallRef.current = callKey;
      onCommentaryGenerated(commentary);
    }, 50);
  }, [question.id, onCommentaryGenerated]);

  const loadingMessages = [
    "Analisando questão médica...",
    "Consultando literatura científica...",
    "Identificando conceitos-chave...",
    "Elaborando explicação detalhada...",
    "Verificando evidências clínicas...",
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading) {
      interval = setInterval(() => {
        setCurrentLoadingMessage((prev) => (prev + 1) % loadingMessages.length);
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [isLoading, loadingMessages.length]);

  // 🧹 CLEANUP: Limpar timeout ao desmontar componente
  useEffect(() => {
    return () => {
      if (callbackTimeoutRef.current) {
        clearTimeout(callbackTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (currentQuestionRef.current !== question.id) {
      setIsTransitioning(true);
      setIsInitialLoading(true);
      // 🧹 LIMPAR savedCommentary ao mudar questão para evitar vazamento
      setSavedCommentary(null);
      setShowCommentary(false);
      currentQuestionRef.current = question.id;

      setTimeout(() => {
        setIsTransitioning(false);
      }, 50);
    }
  }, [question.id]);

  useEffect(() => {
    const checkQuestionCommentary = async () => {
      if (isTransitioning) return;

      const questionAiCommentary = question.ai_commentary || null;
      const hasQuestionCommentary = hasValidAICommentary(questionAiCommentary);
      const hasExistingCommentary = hasValidAICommentary(existingCommentary);

      // PRIORIDADE: question commentary > existing commentary (do QuestionFeedback)
      if (hasQuestionCommentary) {
        const typedCommentary = questionAiCommentary as AICommentaryResponse;

        // 🛡️ EVITAR LOOP: só chamar onCommentaryGenerated se savedCommentary mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          debouncedOnCommentaryGenerated(typedCommentary);
        }
      } else if (hasExistingCommentary) {
        const typedCommentary = existingCommentary as AICommentaryResponse;

        // 🛡️ EVITAR LOOP: só chamar onCommentaryGenerated se savedCommentary mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          debouncedOnCommentaryGenerated(typedCommentary);
        }
      } else {
        // Se não há comentário salvo, limpar o estado
        if (savedCommentary !== null) {
          setSavedCommentary(null);
        }
      }

      // Finalizar loading inicial após verificar os dados
      setIsInitialLoading(false);
    };

    checkQuestionCommentary();
  }, [
    question.id, // Apenas question.id para recarregar ao mudar questão
    question.ai_commentary,
    existingCommentary,
    isTransitioning
    // Removidas dependências que causam loops: hasValidAICommentary, debouncedOnCommentaryGenerated
  ]);

  const saveCommentaryToQuestion = async (commentary: AICommentaryResponse) => {
    try {
      const { error } = await supabase
        .from('questions')
        .update({ ai_commentary: commentary })
        .eq('id', question.id);

      if (error) {
        // Erro silencioso - análise ainda funciona, só não será salva para uso futuro
      }
    } catch (error) {
      // Erro silencioso - análise ainda funciona, só não será salva para uso futuro
    }
  };

  const handleGenerateCommentary = useCallback(async () => {
    // Evitar múltiplas chamadas simultâneas
    if (isLoading) return;

    const alternatives = question.response_choices || question.alternatives;
    if (!alternatives || alternatives.length === 0) return;

    const generatedCommentary = await generateCommentary(
      question.id,
      question.question_content || question.statement,
      alternatives,
      parseInt((question.correct_choice || question.correct_answer).toString()),
      sessionId,
      question.specialty?.name || 'Medicina Geral'
    );

    if (generatedCommentary && currentQuestionRef.current === question.id) {
      // 🛡️ EVITAR LOOP: só atualizar se realmente mudou
      if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(generatedCommentary)) {
        setSavedCommentary(generatedCommentary);
        debouncedOnCommentaryGenerated(generatedCommentary);
      }
      setShowCommentary(true);

      await saveCommentaryToQuestion(generatedCommentary);
    }
  }, [
    isLoading,
    question.id,
    question.response_choices,
    question.alternatives,
    question.question_content,
    question.statement,
    question.correct_choice,
    question.correct_answer,
    question.specialty?.name,
    sessionId,
    generateCommentary
    // Removidas dependências que causam loops: savedCommentary, debouncedOnCommentaryGenerated, saveCommentaryToQuestion
  ]);

  const handleShowSavedCommentary = () => {
    setShowCommentary(true);
  };

  const handleHideCommentary = () => {
    setShowCommentary(false);
  };

  // 🚀 OTIMIZAÇÃO: Usar useMemo para evitar recálculos desnecessários
  const { activeCommentary, hasExistingCommentary, activeCommentarySource } = useMemo(() => {
    const active = savedCommentary || existingCommentary;
    const hasExisting = hasValidAICommentary(active);
    const source = savedCommentary ? 'saved' : existingCommentary ? 'existing' : 'none';

    return {
      activeCommentary: active,
      hasExistingCommentary: hasExisting,
      activeCommentarySource: source
    };
  }, [savedCommentary, existingCommentary, hasValidAICommentary]);

  // 🚫 NÃO MOSTRAR para questões dissertativas (elas têm seu próprio botão de análise)
  const isDiscursiveQuestion = (question.question_format || question.answer_type) === 'DISSERTATIVA';
  if (isDiscursiveQuestion) {
    return null;
  }

  // 🛡️ RENDERIZAÇÃO CONDICIONAL: Evitar early return após hooks
  if (isTransitioning) {
    return (
      <div className="mt-8 relative">
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse">
            <div className="w-4 h-4 bg-gray-300 rounded"></div>
            <div className="w-32 h-4 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 relative">

      {isInitialLoading && (
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse">
            <div className="w-4 h-4 bg-gray-300 rounded"></div>
            <div className="w-32 h-4 bg-gray-300 rounded"></div>
          </div>
        </div>
      )}

      {!isInitialLoading && !hasExistingCommentary && (
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleGenerateCommentary}
              disabled={isLoading}
              variant="hackYellow"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]"
            >
              <Brain className="h-4 w-4" />
              {isLoading ? "Gerando análise..." : "Gerar análise"}
            </Button>
            <HelpDialog />
          </div>

          {isLoading && (
            <Card className="border-2 border-black bg-hackathon-lightBg shadow-card-light max-w-md mx-auto">
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <div className="animate-pulse">
                      <Brain className="h-6 w-6 text-hackathon-black" />
                    </div>
                    <div className="text-lg font-semibold text-hackathon-black">
                      Análise Inteligente
                    </div>
                  </div>

                  <div className="space-y-3">
                    <p className="text-hackathon-black font-medium text-base">
                      {loadingMessages[currentLoadingMessage]}
                    </p>
                    <p className="text-sm text-gray-600">
                      Aguarde aproximadamente 15 segundos para uma análise precisa
                    </p>
                  </div>

                  {/* Barra de progresso animada */}
                  <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div className="bg-gradient-to-r from-hackathon-black to-gray-600 h-2 rounded-full animate-pulse relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-ping"></div>
                    </div>
                  </div>

                  {/* Indicador de pontos animados */}
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-hackathon-black rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-hackathon-black rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-hackathon-black rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && !showCommentary && (
        <div className="mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleShowSavedCommentary}
              variant="hackGreen"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]"
            >
              <Eye className="h-4 w-4" />
              Ver Análise da IA
            </Button>
            <HelpDialog />
          </div>
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && showCommentary && (
        <div className="space-y-4">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleHideCommentary}
              variant="outline"
              size="hack"
              className="flex items-center justify-center gap-2 border-2 border-black hover:bg-gray-50 max-w-[240px]"
            >
              <Eye className="h-4 w-4" />
              Ocultar Análise da IA
            </Button>

            <HelpDialog />
          </div>

          <Card className="animate-fade-in border-2 border-black shadow-card">
            <CardHeader className="border-b-2 border-black bg-hackathon-lightBg py-4">
              <div className="flex items-center justify-between">
                <div className="flex-1"></div>
                <CardTitle className="flex items-center justify-center gap-2 text-lg">
                  <Brain className="h-5 w-5 text-hackathon-black" />
                  Análise da Questão
                </CardTitle>
                <div className="flex-1 flex justify-end">
                  <QuestionAICommentaryFeedback
                    questionId={question.id}
                    userId={question.owner || ''}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4 p-4">
              {activeCommentary?.alternativas?.map((alt, index) => (
                <div
                  key={index}
                  className={`rounded-lg p-4 border-2 transition-all duration-200 transform hover:translate-y-[-2px] ${
                    alt.correta
                      ? 'border-hackathon-green bg-green-50/50 hover:bg-green-50'
                      : 'border-hackathon-red bg-red-50/50 hover:bg-red-50'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    {alt.correta ? (
                      <CheckCircle className="h-5 w-5 text-hackathon-green mt-1 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-hackathon-red mt-1 flex-shrink-0" />
                    )}
                    <div>
                      <h4 className="font-bold mb-2">
                        {alt.texto}
                        {alt.correta && " (Correta)"}
                      </h4>
                      <div
                        className="text-gray-700 leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: cleanMalformedJson(alt.comentario) }}
                      />
                    </div>
                  </div>
                </div>
              ))}

              {activeCommentary?.comentario_final && (
                <div className="rounded-lg p-4 border-2 border-black bg-hackathon-lightBg hover:bg-hackathon-lightBg/80 transition-all duration-200 transform hover:translate-y-[-2px]">
                  <h4 className="font-bold mb-2 flex items-center gap-2">
                    <Brain className="h-4 w-4 text-hackathon-black" />
                    Comentário Final
                  </h4>
                  <div
                    className="text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{ __html: cleanMalformedJson(activeCommentary.comentario_final) }}
                  />
                </div>
              )}

              {activeCommentary?.possivel_erro_no_gabarito && activeCommentary?.justificativa_erro_gabarito && (
                <Alert variant="destructive" className="border-2 border-orange-500 bg-orange-50">
                  <AlertDescription className="flex items-start gap-2">
                    <span className="font-semibold text-orange-700">⚠️ Possível erro no gabarito:</span>
                    <span
                      className="text-orange-700"
                      dangerouslySetInnerHTML={{ __html: activeCommentary.justificativa_erro_gabarito }}
                    />
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {error && (
        <Card className="border-2 border-orange-400 bg-orange-50 max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <div className="flex flex-col items-center gap-4">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full">
                <Brain className="h-6 w-6 text-orange-600" />
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-orange-800">
                  Análise temporariamente indisponível
                </h3>
                <p className="text-sm text-orange-700 leading-relaxed">
                  {error}
                </p>
                <p className="text-xs text-orange-600">
                  Você pode tentar novamente em alguns minutos ou continuar estudando normalmente.
                </p>
              </div>
              <Button
                onClick={() => {
                  resetCommentary();
                  handleGenerateCommentary();
                }}
                variant="outline"
                size="sm"
                className="border-orange-300 text-orange-700 hover:bg-orange-100"
              >
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default QuestionAICommentary;
