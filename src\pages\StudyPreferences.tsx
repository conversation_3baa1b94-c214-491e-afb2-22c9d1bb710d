import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Target,
  Building2,
  Calendar,
  ArrowRight,
  CheckCircle,
  X,
  Search,
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useStudyPreferences, StudyPreferencesFormData } from '@/hooks/useStudyPreferences';
import { useAuth } from '@/contexts/AuthContext';

export default function StudyPreferences() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    preferences,
    availableInstitutions,
    isLoadingInstitutions,
    savePreferences,
    isSaving
  } = useStudyPreferences();

  // Não redirecionar automaticamente - deixar usuário escolher onde quer editar

  const [formData, setFormData] = useState<StudyPreferencesFormData>({
    target_specialty: '',
    specialty_unknown: false,
    study_months: 6,
    target_institutions: [],
    institutions_unknown: false
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Filtrar instituições baseado na busca
  const filteredInstitutions = availableInstitutions?.filter(institution =>
    institution.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Paginação
  const totalPages = Math.ceil(filteredInstitutions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedInstitutions = filteredInstitutions.slice(startIndex, endIndex);

  // Reset página quando busca muda
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleSpecialtyChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      target_specialty: value,
      specialty_unknown: false
    }));
  };

  const handleSpecialtyUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      specialty_unknown: checked,
      target_specialty: checked ? '' : prev.target_specialty
    }));
  };

  const handleInstitutionToggle = (institutionId: string) => {
    if (formData.institutions_unknown) return;

    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.includes(institutionId)
        ? prev.target_institutions.filter(id => id !== institutionId)
        : [...prev.target_institutions, institutionId]
    }));
  };

  const handleInstitutionsUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      institutions_unknown: checked,
      target_institutions: checked ? [] : prev.target_institutions
    }));
  };

  const handleStudyMonthsChange = (value: number[]) => {
    setFormData(prev => ({
      ...prev,
      study_months: value[0]
    }));
  };

  const removeInstitution = (institutionId: string) => {
    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.filter(id => id !== institutionId)
    }));
  };

  const getSelectedInstitutionNames = () => {
    return availableInstitutions
      ?.filter(inst => formData.target_institutions.includes(inst.id))
      .map(inst => inst.name) || [];
  };

  const isFormValid = () => {
    const hasSpecialty = formData.specialty_unknown || formData.target_specialty.trim().length > 0;
    const hasInstitutions = formData.institutions_unknown || formData.target_institutions.length > 0;
    const hasStudyTime = formData.study_months > 0;
    return hasSpecialty && hasInstitutions && hasStudyTime;
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;

    try {
      await savePreferences(formData);
      navigate('/plataformadeestudos');
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
    }
  };

  if (!user) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FEF7CD] via-[#FEF7CD] to-[#F0E68C] flex items-center justify-center p-4">
      <div className="container max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex justify-center mb-6">
            <div className="bg-white border-2 border-black px-5 py-2 shadow-card-sm relative">
              <span className="font-bold text-2xl tracking-tight">MedEvo</span>
              <div className="absolute -right-2 -top-2">
                <span className="bg-hackathon-red text-white text-xs px-2 py-0.5 rounded border border-black font-bold">
                  beta
                </span>
              </div>
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Personalize sua Jornada de Estudos
          </h1>
          <p className="text-gray-600 text-lg">
            Vamos configurar seu perfil para criar um cronograma personalizado
          </p>
        </motion.div>

        {/* Form Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="border-2 border-black shadow-card-lg bg-white">
            <CardContent className="p-8 space-y-8">
              
              {/* Especialidade */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Target className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Especialidade Desejada</h3>
                    <p className="text-gray-600">Qual especialidade você pretende seguir?</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="specialty-unknown"
                      checked={formData.specialty_unknown}
                      onCheckedChange={handleSpecialtyUnknownChange}
                    />
                    <Label htmlFor="specialty-unknown" className="text-sm font-medium">
                      Ainda não sei / Estou decidindo
                    </Label>
                  </div>

                  {!formData.specialty_unknown && (
                    <div className="space-y-2">
                      <Label htmlFor="specialty" className="text-sm font-medium">
                        Digite a especialidade
                      </Label>
                      <Input
                        id="specialty"
                        placeholder="Ex: Pediatria, Cardiologia, Ortopedia..."
                        value={formData.target_specialty}
                        onChange={(e) => handleSpecialtyChange(e.target.value)}
                        className="border-2 border-gray-300 focus:border-blue-500"
                      />
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Instituições */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <Building2 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Instituições Alvo</h3>
                    <p className="text-gray-600">Onde você pretende fazer residência?</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="institutions-unknown"
                      checked={formData.institutions_unknown}
                      onCheckedChange={handleInstitutionsUnknownChange}
                    />
                    <Label htmlFor="institutions-unknown" className="text-sm font-medium">
                      Ainda não sei / Qualquer instituição
                    </Label>
                  </div>

                  {!formData.institutions_unknown && (
                    <>
                      {/* Instituições Selecionadas */}
                      {formData.target_institutions.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Instituições Selecionadas ({formData.target_institutions.length})
                          </Label>
                          <div className="flex flex-wrap gap-2">
                            {getSelectedInstitutionNames().map((name, index) => (
                              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                <Building2 className="h-3 w-3" />
                                {name}
                                <X
                                  className="h-3 w-3 cursor-pointer hover:text-red-500"
                                  onClick={() => removeInstitution(formData.target_institutions[index])}
                                />
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Busca de Instituições */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Buscar Instituições</Label>
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Buscar instituições..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 border-2 border-gray-300 focus:border-green-500"
                          />
                        </div>
                      </div>

                      {/* Lista de Instituições */}
                      <div className="max-h-48 overflow-y-auto border-2 border-gray-200 rounded-lg p-3 space-y-2">
                        {isLoadingInstitutions ? (
                          <div className="text-center py-4 text-gray-500">
                            Carregando instituições...
                          </div>
                        ) : filteredInstitutions.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            Nenhuma instituição encontrada
                          </div>
                        ) : (
                          paginatedInstitutions.map((institution) => (
                            <div
                              key={institution.id}
                              className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
                              onClick={() => handleInstitutionToggle(institution.id)}
                            >
                              <Checkbox
                                checked={formData.target_institutions.includes(institution.id)}
                                onChange={() => {}} // Controlled by parent click
                              />
                              <Label className="flex-1 text-sm cursor-pointer">
                                {institution.name}
                              </Label>
                            </div>
                          ))
                        )}
                      </div>

                      {/* Paginação */}
                      {totalPages > 1 && (
                        <div className="flex items-center justify-between px-3 py-2 border-t-2 border-gray-200 bg-gray-50 rounded-b-lg">
                          <div className="text-xs text-gray-500">
                            Página {currentPage} de {totalPages} ({filteredInstitutions.length} instituições)
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                              disabled={currentPage === 1}
                              className="h-8 w-8 p-0 border-2 border-black"
                            >
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                            <span className="text-xs text-gray-600 px-2 font-medium">
                              {currentPage}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                              disabled={currentPage === totalPages}
                              className="h-8 w-8 p-0 border-2 border-black"
                            >
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              <Separator />

              {/* Tempo de Estudo */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-full">
                    <Clock className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Tempo de Estudo</h3>
                    <p className="text-gray-600">Quantos meses você tem para estudar?</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-center">
                    <span className="text-3xl font-bold text-purple-600">
                      {formData.study_months}
                    </span>
                    <span className="text-lg text-gray-600 ml-2">
                      {formData.study_months === 1 ? 'mês' : 'meses'}
                    </span>
                  </div>

                  <div className="px-4">
                    <Slider
                      value={[formData.study_months]}
                      onValueChange={handleStudyMonthsChange}
                      max={12}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>1 mês</span>
                      <span>12 meses</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <Button
                  onClick={handleSubmit}
                  disabled={!isFormValid() || isSaving}
                  className="w-full h-12 text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-2 border-black shadow-card-sm"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Finalizar Configuração
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
