import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useHierarchyData = (
  selectedSpecialty: string | undefined,
  selectedTheme: string | undefined,
  selectedFocus: string | undefined
) => {
  const [specialties, setSpecialties] = useState<any[]>([]);
  const [themes, setThemes] = useState<any[]>([]);
  const [focuses, setFocuses] = useState<any[]>([]);
  const [extraFocuses, setExtraFocuses] = useState<any[]>([]);

  useEffect(() => {
    fetchSpecialties();
  }, []);

  useEffect(() => {
    if (selectedSpecialty) {
      fetchThemes(selectedSpecialty);
    } else {
      setThemes([]);
    }
  }, [selectedSpecialty]);

  useEffect(() => {
    if (selectedTheme) {
      fetchFocuses(selectedTheme);
    } else {
      setFocuses([]);
    }
  }, [selectedTheme]);

  useEffect(() => {
    if (selectedFocus) {
      fetchExtraFocuses(selectedFocus);
    } else {
      setExtraFocuses([]);
    }
  }, [selectedFocus]);

  const fetchSpecialties = async () => {
    try {
      const { data, error } = await supabase
        .from('flashcards_specialty')
        .select('*')
        .order('name');

      if (error) {
        toast.error('Erro ao carregar especialidades');
        return;
      }

      if (!data) {
        setSpecialties([]);
        return;
      }

      setSpecialties(data);
    } catch (error) {
      toast.error('Erro ao carregar especialidades');
    }
  };

  const fetchThemes = async (specialtyId: string) => {
    try {
      const { data, error } = await supabase
        .from('flashcards_theme')
        .select('*')
        .eq('specialty_id', specialtyId)
        .order('name');

      if (error) {
        toast.error('Erro ao carregar temas');
        return;
      }

      setThemes(data || []);
    } catch (error) {
      toast.error('Erro ao carregar temas');
    }
  };

  const fetchFocuses = async (themeId: string) => {
    try {
      const { data, error } = await supabase
        .from('flashcards_focus')
        .select('*')
        .eq('theme_id', themeId)
        .order('name');

      if (error) {
        toast.error('Erro ao carregar focos');
        return;
      }

      setFocuses(data || []);
    } catch (error) {
      toast.error('Erro ao carregar focos');
    }
  };

  const fetchExtraFocuses = async (focusId: string) => {
    try {
      const { data, error } = await supabase
        .from('flashcards_extrafocus')
        .select('*')
        .eq('focus_id', focusId)
        .order('name');

      if (error) {
        toast.error('Erro ao carregar extra focos');
        return;
      }

      setExtraFocuses(data || []);
    } catch (error) {
      toast.error('Erro ao carregar extra focos');
    }
  };

  return {
    specialties,
    themes,
    focuses,
    extraFocuses,
    fetchSpecialties,
    fetchThemes,
    fetchFocuses,
    fetchExtraFocuses
  };
};
