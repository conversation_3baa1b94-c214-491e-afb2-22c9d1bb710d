
import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Clock, CheckCircle, BookOpen } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { formatDistance } from "date-fns";
import { ptBR } from "date-fns/locale";

interface SessionDetails {
  total_cards: number;
  correct_cards: number;
  start_time: string;
  end_time: string;
  specialty?: { id: string; name: string };
  theme?: { id: string; name: string };
  focus?: { id: string; name: string };
  extrafocus?: { id: string; name: string };
}

export const FlashcardSessionComplete = ({ sessionId }: { sessionId: string }) => {
  const navigate = useNavigate();
  const [sessionDetails, setSessionDetails] = useState<SessionDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSessionDetails = async () => {
      try {
        setIsLoading(true);
        console.log(`🔍 [FlashcardSessionComplete] Fetching session details for ${sessionId}`);
        
        // First get the basic session data
        const { data: session, error } = await supabase
          .from("flashcards_sessions")
          .select(`
            *,
            cards
          `)
          .eq("id", sessionId)
          .single();

        if (error) {
          console.error("❌ [FlashcardSessionComplete] Error fetching session details:", error);
          return;
        }
        
        console.log(`✅ [FlashcardSessionComplete] Session data:`, session);
        
        if (!session) {
          console.error("❌ [FlashcardSessionComplete] No session found");
          return;
        }
        
        // Get the total number of cards from the session.cards array
        const totalCards = Array.isArray(session.cards) ? session.cards.length : 0;
        console.log(`📊 [FlashcardSessionComplete] Cards in session: ${totalCards}`);
        
        // If we have cards, get the first card's details to show hierarchy information
        let hierarchyInfo = {};
        
        if (totalCards > 0 && session.cards[0]) {
          const { data: cardDetails, error: cardError } = await supabase
            .from('flashcards_cards')
            .select(`
              id,
              specialty:flashcards_specialty(*),
              theme:flashcards_theme(*),
              focus:flashcards_focus(*),
              extrafocus:flashcards_extrafocus(*)
            `)
            .eq('id', session.cards[0])
            .single();
            
          if (cardError) {
            console.error("❌ [FlashcardSessionComplete] Error fetching card details:", cardError);
          } else if (cardDetails) {
            console.log(`✅ [FlashcardSessionComplete] Card hierarchy details:`, cardDetails);
            hierarchyInfo = {
              specialty: cardDetails.specialty,
              theme: cardDetails.theme,
              focus: cardDetails.focus,
              extrafocus: cardDetails.extrafocus
            };
          }
        }
        
        // Calculate correct cards from session_cards table
        const { data: reviewedCards, error: reviewedError } = await supabase
          .from('flashcards_session_cards')
          .select('response')
          .eq('session_id', sessionId);
          
        const correctCards = reviewedCards ? 
          reviewedCards.filter(card => 
            card.response === 'easy' || card.response === 'medium'
          ).length : 0;
          
        console.log(`📊 [FlashcardSessionComplete] Correct cards: ${correctCards}/${totalCards}`);
        
        setSessionDetails({
          total_cards: totalCards,
          correct_cards: correctCards || 0,
          start_time: session.start_time,
          end_time: session.end_time,
          ...hierarchyInfo
        });
      } catch (err) {
        console.error("❌ [FlashcardSessionComplete] Error in fetchSessionDetails:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessionDetails();
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando detalhes da sessão...</p>
      </div>
    );
  }

  if (!sessionDetails) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card className="p-6 text-center">
          <p className="text-gray-600">Não foi possível carregar os detalhes da sessão.</p>
          <Button onClick={() => navigate("/flashcards")} className="mt-4">
            Voltar para Flashcards
          </Button>
        </Card>
      </div>
    );
  }

  const duration = sessionDetails.end_time && sessionDetails.start_time
    ? formatDistance(
        new Date(sessionDetails.end_time),
        new Date(sessionDetails.start_time),
        { locale: ptBR }
      )
    : "Tempo indisponível";

  return (
    <div className="max-w-2xl mx-auto p-6">
      <Card className="bg-white border-2 border-black rounded-xl shadow-button p-8 space-y-6 transform hover:translate-y-0.5 hover:shadow-sm transition-all">
        <div className="flex items-center justify-center text-green-500 mb-6">
          <div className="icon-container bg-green-50">
            <CheckCircle size={32} className="text-green-500" />
          </div>
        </div>

        <h1 className="text-2xl font-bold text-center mb-6 slanted-title">
          Sessão Finalizada com Sucesso!
        </h1>

        <div className="space-y-4 filter-card p-6 rounded-xl">
          <div className="flex items-center gap-3 text-gray-700">
            <div className="icon-container bg-orange-50">
              <BookOpen className="text-orange-500" size={20} />
            </div>
            <span className="font-medium">Total de cartões: {sessionDetails.total_cards}</span>
          </div>

          <div className="flex items-center gap-3 text-gray-700">
            <div className="icon-container bg-green-50">
              <CheckCircle className="text-green-500" size={20} />
            </div>
            <span className="font-medium">Cartões corretos: {sessionDetails.correct_cards}</span>
          </div>

          <div className="flex items-center gap-3 text-gray-700">
            <div className="icon-container bg-blue-50">
              <Clock className="text-blue-500" size={20} />
            </div>
            <span className="font-medium">Duração da sessão: {duration}</span>
          </div>

          <div className="border-t-2 border-black/10 pt-4 mt-4">
            <h3 className="font-bold mb-3">Categorias estudadas:</h3>
            <ul className="space-y-2 text-gray-700">
              {sessionDetails.specialty && (
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                  Especialidade: {sessionDetails.specialty.name}
                </li>
              )}
              {sessionDetails.theme && (
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-purple-500"></span>
                  Tema: {sessionDetails.theme.name}
                </li>
              )}
              {sessionDetails.focus && (
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-green-500"></span>
                  Foco: {sessionDetails.focus.name}
                </li>
              )}
              {sessionDetails.extrafocus && (
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                  Extra Foco: {sessionDetails.extrafocus.name}
                </li>
              )}
            </ul>
          </div>
        </div>

        <div className="flex justify-center mt-8">
          <Button 
            onClick={() => navigate("/flashcards")}
            variant="hackGreen"
            size="hack"
            className="min-w-[200px]"
          >
            Voltar para Flashcards
          </Button>
        </div>
      </Card>
    </div>
  );
};
