
import { type FlashcardResponse } from "../types";
import { type PreCalculatedMetrics } from "@/utils/fsrs/types";
import { Button } from "@/components/ui/button";

interface ReviewButtonConfig {
  response: FlashcardResponse;
  label: string;
  className: string;
}

interface ReviewButtonProps {
  config: ReviewButtonConfig;
  metrics: PreCalculatedMetrics;
  onClick: () => void;
  isDisabled?: boolean;
}

export const ReviewButton = ({ config, metrics, onClick, isDisabled }: ReviewButtonProps) => {
  return (
    <Button
      onClick={onClick}
      disabled={isDisabled}
      className={`
        w-full border-2 border-black shadow-button transform-gpu
        transition-all duration-200 hover:translate-y-0.5 hover:shadow-sm 
        font-bold rounded-xl text-white
        ${config.className}
      `}
    >
      <div className="flex flex-col items-center justify-center gap-1">
        <span className="text-sm font-bold">{config.label}</span>
        <span className="text-xs font-medium">
          {metrics.intervalInDays === 0
            ? "Hoje"
            : `${metrics.intervalInDays} dias`}
        </span>
      </div>
    </Button>
  );
};
