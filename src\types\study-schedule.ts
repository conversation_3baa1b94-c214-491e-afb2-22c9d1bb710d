
export interface StudyTopic {
  id?: string;
  scheduleId?: string;
  specialty: string;
  theme: string;
  focus: string;
  specialtyId?: string;
  themeId?: string;
  focusId?: string;
  difficulty: 'Fácil' | 'Médio' | 'Difícil';
  activity: string;
  startTime: string; // Format: "HH:MM" (24-hour format)
  duration: string; // Format: "H:MM" for consistent display
  study_status?: 'pending' | 'completed';
  revision_number?: number;
  last_revision_date?: string;
  next_revision_date?: string;
  parent_item_id?: string;
  revision_chain?: string[];
  weekNumber: number;
  is_manual?: boolean;
  day: string;
  // ✅ NOVO: Informações das instituições que contemplam este foco
  institutions?: Array<{
    id: string;
    name: string;
    relevance: number;
    percentage: string;
  }>;
  focusPrevalence?: string;
}

export interface DaySchedule {
  day: string;
  scheduleId: string;
  weekStartDate: string;
  weekEndDate: string;
  weekNumber: number;
  totalHours: number;
  topics: StudyTopic[];
  calculatedTotalHours?: string;
}

export interface WeeklySchedule {
  recommendations: DaySchedule[];
  currentScheduleId?: string;
}

export interface StudyPeriod {
  startTime: string;
  endTime: string;
}

export interface DayConfig {
  enabled: boolean;
  periods: StudyPeriod[];
}

export interface AIScheduleOptions {
  availableDays: {
    [key: string]: DayConfig;
  };
  weeksCount: number;
  topicDuration: "15" | "30" | "60";
  scheduleOption: "new" | "existing";
  targetWeek?: number;
  domain?: string; // Added domain field to pass through to the schedule generation
  // Novos campos para filtro por instituição
  generationMode?: 'random' | 'institution_based';
  institutionIds?: string[];
  startYear?: number;
  endYear?: number;
}

export interface GenerationStats {
  totalTopics: number;
  totalWeeks: number;
  specialties: string[];
  themes: string[];
  focuses: string[];
  totalHours: number;
  generationTime: number;
  domain: string;
  totalSpecialties: number;
  totalThemes: number;
  totalFocuses: number;
  // ✅ NOVO: Informações sobre repetição de focos
  focusRepetitionWarning?: boolean;
  availableFocusesCount?: number;
  totalSlotsCount?: number;
}
