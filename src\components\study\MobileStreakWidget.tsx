import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CheckCircle, Star, Calendar, Trophy } from 'lucide-react';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '@/hooks/useAuth';

interface MobileStreakWidgetProps {
  userName?: string;
}

const MobileStreakWidget: React.FC<MobileStreakWidgetProps> = ({ userName }) => {
  const { user } = useAuth();
  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading,
    hasError,
    refreshStats
  } = useStreakSystem();

  // Obter nome do usuário dinamicamente
  const displayName = userName ||
    user?.user_metadata?.name ||
    user?.user_metadata?.full_name ||
    user?.email?.split('@')[0] ||
    'Estudante';

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      date,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-orange-50 via-white to-blue-50 rounded-2xl p-3 border-2 border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      {/* Layout Inovador: Horizontal Compacto */}
      <div className="flex items-center gap-3 mb-3">
        {/* Ícone Central */}
        <div className="bg-gradient-to-br from-orange-400 to-orange-600 p-2 rounded-full border-2 border-orange-300 shadow-md">
          <Flame className="h-5 w-5 text-white" />
        </div>

        {/* Informações Principais */}
        <div className="flex-1 min-w-0">
          {/* Linha 1: Saudação */}
          <p className="text-xs font-bold text-gray-700 truncate mb-0.5">
            {getGreeting()}, {displayName}! 👋
          </p>

          {/* Linha 2: Streak + Recorde */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <span className="text-sm font-bold text-orange-600">
                {isLoading ? '...' : currentStreak}
              </span>
              <span className="text-xs text-orange-500 font-medium">
                {currentStreak === 1 ? 'dia' : 'dias'}
              </span>
            </div>

            {maxStreak > 0 && (
              <div className="flex items-center gap-1 bg-amber-50 px-1.5 py-0.5 rounded-full border border-amber-200">
                <Trophy className="h-2.5 w-2.5 text-amber-600" />
                <span className="text-xs font-bold text-amber-700">{maxStreak}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Calendário Semanal Ultra Compacto */}
      <div className="bg-gradient-to-r from-gray-50 to-orange-50/30 rounded-xl p-2.5 border border-gray-200">
        {/* Header do calendário */}
        <div className="flex items-center justify-center mb-2">
          <div className="flex items-center gap-1 text-xs text-gray-600 bg-white px-2 py-1 rounded-full border border-gray-300">
            <Calendar className="h-3 w-3" />
            <span className="font-bold">Dom → Sáb</span>
          </div>
        </div>

        {/* Dias da semana em linha */}
        <div className="flex items-center justify-between gap-1">
          {weekDays.map((day, index) => (
            <motion.div
              key={day.key}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.03 * index }}
              className="flex flex-col items-center flex-1 min-w-0"
            >
              {/* Letra do dia */}
              <span className={`text-xs font-bold mb-1 transition-colors ${
                day.isCurrentDay
                  ? 'text-blue-600'
                  : day.isActive
                    ? 'text-orange-500'
                    : 'text-gray-500'
              }`}>
                {day.shortName}
              </span>

              {/* Círculo do dia - menor e mais elegante */}
              <div className={`
                w-5 h-5 rounded-full flex items-center justify-center border transition-all duration-300 transform hover:scale-110
                ${day.isActive
                  ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white border-orange-300 shadow-md'
                  : day.isCurrentDay
                    ? 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 border-blue-300 ring-1 ring-blue-200'
                    : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-400 border-gray-300'
                }
              `}>
                {day.isActive ? (
                  <CheckCircle className="h-2.5 w-2.5" />
                ) : day.isCurrentDay ? (
                  <Star className="h-2 w-2" />
                ) : null}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Quote compacta com design melhorado */}
      <div className="mt-2.5 text-center bg-blue-50/50 rounded-lg p-2 border border-blue-100">
        <p className="text-xs italic text-gray-700 leading-relaxed font-medium">
          "O sucesso é a soma de pequenos esforços repetidos dia após dia."
        </p>
      </div>

      {/* Indicador de erro */}
      {hasError && (
        <div className="mt-2 text-center">
          <button
            onClick={refreshStats}
            className="text-xs text-red-500 hover:text-red-600 font-medium"
          >
            ↻ Tentar novamente
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default MobileStreakWidget;
