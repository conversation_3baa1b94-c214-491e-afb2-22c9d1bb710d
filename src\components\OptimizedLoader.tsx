import React from 'react';

interface OptimizedLoaderProps {
  height?: string;
  className?: string;
  children?: React.ReactNode;
}

/**
 * Componente de loading otimizado para reduzir queries desnecessárias
 * Usado para lazy loading de componentes pesados
 */
export const OptimizedLoader: React.FC<OptimizedLoaderProps> = ({ 
  height = "h-32", 
  className = "",
  children 
}) => {
  return (
    <div className={`${height} bg-gray-100 rounded-lg animate-pulse ${className}`}>
      {children && (
        <div className="flex items-center justify-center h-full text-gray-500 text-sm">
          {children}
        </div>
      )}
    </div>
  );
};

export default OptimizedLoader;
