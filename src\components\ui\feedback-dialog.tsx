import React, { createContext, useContext, useState, ReactNode } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertTriangle, Info } from "lucide-react";

export type FeedbackType = 'success' | 'error' | 'warning' | 'info';

interface FeedbackDialogData {
  type: FeedbackType;
  title: string;
  description: string;
  isOpen: boolean;
}

interface FeedbackDialogContextType {
  showFeedback: (type: FeedbackType, title: string, description: string) => void;
  closeFeedback: () => void;
}

const FeedbackDialogContext = createContext<FeedbackDialogContextType | undefined>(undefined);

export const useFeedbackDialog = () => {
  const context = useContext(FeedbackDialogContext);
  if (!context) {
    throw new Error('useFeedbackDialog must be used within a FeedbackDialogProvider');
  }
  return context;
};

const getIconAndColor = (type: FeedbackType) => {
  switch (type) {
    case 'success':
      return {
        icon: CheckCircle,
        iconColor: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200'
      };
    case 'error':
      return {
        icon: XCircle,
        iconColor: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      };
    case 'warning':
      return {
        icon: AlertTriangle,
        iconColor: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200'
      };
    case 'info':
      return {
        icon: Info,
        iconColor: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200'
      };
  }
};

export const FeedbackDialogProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [dialogData, setDialogData] = useState<FeedbackDialogData>({
    type: 'info',
    title: '',
    description: '',
    isOpen: false,
  });

  const showFeedback = (type: FeedbackType, title: string, description: string) => {
    setDialogData({
      type,
      title,
      description,
      isOpen: true,
    });
  };

  const closeFeedback = () => {
    setDialogData(prev => ({ ...prev, isOpen: false }));
  };

  const { icon: Icon, iconColor, bgColor, borderColor } = getIconAndColor(dialogData.type);

  return (
    <FeedbackDialogContext.Provider value={{ showFeedback, closeFeedback }}>
      {children}
      <Dialog open={dialogData.isOpen} onOpenChange={closeFeedback}>
        <DialogContent className={`
          w-[90dvw] max-w-md max-h-[80dvh]
          ${bgColor} ${borderColor} border-2
          rounded-2xl overflow-y-auto
          mx-auto my-auto
          flex flex-col justify-center items-center
          p-6
        `}>
          <DialogHeader className="text-center w-full">
            <div className="flex flex-col items-center gap-3 mb-4">
              <div className={`p-3 rounded-full ${bgColor} ${borderColor} border`}>
                <Icon className={`h-8 w-8 ${iconColor}`} />
              </div>
              <DialogTitle className="text-xl font-bold text-gray-900 text-center">
                {dialogData.title}
              </DialogTitle>
            </div>
            <DialogDescription className="text-gray-700 text-base leading-relaxed text-center">
              {dialogData.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center mt-6 w-full">
            <Button
              onClick={closeFeedback}
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-2 rounded-xl font-semibold"
            >
              OK
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </FeedbackDialogContext.Provider>
  );
};
