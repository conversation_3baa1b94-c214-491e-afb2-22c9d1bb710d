import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogPortal,
  DialogOverlay
} from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { BookOpen, PenLine, X, HelpCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

// Custom DialogContent without automatic close button
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-black bg-white p-6 shadow-card-sm sm:rounded-xl max-h-[90vh] overflow-y-auto",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
))
CustomDialogContent.displayName = DialogPrimitive.Content.displayName;

interface TopicSourceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectSource: (source: 'platform' | 'manual') => void;
  dayInfo?: {
    dayIndex: number;
    weekIndex: number;
    day: string;
    scheduleId: string;
  };
}

export const TopicSourceDialog: React.FC<TopicSourceDialogProps> = ({
  open,
  onOpenChange,
  onSelectSource,
  dayInfo,
}) => {
  // console.log("🔄 Renderizando TopicSourceDialog, estado:", {
  //   open,
  //   dayOfWeek: dayInfo?.day,
  //   weekNumber: dayInfo?.weekIndex,
  //   scheduleId: dayInfo?.scheduleId
  // });

  const handlePlatformClick = () => {
    // console.log("🎯 TopicSourceDialog - Platform button clicked", {
    //   dayOfWeek: dayInfo?.day,
    //   weekNumber: dayInfo?.weekIndex,
    //   scheduleId: dayInfo?.scheduleId
    // });
    onSelectSource('platform');
  };

  const handleManualClick = () => {
    // console.log("✏️ TopicSourceDialog - Manual button clicked", {
    //   dayOfWeek: dayInfo?.day,
    //   weekNumber: dayInfo?.weekIndex,
    //   scheduleId: dayInfo?.scheduleId
    // });
    onSelectSource('manual');
  };


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent className="w-[95vw] max-w-[425px] max-h-[85dvh] p-0 gap-0 overflow-hidden border-2 border-black rounded-xl">
        <DialogHeader className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-4 sm:p-5 relative rounded-t-xl">
          <div className="flex items-center justify-between gap-3">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-lg sm:text-xl font-bold text-center">Adicionar Tópico</DialogTitle>
              <DialogDescription className="text-white/90 text-center mt-1 text-sm">
                Escolha como você deseja adicionar um tópico ao seu cronograma de estudos
              </DialogDescription>
            </div>

            {/* Single close button */}
            <button
              onClick={() => onOpenChange(false)}
              className="flex-shrink-0 p-1.5 sm:p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              aria-label="Fechar"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </DialogHeader>

        <div className="p-3 sm:p-4 flex flex-col gap-3 sm:gap-4 w-full overflow-y-auto">
          {/* Informativo */}
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 flex items-start gap-2 sm:gap-3">
            <HelpCircle className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 flex-shrink-0 mt-0.5" />
            <p className="text-xs sm:text-sm text-blue-700 break-words">
              Você pode selecionar um tópico pronto da nossa plataforma ou criar seu próprio tópico personalizado.
            </p>
          </div>

          {/* Plataforma */}
          <Button
            variant="outline"
            className="flex items-center justify-center w-full h-auto px-3 sm:px-4 py-3 border-2 border-blue-100 hover:border-blue-300 hover:bg-blue-50 rounded-xl"
            onClick={handlePlatformClick}
          >
            <div className="flex w-full items-center gap-2 sm:gap-3 min-w-0">
              <div className="bg-blue-100 p-2 sm:p-2.5 rounded-full transition-colors flex-shrink-0">
                <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
              </div>
              <div className="text-start flex-grow overflow-hidden">
                <div className="flex items-center gap-2 flex-wrap">
                  <h3 className="font-bold text-blue-600 text-sm sm:text-base">Tópico da Plataforma</h3>
                  <span className="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full">Recomendado</span>
                </div>
                <p className="text-xs text-gray-500 mt-1 break-words overflow-hidden">
                  Escolha entre centenas de temas organizados por especialidade.
                  Ideal para quem não sabe por onde começar.
                </p>
              </div>
            </div>
          </Button>

          <div className="pl-3 sm:pl-4">
            <ul className="space-y-1.5 sm:space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Tópicos estruturados por especialistas</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Acompanhamento de revisões programadas</span>
              </li>
            </ul>
          </div>

          {/* Manual */}
          <Button
            variant="outline"
            className="flex items-center justify-center w-full h-auto px-3 sm:px-4 py-3 border-2 border-purple-100 hover:border-purple-300 hover:bg-purple-50 rounded-xl"
            onClick={handleManualClick}
          >
            <div className="flex w-full items-center gap-2 sm:gap-3 min-w-0">
              <div className="bg-purple-100 p-2 sm:p-2.5 rounded-full transition-colors flex-shrink-0">
                <PenLine className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
              </div>
              <div className="text-start flex-grow overflow-hidden">
                <h3 className="font-bold text-purple-600 text-sm sm:text-base">Tópico Manual</h3>
                <p className="text-xs text-gray-500 mt-1 break-words overflow-hidden">
                  Crie seu próprio tópico personalizado com conteúdo específico
                  para suas necessidades de estudo.
                </p>
              </div>
            </div>
          </Button>

          <div className="pl-3 sm:pl-4">
            <ul className="space-y-1.5 sm:space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Flexibilidade total para personalizar conteúdos</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Ideal para tópicos específicos não cobertos pela plataforma</span>
              </li>
            </ul>
          </div>
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};

export default TopicSourceDialog;
