-- =====================================================
-- ÍNDICES DE PERFORMANCE PARA OTIMIZAÇÃO DE QUESTÕES
-- =====================================================

-- Verificar índices existentes antes de criar novos
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'questions'
ORDER BY indexname;

-- =====================================================
-- ÍNDICES COMPOSTOS PARA FILTROS FREQUENTES
-- =====================================================

-- Índice principal para domain + specialty (muito usado)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_specialty 
ON questions(domain, specialty_id) 
WHERE domain IS NOT NULL AND specialty_id IS NOT NULL;

-- Índice para domain + theme
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_theme 
ON questions(domain, theme_id) 
WHERE domain IS NOT NULL AND theme_id IS NOT NULL;

-- Índice para domain + focus
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_focus 
ON questions(domain, focus_id) 
WHERE domain IS NOT NULL AND focus_id IS NOT NULL;

-- Índice para domain + year (filtro temporal comum)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_year 
ON questions(domain, year) 
WHERE domain IS NOT NULL AND year IS NOT NULL;

-- Índice para domain + location
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_location 
ON questions(domain, location_id) 
WHERE domain IS NOT NULL AND location_id IS NOT NULL;

-- Índice para domain + question_type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_type 
ON questions(domain, question_type) 
WHERE domain IS NOT NULL AND question_type IS NOT NULL;

-- =====================================================
-- ÍNDICES PARA COMBINAÇÕES MÚLTIPLAS
-- =====================================================

-- Índice para domain + specialty + theme (combinação frequente)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_specialty_theme 
ON questions(domain, specialty_id, theme_id) 
WHERE domain IS NOT NULL AND specialty_id IS NOT NULL AND theme_id IS NOT NULL;

-- Índice para domain + specialty + focus
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_specialty_focus 
ON questions(domain, specialty_id, focus_id) 
WHERE domain IS NOT NULL AND specialty_id IS NOT NULL AND focus_id IS NOT NULL;

-- Índice para domain + theme + focus
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_theme_focus 
ON questions(domain, theme_id, focus_id) 
WHERE domain IS NOT NULL AND theme_id IS NOT NULL AND focus_id IS NOT NULL;

-- =====================================================
-- ÍNDICES PARA ORDENAÇÃO E PAGINAÇÃO
-- =====================================================

-- Índice para ordenação por data de criação com domain
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_created 
ON questions(domain, created_at DESC) 
WHERE domain IS NOT NULL;

-- Índice para ordenação por ID com domain (para paginação consistente)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_domain_id 
ON questions(domain, id) 
WHERE domain IS NOT NULL;

-- =====================================================
-- ÍNDICES PARA TABELAS RELACIONADAS
-- =====================================================

-- Otimizar joins com study_categories
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_study_categories_type_name 
ON study_categories(type, name) 
WHERE type IS NOT NULL;

-- Otimizar joins com exam_locations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exam_locations_name 
ON exam_locations(name) 
WHERE name IS NOT NULL;

-- Otimizar question_institutions para joins
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_question_institutions_question 
ON question_institutions(question_id, institution_id);

-- =====================================================
-- ÍNDICES PARA USER_ANSWERS (questões respondidas)
-- =====================================================

-- Índice para buscar respostas corretas por usuário
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_answers_user_correct 
ON user_answers(user_id, is_correct, question_id) 
WHERE is_correct = true;

-- Índice para performance de exclusão de questões respondidas
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_answers_question_user 
ON user_answers(question_id, user_id, is_correct);

-- =====================================================
-- ESTATÍSTICAS E ANÁLISE
-- =====================================================

-- Atualizar estatísticas das tabelas para melhor planejamento de queries
ANALYZE questions;
ANALYZE study_categories;
ANALYZE exam_locations;
ANALYZE question_institutions;
ANALYZE user_answers;

-- =====================================================
-- VERIFICAÇÃO DOS NOVOS ÍNDICES
-- =====================================================

-- Verificar tamanho dos índices criados
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('questions', 'study_categories', 'exam_locations', 'question_institutions', 'user_answers')
ORDER BY pg_relation_size(indexrelid) DESC;

-- Verificar uso dos índices
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
AND tablename = 'questions'
ORDER BY idx_scan DESC;

-- =====================================================
-- QUERY DE EXEMPLO PARA TESTAR PERFORMANCE
-- =====================================================

-- Exemplo de query que deve ser otimizada pelos novos índices
EXPLAIN (ANALYZE, BUFFERS) 
SELECT q.id, q.statement, q.year,
       s.name as specialty_name,
       t.name as theme_name,
       f.name as focus_name
FROM questions q
LEFT JOIN study_categories s ON q.specialty_id = s.id
LEFT JOIN study_categories t ON q.theme_id = t.id  
LEFT JOIN study_categories f ON q.focus_id = f.id
WHERE q.domain = 'residencia'
  AND q.specialty_id = 'some-specialty-id'
  AND q.year >= 2020
ORDER BY q.created_at DESC
LIMIT 50;

-- =====================================================
-- MANUTENÇÃO DOS ÍNDICES
-- =====================================================

-- Script para reindexar periodicamente (executar durante manutenção)
-- REINDEX INDEX CONCURRENTLY idx_questions_domain_specialty;
-- REINDEX INDEX CONCURRENTLY idx_questions_domain_theme;
-- REINDEX INDEX CONCURRENTLY idx_questions_domain_focus;

-- Verificar fragmentação dos índices
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as size,
    round(100 * pg_relation_size(indexrelid) / pg_relation_size(indrelid), 2) as ratio
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
AND tablename = 'questions'
ORDER BY pg_relation_size(indexrelid) DESC;
