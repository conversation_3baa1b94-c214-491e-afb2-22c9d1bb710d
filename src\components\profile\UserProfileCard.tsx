import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";
import { useUserData } from "@/hooks/useUserData";
import { getGravatarUrl } from "@/utils/gravatarUtils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface Profile {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  username: string | null;
  level: number;
  email?: string;
}

interface UserProfileCardProps {
  collapsed?: boolean;
}

export const UserProfileCard = ({ collapsed = false }: UserProfileCardProps) => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const navigate = useNavigate();

  const { user, profile: userData } = useUserData();

  const fetchProfile = async () => {
    if (!user || !userData) return;

    const gravatarUrl = getGravatarUrl(user.email || '');

    setProfile({
      ...userData,
      email: user.email || undefined,
      avatar_url: userData.avatar_url || gravatarUrl
    });
  };

  useEffect(() => {
    fetchProfile();

    const channel = supabase
      .channel('profile_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'profiles' },
        fetchProfile
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleProfileClick = () => {
    navigate('/profile');
  };

  if (!profile) return null;

  const content = (
    <div
      className={cn(
        "flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 cursor-pointer transition-all duration-200",
        collapsed && "justify-center p-1"
      )}
      onClick={handleProfileClick}
    >
      <Avatar className="h-10 w-10 shrink-0">
        <AvatarImage src={profile.avatar_url || ''} />
        <AvatarFallback>
          {profile.full_name?.charAt(0) || profile.username?.charAt(0) || '?'}
        </AvatarFallback>
      </Avatar>

      {!collapsed && (
        <div className="min-w-0">
          <div className="flex items-center gap-2">
            <p className="font-medium truncate">
              {profile.full_name || profile.username || profile.email || 'Usuário'}
            </p>
            <span className="shrink-0 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
              Nível {profile.level || 1}
            </span>
          </div>
          {profile.email && (
            <p className="text-xs text-muted-foreground truncate">{profile.email}</p>
          )}
        </div>
      )}
    </div>
  );

  if (collapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {content}
          </TooltipTrigger>
          <TooltipContent side="right" className="flex flex-col gap-1">
            <p className="font-medium">
              {profile.full_name || profile.username || 'Usuário'}
            </p>
            <p className="text-sm text-muted-foreground">{profile.email}</p>
            <p className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full inline-block">
              Nível {profile.level || 1}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
};