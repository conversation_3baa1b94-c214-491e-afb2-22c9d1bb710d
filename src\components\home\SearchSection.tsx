
import React, { useEffect } from "react";
import { SearchBar } from "@/components/search/SearchBar";

interface SearchSectionProps {
  searchPlaceholder: string;
}

export const SearchSection: React.FC<SearchSectionProps> = ({ searchPlaceholder }) => {
  useEffect(() => {
    // console.log("🔍 [SearchSection] Component rendered");
  }, []);
  
  return (
    <div className="max-w-2xl mx-auto relative z-10">
      <div className="relative group">
        <SearchBar customPlaceholder={searchPlaceholder || "Buscar medicamentos, condutas, calculadoras..."} />
      </div>
    </div>
  );
};
