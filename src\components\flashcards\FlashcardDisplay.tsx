import { Card } from "@/components/ui/card";
import { Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";
import type { FlashcardWithHierarchy } from "@/types/flashcard";
import { CardSide } from "./CardSide";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface FlashcardDisplayProps {
  front: string;
  back: string;
  frontImage?: string | null;
  backImage?: string | null;
  isFlipped: boolean;
  onFlip: () => void;
  createdAt?: string;
  userId?: string;
  currentCard?: FlashcardWithHierarchy;
  onResponse?: (response: 'error' | 'hard' | 'medium' | 'easy') => void;
}

export const FlashcardDisplay = ({ 
  front, 
  back, 
  frontImage,
  backImage,
  isFlipped, 
  onFlip,
  createdAt,
  userId,
  currentCard,
  onResponse
}: FlashcardDisplayProps) => {
  const [creatorName, setCreatorName] = useState<string>("Não disponível");
  const [frontImageOpen, setFrontImageOpen] = useState(false);
  const [backImageOpen, setBackImageOpen] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [originalCreatorName, setOriginalCreatorName] = useState<string | null>(null);

  useEffect(() => {
    
    if (currentCard?.user_id) {
      fetchCreatorName(currentCard.user_id);
    }

    const handleKeyPress = (e: KeyboardEvent) => {
      
      // Prevent handling if any input is focused
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA') {
        return;
      }

      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        if (!frontImageOpen && !backImageOpen) {
          onFlip();
        }
      } else if (isFlipped && onResponse) {
        switch (e.key) {
          case '1':
            onResponse('error');
            break;
          case '2':
            onResponse('hard');
            break;
          case '3':
            onResponse('medium');
            break;
          case '4':
            onResponse('easy');
            break;
          case 'h':
          case '?':
            setShowKeyboardShortcuts(prev => !prev);
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentCard, isFlipped, onFlip, onResponse, frontImageOpen, backImageOpen]);

  useEffect(() => {
    setFrontImageOpen(false);
    setBackImageOpen(false);
  }, [isFlipped]);

  useEffect(() => {
    async function fetchOriginalCreator() {
      if (currentCard?.origin_id) {
        const { data: cardOrig, error: cardOrigError } = await supabase
          .from('flashcards_cards')
          .select('user_id')
          .eq('id', currentCard.origin_id)
          .maybeSingle();

        if (cardOrig && cardOrig.user_id) {
          // Busca o nome do usuário original
          const { data: profOrig, error: profOrigError } = await supabase
            .from('profiles')
            .select('full_name, username')
            .eq('id', cardOrig.user_id)
            .maybeSingle();

          if (profOrig) {
            setOriginalCreatorName(profOrig.full_name || profOrig.username || "Usuário original");
          }
        }
      }
    }

    fetchOriginalCreator();
  }, [currentCard, isFlipped, onFlip, onResponse, frontImageOpen, backImageOpen]);

  const fetchCreatorName = async (uid: string) => {

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('full_name, username')
      .eq('id', uid)
      .single();

    if (error) {
      console.error('❌ [FlashcardDisplay] Error fetching creator name:', error);
      return;
    }

    if (profile) {
      setCreatorName(profile.full_name || profile.username || "Usuário");
    }
  };

  const handleFlip = (e: React.MouseEvent) => {
    if (!frontImageOpen && !backImageOpen) {
      onFlip();
    }
  };

  const formattedDate = currentCard?.created_at 
    ? format(new Date(currentCard.created_at), "dd 'de' MMMM 'de' yyyy 'às' HH:mm")
    : "Data não disponível";

  const isCopy = !!currentCard?.origin_id;

  return (
    <div className="relative w-full">
      <Card 
        className={`
          p-8 cursor-pointer min-h-[400px] flex flex-col
          bg-white border-2 border-black rounded-xl 
          shadow-card-sm hover:shadow-card transform-gpu
          group perspective-[1000px] transition-all duration-300
        `}
        onClick={handleFlip}
      >
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-2 right-2 h-8 w-8 
            text-muted-foreground hover:text-primary z-10
            opacity-70 group-hover:opacity-100 transition-opacity"
          onClick={(e) => {
            e.stopPropagation();
            setShowInfo(true);
          }}
        >
          <Info className="h-4 w-4" />
        </Button>

        <div className="flex-grow relative">
          <div 
            className={`
              absolute inset-0 w-full h-full
              transition-transform duration-500 ease-in-out
              transform-style-preserve-3d
              ${isFlipped ? 'rotate-y-180' : ''}
            `}
          >
            <CardSide
              content={front}
              image={frontImage}
              isImageOpen={frontImageOpen}
              onImageOpenChange={setFrontImageOpen}
              actionText="Pressione Espaço ou Enter para ver o verso"
              isVisible={!isFlipped}
            />
            
            <CardSide
              content={back}
              image={backImage}
              isImageOpen={backImageOpen}
              onImageOpenChange={setBackImageOpen}
              actionText="Pressione Espaço ou Enter para ver a frente"
              isVisible={isFlipped}
              className="rotate-y-180"
            />
          </div>
        </div>
      </Card>

      <Dialog open={showInfo} onOpenChange={setShowInfo}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto border-2 border-black bg-white shadow-card-sm">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              Informações do Flashcard
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            <div className="border-b-2 border-black/10 pb-4">
              <p className="text-lg font-bold mb-2">Criado por</p>
              {!isCopy ? (
                <p className="text-base">{creatorName}</p>
              ) : (
                <div className="space-y-1">
                  <p className="text-base">
                    <span className="font-semibold">Original:</span>{" "}
                    {originalCreatorName ? originalCreatorName : "Usuário desconhecido"}
                  </p>
                  <p className="text-base">
                    <span className="font-semibold">Copiado por:</span> {creatorName}
                  </p>
                </div>
              )}
              <p className="text-sm text-muted-foreground mt-1">{formattedDate}</p>
            </div>
            
            <div className="space-y-3">
              <p className="text-lg font-bold">Hierarquia</p>
              {currentCard?.hierarchy?.specialty && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Especialidade</p>
                  <p className="text-sm bg-gray-50 p-2 rounded-lg border-2 border-black/10">
                    {currentCard.hierarchy.specialty.name}
                  </p>
                </div>
              )}
              {currentCard?.hierarchy?.theme && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Tema</p>
                  <p className="text-sm bg-gray-50 p-2 rounded-lg border-2 border-black/10">
                    {currentCard.hierarchy.theme.name}
                  </p>
                </div>
              )}
              {currentCard?.hierarchy?.focus && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Foco</p>
                  <p className="text-sm bg-gray-50 p-2 rounded-lg border-2 border-black/10">
                    {currentCard.hierarchy.focus.name}
                  </p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {showKeyboardShortcuts && (
        <div className="absolute top-full left-0 mt-4 
        bg-white border-2 border-black rounded-lg shadow-card-sm 
        p-4 space-y-2 text-sm">
          <h4 className="font-semibold mb-2">Atalhos do Teclado:</h4>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">Space</span> / <span className="font-mono bg-muted px-2 py-0.5 rounded">Enter</span> - Mostra ou esconde resposta</p>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">1</span> - Errei</p>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">2</span> - Difícil</p>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">3</span> - Médio</p>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">4</span> - Fácil</p>
          <p><span className="font-mono bg-muted px-2 py-0.5 rounded">H</span> / <span className="font-mono bg-muted px-2 py-0.5 rounded">?</span> - Mostra/esconde atalhos</p>
        </div>
      )}
    </div>
  );
};
