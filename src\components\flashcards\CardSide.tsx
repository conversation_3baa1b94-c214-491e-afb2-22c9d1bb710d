import { FlashcardImage } from "./FlashcardImage";

interface CardSideProps {
  content: string;
  image?: string | null;
  isImageOpen: boolean;
  onImageOpenChange: (open: boolean) => void;
  actionText: string;
  isVisible: boolean;
  className?: string;
}

export const CardSide = ({
  content,
  image,
  isImageOpen,
  onImageOpenChange,
  actionText,
  isVisible,
  className = "",
}: CardSideProps) => {
  // Remover "Categoria:" do conteúdo principal, se presente
  const cleanContent = content.replace(/\n*Categoria:.*(\n|$)/g, "").trim();

  const formattedContent = cleanContent.split('\n').map((line, index) => (
    <span key={index}>
      {line}
      {index < cleanContent.split('\n').length - 1 && <br />}
    </span>
  ));
  
  return (
    <div 
      className={`
        absolute inset-0 w-full h-full
        backface-hidden 
        ${!isVisible ? 'invisible' : ''}
        ${className}
        bg-white rounded-lg
        transform-gpu
      `}
    >
      <div className="flex flex-col justify-between h-full">
        <div className="flex-1 flex flex-col justify-center space-y-6 py-4">
          <p className="text-lg text-center px-4 whitespace-pre-line">{cleanContent}</p>
          {image && (
            <div className="flex justify-center items-center py-2">
              <FlashcardImage 
                src={image} 
                alt="Card image" 
                isOpen={isImageOpen}
                onOpenChange={onImageOpenChange}
              />
            </div>
          )}
        </div>
        <div className="text-center py-4 text-sm text-muted-foreground">
          {actionText}
        </div>
      </div>
    </div>
  );
};
