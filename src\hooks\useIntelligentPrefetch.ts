import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

/**
 * Hook para prefetch inteligente de dados que o usuário provavelmente precisará
 * Carrega dados em background para melhorar a experiência
 */
export const useIntelligentPrefetch = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Prefetch dados estáticos na inicialização
  useEffect(() => {
    const prefetchStaticData = async () => {
      // Prefetch dados que são usados em quase todas as páginas
      const prefetchPromises = [
        // Categorias de estudo
        queryClient.prefetchQuery({
          queryKey: ['static-study-categories-all'],
          queryFn: async () => {
            console.log('📊 [useIntelligentPrefetch] Prefetching study categories...');

            const { data } = await supabase
              .from('study_categories')
              .select('id, name, type, parent_id')
              .order('type, name');

            console.log('✅ [useIntelligentPrefetch] Study categories prefetched:', data?.length || 0);
            return data || [];
          },
          staleTime: 60 * 60 * 1000, // 1 hora
        }),

        // Locais de exame
        queryClient.prefetchQuery({
          queryKey: ['static-locations'],
          queryFn: async () => {
            const { data } = await supabase
              .from('exam_locations')
              .select('*')
              .order('name');
            return data || [];
          },
          staleTime: 60 * 60 * 1000,
        }),

        // Hierarquia de flashcards
        queryClient.prefetchQuery({
          queryKey: ['static-flashcard-hierarchy-all'],
          queryFn: async () => {
            const [
              { data: specialties },
              { data: themes },
              { data: focuses },
              { data: extrafocuses }
            ] = await Promise.all([
              supabase.from('flashcards_specialty').select('*').order('name'),
              supabase.from('flashcards_theme').select('*').order('name'),
              supabase.from('flashcards_focus').select('*').order('name'),
              supabase.from('flashcards_extrafocus').select('*').order('name')
            ]);

            return {
              specialties: specialties || [],
              themes: themes || [],
              focuses: focuses || [],
              extrafocuses: extrafocuses || [],
            };
          },
          staleTime: 60 * 60 * 1000,
        }),
      ];

      try {
        await Promise.allSettled(prefetchPromises);
      } catch (error) {
        console.error('Erro no prefetch:', error);
      }
    };

    // Executar prefetch após um pequeno delay para não bloquear a UI inicial
    const timeoutId = setTimeout(prefetchStaticData, 1000);
    return () => clearTimeout(timeoutId);
  }, [queryClient]);

  // Prefetch baseado na navegação do usuário
  const prefetchForPage = async (page: string) => {
    switch (page) {
      case 'questions':
      case 'study':
        // Prefetch dados de questões mais comuns
        await prefetchQuestionData();
        break;

      case 'flashcards':
        // Prefetch dados de flashcards
        await prefetchFlashcardData();
        break;

      case 'progress':
        // Prefetch dados de progresso
        await prefetchProgressData();
        break;
    }
  };

  const prefetchQuestionData = async () => {
    try {
      // Prefetch anos mais comuns
      queryClient.prefetchQuery({
        queryKey: ['common-years'],
        queryFn: async () => {
          const { data } = await supabase
            .from('questions')
            .select('year')
            .gte('year', 2020)
            .limit(1000);

          const years = [...new Set(data?.map(q => q.year).filter(Boolean))].sort((a, b) => b - a);
          return years.slice(0, 10); // Top 10 anos mais recentes
        },
        staleTime: 30 * 60 * 1000, // 30 minutos
      });
    } catch (error) {
      console.error('Erro no prefetch de questões:', error);
    }
  };

  const prefetchFlashcardData = async () => {
    try {
      // Prefetch estatísticas básicas de flashcards do usuário
      if (!user?.id) return;

      queryClient.prefetchQuery({
        queryKey: ['user-flashcard-stats', user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('flashcards_cards')
            .select('id, specialty_id, theme_id, focus_id')
            .eq('user_id', user.id)
            .eq('current_state', 'available')
            .limit(100);

          return data || [];
        },
        staleTime: 10 * 60 * 1000, // 10 minutos
      });
    } catch (error) {
      console.error('Erro no prefetch de flashcards:', error);
    }
  };

  const prefetchProgressData = async () => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user?.id) return;

      // Prefetch estatísticas básicas de progresso
      queryClient.prefetchQuery({
        queryKey: ['user-basic-stats', user.id],
        queryFn: async () => {
          try {
            const { data, error } = await supabase
              .from('user_answers')
              .select('is_correct, created_at')
              .eq('user_id', user.id)
              .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Últimos 30 dias
              .limit(1000);

            if (error) return [];
            return data || [];
          } catch (error) {
            return [];
          }
        },
        staleTime: 5 * 60 * 1000, // 5 minutos
      });
    } catch (error) {
      // Erro silencioso no prefetch
    }
  };

  // Prefetch de dados relacionados quando o usuário interage com filtros
  const prefetchRelatedData = async (filters: any) => {
    if (!filters) return;

    try {
      // Se o usuário selecionou uma especialidade, prefetch temas relacionados
      if (filters.specialties?.length > 0) {
        queryClient.prefetchQuery({
          queryKey: ['related-themes', filters.specialties],
          queryFn: async () => {
            const { data } = await supabase
              .from('study_categories')
              .select('*')
              .eq('type', 'theme')
              .in('parent_id', filters.specialties)
              .order('name');

            return data || [];
          },
          staleTime: 30 * 60 * 1000,
        });
      }

      // Se selecionou tema, prefetch focos relacionados
      if (filters.themes?.length > 0) {
        queryClient.prefetchQuery({
          queryKey: ['related-focuses', filters.themes],
          queryFn: async () => {
            const { data } = await supabase
              .from('study_categories')
              .select('*')
              .eq('type', 'focus')
              .in('parent_id', filters.themes)
              .order('name');

            return data || [];
          },
          staleTime: 30 * 60 * 1000,
        });
      }
    } catch (error) {
      console.error('Erro no prefetch de dados relacionados:', error);
    }
  };

  // Limpar cache antigo para liberar memória
  const cleanupOldCache = () => {
    // Remover queries antigas que não são mais necessárias
    queryClient.removeQueries({
      predicate: (query) => {
        const lastUpdated = query.state.dataUpdatedAt;
        const oneHourAgo = Date.now() - 60 * 60 * 1000;

        // Remover queries antigas que não são estáticas
        return lastUpdated < oneHourAgo &&
               !query.queryKey.some(key => typeof key === 'string' && key.includes('static'));
      },
    });
  };

  // Cleanup automático a cada 30 minutos
  useEffect(() => {
    const interval = setInterval(cleanupOldCache, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, [queryClient]);

  return {
    prefetchForPage,
    prefetchRelatedData,
    cleanupOldCache,
  };
};
