import { supabase } from "@/integrations/supabase/client";

export class AudioPlayer {
  private audio: HTMLAudioElement | null = null;
  private bellCount = 0;
  private bellTimeout: NodeJS.Timeout | null = null;
  private audioUrl: string | null = null;

  constructor(audioPath: string) {
    this.initializeAudio(audioPath);
  }

  private async initializeAudio(audioPath: string) {
    try {
      // First try to get from Supabase storage
      const { data } = await supabase
        .storage
        .from('sounds')
        .getPublicUrl(audioPath);

      if (!data?.publicUrl) {
        console.error('Error getting audio from storage: No public URL available');
        // Fallback to local public folder
        this.audioUrl = `/sounds/${audioPath}`;
      } else {
        this.audioUrl = data.publicUrl;
      }

      this.audio = new Audio(this.audioUrl);
      this.audio.addEventListener('error', (e) => {
        console.error('Audio error:', e);
      });
    } catch (error) {
      console.error('Error initializing audio:', error);
    }
  }

  async playBell(maxCount: number = 2) {
    if (!this.audio) {
      console.warn('Audio not initialized');
      return;
    }

    try {
      this.bellCount = 0;
      await this.playBellSequence(maxCount);
    } catch (error) {
      console.error('Error playing bell:', error);
      // Try to reinitialize audio
      if (this.audioUrl) {
        this.audio = new Audio(this.audioUrl);
      }
    }
  }

  private async playBellSequence(maxCount: number) {
    if (!this.audio || this.bellCount >= maxCount) {
      this.cleanup();
      return;
    }

    try {
      this.audio.currentTime = 0;
      await this.audio.play();
      this.bellCount++;
      
      this.bellTimeout = setTimeout(() => {
        this.playBellSequence(maxCount);
      }, 1000);
    } catch (error) {
      console.error('Error in bell sequence:', error);
      this.cleanup();
    }
  }

  cleanup() {
    if (this.bellTimeout) {
      clearTimeout(this.bellTimeout);
      this.bellTimeout = null;
    }
    this.bellCount = 0;
  }

  destroy() {
    this.cleanup();
    if (this.audio) {
      this.audio.remove();
      this.audio = null;
    }
  }
}

export const createAudioPlayer = (audioPath: string) => {
  return new AudioPlayer(audioPath);
};