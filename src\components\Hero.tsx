
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Award } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect } from "react";
import AuthDialog from "@/components/auth/AuthDialog";
import { motion } from "framer-motion";

export const Hero = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  useEffect(() => {
    if (user && showAuthDialog) {
      setShowAuthDialog(false);
      navigate("/plataformadeestudos");
    }
  }, [user, showAuthDialog, navigate]);

  const handleClick = () => {
    if (user) {
      navigate("/plataformadeestudos");
    } else {
      setShowAuthDialog(true);
    }
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  const features = [
    { icon: Eye, text: "Questões de especialidades específicas" },
    { icon: BookO<PERSON>, text: "Conteúdo atualizado e revisado" },
    { icon: Award, text: "Prepare-se para residência" },
  ];

  return (
    <>
      <div className="min-h-[90vh] flex flex-col items-center justify-center px-4 py-16 relative overflow-hidden bg-gradient-to-br from-[#0EA5E9] via-[#3B82F6] to-[#8B5CF6]">
        <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
        
        <div className="relative z-10 max-w-5xl mx-auto text-center">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="mb-10"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
              Estude Oftalmologia com{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-yellow-500">
                Foco e Eficiência
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Questões comentadas, análises detalhadas e cronograma personalizado para médicos e residentes em oftalmologia.
            </p>
          </motion.div>

          <motion.div 
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="mb-12"
          >
            <button 
              className="group bg-white hover:bg-white/90 text-primary text-lg px-8 py-4 rounded-full flex items-center gap-2 mx-auto transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={handleClick}
            >
              Começar agora
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </motion.div>

          <motion.div 
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="flex flex-wrap justify-center gap-4"
          >
            {features.map((feature, index) => (
              <div 
                key={index}
                className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full text-white"
              >
                <feature.icon className="w-4 h-4" />
                <span>{feature.text}</span>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
      
      <AuthDialog 
        open={showAuthDialog} 
        onOpenChange={setShowAuthDialog} 
        hidden={true} 
        onSuccess={() => setShowAuthDialog(false)}
      />
    </>
  );
};
