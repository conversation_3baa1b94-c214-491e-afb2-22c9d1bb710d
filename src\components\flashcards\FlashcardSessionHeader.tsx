
import React from 'react';
import { Keyboard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { LikeButtons } from "./LikeButtons";
import { useToast } from "@/components/ui/use-toast";
import { KeyboardShortcutsToast } from "./KeyboardShortcutsToast";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";

interface FlashcardSessionHeaderProps {
  currentCard: FlashcardWithHierarchy;
  currentIndex: number;
  totalCards: number;
}

export const FlashcardSessionHeader = ({ 
  currentCard, 
  currentIndex, 
  totalCards 
}: FlashcardSessionHeaderProps) => {
  const { toast } = useToast();

  const showKeyboardShortcuts = () => {
    toast({
      title: "Atalhos do Teclado",
      description: <KeyboardShortcutsToast />,
      className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-auto",
    });
  };

  return (
    <div className="flex items-center justify-between gap-4 mb-6">
      <div className="flex items-center gap-2">
        <span 
          className="inline-flex items-center gap-2 
          bg-hackathon-yellow/10 text-black px-3 py-1 rounded-full
          border-2 border-black shadow-sm hover:shadow-md transition-all"
        >
          <div className="w-2 h-2 bg-hackathon-yellow rounded-full animate-pulse" />
          <span className="text-sm font-medium">Sessão em Progresso</span>
        </span>
      </div>
      
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 rounded-full border-2 border-black
          hover:bg-hackathon-yellow/10 transition-colors"
          onClick={showKeyboardShortcuts}
        >
          <Keyboard className="h-4 w-4" />
        </Button>

        <LikeButtons
          cardId={currentCard.id}
          initialLikes={currentCard.likes || 0}
          initialDislikes={currentCard.dislikes || 0}
          initialLikedBy={currentCard.liked_by || []}
          initialDislikedBy={currentCard.disliked_by || []}
        />

        <div 
          className="inline-flex items-center gap-2 
          bg-hackathon-yellow/10 px-3 py-1 rounded-full
          border-2 border-black shadow-sm hover:shadow-md transition-all"
        >
          <span className="text-sm font-bold">
            {currentIndex + 1}/{totalCards}
          </span>
        </div>
      </div>
    </div>
  );
};
