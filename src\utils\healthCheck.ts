import { supabase } from '@/integrations/supabase/client';

interface HealthStatus {
  database: boolean;
  auth: boolean;
  storage: boolean;
  functions: boolean;
  overall: 'healthy' | 'degraded' | 'down';
  timestamp: Date;
  latency: {
    database: number;
    auth: number;
    storage: number;
  };
}

export class HealthChecker {
  private static instance: HealthChecker;
  private lastCheck: HealthStatus | null = null;
  private checkInterval: NodeJS.Timeout | null = null;

  static getInstance(): HealthChecker {
    if (!HealthChecker.instance) {
      HealthChecker.instance = new HealthChecker();
    }
    return HealthChecker.instance;
  }

  async performHealthCheck(): Promise<HealthStatus> {
    const startTime = Date.now();
    const status: HealthStatus = {
      database: false,
      auth: false,
      storage: false,
      functions: false,
      overall: 'down',
      timestamp: new Date(),
      latency: {
        database: 0,
        auth: 0,
        storage: 0,
      }
    };

    try {
      // Test database connection
      const dbStart = Date.now();
      const { error: dbError } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      status.latency.database = Date.now() - dbStart;
      status.database = !dbError;

      // Test auth
      const authStart = Date.now();
      const { error: authError } = await supabase.auth.getSession();
      status.latency.auth = Date.now() - authStart;
      status.auth = !authError;

      // Test storage
      const storageStart = Date.now();
      const { error: storageError } = await supabase.storage
        .from('sounds')
        .list('', { limit: 1 });
      status.latency.storage = Date.now() - storageStart;
      status.storage = !storageError;

      // Test functions (optional)
      try {
        await supabase.functions.invoke('health-check', { body: {} });
        status.functions = true;
      } catch {
        status.functions = false; // Functions are optional
      }

      // Determine overall status
      const criticalServices = [status.database, status.auth];
      const allCriticalHealthy = criticalServices.every(s => s);
      const allServicesHealthy = [status.database, status.auth, status.storage].every(s => s);

      if (allServicesHealthy) {
        status.overall = 'healthy';
      } else if (allCriticalHealthy) {
        status.overall = 'degraded';
      } else {
        status.overall = 'down';
      }

    } catch (error) {
      console.error('Health check failed:', error);
      status.overall = 'down';
    }

    this.lastCheck = status;
    return status;
  }

  startMonitoring(intervalMs: number = 60000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(async () => {
      const status = await this.performHealthCheck();

      // Monitor critical issues silently
      if (status.overall === 'down' || status.overall === 'degraded') {
        // System health issues detected - handled silently
      }

      // Monitor high latency silently
      if (status.latency.database > 2000) {
        // High database latency detected - handled silently
      }
    }, intervalMs);
  }

  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  getLastStatus(): HealthStatus | null {
    return this.lastCheck;
  }

  isHealthy(): boolean {
    return this.lastCheck?.overall === 'healthy';
  }
}

// Export singleton instance
export const healthChecker = HealthChecker.getInstance();

// Auto-start monitoring in production - DESABILITADO
// if (import.meta.env.PROD) {
//   healthChecker.startMonitoring(30000); // Check every 30 seconds in production
// }
