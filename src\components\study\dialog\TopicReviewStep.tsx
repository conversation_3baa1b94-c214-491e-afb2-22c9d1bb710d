
import React from "react";
import { CheckCircle } from "lucide-react";
import { TopicReviewStepProps } from "./types";

export const TopicReviewStep = ({
  isManual,
  manualSpecialty,
  manualTheme,
  manualFocus,
  selectedSpecialty,
  selectedTheme,
  selectedFocus,
  startTime,
  duration,
  activity,
  shouldShowFocus
}: TopicReviewStepProps) => {
  return (
    <div className="p-6 space-y-6">
      <div className="bg-green-50 border border-green-100 rounded-lg p-4 mb-6 flex items-start gap-3">
        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
        <p className="text-sm text-green-700 break-words">
          Revise os detalhes do seu tópico de estudo antes de confirmar.
        </p>
      </div>

      <div className="space-y-6">
        <div className="border rounded-lg p-4 bg-white">
          <h3 className="font-semibold mb-2 text-green-700">Tópico de Estudo</h3>
          <div className="space-y-2">
            <div className="flex gap-2">
              <span className="font-medium text-gray-500">Especialidade:</span>
              <span>{isManual ? manualSpecialty : selectedSpecialty}</span>
            </div>
            <div className="flex gap-2">
              <span className="font-medium text-gray-500">Tema:</span>
              <span>{isManual ? manualTheme : selectedTheme}</span>
            </div>
            {shouldShowFocus && (
              <div className="flex gap-2">
                <span className="font-medium text-gray-500">Foco:</span>
                <span>{isManual ? manualFocus : selectedFocus}</span>
              </div>
            )}
          </div>
        </div>

        <div className="border rounded-lg p-4 bg-white">
          <h3 className="font-semibold mb-2 text-green-700">Horário</h3>
          <div className="space-y-2">
            <div className="flex gap-2">
              <span className="font-medium text-gray-500">Início:</span>
              <span>{startTime}</span>
            </div>
            <div className="flex gap-2">
              <span className="font-medium text-gray-500">Duração:</span>
              <span>{duration} horas</span>
            </div>
            <div className="flex gap-2">
              <span className="font-medium text-gray-500">Atividade:</span>
              <span>{activity || "Não especificada"}</span>
            </div>
          </div>
        </div>

        <div className="border rounded-lg p-4 bg-white">
          <h3 className="font-semibold mb-2 text-green-700">Dicas</h3>
          <ul className="space-y-2 text-sm">
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Revise seu estudo regularmente para melhor retenção</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Combine teoria e prática para fixar o conhecimento</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>Faça pausas entre os períodos de estudo</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TopicReviewStep;
