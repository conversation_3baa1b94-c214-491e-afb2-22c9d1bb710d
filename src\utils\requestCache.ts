/**
 * Cache global para evitar chamadas duplicadas durante a mesma operação
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  promise?: Promise<T>;
}

class RequestCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly TTL = 5000; // 5 segundos

  /**
   * Executa uma função apenas uma vez por chave, retornando o resultado cacheado
   * para chamadas subsequentes dentro do TTL
   */
  async executeOnce<T>(key: string, fn: () => Promise<T>): Promise<T> {
    const now = Date.now();
    const cached = this.cache.get(key);

    // Se há uma promessa em andamento, aguardar ela
    if (cached?.promise) {
      return cached.promise;
    }

    // Se há dados válidos no cache, retornar
    if (cached && (now - cached.timestamp) < this.TTL) {
      return cached.data;
    }

    // Executar a função e cachear o resultado

    const promise = fn();

    // Armazenar a promessa para evitar execuções paralelas
    this.cache.set(key, {
      data: null,
      timestamp: now,
      promise
    });

    try {
      const result = await promise;

      // Armazenar o resultado final
      this.cache.set(key, {
        data: result,
        timestamp: now,
        promise: undefined
      });

      return result;
    } catch (error) {
      // Remover do cache em caso de erro
      this.cache.delete(key);
      throw error;
    }
  }

  /**
   * Limpa o cache para uma chave específica
   */
  invalidate(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Remove entradas expiradas do cache
   */
  cleanup(): void {
    const now = Date.now();
    let removed = 0;

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > this.TTL && !entry.promise) {
        this.cache.delete(key);
        removed++;
      }
    }

    if (removed > 0) {

    }
  }
}

// Instância global do cache
export const requestCache = new RequestCache();

// Cleanup automático a cada 30 segundos
setInterval(() => {
  requestCache.cleanup();
}, 30000);
