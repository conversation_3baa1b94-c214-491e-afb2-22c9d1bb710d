
import { motion } from "framer-motion";
import { Check } from "lucide-react";

export function MethodologySection() {
  const steps = [
    {
      number: "1",
      title: "Diagnóstico Personalizado",
      description: "Identificamos seus pontos fortes e fracos através de uma avaliação completa",
      icon: "🔍"
    },
    {
      number: "2",
      title: "Plano de Estudos",
      description: "Criamos um cronograma inteligente baseado em suas necessidades específicas",
      icon: "📋"
    },
    {
      number: "3",
      title: "Prática com Feedback",
      description: "Questões comentadas e simulados para consolidar seu conhecimento",
      icon: "📝"
    },
    {
      number: "4",
      title: "Revisão Inteligente",
      description: "Sistema que programa revisões no momento ideal para fixação definitiva",
      icon: "🔄"
    }
  ];

  const benefits = [
    "Economize tempo com um estudo direcionado",
    "Elimine a procrastinação com metas diárias claras",
    "Fixe o conteúdo com o sistema de revisões espaçadas",
    "Identifique e fortaleça seus pontos fracos",
    "Acompanhe seu progresso com métricas detalhadas"
  ];

  return (
    <section className="py-20 px-4 bg-white">
      <div className="container mx-auto">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
            Metodologia Comprovada
          </h2>
          <p className="text-lg text-gray-700">
            Um método baseado em ciência cognitiva e experiência de milhares de aprovações
          </p>
        </motion.div>
        
        {/* Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              className="relative bg-white rounded-xl p-6 shadow-lg border border-gray-100"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="absolute -top-4 -left-4 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center font-bold text-lg">
                {step.number}
              </div>
              <div className="text-4xl mb-4">{step.icon}</div>
              <h3 className="text-xl font-bold mb-2 text-gray-800">{step.title}</h3>
              <p className="text-gray-600">{step.description}</p>
            </motion.div>
          ))}
        </div>
        
        {/* Illustration with Benefits */}
        <div className="flex flex-col md:flex-row items-center gap-10 lg:gap-20">
          <motion.div 
            className="flex-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="max-w-md mx-auto md:ml-0 bg-gradient-to-br from-primary to-indigo-600 p-1 rounded-xl shadow-xl">
              <div className="bg-white rounded-lg p-6">
                <div className="text-center mb-6">
                  <span className="inline-block bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-semibold mb-2">
                    Metodologia exclusiva
                  </span>
                  <h3 className="text-2xl font-bold text-gray-800">
                    Por que funciona?
                  </h3>
                </div>
                
                <ul className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="flex gap-3">
                      <div className="bg-primary/10 rounded-full p-1 h-min mt-0.5">
                        <Check className="w-4 h-4 text-primary" />
                      </div>
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
          
          <motion.div 
            className="flex-1"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative">
              <img 
                src="/placeholder.svg" 
                alt="Metodologia de Estudos" 
                className="w-full h-auto rounded-xl shadow-lg" 
              />
              <div className="absolute top-4 right-4 bg-primary text-white py-1 px-3 rounded-full text-sm font-bold">
                Aprovado por especialistas
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
