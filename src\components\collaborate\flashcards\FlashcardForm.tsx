
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { HierarchySelect } from "./HierarchySelect";
import { FlashcardInputs } from "./form/FlashcardInputs";
import { useHierarchyData } from "./hooks/useHierarchyData";
import type { FlashcardFormProps } from "./types";

export const FlashcardForm = ({
  selectedSpecialty,
  selectedTheme,
  selectedFocus,
  onCreateSuccess
}: FlashcardFormProps) => {
  const [front, setFront] = useState('');
  const [back, setBack] = useState('');
  const [frontImage, setFrontImage] = useState('');
  const [backImage, setBackImage] = useState('');
  const [localSelectedSpecialty, setLocalSelectedSpecialty] = useState(selectedSpecialty);
  const [localSelectedTheme, setLocalSelectedTheme] = useState(selectedTheme);
  const [localSelectedFocus, setLocalSelectedFocus] = useState(selectedFocus);
  const [selectedExtraFocus, setSelectedExtraFocus] = useState<string>('');

  const {
    specialties,
    themes,
    focuses,
    extraFocuses
  } = useHierarchyData(
    localSelectedSpecialty || undefined,
    localSelectedTheme || undefined,
    localSelectedFocus || undefined
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Usuário não autenticado');
        return;
      }

      if (!localSelectedSpecialty || !front.trim() || !back.trim()) {
        toast.error('Preencha os campos obrigatórios');
        return;
      }

      // 1º: cria o flashcard sem origin_id (será atualizado após o insert)
      const { data: insertData, error } = await supabase
        .from('flashcards_cards')
        .insert({
          user_id: user.id,
          front: front.trim(),
          back: back.trim(),
          front_image: frontImage || null,
          back_image: backImage || null,
          specialty_id: localSelectedSpecialty,
          theme_id: localSelectedTheme || null,
          focus_id: localSelectedFocus || null,
          extrafocus_id: selectedExtraFocus || null,
          current_state: 'available',
          is_shared: false, // Mudança aqui: compartilhamento desativado por padrão
          origin_id: null
        })
        .select('id')
        .single();

      if (error || !insertData) throw error;

      // 2º: atualiza origin_id = id
      await supabase
        .from('flashcards_cards')
        .update({ origin_id: insertData.id })
        .eq('id', insertData.id);

      toast.success('Flashcard criado com sucesso!');
      
      // Reset form
      setFront('');
      setBack('');
      setFrontImage('');
      setBackImage('');
      setSelectedExtraFocus('');
      
      if (onCreateSuccess) {
        onCreateSuccess();
      }
      
    } catch {
      toast.error('Erro ao criar flashcard');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <FlashcardInputs
        front={front}
        setFront={setFront}
        back={back}
        setBack={setBack}
        frontImage={frontImage}
        setFrontImage={setFrontImage}
        backImage={backImage}
        setBackImage={setBackImage}
      />

      <div className="grid gap-4">
        <HierarchySelect
          label="Especialidade *"
          value={localSelectedSpecialty}
          onChange={setLocalSelectedSpecialty}
          options={specialties}
          placeholder="Selecione uma especialidade"
        />

        <HierarchySelect
          label="Tema"
          value={localSelectedTheme}
          onChange={setLocalSelectedTheme}
          options={themes}
          placeholder="Selecione um tema"
          disabled={!localSelectedSpecialty}
        />

        <HierarchySelect
          label="Foco"
          value={localSelectedFocus}
          onChange={setLocalSelectedFocus}
          options={focuses}
          placeholder="Selecione um foco"
          disabled={!localSelectedTheme}
        />

        <HierarchySelect
          label="Extra Foco"
          value={selectedExtraFocus}
          onChange={setSelectedExtraFocus}
          options={extraFocuses}
          placeholder="Selecione um extra foco"
          disabled={!localSelectedFocus}
        />
      </div>

      <Button type="submit" className="w-full">
        Criar Flashcard
      </Button>
    </form>
  );
};
