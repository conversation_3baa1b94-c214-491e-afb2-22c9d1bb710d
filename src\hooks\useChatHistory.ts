import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Message } from '@/types/chat';
import { useToast } from '@/hooks/use-toast';
import { removeMarkdown } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';

interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
}

interface ChatMetadata {
  threadId: string;
  [key: string]: any;
}

export const useChatHistory = (threadId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const loadThreads = useCallback(async () => {
    try {
      const { data: messagesData, error } = await supabase
        .from('pedbook_chat_history')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const threadMap = new Map<string, Message[]>();
      messagesData.forEach((msg) => {
        const metadata = msg.metadata as ChatMetadata;
        const threadId = metadata?.threadId;
        if (!threadId) return;

        const threadMessages = threadMap.get(threadId) || [];
        threadMessages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
        });
        threadMap.set(threadId, threadMessages);
      });

      const threadList: Thread[] = Array.from(threadMap.entries()).map(([id, messages]) => {
        const lastMessage = messages[messages.length - 1];
        const firstUserMessage = messages.find(m => m.role === 'user');
        const title = firstUserMessage
          ? removeMarkdown(firstUserMessage.content.slice(0, 50))
          : 'Nova conversa';

        return {
          id,
          title,
          lastMessage: lastMessage ? lastMessage.content : '',
          createdAt: messages[0].timestamp,
        };
      });

      threadList.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      setThreads(threadList);
    } catch (error) {
      toast({
        title: "Erro ao carregar conversas",
        description: "Ocorreu um erro ao carregar o histórico de conversas.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const deleteAllThreads = async () => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setThreads([]);
      setMessages([]);


    } catch (error) {

    }
  };

  const loadMessages = useCallback(async (threadId: string) => {
    try {
      const { data: messagesData, error } = await supabase
        .from('pedbook_chat_history')
        .select('*')
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedMessages = messagesData.map((msg): Message => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        timestamp: new Date(msg.created_at),
      }));

      setMessages(formattedMessages);
    } catch (error) {
      toast({
        title: "Erro ao carregar mensagens",
        description: "Ocorreu um erro ao carregar as mensagens da conversa.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const saveMessage = async (message: Message, threadId: string) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .insert({
          user_id: user?.id,
          role: message.role,
          content: message.content,
          metadata: { threadId },
        });

      if (error) throw error;

      setMessages(prev => [...prev, message]);
      await loadThreads();
    } catch (error) {
      toast({
        title: "Erro ao salvar mensagem",
        description: "Ocorreu um erro ao salvar sua mensagem.",
        variant: "destructive",
      });
    } finally {
      if (message.role === 'assistant') {
        setIsLoading(false);
      }
    }
  };

  const createNewThread = () => {
    const newThread = {
      id: crypto.randomUUID(),
      title: 'Nova conversa',
      lastMessage: '',
      createdAt: new Date(),
    };
    setThreads(prev => [newThread, ...prev]);
    setMessages([]);
    return newThread;
  };

  const renameThread = async (threadId: string, newTitle: string) => {
    setThreads(prev =>
      prev.map(thread =>
        thread.id === threadId
          ? { ...thread, title: newTitle }
          : thread
      )
    );
  };

  const deleteThread = async (threadId: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id);

      if (error) throw error;

      setThreads(prev => prev.filter(thread => thread.id !== threadId));
      if (threadId === threadId) {
        setMessages([]);
      }
    } catch (error) {
      toast({
        title: "Erro ao excluir conversa",
        description: "Ocorreu um erro ao excluir a conversa.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user?.id) {
      loadThreads();
    }
  }, [loadThreads, user?.id]);

  useEffect(() => {
    if (threadId && user?.id) {
      loadMessages(threadId);
    } else {
      setMessages([]);
    }
  }, [threadId, loadMessages, user?.id]);

  return {
    messages,
    threads,
    isLoading,
    saveMessage,
    createNewThread,
    renameThread,
    deleteThread,
    deleteAllThreads,
  };
};
