import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export const useAnsweredQuestions = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Configurar um listener para atualizações na tabela user_answers
  useEffect(() => {
    const channel = supabase
      .channel('user_answers_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_answers'
      }, () => {
        // Quando houver qualquer mudança na tabela, invalidar a query
        queryClient.invalidateQueries({ queryKey: ['correct-questions'] });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  return useQuery({
    queryKey: ['correct-questions'],
    queryFn: async () => {
      try {
        // Obter o usuário atual
        const { data: userData, error: authError } = await supabase.auth.getUser();

        if (authError || !userData.user?.id) {
          return []; // Retornar array vazio em vez de erro se não autenticado
        }

        const userId = userData.user.id;

        // Buscar apenas as questões que o usuário acertou
        const { data: userAnswers, error } = await supabase
          .from('user_answers')
          .select('question_id')
          .eq('user_id', userId)
          .eq('is_correct', true); // Apenas questões acertadas

        if (error) {
          return []; // Retornar array vazio em caso de erro
        }
      } catch (error) {
        return []; // Retornar array vazio para qualquer erro
      }

      // Extrair IDs únicos das questões acertadas
      const correctQuestionIds = [...new Set(userAnswers?.map(answer => answer.question_id) || [])];

      // Se não houver questões acertadas, retornar null para não aplicar o filtro
      if (correctQuestionIds.length === 0) {
        return null;
      }

      return {
        ids: correctQuestionIds,
        count: correctQuestionIds.length
      };
    },
    enabled: !!user?.id, // Só executar se o usuário estiver autenticado
    staleTime: 30 * 1000 // 30 segundos (reduzido para atualizar mais frequentemente)
  });
};
