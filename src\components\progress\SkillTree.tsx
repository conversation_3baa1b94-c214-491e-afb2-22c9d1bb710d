
import { Card } from "@/components/ui/card";
import { useSkillTreeData } from "@/hooks/useProgressData";
import { useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Search, CheckCircle, Medal, Award, Book, Trophy, Brain } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

interface CategoryData {
  name: string;
  correct: number;
  total: number;
}

const ITEMS_PER_PAGE = 8;

export const SkillTree = () => {
  const { data: stats, isLoading } = useSkillTreeData();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const filterCategories = (data: Record<string, CategoryData>) => {
    if (!searchTerm) return Object.entries(data);
    
    return Object.entries(data).filter(([_, categoryData]) =>
      categoryData.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const paginateData = (data: [string, CategoryData][]) => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return data.slice(startIndex, endIndex);
  };

  const renderPagination = (totalItems: number) => {
    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center gap-2 mt-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="h-8 px-3 text-xs"
        >
          Anterior
        </Button>
        <span className="flex items-center px-4 text-sm bg-primary/10 rounded-md">
          Página {currentPage} de {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="h-8 px-3 text-xs"
        >
          Próxima
        </Button>
      </div>
    );
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 85) return "bg-[#58CC02]";
    if (percentage >= 60) return "bg-[#1CB0F6]";
    if (percentage >= 30) return "bg-[#FF9600]";
    return "bg-[#FF4B4B]";
  };

  const getProgressIcon = (percentage: number) => {
    if (percentage >= 85) return <Trophy className="h-4 w-4 text-[#58CC02]" />;
    if (percentage >= 60) return <Medal className="h-4 w-4 text-[#1CB0F6]" />;
    if (percentage >= 30) return <Award className="h-4 w-4 text-[#FF9600]" />;
    return <Book className="h-4 w-4 text-[#FF4B4B]" />;
  };

  const renderCategoryStats = (data: Record<string, CategoryData>) => {
    if (Object.keys(data).length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-10 bg-gray-50 rounded-xl border border-gray-100">
          <Brain className="h-12 w-12 text-gray-300 mb-4" />
          <p className="text-center text-gray-500">
            Nenhum dado disponível para esta categoria.
          </p>
          <p className="text-center text-gray-400 text-sm mt-2">
            Complete algumas questões para ver seu progresso.
          </p>
        </div>
      );
    }

    const filteredData = filterCategories(data);
    const paginatedData = paginateData(filteredData);

    return (
      <>
        <div className="relative mb-6">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Pesquisar..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); // Reset to first page on search
            }}
            className="pl-10"
          />
        </div>

        <div className="grid gap-3">
          {paginatedData.map(([id, categoryData], index) => {
            const accuracy = categoryData.total > 0 
              ? (categoryData.correct / categoryData.total) * 100 
              : 0;
            const progressColor = getProgressColor(accuracy);
            const progressIcon = getProgressIcon(accuracy);
            
            return (
              <motion.div 
                key={id} 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="relative overflow-hidden"
              >
                <div className="border rounded-xl bg-white hover:shadow-md transition-shadow p-4">
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 rounded-full bg-gray-100">
                        {progressIcon}
                      </div>
                      <span className="font-medium text-gray-800">{categoryData.name}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="text-xs px-2 py-1 rounded-md bg-primary/5 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-primary" />
                        <span className="text-primary font-medium">{Math.round(accuracy)}%</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {categoryData.correct}/{categoryData.total}
                      </span>
                    </div>
                  </div>
                  
                  <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
                    <motion.div 
                      className={`absolute top-0 left-0 h-full ${progressColor}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${accuracy}%` }}
                      transition={{ duration: 0.8, delay: index * 0.05 }}
                    />
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {renderPagination(filteredData.length)}
      </>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse space-y-2">
          <Skeleton className="h-10 w-full" />
          <div className="grid grid-cols-3 gap-4">
            <Skeleton className="h-20 rounded-xl" />
            <Skeleton className="h-20 rounded-xl" />
            <Skeleton className="h-20 rounded-xl" />
          </div>
          <Skeleton className="h-40 rounded-xl" />
          <Skeleton className="h-40 rounded-xl" />
        </div>
      </div>
    );
  }

  if (!stats || (!stats.bySpecialty && !stats.byTheme && !stats.byFocus)) {
    return (
      <div className="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-xl border border-gray-100">
        <Brain className="h-16 w-16 text-gray-300 mb-4" />
        <p className="text-center text-gray-500 font-medium text-lg">
          Nenhuma estatística disponível.
        </p>
        <p className="text-center text-gray-400 mt-2 max-w-md">
          Complete algumas questões para ver seu progresso aqui. As estatísticas te ajudarão a identificar seus pontos fortes e fracos.
        </p>
        <Button 
          onClick={() => window.location.href = '/questions'} 
          className="mt-6 bg-[#1CB0F6] hover:bg-[#089be2] text-white"
        >
          <Book className="h-4 w-4 mr-2" />
          Praticar Questões
        </Button>
      </div>
    );
  }

  // Animation variants for staggered animations
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  return (
    <div>
      <motion.div 
        variants={container}
        initial="hidden"
        animate="show"
        className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8"
      >
        <motion.div variants={item} className="flex flex-col bg-[#58CC02]/10 rounded-xl p-4 border border-[#58CC02]/20 hover:shadow-sm transition-shadow">
          <div className="flex items-center gap-2 mb-2">
            <div className="p-2 rounded-full bg-[#58CC02]/20">
              <Book className="h-5 w-5 text-[#58CC02]" />
            </div>
            <span className="text-sm text-gray-600">Total de Questões</span>
          </div>
          <span className="text-3xl font-bold text-gray-800">{stats.totalQuestions}</span>
        </motion.div>

        <motion.div variants={item} className="flex flex-col bg-green-50 rounded-xl p-4 border border-green-100 hover:shadow-sm transition-shadow">
          <div className="flex items-center gap-2 mb-2">
            <div className="p-2 rounded-full bg-green-100">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <span className="text-sm text-gray-600">Respostas Corretas</span>
          </div>
          <span className="text-3xl font-bold text-green-600">{stats.correctAnswers}</span>
        </motion.div>

        <motion.div variants={item} className="flex flex-col bg-red-50 rounded-xl p-4 border border-red-100 hover:shadow-sm transition-shadow">
          <div className="flex items-center gap-2 mb-2">
            <div className="p-2 rounded-full bg-red-100">
              <Book className="h-5 w-5 text-red-500" />
            </div>
            <span className="text-sm text-gray-600">Respostas Incorretas</span>
          </div>
          <span className="text-3xl font-bold text-red-500">{stats.incorrectAnswers}</span>
        </motion.div>
      </motion.div>

      <Tabs defaultValue="specialties" className="space-y-6">
        <TabsList className="w-full p-1 bg-gray-100 rounded-xl grid grid-cols-3 gap-1">
          <TabsTrigger 
            value="specialties" 
            className="rounded-lg py-2.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm transition-all duration-200"
          >
            <Brain className="h-4 w-4 mr-2" />
            Especialidades
          </TabsTrigger>
          <TabsTrigger 
            value="themes" 
            className="rounded-lg py-2.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm transition-all duration-200"
          >
            <Book className="h-4 w-4 mr-2" />
            Temas
          </TabsTrigger>
          <TabsTrigger 
            value="focuses" 
            className="rounded-lg py-2.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm transition-all duration-200"
          >
            <Trophy className="h-4 w-4 mr-2" />
            Focos
          </TabsTrigger>
        </TabsList>

        <TabsContent value="specialties" className="space-y-4 animate-in fade-in-50">
          <div className="bg-blue-50 p-4 rounded-xl border border-blue-100 mb-4">
            <h3 className="text-blue-700 font-medium flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Especialidades Médicas
            </h3>
            <p className="text-sm text-blue-600 mt-1">
              Acompanhe seu desempenho nas diferentes especialidades da medicina
            </p>
          </div>
          {renderCategoryStats(stats.bySpecialty)}
        </TabsContent>

        <TabsContent value="themes" className="space-y-4 animate-in fade-in-50">
          <div className="bg-purple-50 p-4 rounded-xl border border-purple-100 mb-4">
            <h3 className="text-purple-700 font-medium flex items-center gap-2">
              <Book className="h-5 w-5" />
              Temas Específicos
            </h3>
            <p className="text-sm text-purple-600 mt-1">
              Visualize seu progresso nos diferentes temas dentro de cada especialidade
            </p>
          </div>
          {renderCategoryStats(stats.byTheme)}
        </TabsContent>

        <TabsContent value="focuses" className="space-y-4 animate-in fade-in-50">
          <div className="bg-amber-50 p-4 rounded-xl border border-amber-100 mb-4">
            <h3 className="text-amber-700 font-medium flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Focos de Estudo
            </h3>
            <p className="text-sm text-amber-600 mt-1">
              Analise seu desempenho nos diferentes focos de estudo e priorize suas revisões
            </p>
          </div>
          {renderCategoryStats(stats.byFocus)}
        </TabsContent>
      </Tabs>
    </div>
  );
};
