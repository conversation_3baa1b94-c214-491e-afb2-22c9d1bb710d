
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { User, Settings2, UserCircle2, ArrowLeft, Home, Users, ChevronDown } from "lucide-react";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { AvatarUpload } from "@/components/profile/AvatarUpload";
import { ReferralStats } from "@/components/referral/ReferralStats";
import { StudyPreferencesSection } from "@/components/settings/StudyPreferencesSection";

const Settings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
  });
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [activeTab, setActiveTab] = useState("profile");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const dropdownButton = document.querySelector('[data-dropdown-button]');
      const dropdownMenu = document.querySelector('[data-dropdown-menu]');

      if (isDropdownOpen &&
          !dropdownButton?.contains(target) &&
          !dropdownMenu?.contains(target)) {
        setIsDropdownOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isDropdownOpen]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        if (error) throw error;
        
        setProfile(data);
        setFormData({
          full_name: data.full_name || "",
          email: user.email || "",
        });
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast({
          title: "Erro ao carregar perfil",
          description: "Não foi possível carregar seus dados. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user, toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSaveProfile = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { error } = await supabase
        .from("profiles")
        .update({
          full_name: formData.full_name,
        })
        .eq("id", user.id);

      if (error) throw error;

      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram atualizadas com sucesso.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Erro ao atualizar perfil",
        description: "Não foi possível atualizar seus dados. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = (url: string) => {
    setProfile({
      ...profile,
      avatar_url: url
    });
  };

  const getTabLabel = (value: string) => {
    switch (value) {
      case "profile": return "Perfil";
      case "account": return "Conta";
      case "referrals": return "Referências";
      case "preferences": return "Preferências";
      default: return "Perfil";
    }
  };

  const getTabIcon = (value: string) => {
    switch (value) {
      case "profile": return <UserCircle2 className="h-4 w-4" />;
      case "account": return <User className="h-4 w-4" />;
      case "referrals": return <Users className="h-4 w-4" />;
      case "preferences": return <Settings2 className="h-4 w-4" />;
      default: return <UserCircle2 className="h-4 w-4" />;
    }
  };

  if (!user) {
    navigate("/");
    return null;
  }

  return (
    <div className="min-h-screen bg-[#FEF7CD] flex flex-col">
      <Header />
      
      {/* Mobile back button when header is hidden */}
      <div className="sm:hidden sticky top-0 z-40 bg-[#FEF7CD] border-b-2 border-black p-2">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/plataformadeestudos')}
            className="border-2 border-black p-1 flex items-center gap-1 bg-white/80 hover:bg-white"
          >
            <ArrowLeft size={16} />
            <span>Voltar</span>
          </Button>
          <h1 className="text-lg font-bold">Configurações</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/')}
            className="border-2 border-black p-1 bg-white/80 hover:bg-white"
          >
            <Home size={16} />
          </Button>
        </div>
      </div>
      
      <div className="container py-8 flex-grow">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-3xl font-bold">Configurações</h1>
            <p className="text-gray-600">Gerencie suas preferências e dados pessoais</p>
          </div>

          <Card className="border-2 border-black shadow-card-sm bg-white/90 backdrop-blur-sm">
            <CardHeader className="border-b border-black/20 bg-gradient-to-r from-[#FEF7CD]/30 to-white/50">
              <CardTitle className="text-2xl">Perfil do Usuário</CardTitle>
              <CardDescription className="text-gray-700">
                Atualize suas informações pessoais e configurações de conta
              </CardDescription>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              {/* Mobile Dropdown - Apenas no mobile */}
              <div className="block sm:hidden mb-6 relative">
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                  data-dropdown-button
                  className="w-full justify-between border-2 border-black bg-[#FEF7CD]/50 hover:bg-white/60 h-12 text-base font-medium"
                >
                  <div className="flex items-center gap-3">
                    {getTabIcon(activeTab)}
                    <span>{getTabLabel(activeTab)}</span>
                  </div>
                  <ChevronDown className={`h-5 w-5 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </Button>

                {isDropdownOpen && (
                  <div
                    data-dropdown-menu
                    className="absolute top-full left-0 right-0 mt-1 bg-white border-2 border-black rounded-md shadow-lg z-50"
                  >
                    <button
                      onClick={() => {
                        setActiveTab("profile");
                        setIsDropdownOpen(false);
                      }}
                      className="w-full flex items-center gap-3 p-4 text-base hover:bg-[#FFE4B5]/50 text-left border-b border-gray-200 last:border-b-0"
                    >
                      <UserCircle2 className="h-5 w-5" />
                      <span>Perfil</span>
                    </button>
                    <button
                      onClick={() => {
                        setActiveTab("account");
                        setIsDropdownOpen(false);
                      }}
                      className="w-full flex items-center gap-3 p-4 text-base hover:bg-[#E6F3E6]/50 text-left border-b border-gray-200 last:border-b-0"
                    >
                      <User className="h-5 w-5" />
                      <span>Conta</span>
                    </button>
                    <button
                      onClick={() => {
                        setActiveTab("referrals");
                        setIsDropdownOpen(false);
                      }}
                      className="w-full flex items-center gap-3 p-4 text-base hover:bg-[#E6F2FF]/50 text-left border-b border-gray-200 last:border-b-0"
                    >
                      <Users className="h-5 w-5" />
                      <span>Referências</span>
                    </button>
                    <button
                      onClick={() => {
                        setActiveTab("preferences");
                        setIsDropdownOpen(false);
                      }}
                      className="w-full flex items-center gap-3 p-4 text-base hover:bg-[#FFE6E6]/50 text-left border-b border-gray-200 last:border-b-0"
                    >
                      <Settings2 className="h-5 w-5" />
                      <span>Preferências</span>
                    </button>
                  </div>
                )}
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                {/* Desktop Tabs - Apenas no desktop */}
                <TabsList className="hidden sm:grid mb-6 border-2 border-black p-1 bg-[#FEF7CD]/50 grid-cols-4 w-full gap-1 h-12">
                  <TabsTrigger
                    value="profile"
                    className="flex items-center justify-center gap-2 px-2 py-2 text-sm data-[state=active]:bg-[#FFE4B5] data-[state=active]:text-black data-[state=active]:border data-[state=active]:border-black/30 hover:bg-white/60 rounded-sm"
                  >
                    <UserCircle2 className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Perfil</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="account"
                    className="flex items-center justify-center gap-2 px-2 py-2 text-sm data-[state=active]:bg-[#E6F3E6] data-[state=active]:text-black data-[state=active]:border data-[state=active]:border-black/30 hover:bg-white/60 rounded-sm"
                  >
                    <User className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Conta</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="referrals"
                    className="flex items-center justify-center gap-2 px-2 py-2 text-sm data-[state=active]:bg-[#E6F2FF] data-[state=active]:text-black data-[state=active]:border data-[state=active]:border-black/30 hover:bg-white/60 rounded-sm"
                  >
                    <Users className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Referências</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="preferences"
                    className="flex items-center justify-center gap-2 px-2 py-2 text-sm data-[state=active]:bg-[#FFE6E6] data-[state=active]:text-black data-[state=active]:border data-[state=active]:border-black/30 hover:bg-white/60 rounded-sm"
                  >
                    <Settings2 className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Preferências</span>
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="profile">
                  <div className="space-y-6">
                    <div className="flex flex-col items-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
                      <AvatarUpload 
                        url={profile?.avatar_url || null}
                        email={formData.email}
                        onUpload={handleAvatarUpload}
                      />
                      <div>
                        <h3 className="text-lg font-medium">{formData.full_name || "Usuário"}</h3>
                        <p className="text-sm text-gray-500">{formData.email}</p>
                        <p className="text-sm text-gray-500 mt-1">
                          {profile?.preparation_type === "residencia" 
                            ? "Estudante para Residência Médica" 
                            : profile?.preparation_type === "titulo" 
                              ? `Especialidade: ${profile?.specialty || "Não informada"}` 
                              : "Tipo de preparação não informado"}
                        </p>
                      </div>
                    </div>

                    <Separator className="bg-black/20" />

                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="full_name" className="text-gray-800 font-medium">Nome completo</Label>
                          <Input
                            id="full_name"
                            name="full_name"
                            value={formData.full_name}
                            onChange={handleInputChange}
                            className="border-2 border-black/30 bg-white/80 focus:border-black focus:bg-white"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-gray-800 font-medium">Email</Label>
                          <Input
                            id="email"
                            name="email"
                            value={formData.email}
                            disabled
                            className="border-2 border-black/30 bg-[#FEF7CD]/30"
                          />
                          <p className="text-xs text-gray-600">O email não pode ser alterado</p>
                        </div>
                      </div>

                      <Button
                        onClick={handleSaveProfile}
                        disabled={loading}
                        className="mt-4 bg-[#FFE4B5] text-black hover:bg-[#FFD700] border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        {loading ? "Salvando..." : "Salvar alterações"}
                      </Button>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="account">
                  <div className="space-y-6">
                    <div className="space-y-2 p-4 bg-[#E6F3E6]/30 rounded-lg border border-black/10">
                      <h3 className="text-lg font-medium text-gray-800">Alterar senha</h3>
                      <p className="text-sm text-gray-600">
                        Para alterar sua senha, use a opção "Esqueci minha senha" na tela de login.
                      </p>
                    </div>

                    <Separator className="bg-black/20" />

                    <div className="space-y-2 p-4 bg-[#FFE6E6]/30 rounded-lg border border-red-200">
                      <h3 className="text-lg font-medium text-red-700">Zona de perigo</h3>
                      <p className="text-sm text-gray-600">
                        Tenha cuidado com estas ações, elas não podem ser desfeitas.
                      </p>

                      <Button
                        variant="destructive"
                        className="mt-2 border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        Excluir minha conta
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="referrals">
                  <ReferralStats />
                </TabsContent>

                <TabsContent value="preferences">
                  <StudyPreferencesSection />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;
