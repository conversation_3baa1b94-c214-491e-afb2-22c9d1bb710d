
export const isValidTimeRange = (startTime: string, endTime: string): boolean => {
  if (!startTime || !endTime) return false;
  
  const start = new Date(`2000-01-01T${startTime}`);
  const end = new Date(`2000-01-01T${endTime}`);
  
  // Check if end time is after start time
  if (end <= start) return false;
  
  // Check if the difference is at least 15 minutes
  const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
  return diffMinutes >= 15;
};

export const validateForm = (data: any) => {
  const errors: string[] = [];
  
  // Check if at least one day is selected
  const enabledDays = Object.values(data.availableDays).filter((day: any) => day.enabled);
  if (enabledDays.length === 0) {
    errors.push("Selecione pelo menos um dia da semana");
  }

  // Validate time ranges
  let hasInvalidTimeRange = false;
  enabledDays.forEach((dayConfig: any) => {
    dayConfig.periods.forEach((period: any) => {
      if (!isValidTimeRange(period.startTime, period.endTime)) {
        hasInvalidTimeRange = true;
      }
    });
  });

  if (hasInvalidTimeRange) {
    errors.push("Verifique os horários. Cada período deve ter no mínimo 15 minutos");
  }

  // Validate schedule option
  if (data.scheduleOption === "existing" && !data.targetWeek) {
    errors.push("Selecione uma semana existente");
  }

  // Validate week count
  if (data.scheduleOption === "new") {
    const weekCountNumber = typeof data.weeksCount === 'number' ? data.weeksCount : parseInt(data.weeksCount);
    if (isNaN(weekCountNumber) || weekCountNumber < 1 || weekCountNumber > 50) {
      errors.push("O número de semanas deve estar entre 1 e 50");
    }
  }

  // Validate institution-based generation
  if (data.generationMode === "institution_based") {
    if (!data.institutionIds || data.institutionIds.length === 0) {
      errors.push("Selecione pelo menos uma instituição para geração baseada em instituições");
    }

    // Validate year range if provided
    if (data.startYear && data.endYear && data.startYear > data.endYear) {
      errors.push("O ano inicial deve ser menor ou igual ao ano final");
    }
  }

  return { isValid: errors.length === 0, errors };
};
