
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { FlashcardsList } from "./components/FlashcardsList";
import { PendingFlashcardsList } from "./components/PendingFlashcardsList";
import { MyFlashcardsHierarchy } from "./components/MyFlashcardsHierarchy";
import type { FlashcardWithHierarchy } from "@/types/flashcard";

interface FlashcardListProps {
  flashcards: FlashcardWithHierarchy[];
  isAdmin?: boolean;
}

export const FlashcardList = ({ flashcards, isAdmin = false }: FlashcardListProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<any>({});
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const cardsPerPage = 10;



  const handleDelete = async (cardId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const { data: card } = await supabase
        .from('flashcards_cards')
        .select('user_id')
        .eq('id', cardId)
        .single();

      if (!card || card.user_id !== user?.id) {
        toast({
          title: "Erro",
          description: "Você só pode deletar seus próprios flashcards",
          variant: "destructive"
        });
        return;
      }

      const { error: sessionCardsError } = await supabase
        .from('flashcards_session_cards')
        .delete()
        .eq('card_id', cardId);
      if (sessionCardsError) throw sessionCardsError;

      const { error: reviewsError } = await supabase
        .from('flashcards_reviews')
        .delete()
        .eq('card_id', cardId);
      if (reviewsError) throw reviewsError;

      const { error: cardError } = await supabase
        .from('flashcards_cards')
        .delete()
        .eq('id', cardId);
      if (cardError) throw cardError;



      if ((flashcards.length - 1) % cardsPerPage === 0 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } catch {

    }
  };

  const handleApprove = async (cardId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      const { data: profile } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user?.id)
        .single();

      if (!profile?.is_admin) {

        return;
      }

      const { error } = await supabase
        .from('flashcards_cards')
        .update({ current_state: 'available' })
        .eq('id', cardId);
      if (error) throw error;


    } catch {

    }
  };

  const handleReject = async (cardId: string, rejectionReason: string) => {
    if (!rejectionReason.trim()) {
      toast({
        title: "Erro",
        description: "É necessário informar um motivo para rejeição",
        variant: "destructive"
      });
      return;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();

      const { data: profile } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user?.id)
        .single();

      if (!profile?.is_admin) {
        toast({
          title: "Erro",
          description: "Apenas administradores podem rejeitar flashcards",
          variant: "destructive"
        });
        return;
      }

      const { error } = await supabase
        .from('flashcards_cards')
        .update({
          current_state: 'rejected',
          rejection_reason: rejectionReason
        })
        .eq('id', cardId);
      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Flashcard rejeitado com sucesso",
      });
    } catch {
      toast({
        title: "Erro",
        description: "Não foi possível rejeitar o flashcard",
        variant: "destructive"
      });
    }
  };

  const filteredCards = flashcards.filter(card => {
    let matchesFilters = true;
    let matchesSearch = true;

    if (filters.specialty && card.hierarchy.specialty?.id !== filters.specialty) {
      matchesFilters = false;
    }
    if (filters.theme && card.hierarchy.theme?.id !== filters.theme) {
      matchesFilters = false;
    }
    if (filters.focus && card.hierarchy.focus?.id !== filters.focus) {
      matchesFilters = false;
    }
    if (filters.extrafocus && card.hierarchy.extraFocus?.id !== filters.extrafocus) {
      matchesFilters = false;
    }

    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      matchesSearch =
        card.front.toLowerCase().includes(searchLower) ||
        card.back.toLowerCase().includes(searchLower);
    }

    return matchesFilters && matchesSearch;
  });

  const pendingCards = filteredCards.filter(card => card.current_state === 'pending');
  const otherCards = filteredCards;

  const indexOfLastCard = currentPage * cardsPerPage;
  const indexOfFirstCard = indexOfLastCard - cardsPerPage;
  const currentCards = otherCards.slice(indexOfFirstCard, indexOfLastCard);
  const totalPages = Math.ceil(otherCards.length / cardsPerPage);

  return (
    <div className="space-y-4">
      <Tabs defaultValue="cards" className="w-full">
        <TabsList>
          <TabsTrigger value="cards">Flashcards</TabsTrigger>
          {isAdmin && (
            <TabsTrigger value="pending">
              Aguardando Aprovação ({pendingCards.length})
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="cards">
          <MyFlashcardsHierarchy
            flashcards={otherCards}
            onDelete={handleDelete}
          />
        </TabsContent>

        {isAdmin && (
          <TabsContent value="pending">
            <PendingFlashcardsList
              cards={pendingCards}
              onApprove={handleApprove}
              onReject={handleReject}
              onDelete={handleDelete}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
