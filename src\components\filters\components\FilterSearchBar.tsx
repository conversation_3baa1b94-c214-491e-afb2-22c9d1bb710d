
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface FilterSearchBarProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
}

export const FilterSearchBar = ({ placeholder, value, onChange }: FilterSearchBarProps) => {
  return (
    <div className="relative">
      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10 bg-white border-2 border-black shadow-sm focus-visible:ring-1 focus-visible:ring-gray-400 focus-visible:outline-none"
      />
    </div>
  );
};
