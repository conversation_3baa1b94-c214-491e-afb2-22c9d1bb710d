import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import { AdminMenu } from '@/components/admin/AdminMenu';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Trash2, Search, Filter, AlertTriangle, Zap, Bomb } from 'lucide-react';

interface CategoryWithCount {
  id: string;
  name: string;
  type: 'specialty' | 'theme' | 'focus';
  parent_id?: string;
  parent_name?: string;
  question_count: number;
  created_at: string;
}

export default function CategoryManagement() {
  const [specialties, setSpecialties] = useState<CategoryWithCount[]>([]);
  const [themes, setThemes] = useState<CategoryWithCount[]>([]);
  const [focuses, setFocuses] = useState<CategoryWithCount[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all');
  const [selectedTheme, setSelectedTheme] = useState<string>('all');
  const [minQuestions, setMinQuestions] = useState<string>('');
  const [maxQuestions, setMaxQuestions] = useState<string>('');
  const [isDeletingEmpty, setIsDeletingEmpty] = useState(false);
  const [showOnlyEmpty, setShowOnlyEmpty] = useState(false);
  const [isDeletingAll, setIsDeletingAll] = useState<{
    specialties: boolean;
    themes: boolean;
    focuses: boolean;
  }>({
    specialties: false,
    themes: false,
    focuses: false
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadSpecialties(),
        loadThemes(),
        loadFocuses()
      ]);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('Erro ao carregar categorias');
    } finally {
      setLoading(false);
    }
  };

  const loadSpecialties = async () => {
    const { data, error } = await supabase.rpc('get_categories_with_question_count', {
      p_type: 'specialty'
    });

    if (error) {
      console.error('Error loading specialties:', error);
      return;
    }

    setSpecialties(data || []);
  };

  const loadThemes = async () => {
    const { data, error } = await supabase.rpc('get_categories_with_question_count', {
      p_type: 'theme'
    });

    if (error) {
      console.error('Error loading themes:', error);
      return;
    }

    setThemes(data || []);
  };

  const loadFocuses = async () => {
    console.log('🔍 Carregando focos...');
    const { data, error } = await supabase.rpc('get_categories_with_question_count', {
      p_type: 'focus'
    });

    if (error) {
      console.error('Error loading focuses:', error);
      return;
    }

    console.log(`📊 Focos carregados: ${data?.length || 0}`);
    setFocuses(data || []);
  };

  const deleteCategory = async (category: CategoryWithCount) => {
    if (category.question_count > 0) {
      toast.error(`Não é possível deletar ${category.name} pois possui ${category.question_count} questões associadas`);
      return;
    }

    try {
      // Verificar se tem categorias filhas
      const { data: children, error: childrenError } = await supabase
        .from('study_categories')
        .select('id, name, type')
        .eq('parent_id', category.id);

      if (childrenError) throw childrenError;

      if (children && children.length > 0) {
        const childTypes = children[0].type === 'theme' ? 'temas' : 'focos';
        toast.error(`Não é possível deletar ${category.name} pois possui ${children.length} ${childTypes} filhos. Delete os filhos primeiro.`);
        return;
      }

      // Se não tem filhos, pode deletar
      const { error } = await supabase
        .from('study_categories')
        .delete()
        .eq('id', category.id);

      if (error) throw error;

      toast.success(`${category.name} deletado com sucesso`);
      await loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Erro ao deletar categoria');
    }
  };

  const forceDeleteCategory = async (category: CategoryWithCount) => {
    try {
      // Usar a função auxiliar já criada
      const result = await deleteRecursively(category.id, category.type);

      toast.success(
        `Exclusão completa: ${category.name} e ${result.categoriesDeleted - 1} subcategorias deletadas, ` +
        `${result.questionsDeleted} questões removidas`
      );
      await loadCategories();
    } catch (error) {
      console.error('Error force deleting category:', error);
      toast.error('Erro ao deletar categoria forçadamente');
    }
  };

  const deleteAllEmptyCategories = async () => {
    setIsDeletingEmpty(true);
    try {
      let totalDeleted = 0;
      const deletedByType = { specialty: 0, theme: 0, focus: 0 };

      // Buscar todas as relações pai-filho de uma vez
      const { data: allRelations } = await supabase
        .from('study_categories')
        .select('id, parent_id');

      const childrenMap = new Map<string, string[]>();
      allRelations?.forEach(rel => {
        if (rel.parent_id) {
          if (!childrenMap.has(rel.parent_id)) {
            childrenMap.set(rel.parent_id, []);
          }
          childrenMap.get(rel.parent_id)!.push(rel.id);
        }
      });

      // Deletar em ordem: focos → temas → especialidades (para evitar foreign key constraints)
      const types = ['focus', 'theme', 'specialty'] as const;

      for (const type of types) {
        const categories = type === 'specialty' ? specialties :
                          type === 'theme' ? themes : focuses;

        // Filtrar categorias vazias (sem questões e sem filhos)
        const emptyCategories = categories.filter(category =>
          category.question_count === 0 &&
          (!childrenMap.has(category.id) || childrenMap.get(category.id)!.length === 0)
        );

        // Deletar em lotes para melhor performance
        if (emptyCategories.length > 0) {
          const categoryIds = emptyCategories.map(c => c.id);

          const { error, count } = await supabase
            .from('study_categories')
            .delete({ count: 'exact' })
            .in('id', categoryIds);

          if (!error && count) {
            deletedByType[type] += count;
            totalDeleted += count;
          }
        }
      }

      if (totalDeleted > 0) {
        const summary = [];
        if (deletedByType.specialty > 0) summary.push(`${deletedByType.specialty} especialidades`);
        if (deletedByType.theme > 0) summary.push(`${deletedByType.theme} temas`);
        if (deletedByType.focus > 0) summary.push(`${deletedByType.focus} focos`);

        toast.success(`Limpeza concluída! Deletadas: ${summary.join(', ')} (${totalDeleted} total)`);
      } else {
        toast.info('Nenhuma categoria vazia encontrada para deletar');
      }

      await loadCategories();
    } catch (error) {
      console.error('Error deleting empty categories:', error);
      toast.error('Erro ao deletar categorias vazias');
    } finally {
      setIsDeletingEmpty(false);
    }
  };

  const filterCategories = (categories: CategoryWithCount[]) => {
    const filtered = categories.filter(category => {
      const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           category.parent_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesSpecialty = selectedSpecialty === 'all' || !selectedSpecialty ||
                              category.parent_id === selectedSpecialty ||
                              category.id === selectedSpecialty;

      const matchesTheme = selectedTheme === 'all' || !selectedTheme ||
                          category.parent_id === selectedTheme ||
                          category.id === selectedTheme;

      const matchesMinQuestions = !minQuestions ||
                                 category.question_count >= parseInt(minQuestions);

      const matchesMaxQuestions = !maxQuestions ||
                                 category.question_count <= parseInt(maxQuestions);

      const matchesEmptyFilter = !showOnlyEmpty || category.question_count === 0;

      const passes = matchesSearch && matchesSpecialty && matchesTheme &&
                     matchesMinQuestions && matchesMaxQuestions && matchesEmptyFilter;

      // Log apenas se for foco e não passar no filtro
      if (category.type === 'focus' && !passes) {
        console.log(`🚫 Foco filtrado: ${category.name}`, {
          searchTerm,
          selectedSpecialty,
          selectedTheme,
          minQuestions,
          maxQuestions,
          showOnlyEmpty,
          matchesSearch,
          matchesSpecialty,
          matchesTheme,
          matchesMinQuestions,
          matchesMaxQuestions,
          matchesEmptyFilter
        });
      }

      return passes;
    });

    return filtered;
  };

  const getEmptyCategoriesCount = () => {
    const allCategories = [...specialties, ...themes, ...focuses];
    return allCategories.filter(cat => cat.question_count === 0).length;
  };

  const deleteAllByType = async (type: 'specialty' | 'theme' | 'focus') => {
    const typeKey = type === 'specialty' ? 'specialties' :
                   type === 'theme' ? 'themes' : 'focuses';

    setIsDeletingAll(prev => ({ ...prev, [typeKey]: true }));

    try {
      const categories = type === 'specialty' ? specialties :
                        type === 'theme' ? themes : focuses;

      // Verificações específicas por tipo - mais rigorosas
      if (type === 'specialty') {
        // Para especialidades: verificar se QUALQUER categoria descendente tem questões
        let hasQuestionsInHierarchy = false;
        let problematicCategory = '';

        for (const specialty of specialties) {
          // Verificar questões diretas da especialidade
          if (specialty.question_count > 0) {
            hasQuestionsInHierarchy = true;
            problematicCategory = `"${specialty.name}" tem ${specialty.question_count} questões diretas`;
            break;
          }

          // Verificar temas filhos
          const childThemes = themes.filter(t => t.parent_id === specialty.id);
          for (const theme of childThemes) {
            if (theme.question_count > 0) {
              hasQuestionsInHierarchy = true;
              problematicCategory = `"${specialty.name}" → "${theme.name}" tem ${theme.question_count} questões`;
              break;
            }

            // Verificar focos netos
            const childFocuses = focuses.filter(f => f.parent_id === theme.id);
            for (const focus of childFocuses) {
              if (focus.question_count > 0) {
                hasQuestionsInHierarchy = true;
                problematicCategory = `"${specialty.name}" → "${theme.name}" → "${focus.name}" tem ${focus.question_count} questões`;
                break;
              }
            }
            if (hasQuestionsInHierarchy) break;
          }
          if (hasQuestionsInHierarchy) break;
        }

        if (hasQuestionsInHierarchy) {
          toast.error(
            `Não é possível deletar todas as especialidades. ${problematicCategory}. ` +
            `Use "Deletar Vazias" ou delete categorias individualmente.`
          );
          return;
        }
      } else if (type === 'theme') {
        // Para temas: verificar se QUALQUER foco filho tem questões
        let hasQuestionsInHierarchy = false;
        let problematicCategory = '';

        for (const theme of themes) {
          // Verificar questões diretas do tema
          if (theme.question_count > 0) {
            hasQuestionsInHierarchy = true;
            problematicCategory = `"${theme.name}" tem ${theme.question_count} questões diretas`;
            break;
          }

          // Verificar focos filhos
          const childFocuses = focuses.filter(f => f.parent_id === theme.id);
          for (const focus of childFocuses) {
            if (focus.question_count > 0) {
              hasQuestionsInHierarchy = true;
              problematicCategory = `"${theme.name}" → "${focus.name}" tem ${focus.question_count} questões`;
              break;
            }
          }
          if (hasQuestionsInHierarchy) break;
        }

        if (hasQuestionsInHierarchy) {
          toast.error(
            `Não é possível deletar todos os temas. ${problematicCategory}. ` +
            `Use "Deletar Vazias" ou delete categorias individualmente.`
          );
          return;
        }
      }

      // Filtrar apenas categorias que realmente podem ser deletadas (sem questões)
      const categoriesToDelete = categories.filter(cat => cat.question_count === 0);

      if (categoriesToDelete.length === 0) {
        toast.info(`Todas as ${type === 'specialty' ? 'especialidades' : type === 'theme' ? 'temas' : 'focos'} têm questões associadas. Use "Deletar Vazias" para remover apenas as vazias.`);
        return;
      }

      if (categoriesToDelete.length < categories.length) {
        toast.warning(`Apenas ${categoriesToDelete.length} de ${categories.length} ${type === 'specialty' ? 'especialidades' : type === 'theme' ? 'temas' : 'focos'} serão deletadas (as que não têm questões).`);
      }

      // Executar exclusão em cascata recursiva
      let totalDeleted = 0;
      let totalQuestionsDeleted = 0;

      // Processar categorias em ordem (filhos primeiro para evitar foreign key constraints)
      const sortedCategories = [...categoriesToDelete].sort((a, b) => {
        // Para focos: ordem alfabética (não têm filhos)
        if (type === 'focus') return a.name.localeCompare(b.name);

        // Para temas e especialidades: processar filhos primeiro
        // Categorias com menos questões geralmente são filhos, então processar por ordem crescente
        return a.question_count - b.question_count;
      });

      for (const category of sortedCategories) {
        try {
          // Verificação adicional antes de deletar
          if (category.question_count > 0) {
            console.warn(`Pulando ${category.name} - tem ${category.question_count} questões`);
            continue;
          }

          const result = await deleteRecursively(category.id, category.type);
          totalDeleted += result.categoriesDeleted;
          totalQuestionsDeleted += result.questionsDeleted;
        } catch (error) {
          console.warn(`Erro ao deletar ${category.name}:`, error);
          // Continuar com as outras categorias mesmo se uma falhar
        }
      }

      const typeName = type === 'specialty' ? 'especialidades' :
                      type === 'theme' ? 'temas' : 'focos';

      if (totalDeleted > 0) {
        toast.success(
          `Exclusão de ${typeName} concluída! ` +
          `${totalDeleted} categorias deletadas` +
          (totalQuestionsDeleted > 0 ? ` e ${totalQuestionsDeleted} questões removidas` : '') +
          `.`
        );
      } else {
        toast.info(`Nenhuma ${typeName.slice(0, -1)} foi deletada. Todas têm questões associadas.`);
      }

      await loadCategories();
    } catch (error) {
      console.error(`Error deleting all ${type}:`, error);
      toast.error(`Erro ao deletar todos os ${type === 'specialty' ? 'especialidades' :
                   type === 'theme' ? 'temas' : 'focos'}`);
    } finally {
      setIsDeletingAll(prev => ({ ...prev, [typeKey]: false }));
    }
  };

  // Função auxiliar para exclusão recursiva (extraída da forceDeleteCategory)
  const deleteRecursively = async (categoryId: string, categoryType: string) => {
    let categoriesDeleted = 0;
    let questionsDeleted = 0;

    // 1. Buscar categorias filhas
    const { data: children, error: childrenError } = await supabase
      .from('study_categories')
      .select('id, type')
      .eq('parent_id', categoryId);

    if (childrenError) throw childrenError;

    // 2. Deletar recursivamente todas as categorias filhas
    if (children && children.length > 0) {
      for (const child of children) {
        const result = await deleteRecursively(child.id, child.type);
        categoriesDeleted += result.categoriesDeleted;
        questionsDeleted += result.questionsDeleted;
      }
    }

    // 3. Deletar questões desta categoria
    let categoryQuestionsDeleted = 0;
    if (categoryType === 'specialty') {
      const { count } = await supabase
        .from('questions')
        .delete({ count: 'exact' })
        .eq('specialty_id', categoryId);
      categoryQuestionsDeleted = count || 0;
    } else if (categoryType === 'theme') {
      const { count } = await supabase
        .from('questions')
        .delete({ count: 'exact' })
        .eq('theme_id', categoryId);
      categoryQuestionsDeleted = count || 0;
    } else if (categoryType === 'focus') {
      const { count } = await supabase
        .from('questions')
        .delete({ count: 'exact' })
        .eq('focus_id', categoryId);
      categoryQuestionsDeleted = count || 0;
    }

    questionsDeleted += categoryQuestionsDeleted;

    // 4. Deletar a categoria
    const { error: deleteError } = await supabase
      .from('study_categories')
      .delete()
      .eq('id', categoryId);

    if (deleteError) throw deleteError;
    categoriesDeleted++;

    return { categoriesDeleted, questionsDeleted };
  };

  const CategoryCard = ({ category }: { category: CategoryWithCount }) => {
    const [childrenCount, setChildrenCount] = React.useState<number | null>(null);

    React.useEffect(() => {
      const loadChildrenCount = async () => {
        const { data, error } = await supabase
          .from('study_categories')
          .select('id', { count: 'exact' })
          .eq('parent_id', category.id);

        if (!error) {
          setChildrenCount(data?.length || 0);
        }
      };

      loadChildrenCount();
    }, [category.id]);

    return (
      <Card className="mb-4">
        <CardContent className="pt-4">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="font-semibold text-lg">{category.name}</h3>
              {category.parent_name && (
                <p className="text-sm text-muted-foreground">
                  Pertence a: {category.parent_name}
                </p>
              )}
              <div className="flex gap-2 mt-2 flex-wrap">
                <Badge variant={category.question_count > 0 ? "default" : "secondary"}>
                  {category.question_count} questões
                </Badge>
                <Badge variant="outline">
                  {category.type === 'specialty' ? 'Especialidade' :
                   category.type === 'theme' ? 'Tema' : 'Foco'}
                </Badge>
                {childrenCount !== null && childrenCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {childrenCount} {category.type === 'specialty' ? 'temas' : 'focos'} filhos
                  </Badge>
                )}
              </div>
            </div>
          <div className="flex gap-2">
            {category.question_count === 0 && (childrenCount === null || childrenCount === 0) ? (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                    <AlertDialogDescription>
                      Tem certeza que deseja deletar "{category.name}"? Esta ação não pode ser desfeita.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction onClick={() => deleteCategory(category)}>
                      Deletar
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            ) : (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                      Exclusão em Cascata
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      "{category.name}" possui:
                      {category.question_count > 0 && (
                        <>
                          <br />• {category.question_count} questões associadas
                        </>
                      )}
                      {childrenCount && childrenCount > 0 && (
                        <>
                          <br />• {childrenCount} {category.type === 'specialty' ? 'temas' : 'focos'} filhos
                        </>
                      )}
                      <br /><br />
                      <strong>ATENÇÃO:</strong> Esta ação irá deletar permanentemente:
                      <br />• A categoria "{category.name}"
                      {childrenCount && childrenCount > 0 && (
                        <>
                          <br />• Todas as {childrenCount} subcategorias filhas
                        </>
                      )}
                      {category.question_count > 0 && (
                        <>
                          <br />• Todas as questões associadas (incluindo das subcategorias)
                        </>
                      )}
                      <br /><br />
                      Esta ação não pode ser desfeita!
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => forceDeleteCategory(category)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Deletar Tudo
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
    );
  };

  const DeleteAllButton = ({
    type,
    categories,
    typeName
  }: {
    type: 'specialty' | 'theme' | 'focus';
    categories: CategoryWithCount[];
    typeName: string;
  }) => {
    const typeKey = type === 'specialty' ? 'specialties' :
                   type === 'theme' ? 'themes' : 'focuses';
    const isDeleting = isDeletingAll[typeKey];
    const filteredCategories = filterCategories(categories);
    const emptyCategories = filteredCategories.filter(cat => cat.question_count === 0);
    const categoriesWithQuestions = filteredCategories.filter(cat => cat.question_count > 0);

    if (filteredCategories.length === 0) return null;

    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="destructive"
            size="sm"
            className="mb-4 flex items-center gap-2"
            disabled={isDeleting}
          >
            <Bomb className="h-4 w-4" />
            {isDeleting ? 'Deletando...' : `Deletar Todos (${filteredCategories.length})`}
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Bomb className="h-5 w-5 text-red-500" />
              Exclusão de {typeName}
            </AlertDialogTitle>
            <AlertDialogDescription>
              <strong>Análise das {filteredCategories.length} {typeName.toLowerCase()}:</strong>
              <br />• {emptyCategories.length} sem questões (serão deletadas)
              <br />• {categoriesWithQuestions.length} com questões (serão ignoradas)

              {emptyCategories.length === 0 ? (
                <>
                  <br /><br />
                  <strong className="text-yellow-600">
                    ⚠️ Nenhuma categoria será deletada pois todas têm questões associadas.
                  </strong>
                </>
              ) : (
                <>
                  <br /><br />
                  <strong>⚠️ ATENÇÃO - EXCLUSÃO SEGURA:</strong>
                  <br />• Apenas categorias VAZIAS serão deletadas
                  <br />• Categorias com questões serão preservadas
                  {type === 'specialty' && (
                    <>
                      <br />• Temas e focos filhos vazios também serão removidos
                    </>
                  )}
                  {type === 'theme' && (
                    <>
                      <br />• Focos filhos vazios também serão removidos
                    </>
                  )}

                  <br /><br />
                  <strong className="text-blue-600">
                    Esta é uma operação SEGURA - não deleta questões.
                  </strong>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteAllByType(type)}
              className={emptyCategories.length > 0 ? "bg-orange-600 hover:bg-orange-700" : "bg-gray-400"}
              disabled={emptyCategories.length === 0}
            >
              {emptyCategories.length > 0 ? `Deletar ${emptyCategories.length} Vazias` : 'Nada para Deletar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <>
      <Header />
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Gerenciamento de Categorias</h1>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                className="flex items-center gap-2"
                disabled={isDeletingEmpty || getEmptyCategoriesCount() === 0}
              >
                <Zap className="h-4 w-4" />
                {isDeletingEmpty ? 'Deletando...' : `Deletar Vazias (${getEmptyCategoriesCount()})`}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-orange-500" />
                  Limpeza em Massa
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Esta ação irá deletar <strong>{getEmptyCategoriesCount()} categorias vazias</strong> (sem questões e sem subcategorias).
                  <br /><br />
                  <strong>Ordem de exclusão:</strong>
                  <br />• 1º Focos vazios
                  <br />• 2º Temas vazios
                  <br />• 3º Especialidades vazias
                  <br /><br />
                  Esta ação não pode ser desfeita, mas é segura pois só remove categorias completamente vazias.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={deleteAllEmptyCategories}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Limpar Tudo
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        <AdminMenu />

        {/* Statistics Card */}
        {getEmptyCategoriesCount() > 0 && (
          <Card className="mb-6 border-orange-200 bg-orange-50">
            <CardContent className="pt-4">
              <div className="flex items-center gap-3">
                <div className="bg-orange-100 p-2 rounded-full">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="font-medium text-orange-800">
                    {getEmptyCategoriesCount()} categorias vazias encontradas
                  </p>
                  <p className="text-sm text-orange-600">
                    Especialidades: {specialties.filter(s => s.question_count === 0).length} •
                    Temas: {themes.filter(t => t.question_count === 0).length} •
                    Focos: {focuses.filter(f => f.question_count === 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filtros
            </CardTitle>
            <CardDescription>
              Filtre categorias por nome, especialidade, tema ou quantidade de questões
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                <SelectTrigger>
                  <SelectValue placeholder="Especialidade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  {specialties.map(specialty => (
                    <SelectItem key={specialty.id} value={specialty.id}>
                      {specialty.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                <SelectTrigger>
                  <SelectValue placeholder="Tema" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  {themes
                    .filter(theme => selectedSpecialty === 'all' || !selectedSpecialty || theme.parent_id === selectedSpecialty)
                    .map(theme => (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                placeholder="Min. questões"
                type="number"
                value={minQuestions}
                onChange={(e) => setMinQuestions(e.target.value)}
              />

              <Input
                placeholder="Max. questões"
                type="number"
                value={maxQuestions}
                onChange={(e) => setMaxQuestions(e.target.value)}
              />

              <Button
                variant={showOnlyEmpty ? "default" : "outline"}
                onClick={() => setShowOnlyEmpty(!showOnlyEmpty)}
                className="flex items-center gap-2"
              >
                <AlertTriangle className="h-4 w-4" />
                {showOnlyEmpty ? 'Todas' : 'Só Vazias'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs defaultValue="specialties" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="specialties">
              Especialidades ({filterCategories(specialties).length})
            </TabsTrigger>
            <TabsTrigger value="themes">
              Temas ({filterCategories(themes).length})
            </TabsTrigger>
            <TabsTrigger value="focuses">
              Focos ({(() => {
                console.log(`🔍 Estado dos filtros:`, {
                  searchTerm,
                  selectedSpecialty,
                  selectedTheme,
                  minQuestions,
                  maxQuestions,
                  showOnlyEmpty,
                  totalFocuses: focuses.length
                });
                const filtered = filterCategories(focuses);
                console.log(`🎯 Focos filtrados: ${filtered.length} de ${focuses.length} total`);
                return filtered.length;
              })()})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="specialties" className="space-y-4">
            {loading ? (
              <div className="text-center py-8">Carregando especialidades...</div>
            ) : filterCategories(specialties).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhuma especialidade encontrada
              </div>
            ) : (
              <>
                <DeleteAllButton
                  type="specialty"
                  categories={specialties}
                  typeName="Especialidades"
                />
                {filterCategories(specialties).map(specialty => (
                  <CategoryCard key={specialty.id} category={specialty} />
                ))}
              </>
            )}
          </TabsContent>

          <TabsContent value="themes" className="space-y-4">
            {loading ? (
              <div className="text-center py-8">Carregando temas...</div>
            ) : filterCategories(themes).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum tema encontrado
              </div>
            ) : (
              <>
                <DeleteAllButton
                  type="theme"
                  categories={themes}
                  typeName="Temas"
                />
                {filterCategories(themes).map(theme => (
                  <CategoryCard key={theme.id} category={theme} />
                ))}
              </>
            )}
          </TabsContent>

          <TabsContent value="focuses" className="space-y-4">
            {loading ? (
              <div className="text-center py-8">Carregando focos...</div>
            ) : filterCategories(focuses).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum foco encontrado
              </div>
            ) : (
              <>
                <DeleteAllButton
                  type="focus"
                  categories={focuses}
                  typeName="Focos"
                />
                {filterCategories(focuses).map(focus => (
                  <CategoryCard key={focus.id} category={focus} />
                ))}
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
