/**
 * Configurações de domínio para diferentes ambientes
 */

export interface DomainConfig {
  domain: string;
  url: string;
  isProduction: boolean;
  allowedOrigins: string[];
}

/**
 * Obter configuração de domínio baseada no ambiente
 */
export const getDomainConfig = (): DomainConfig => {
  const isProduction = import.meta.env.PROD;
  const isDevelopment = import.meta.env.DEV;
  
  if (isProduction) {
    return {
      domain: 'medevo.com.br',
      url: 'https://medevo.com.br',
      isProduction: true,
      allowedOrigins: [
        'https://medevo.com.br',
        'https://www.medevo.com.br'
      ]
    };
  }
  
  // Desenvolvimento
  return {
    domain: 'localhost',
    url: `http://localhost:${window.location.port || '800'}`,
    isProduction: false,
    allowedOrigins: [
      'http://localhost:800',
      'http://localhost:5173',
      'https://localhost:800',
      'https://localhost:5173'
    ]
  };
};

/**
 * Verificar se a origem atual é permitida
 */
export const isOriginAllowed = (origin?: string): boolean => {
  const config = getDomainConfig();
  const currentOrigin = origin || window.location.origin;
  
  return config.allowedOrigins.includes(currentOrigin);
};

/**
 * Obter URL base para APIs
 */
export const getApiBaseUrl = (): string => {
  const config = getDomainConfig();
  return config.url;
};

/**
 * Configurações específicas para Supabase Auth (Vercel)
 */
export const getSupabaseAuthConfig = () => {
  const config = getDomainConfig();

  return {
    site_url: config.url,
    additional_redirect_urls: config.allowedOrigins,
    // Configurações de cookie para Vercel (sem Cloudflare)
    cookieOptions: {
      secure: config.isProduction,
      sameSite: config.isProduction ? 'lax' : 'lax', // lax é melhor para Vercel
      httpOnly: false
    }
  };
};

/**
 * Configurações para Vercel (sem Cloudflare)
 */
export const getVercelConfig = () => {
  const config = getDomainConfig();

  return {
    domain: config.domain,
    // Configurações otimizadas para Vercel
    deploymentSettings: {
      domain: config.isProduction ? 'medevo.com.br' : 'localhost',
      secure: config.isProduction,
      headers: {
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    }
  };
};

/**
 * Verificar se estamos no domínio correto
 */
export const validateCurrentDomain = (): boolean => {
  const config = getDomainConfig();
  const currentOrigin = window.location.origin;
  
  if (config.isProduction) {
    return currentOrigin.includes('medevo.com.br');
  }
  
  // Em desenvolvimento, aceitar localhost
  return currentOrigin.includes('localhost');
};

/**
 * Redirecionar para o domínio correto se necessário
 */
export const redirectToCorrectDomain = (): void => {
  if (!validateCurrentDomain()) {
    const config = getDomainConfig();
    const currentPath = window.location.pathname + window.location.search;
    const correctUrl = config.url + currentPath;
    
    console.warn(`Redirecionando para domínio correto: ${correctUrl}`);
    window.location.href = correctUrl;
  }
};

/**
 * Configurações de CORS para fetch requests
 */
export const getCorsConfig = () => {
  const config = getDomainConfig();
  
  return {
    mode: 'cors' as RequestMode,
    credentials: 'include' as RequestCredentials,
    headers: {
      'Content-Type': 'application/json',
      'Origin': config.url
    }
  };
};

/**
 * Log de configuração atual (apenas em desenvolvimento)
 */
export const logDomainConfig = (): void => {
  // Log removido para reduzir ruído no console
};
