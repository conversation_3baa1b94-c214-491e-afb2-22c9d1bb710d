import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from "@/integrations/supabase/client";
import type { DaySchedule } from '@/types/study-schedule';

interface FilterOption {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
}

/**
 * Hook otimizado para carregamento de categorias de estudo
 * Carrega apenas categorias necessárias baseadas no cronograma atual
 */
export const useOptimizedCategories = (weeklyPlan: DaySchedule[] = []) => {
  // Estabilizar query key baseada nos IDs únicos de categorias usadas
  // em vez do tamanho do array que muda constantemente
  const usedCategoryIds = React.useMemo(() => {
    const ids = new Set<string>();
    weeklyPlan.forEach(day => {
      day.topics.forEach(topic => {
        if (topic.specialtyId) ids.add(topic.specialtyId);
        if (topic.themeId) ids.add(topic.themeId);
        if (topic.focusId) ids.add(topic.focusId);
      });
    });
    return Array.from(ids).sort().join(','); // String estável dos IDs
  }, [weeklyPlan]);

  return useQuery({
    queryKey: ["study-categories-optimized", usedCategoryIds],
    queryFn: async (): Promise<FilterOption[]> => {
      // Log removido - problema resolvido com memoização
      // Extrair IDs de categorias dos tópicos existentes
      const usedCategoryIds = new Set<string>();
      const usedSpecialtyIds = new Set<string>();
      const usedThemeIds = new Set<string>();
      
      weeklyPlan.forEach(day => {
        day.topics.forEach(topic => {
          if (topic.specialtyId) {
            usedCategoryIds.add(topic.specialtyId);
            usedSpecialtyIds.add(topic.specialtyId);
          }
          if (topic.themeId) {
            usedCategoryIds.add(topic.themeId);
            usedThemeIds.add(topic.themeId);
          }
          if (topic.focusId) {
            usedCategoryIds.add(topic.focusId);
          }
        });
      });

      // Se não há tópicos, carregar apenas especialidades básicas
      if (usedCategoryIds.size === 0) {
        const { data, error } = await supabase
          .from("study_categories")
          .select("id, name, type, parent_id")
          .eq("type", "specialty")
          .order("name")
          .limit(20); // Limitar para melhor performance

        if (error) {
          throw error;
        }

        return data.map(category => ({
          id: category.id,
          name: category.name,
          type: category.type,
          parentId: category.parent_id
        })) as FilterOption[];
      }

      // Estratégia de carregamento otimizada:
      // 1. Carregar categorias usadas
      // 2. Carregar temas filhos das especialidades usadas
      // 3. Carregar focos filhos dos temas usados
      
      const queries = [];
      
      // Query 1: Categorias já usadas
      if (usedCategoryIds.size > 0) {
        queries.push(
          supabase
            .from("study_categories")
            .select("id, name, type, parent_id")
            .in("id", Array.from(usedCategoryIds))
        );
      }

      // Query 2: Temas das especialidades usadas (para permitir expansão)
      if (usedSpecialtyIds.size > 0) {
        queries.push(
          supabase
            .from("study_categories")
            .select("id, name, type, parent_id")
            .eq("type", "theme")
            .in("parent_id", Array.from(usedSpecialtyIds))
            .limit(50) // Limitar para performance
        );
      }

      // Query 3: Focos dos temas usados (para permitir expansão)
      if (usedThemeIds.size > 0) {
        queries.push(
          supabase
            .from("study_categories")
            .select("id, name, type, parent_id")
            .eq("type", "focus")
            .in("parent_id", Array.from(usedThemeIds))
            .limit(100) // Limitar para performance
        );
      }

      // Executar queries em paralelo
      const results = await Promise.all(queries);
      
      // Combinar resultados
      const allCategories: FilterOption[] = [];
      const seenIds = new Set<string>();

      results.forEach(result => {
        if (result.data) {
          result.data.forEach(category => {
            if (!seenIds.has(category.id)) {
              seenIds.add(category.id);
              allCategories.push({
                id: category.id,
                name: category.name,
                type: category.type,
                parentId: category.parent_id
              });
            }
          });
        }
      });

      // Verificar se há erros
      const errors = results.filter(result => result.error);
      if (errors.length > 0) {
        throw errors[0].error;
      }

      return allCategories;
    },
    staleTime: 10 * 60 * 1000, // 10 minutos de cache
    cacheTime: 30 * 60 * 1000, // 30 minutos no cache
    enabled: true, // Sempre habilitado, mas com lógica condicional interna
    retry: 2,
    retryDelay: 1000
  });
};

/**
 * Hook para carregar categorias completas quando necessário (ex: criação de tópicos)
 */
export const useFullCategories = (enabled: boolean = false) => {
  return useQuery({
    queryKey: ["study-categories-full"],
    queryFn: async (): Promise<FilterOption[]> => {
      const { data, error } = await supabase
        .from("study_categories")
        .select("id, name, type, parent_id")
        .order("type, name");

      if (error) {
        throw error;
      }

      return data.map(category => ({
        id: category.id,
        name: category.name,
        type: category.type,
        parentId: category.parent_id
      })) as FilterOption[];
    },
    staleTime: 15 * 60 * 1000, // 15 minutos de cache
    cacheTime: 60 * 60 * 1000, // 1 hora no cache
    enabled,
    retry: 2,
    retryDelay: 1000
  });
};

/**
 * Hook para carregar categorias por demanda (lazy loading)
 */
export const useLazyCategoriesByType = (type: 'specialty' | 'theme' | 'focus', parentId?: string) => {
  return useQuery({
    queryKey: ["study-categories-lazy", type, parentId],
    queryFn: async (): Promise<FilterOption[]> => {
      let query = supabase
        .from("study_categories")
        .select("id, name, type, parent_id")
        .eq("type", type)
        .order("name");

      if (parentId) {
        query = query.eq("parent_id", parentId);
      }

      const { data, error } = await query.limit(50); // Limitar para performance

      if (error) {
        throw error;
      }

      return data.map(category => ({
        id: category.id,
        name: category.name,
        type: category.type,
        parentId: category.parent_id
      })) as FilterOption[];
    },
    staleTime: 10 * 60 * 1000, // 10 minutos de cache
    cacheTime: 30 * 60 * 1000, // 30 minutos no cache
    enabled: !!type,
    retry: 2,
    retryDelay: 1000
  });
};
