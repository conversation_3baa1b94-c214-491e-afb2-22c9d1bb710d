
import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

interface QuestionDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  statement: string;
  alternatives: string[];
}

export const QuestionDetailsDialog: React.FC<QuestionDetailsDialogProps> = ({
  isOpen,
  onClose,
  statement,
  alternatives,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Detalhes da Questão</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-full"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            Visualize o enunciado completo e as alternativas
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-2">
          <div>
            <h3 className="text-lg font-medium mb-2">Enunciado</h3>
            <div className="bg-muted/20 p-4 rounded-md whitespace-pre-line">
              {statement}
            </div>
          </div>

          {alternatives && alternatives.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-2">Alternativas</h3>
              <div className="space-y-2">
                {alternatives.map((alternative, index) => (
                  <div
                    key={index}
                    className="bg-muted/10 p-3 rounded-md whitespace-pre-line"
                  >
                    <span className="font-semibold">
                      {String.fromCharCode(65 + index)}:{" "}
                    </span>
                    {alternative}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuestionDetailsDialog;
