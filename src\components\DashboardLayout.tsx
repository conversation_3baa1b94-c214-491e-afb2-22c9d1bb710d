import { LayoutGrid, BookOpen, Users, Activity, Settings, FilePlus, LogOut, Menu, Brain, Timer, MessageSquare, Library, PlusCircle, Share2, ShieldCheck } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "./ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { UserProfileCard } from "./profile/UserProfileCard";
import { SimplePomodoroTimer } from "./pomodoro/SimplePomodoroTimer";
import { Badge } from "./ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { useAdminData } from "@/hooks/useAdminData";

const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showPomodoro, setShowPomodoro] = useState(false);

  // ✅ Usar hook centralizado para admin
  const { isAdmin, isLoading: adminLoading } = useAdminData();
  const userRole = isAdmin ? "Administrador" : "Usuário";

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Logout error:', error);

      } else {

        navigate("/");
      }
    } catch (error) {
      console.error('Logout error:', error);

    }
  };

  const getNavItems = () => {
    const baseItems = [
      { icon: LayoutGrid, label: "Painel de Controle", path: "/dashboard" },
      { icon: BookOpen, label: "Questões", path: "/questions" },
      { icon: Library, label: "Flashcards", path: "/flashcards" },
      { icon: PlusCircle, label: "Colaborar Flashcards", path: "/collaborate/flashcards" },
      { icon: Share2, label: "Flashcards Colaborativos", path: "/collaborative/flashcards" },
      { icon: Users, label: "Grupo de Estudo", path: "/study-groups" },
      { icon: Activity, label: "Progresso", path: "/progress" },
      { icon: Brain, label: "Recomendações IA", path: "/recommendations" },
      { icon: MessageSquare, label: "Chat", path: "/chat" },
    ];

    const adminItems = [
      { icon: FilePlus, label: "Colaborar", path: "/questions/create" },
      { icon: Settings, label: "Configurações", path: "/settings" },
    ];

    // Add admin section if user is admin
    if (isAdmin) {
      adminItems.push({ icon: ShieldCheck, label: "Administração", path: "/admin" });
    }

    return isAdmin ? [...baseItems, ...adminItems] : baseItems;
  };

  const navItems = getNavItems();

  return (
    <div className="min-h-screen flex">
      <nav className={cn(
        "border-r border-gray-200 transition-all duration-300 flex flex-col",
        isCollapsed ? "w-20" : "w-64",
        "sticky top-0 h-screen"
      )}>
        <div className="flex items-center justify-between p-4">
          {!isCollapsed && (
            <div className="flex items-center gap-2 overflow-hidden">
              <img
                src="/logomedevo.webp"
                alt="MedEvo Logo"
                className="w-6 h-6 object-contain shrink-0"
              />
              <h1 className="text-2xl font-bold text-primary truncate">MedEvo</h1>
              <Badge variant={isAdmin ? "default" : "secondary"} className="shrink-0">
                {userRole}
              </Badge>
            </div>
          )}
          {isCollapsed && (
            <img
              src="/logomedevo.webp"
              alt="MedEvo Logo"
              className="w-8 h-8 object-contain mx-auto"
            />
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className={cn(
              "h-10 w-10 rounded-full hover:bg-gray-100",
              isCollapsed && "mx-auto"
            )}
          >
            <Menu size={24} />
          </Button>
        </div>

        <div className="flex-1 px-4 py-2 space-y-2 overflow-y-auto">
          <TooltipProvider delayDuration={0}>
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Tooltip key={item.path}>
                  <TooltipTrigger asChild>
                    <Link
                      to={item.path}
                      className={cn(
                        "flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-200",
                        isActive
                          ? "bg-primary/10 text-primary"
                          : "hover:bg-gray-100",
                        isCollapsed && "justify-center px-2"
                      )}
                    >
                      <Icon size={24} className="shrink-0" />
                      {!isCollapsed && (
                        <span className="truncate">{item.label}</span>
                      )}
                    </Link>
                  </TooltipTrigger>
                  {isCollapsed && (
                    <TooltipContent side="right">
                      {item.label}
                    </TooltipContent>
                  )}
                </Tooltip>
              );
            })}
          </TooltipProvider>
        </div>

        <div className="mt-auto p-4 border-t border-gray-200 space-y-4">
          {!isCollapsed && (
            <Button
              variant="outline"
              size="sm"
              className="w-full flex items-center gap-2"
              onClick={() => setShowPomodoro(!showPomodoro)}
            >
              <Timer className="h-5 w-5 shrink-0" />
              <span className="truncate">
                {showPomodoro ? "Ocultar Pomodoro" : "Iniciar Pomodoro"}
              </span>
            </Button>
          )}

          {!isCollapsed && showPomodoro && <SimplePomodoroTimer />}

          <UserProfileCard collapsed={isCollapsed} />

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "flex items-center gap-2",
                    isCollapsed ? "w-10 h-10 p-0" : "w-full"
                  )}
                  onClick={handleLogout}
                >
                  <LogOut size={20} className="shrink-0" />
                  {!isCollapsed && "Sair"}
                </Button>
              </TooltipTrigger>
              {isCollapsed && (
                <TooltipContent side="right">
                  Sair
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      </nav>
      <main className="flex-1 p-8 overflow-auto">
        {children}
      </main>
    </div>
  );
};

export default DashboardLayout;
