import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface FilterBarProps {
  onFilterChange: (filters: any) => void;
  onSortChange: (sort: string) => void;
}

export const FilterBar = ({ onFilterChange, onSortChange }: FilterBarProps) => {
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("_all");
  const [selectedTheme, setSelectedTheme] = useState<string>("_all");
  const [selectedFocus, setSelectedFocus] = useState<string>("_all");
  const [selectedExtraFocus, setSelectedExtraFocus] = useState<string>("_all");
  const [sortBy, setSortBy] = useState("recent");

  const { data: specialties } = useQuery({
    queryKey: ['specialties'],
    queryFn: async () => {
      const { data } = await supabase
        .from('flashcards_specialty')
        .select('*')
        .order('name');
      return data || [];
    }
  });

  const { data: themes } = useQuery({
    queryKey: ['themes', selectedSpecialty],
    queryFn: async () => {
      if (selectedSpecialty === '_all') return [];
      const { data } = await supabase
        .from('flashcards_theme')
        .select('*')
        .eq('specialty_id', selectedSpecialty)
        .order('name');
      return data || [];
    },
    enabled: !!selectedSpecialty
  });

  const { data: focuses } = useQuery({
    queryKey: ['focuses', selectedTheme],
    queryFn: async () => {
      if (selectedTheme === '_all') return [];
      const { data } = await supabase
        .from('flashcards_focus')
        .select('*')
        .eq('theme_id', selectedTheme)
        .order('name');
      return data || [];
    },
    enabled: !!selectedTheme
  });

  const { data: extraFocuses } = useQuery({
    queryKey: ['extraFocuses', selectedFocus],
    queryFn: async () => {
      if (selectedFocus === '_all') return [];
      const { data } = await supabase
        .from('flashcards_extrafocus')
        .select('*')
        .eq('focus_id', selectedFocus)
        .order('name');
      return data || [];
    },
    enabled: !!selectedFocus
  });

  const handleSpecialtyChange = (value: string) => {
    setSelectedSpecialty(value);
    setSelectedTheme("_all");
    setSelectedFocus("_all");
    setSelectedExtraFocus("_all");
    onFilterChange({ specialty: value === '_all' ? undefined : value });
  };

  const handleThemeChange = (value: string) => {
    setSelectedTheme(value);
    setSelectedFocus("_all");
    setSelectedExtraFocus("_all");
    onFilterChange({ 
      specialty: selectedSpecialty === '_all' ? undefined : selectedSpecialty, 
      theme: value === '_all' ? undefined : value 
    });
  };

  const handleFocusChange = (value: string) => {
    setSelectedFocus(value);
    setSelectedExtraFocus("_all");
    onFilterChange({ 
      specialty: selectedSpecialty === '_all' ? undefined : selectedSpecialty, 
      theme: selectedTheme === '_all' ? undefined : selectedTheme, 
      focus: value === '_all' ? undefined : value 
    });
  };

  const handleExtraFocusChange = (value: string) => {
    setSelectedExtraFocus(value);
    onFilterChange({
      specialty: selectedSpecialty === '_all' ? undefined : selectedSpecialty,
      theme: selectedTheme === '_all' ? undefined : selectedTheme,
      focus: selectedFocus === '_all' ? undefined : selectedFocus,
      extraFocus: value === '_all' ? undefined : value
    });
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    onSortChange(value);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select value={selectedSpecialty} onValueChange={handleSpecialtyChange}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma especialidade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todas as especialidades</SelectItem>
            {specialties?.map((specialty) => (
              <SelectItem key={specialty.id} value={specialty.id}>
                {specialty.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedTheme} onValueChange={handleThemeChange} disabled={selectedSpecialty === '_all'}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione um tema" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os temas</SelectItem>
            {themes?.map((theme) => (
              <SelectItem key={theme.id} value={theme.id}>
                {theme.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedFocus} onValueChange={handleFocusChange} disabled={selectedTheme === '_all'}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione um foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os focos</SelectItem>
            {focuses?.map((focus) => (
              <SelectItem key={focus.id} value={focus.id}>
                {focus.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedExtraFocus} onValueChange={handleExtraFocusChange} disabled={selectedFocus === '_all'}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione um extra foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os extra focos</SelectItem>
            {extraFocuses?.map((extraFocus) => (
              <SelectItem key={extraFocus.id} value={extraFocus.id}>
                {extraFocus.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end">
        <Select value={sortBy} onValueChange={handleSortChange}>
          <SelectTrigger className="w-[220px]">
            <SelectValue placeholder="Ordenar por" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Mais recentes</SelectItem>
            <SelectItem value="oldest">Mais antigos</SelectItem>
            <SelectItem value="likes">Mais curtidos</SelectItem>
            <SelectItem value="dislikes_desc">Mais descurtidos</SelectItem>
            <SelectItem value="imported">Mais importados</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
