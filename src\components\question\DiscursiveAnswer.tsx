
import React from 'react';
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

interface DiscursiveAnswerProps {
  value: string;
  onChange: (text: string) => void;
  onSubmit: () => void;
  onAnalyze?: () => void;  // Nova prop para analisar a resposta
  readOnly?: boolean;
  hasAnswered: boolean;
  isAnalyzing?: boolean;   // Nova prop para indicar estado de análise
  hasBeenEvaluated?: boolean; // Nova prop para indicar se já foi avaliada
}

export const DiscursiveAnswer = React.memo(({
  value,
  onChange,
  onSubmit,
  onAnalyze,
  readOnly = false,
  hasAnswered,
  isAnalyzing = false,
  hasBeenEvaluated = false
}: DiscursiveAnswerProps) => {
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!value.trim()) {
      toast({
        title: "Resposta vazia",
        description: "Por favor, escreva sua resposta antes de confirmar",
        variant: "destructive"
      });
      return;
    }

    onSubmit();
  };

  return (
    <div className="space-y-4">
      {!hasAnswered ? (
        // Modo de edição: Mostrar textarea editável + botão confirmar
        <div className="space-y-2">
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Digite sua resposta aqui..."
            className="min-h-[150px]"
            readOnly={readOnly}
          />

          <div className="flex justify-end">
            <Button
              onClick={handleSubmit}
              disabled={!value.trim() || readOnly}
              className="w-full md:w-auto"
            >
              Confirmar Resposta
            </Button>
          </div>
        </div>
      ) : (
        // Modo visualização: Mostrar resposta formatada + botão análise
        <div className="space-y-4">
          <div>
            <div className="font-medium text-gray-700 mb-2">Sua Resposta:</div>
            <div className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50/50 whitespace-pre-wrap">
              {value}
            </div>
          </div>

          {onAnalyze && !hasBeenEvaluated && (
            <div className="flex justify-end">
              <Button
                onClick={onAnalyze}
                disabled={isAnalyzing}
                className="w-full md:w-auto bg-blue-600 hover:bg-blue-700"
              >
                {isAnalyzing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analisando...
                  </>
                ) : (
                  "Gerar análise da minha resposta"
                )}
              </Button>
            </div>
          )}

          {hasBeenEvaluated && (
            <div className="flex justify-center mt-4">
              <div className="text-sm text-gray-600 bg-gray-100 px-4 py-2 rounded-lg border">
                ✅ Questão já foi avaliada pela IA
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

DiscursiveAnswer.displayName = 'DiscursiveAnswer';
