import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Bar<PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

interface PrevalenceChartProps {
  data: Array<{
    name: string;
    count: number;
    percentage: number;
  }>;
  title: string;
}

export const PrevalenceChart: React.FC<PrevalenceChartProps> = ({ data, title }) => {
  // Cores para as barras
  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1',
    '#14B8A6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4',
    '#84CC16', '#F97316', '#EC4899', '#6366F1', '#14B8A6'
  ];

  const formatTooltip = (value: any, name: string, props: any) => {
    if (name === 'percentage') {
      return [`${value.toFixed(2)}%`, 'Percentual'];
    }
    if (name === 'count') {
      return [`${value} questões`, 'Quantidade'];
    }
    return [value, name];
  };

  const formatLabel = (label: string) => {
    // Truncar labels muito longos
    return label.length > 20 ? `${label.substring(0, 20)}...` : label;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data.slice(0, 15)} // Mostrar apenas top 15 para melhor visualização
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 60,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
                fontSize={12}
                tickFormatter={formatLabel}
              />
              <YAxis 
                yAxisId="left"
                orientation="left"
                label={{ value: 'Percentual (%)', angle: -90, position: 'insideLeft' }}
              />
              <YAxis 
                yAxisId="right"
                orientation="right"
                label={{ value: 'Quantidade', angle: 90, position: 'insideRight' }}
              />
              <Tooltip 
                formatter={formatTooltip}
                labelFormatter={(label) => `Categoria: ${label}`}
                contentStyle={{
                  backgroundColor: '#f8fafc',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px'
                }}
              />
              <Bar 
                yAxisId="left"
                dataKey="percentage" 
                name="percentage"
                radius={[4, 4, 0, 0]}
              >
                {data.slice(0, 15).map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        {data.length > 15 && (
          <p className="text-sm text-gray-500 mt-2 text-center">
            Mostrando top 15 de {data.length} itens
          </p>
        )}
      </CardContent>
    </Card>
  );
};
