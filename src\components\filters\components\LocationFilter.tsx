
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useHierarchicalFilterCounts } from "@/hooks/useOptimizedFilterSelection";
import type { SelectedFilters } from "@/types/question";

interface LocationFilterProps {
  locations: { id: string; name: string }[];
  selectedLocations: string[];
  onToggleLocation: (id: string) => void;
  questionCounts: {
    totalCounts: { [key: string]: number };
    filteredCounts: { [key: string]: number };
  };
  hasActiveFilters: boolean;
  selectedFilters: SelectedFilters; // Novo prop para filtros hierárquicos
}

export const LocationFilter = ({
  locations,
  selectedLocations,
  onToggleLocation,
  questionCounts,
  hasActiveFilters,
  selectedFilters
}: LocationFilterProps) => {
  // Usar contagens hierárquicas se há filtros de categoria selecionados
  const hasCategoryFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0)
  );

  const { data: hierarchicalCounts, isLoading: isLoadingHierarchical } = useHierarchicalFilterCounts(
    selectedFilters,
    'locations'
  );

  const getLocationCount = (locationId: string) => {
    // Se há filtros de categoria, usar contagens hierárquicas
    if (hasCategoryFilters && hierarchicalCounts) {
      return hierarchicalCounts[locationId] || 0;
    }

    // Caso contrário, usar lógica original
    if (hasActiveFilters) {
      return questionCounts.filteredCounts[locationId] || 0;
    }
    return questionCounts.totalCounts[locationId] || 0;
  };

  const availableLocations = locations
    .map(location => ({
      ...location,
      count: getLocationCount(location.id)
    }))
    .filter(location => location.count > 0)
    .sort((a, b) => b.count - a.count);

  // Mostrar indicador de carregamento se estiver carregando contagens hierárquicas
  if (hasCategoryFilters && isLoadingHierarchical) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-2 rounded-lg bg-gray-100 animate-pulse">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded bg-gray-300"></div>
              <div className="h-4 w-32 bg-gray-300 rounded"></div>
            </div>
            <div className="h-6 w-12 bg-gray-300 rounded-full"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {availableLocations.map((location) => (
        <div
          key={location.id}
          className={cn(
            'flex items-center justify-between p-2 rounded-lg transition-all duration-200 cursor-pointer',
            'hover:bg-[#FEF7CD]/50',
            selectedLocations.includes(location.id) && 'bg-[#FEF7CD]'
          )}
          onClick={() => onToggleLocation(location.id)}
        >
          <div className="flex items-center gap-2">
            <div
              className={cn(
                "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center",
                selectedLocations.includes(location.id)
                  ? "bg-[#FF6B00] border-black text-white"
                  : "border-black hover:border-[#FF6B00]",
              )}
            >
              {selectedLocations.includes(location.id) && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  className="w-3.5 h-3.5"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </div>
            <span className={cn(
              "text-sm transition-colors duration-200",
              selectedLocations.includes(location.id) ? "text-[#FF6B00] font-medium" : "text-gray-700"
            )}>
              {location.name}
            </span>
          </div>
          <div
            className={cn(
              "min-w-[3rem] text-center px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200",
              selectedLocations.includes(location.id)
                ? "bg-[#FF6B00] text-white"
                : "bg-gray-100 text-gray-600",
              hasCategoryFilters && "ring-2 ring-blue-200" // Indicador visual de filtro hierárquico
            )}
          >
            {location.count}
          </div>
        </div>
      ))}
      {availableLocations.length === 0 && (
        <p className="text-center text-gray-500 py-4">
          {hasCategoryFilters
            ? "Nenhuma instituição encontrada para as especialidades/temas/focos selecionados"
            : "Nenhuma instituição encontrada com questões disponíveis para os filtros selecionados"
          }
        </p>
      )}
    </div>
  );
};
