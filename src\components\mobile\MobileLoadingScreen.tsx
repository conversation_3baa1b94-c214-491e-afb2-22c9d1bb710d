import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

interface MobileLoadingScreenProps {
  message?: string;
}

const MobileLoadingScreen = ({ message = "Carregando..." }: MobileLoadingScreenProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FEF7CD] via-[#FEF7CD] to-hackathon-yellow/20 flex items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        {/* Logo */}
        <motion.div
          initial={{ y: -20 }}
          animate={{ y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="bg-white border-2 border-black px-6 py-3 shadow-card-sm mx-auto inline-block">
            <span className="font-bold text-2xl tracking-tight">Med EVO</span>
          </div>
          <div className="absolute -right-2 -top-2">
            <span className="bg-hackathon-red text-white text-xs px-2 py-0.5 rounded border border-black font-bold">
              beta
            </span>
          </div>
        </motion.div>

        {/* Loading Spinner */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mb-6"
        >
          <Loader2 className="h-8 w-8 animate-spin text-hackathon-red mx-auto" />
        </motion.div>

        {/* Loading Message */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-gray-700 font-medium"
        >
          {message}
        </motion.p>

        {/* Animated dots */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="flex justify-center gap-1 mt-4"
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2
              }}
              className="w-2 h-2 bg-hackathon-red rounded-full"
            />
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default MobileLoadingScreen;
