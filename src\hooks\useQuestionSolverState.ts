import React, { useReducer, useCallback } from 'react';

// Estado consolidado para evitar múl<PERSON>los re-renders
interface QuestionSolverState {
  selectedAnswers: Record<string, string | null>;
  answeredQuestions: Set<string>;
  feedbackStates: Record<string, boolean>;
  correctnessStatuses: Record<string, boolean | null>;
  showExplanations: Record<string, boolean>;
  isSubmitting: boolean;
}

// Actions para o reducer
type QuestionSolverAction =
  | { type: 'SELECT_ANSWER'; questionId: string; answer: string | null }
  | { type: 'MARK_AS_ANSWERED'; questionId: string }
  | { type: 'SET_FEEDBACK'; questionId: string; isCorrect: boolean }
  | { type: 'TOGGLE_EXPLANATION'; questionId: string }
  | { type: 'SET_SUBMITTING'; isSubmitting: boolean }
  | { type: 'RESET_QUESTION'; questionId: string }
  | { type: 'RESET_ALL' };

// Reducer otimizado
const questionSolverReducer = (
  state: QuestionSolverState,
  action: QuestionSolverAction
): QuestionSolverState => {
  switch (action.type) {
    case 'SELECT_ANSWER': {
      // ✅ CORREÇÃO: Apenas armazenar a resposta selecionada,
      // NÃO marcar como respondida até ser confirmada e salva
      return {
        ...state,
        selectedAnswers: {
          ...state.selectedAnswers,
          [action.questionId]: action.answer
        }
        // answeredQuestions não é modificado aqui!
      };
    }

    case 'MARK_AS_ANSWERED': {
      const newAnsweredQuestions = new Set(state.answeredQuestions);
      newAnsweredQuestions.add(action.questionId);

      return {
        ...state,
        answeredQuestions: newAnsweredQuestions
      };
    }

    case 'SET_FEEDBACK':
      return {
        ...state,
        feedbackStates: {
          ...state.feedbackStates,
          [action.questionId]: true
        },
        correctnessStatuses: {
          ...state.correctnessStatuses,
          [action.questionId]: action.isCorrect
        }
      };

    case 'TOGGLE_EXPLANATION':
      return {
        ...state,
        showExplanations: {
          ...state.showExplanations,
          [action.questionId]: !state.showExplanations[action.questionId]
        }
      };

    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.isSubmitting
      };

    case 'RESET_QUESTION':
      const newAnsweredQuestionsReset = new Set(state.answeredQuestions);
      newAnsweredQuestionsReset.delete(action.questionId);

      return {
        ...state,
        selectedAnswers: {
          ...state.selectedAnswers,
          [action.questionId]: null
        },
        answeredQuestions: newAnsweredQuestionsReset,
        feedbackStates: {
          ...state.feedbackStates,
          [action.questionId]: false
        },
        correctnessStatuses: {
          ...state.correctnessStatuses,
          [action.questionId]: null
        },
        showExplanations: {
          ...state.showExplanations,
          [action.questionId]: false
        }
      };

    case 'RESET_ALL':
      return {
        selectedAnswers: {},
        answeredQuestions: new Set(),
        feedbackStates: {},
        correctnessStatuses: {},
        showExplanations: {},
        isSubmitting: false
      };

    default:
      return state;
  }
};

// Estado inicial
const initialState: QuestionSolverState = {
  selectedAnswers: {},
  answeredQuestions: new Set(),
  feedbackStates: {},
  correctnessStatuses: {},
  showExplanations: {},
  isSubmitting: false
};

/**
 * Hook otimizado para gerenciar estado do QuestionSolver
 * Usa useReducer para evitar múltiplos re-renders
 */
export const useQuestionSolverState = () => {
  const [state, dispatch] = useReducer(questionSolverReducer, initialState);

  // Actions memoizadas para evitar re-renders desnecessários
  const selectAnswer = useCallback((questionId: string, answer: string | null) => {
    dispatch({ type: 'SELECT_ANSWER', questionId, answer });
  }, []);

  const markAsAnswered = useCallback((questionId: string) => {
    dispatch({ type: 'MARK_AS_ANSWERED', questionId });
  }, []);

  const setFeedback = useCallback((questionId: string, isCorrect: boolean) => {
    dispatch({ type: 'SET_FEEDBACK', questionId, isCorrect });
  }, []);

  const toggleExplanation = useCallback((questionId: string) => {
    dispatch({ type: 'TOGGLE_EXPLANATION', questionId });
  }, []);

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    dispatch({ type: 'SET_SUBMITTING', isSubmitting });
  }, []);

  const resetQuestion = useCallback((questionId: string) => {
    dispatch({ type: 'RESET_QUESTION', questionId });
  }, []);

  const resetAll = useCallback(() => {
    dispatch({ type: 'RESET_ALL' });
  }, []);

  // Getters memoizados
  const getSelectedAnswer = useCallback((questionId: string) => {
    return state.selectedAnswers[questionId] || null;
  }, [state.selectedAnswers]);

  const isQuestionAnswered = useCallback((questionId: string) => {
    return state.answeredQuestions.has(questionId);
  }, [state.answeredQuestions]);

  const hasQuestionFeedback = useCallback((questionId: string) => {
    return state.feedbackStates[questionId] || false;
  }, [state.feedbackStates]);

  const getQuestionCorrectness = useCallback((questionId: string) => {
    const result = state.correctnessStatuses[questionId];
    return result !== undefined ? result : null;
  }, [state.correctnessStatuses]);

  const isExplanationVisible = useCallback((questionId: string) => {
    return state.showExplanations[questionId] || false;
  }, [state.showExplanations]);

  // Estatísticas computadas
  const stats = {
    totalAnswered: state.answeredQuestions.size,
    totalCorrect: Object.values(state.correctnessStatuses).filter(Boolean).length,
    totalIncorrect: Object.values(state.correctnessStatuses).filter(status => status === false).length,
    accuracyRate: state.answeredQuestions.size > 0
      ? (Object.values(state.correctnessStatuses).filter(Boolean).length / state.answeredQuestions.size) * 100
      : 0
  };

  return {
    // Estado
    state,
    
    // Actions
    selectAnswer,
    markAsAnswered,
    setFeedback,
    toggleExplanation,
    setSubmitting,
    resetQuestion,
    resetAll,
    
    // Getters
    getSelectedAnswer,
    isQuestionAnswered,
    hasQuestionFeedback,
    getQuestionCorrectness,
    isExplanationVisible,
    
    // Estatísticas
    stats
  };
};
