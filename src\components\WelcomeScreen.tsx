import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>, Sparkles, Trophy, Users, Zap, ArrowRight } from "lucide-react";
import confetti from "canvas-confetti";
import { ReferralWelcomeManager } from "@/components/referral/ReferralWelcomeManager";
import { useReferralStatus } from "@/hooks/useReferralStatus";

interface WelcomeScreenProps {
  onGetPremiumAccess: () => void;
  userName?: string;
}

export const WelcomeScreen = ({ onGetPremiumAccess, userName }: WelcomeScreenProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [canProceed, setCanProceed] = useState(false);
  const { isReferred, showNotification, isLoading: referralLoading } = useReferralStatus();

  // Verificar se pode prosseguir baseado no status do referral
  useEffect(() => {
    // Se não há referral ou se o referral já foi processado, pode prosseguir
    if (!referralLoading) {
      if (!isReferred || (isReferred && !showNotification)) {
        setCanProceed(true);
      } else {
        setCanProceed(false);
      }
    } else {
      setCanProceed(false);
    }
  }, [isReferred, showNotification, referralLoading]);

  const handleGetPremiumAccess = async () => {
    if (isRedirecting || !canProceed) return; // Prevenir cliques múltiplos e aguardar referral

    setIsLoading(true);
    setIsRedirecting(true);
    
    // 🎉 Confetes animados
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval: any = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);
      
      // Confetes do lado esquerdo
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      });
      
      // Confetes do lado direito
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      });
    }, 250);

    // Aguardar um pouco para mostrar a animação
    setTimeout(() => {
      setIsLoading(false);
      onGetPremiumAccess();
    }, 2000);
  };

  const handleReferralComplete = () => {
    setCanProceed(true);
  };

  return (
    <ReferralWelcomeManager onComplete={handleReferralComplete}>
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-yellow-50 to-orange-100 flex items-center justify-center p-4">
        <div className="w-full max-w-4xl mx-auto">
        {/* Header com animação */}
        <div className="text-center mb-8 animate-fade-in">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="relative">
              <Brain className="h-12 w-12 text-orange-600 animate-pulse" />
              <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent">
              MedEvo
            </h1>
          </div>
          
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
            {userName ? `Bem-vindo, ${userName}! 🎉` : "Bem-vindo ao futuro dos estudos! 🎉"}
          </h2>
          
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Você acabou de entrar na plataforma mais avançada para estudos médicos do Brasil
          </p>
        </div>

        {/* Cards de features */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="border-2 border-orange-200 bg-white/80 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">IA Avançada</h3>
              <p className="text-gray-600">
                Análises inteligentes das suas respostas com feedback personalizado
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-200 bg-white/80 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Gamificação (em breve)</h3>
              <p className="text-gray-600">
                Sistema de conquistas, streaks e rankings para manter você motivado
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-200 bg-white/80 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Performance</h3>
              <p className="text-gray-600">
                Estatísticas detalhadas e insights para otimizar seus estudos
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Seção principal */}
        <Card className="border-2 border-orange-300 bg-white/90 backdrop-blur-sm shadow-xl">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Users className="h-6 w-6 text-orange-600" />
                <span className="text-lg font-semibold text-gray-700">
                  Você está entre os primeiros usuários!
                </span>
              </div>
              
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                Acesso Premium Gratuito Durante o BETA
              </h3>
              
              <p className="text-lg text-gray-600 mb-6 max-w-3xl mx-auto leading-relaxed">
                Como você está nos ajudando a construir a melhor plataforma de estudos médicos, 
                você terá <strong className="text-orange-600">acesso completo e gratuito</strong> a 
                todos os recursos premium durante toda a fase BETA.
              </p>

              <div className="bg-gradient-to-r from-orange-100 to-yellow-100 rounded-lg p-6 mb-6">
                <h4 className="text-xl font-bold text-gray-800 mb-3">O que você terá acesso:</h4>
                <div className="grid md:grid-cols-2 gap-4 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Banco completo de questões</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Análises de IA</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Flashcards (em breve)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Estatísticas avançadas</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Sessões de estudo personalizadas</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Suporte prioritário</span>
                  </div>
                </div>
              </div>
            </div>

            <Button
              onClick={handleGetPremiumAccess}
              disabled={isLoading || isRedirecting || !canProceed}
              size="lg"
              className="w-full max-w-md mx-auto bg-gradient-to-r from-orange-600 to-yellow-600 hover:from-orange-700 hover:to-yellow-700 text-white font-bold py-4 px-6 md:px-8 rounded-xl text-base md:text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              {isLoading ? (
                <div className="flex items-center gap-3">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Ativando seu acesso...
                </div>
              ) : !canProceed ? (
                <div className="flex items-center gap-3">
                  <div className="animate-pulse rounded-full h-5 w-5 bg-white/30"></div>
                  Processando convite...
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <Sparkles className="h-5 w-5" />
                  Ativar Acesso Premium BETA
                  <ArrowRight className="h-5 w-5" />
                </div>
              )}
            </Button>

            <p className="text-sm text-gray-500 mt-4">
              Sem compromisso • Sem cartão de crédito • Acesso imediato
            </p>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-600">
          <p className="text-sm">
            Obrigado por fazer parte da nossa jornada! 🚀
          </p>
        </div>
        </div>
      </div>
    </ReferralWelcomeManager>
  );
};

export default WelcomeScreen;
