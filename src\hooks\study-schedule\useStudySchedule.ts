
import { useState, useCallback } from 'react';
import { useToast } from "@/hooks/use-toast";
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import type { WeeklySchedule, StudyTopic, AIScheduleOptions, GenerationStats } from './types';
import { useScheduleManagement } from './useScheduleManagement';
import { useStudyTopics } from './useStudyTopics';
import { useAISchedule } from './useAISchedule';

export const useStudySchedule = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [weeklySchedule, setWeeklySchedule] = useState<WeeklySchedule | null>(null);
  const [generationStats, setGenerationStats] = useState<GenerationStats | null>(null);
  const { toast } = useToast();
  const user = useUser();
  const { domain, isReady } = useDomain();

  const {
    loadCurrentSchedule: fetchSchedule,
    addWeeks,
    deleteWeek,
    deleteAllWeeks,
    updateTopic,
    isLoading: isScheduleLoading
  } = useScheduleManagement();

  const {
    markTopicAsStudied,
    deleteTopic,
    isLoading: isTopicLoading
  } = useStudyTopics(); // ✅ SEM CALLBACK - não vai mais causar reload!

  const {
    generateAISchedule,
    isLoading: isAILoading
  } = useAISchedule(
    setGenerationStats,
    addWeeks
  ); // ✅ Removido callback que causava reload

  const loadCurrentSchedule = useCallback(async () => {
    try {
      setIsLoading(true);
      const schedule = await fetchSchedule();
      if (schedule) {
        setWeeklySchedule(schedule);
      }
    } catch (error: any) {
      toast({
        title: "Error loading schedule",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [fetchSchedule, toast]);

  const handleDeleteWeek = useCallback(async (weekNumber: number) => {
    try {
      setIsLoading(true);
      console.log(`🔥 useStudySchedule - Attempting to delete week ${weekNumber}`);
      const result = await deleteWeek(weekNumber);
      console.log("Delete week result:", result);

      toast({
        title: "Week deleted",
        description: `Week ${weekNumber} has been deleted successfully.`,
      });

      // Reload the schedule
      console.log("Reloading schedule after week deletion");
      const schedule = await fetchSchedule();
      if (schedule) {
        console.log("Schedule after deletion:", schedule);
        setWeeklySchedule(schedule);
      } else {
        console.log("No schedule returned after deletion");
      }
    } catch (error: any) {
      console.error('Error in handleDeleteWeek:', error);
      toast({
        title: "Error deleting week",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [deleteWeek, fetchSchedule, toast]);

  const handleDeleteAllWeeks = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await deleteAllWeeks();

      toast({
        title: "All weeks deleted",
        description: "All weeks have been deleted successfully.",
      });

      // Reload the schedule
      const schedule = await fetchSchedule();
      if (schedule) {
        setWeeklySchedule(schedule);
      } else {
        setWeeklySchedule(null);
      }
    } catch (error: any) {
      toast({
        title: "Error deleting all weeks",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [deleteAllWeeks, fetchSchedule, toast]);

  return {
    weeklySchedule,
    isLoading: isLoading || isScheduleLoading || isTopicLoading || isAILoading,
    generationStats,
    loadCurrentSchedule,
    updateTopic,
    addWeeks,
    deleteWeek: handleDeleteWeek,
    deleteAllWeeks: handleDeleteAllWeeks,
    generateAISchedule,
    markTopicAsStudied,
    deleteTopic,
  };
};
