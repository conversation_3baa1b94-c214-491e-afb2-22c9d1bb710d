
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useQuery } from "@tanstack/react-query";
import { Play, Plus, Info, Brain, Calendar } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { usePageVisibility } from '@/hooks/usePageVisibility';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";

const getTodayISOString = () => new Date().toISOString().split('T')[0];

export const FlashcardDailySummary: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showInfoDialog, setShowInfoDialog] = useState(false);

  const { user } = useAuth();

  // Auto-refresh quando o usuário volta para a aba
  usePageVisibility({
    queryKeys: ['daily-review-cards-mini'],
    delay: 500
  });

  const { data: cardsCount = 0, isLoading } = useQuery({
    queryKey: ['daily-review-cards-mini', user?.id],
    queryFn: async () => {
      if (!user) return 0;
      const today = getTodayISOString();
      const { data: reviews, error } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .lte('next_review_date', today);

      if (error) {
        return 0;
      }
      return reviews?.length || 0;
    },
    enabled: !!user,
  });

  const handleReview = async () => {
    if (cardsCount > 0) {
      // Redireciona para os flashcards revisão, mesmo fluxo do botão "Revisar Agora"
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("User not authenticated");
        const today = getTodayISOString();
        const { data: cardsToReview, error: cardsError } = await supabase
          .from('flashcards_reviews')
          .select('card_id')
          .eq('user_id', user.id)
          .lte('next_review_date', today);

        if (cardsError) throw cardsError;

        const cardIds = cardsToReview?.map(review => review.card_id) || [];

        const { data: session, error: sessionError } = await supabase
          .from('flashcards_sessions')
          .insert({
            user_id: user.id,
            status: 'in_progress',
            total_cards: cardIds.length,
            cards: cardIds,
            correct_cards: 0
          })
          .select()
          .single();

        if (sessionError) throw sessionError;

        if (cardIds.length > 0) {
          await supabase
            .from('flashcards_cards')
            .update({ current_state: 'reviewing' })
            .in('id', cardIds);
        }

        navigate(`/flashcards/session/${session.id}`);
      } catch (error) {
        toast({
          title: "Erro ao iniciar revisão",
          description: "Não foi possível iniciar a sessão de revisão.",
          variant: "destructive"
        });
      }
    }
  };

  const handleStudyRedirect = () => {
    navigate("/flashcards/study");
  };

  if (isLoading) return null;

  return (
    <>
      <div className="relative flex flex-row items-center justify-between bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 mb-4 shadow-xs opacity-70">
        <div className="flex items-center gap-3">
          <span className="font-bold text-gray-500 text-xl">0</span>
          <span className="text-base text-gray-600">
            cards para revisar
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            disabled
            size="sm"
            className="px-3 py-1 gap-1 font-bold text-sm bg-gray-400 text-gray-600 cursor-not-allowed"
          >
            <span className="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-semibold mr-1">
              Beta
            </span>
            Em breve
          </Button>

          {/* Info button */}
          <Button
            onClick={() => setShowInfoDialog(true)}
            size="sm"
            variant="outline"
            className="h-8 w-8 rounded-full p-0 bg-blue-500 hover:bg-blue-600 text-white border-blue-500"
          >
            <Info className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Info Dialog */}
      <Dialog open={showInfoDialog} onOpenChange={setShowInfoDialog}>
        <DialogContent className="max-w-[95vw] sm:max-w-md mx-auto max-h-[90dvh] overflow-y-auto rounded-2xl">
          <DialogHeader className="text-center pr-0">
            <DialogTitle className="flex items-center justify-center gap-2 text-lg pr-12">
              <Brain className="h-5 w-5 text-red-500" />
              Flashcards Diários
            </DialogTitle>
            <DialogDescription>
              Informações sobre o sistema de flashcards diários com repetição espaçada
            </DialogDescription>
          </DialogHeader>
          <div className="text-center space-y-3 pt-3">
            <div className="bg-red-50 p-3 rounded-xl border border-red-200">
              <h4 className="font-semibold text-red-800 mb-2 text-sm">
                📚 Resumo Diário de Flashcards
              </h4>
              <p className="text-red-700 text-xs leading-relaxed">
                Widget que mostra quantos <strong>flashcards</strong> você tem para revisar hoje,
                baseado no algoritmo de <strong>repetição espaçada FSRS-5</strong>.
              </p>
            </div>

            <div className="bg-blue-50 p-3 rounded-xl border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2 text-sm">🎯 Funcionalidades:</h4>
              <div className="text-blue-700 text-xs space-y-1">
                <div>• <strong>Contagem Inteligente:</strong> Cards programados para hoje</div>
                <div>• <strong>Sessões Rápidas:</strong> Inicie revisões com um clique</div>
                <div>• <strong>Progresso Visual:</strong> Acompanhe cards revisados</div>
                <div>• <strong>Integração Total:</strong> Conectado ao sistema principal</div>
              </div>
            </div>

            <div className="bg-green-50 p-3 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-800 mb-2 flex items-center justify-center gap-1 text-sm">
                <Calendar className="h-4 w-4" />
                Status: Pronto
              </h4>
              <p className="text-green-700 text-xs leading-relaxed">
                Funcionalidade <strong>desenvolvida</strong> e aguardando lançamento
                junto com o sistema completo de flashcards.
              </p>
            </div>

            <div className="text-center pt-2">
              <Button
                onClick={() => setShowInfoDialog(false)}
                size="sm"
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-1 rounded-lg text-xs"
              >
                Entendi! 🚀
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
