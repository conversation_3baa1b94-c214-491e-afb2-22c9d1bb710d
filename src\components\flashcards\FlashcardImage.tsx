import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useEffect, useState } from "react";

interface FlashcardImageProps {
  src?: string | null;
  alt?: string;
  className?: string;
  onError?: (error: any) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const FlashcardImage = ({ 
  src, 
  alt = "Flashcard image", 
  className,
  onError,
  isOpen,
  onOpenChange
}: FlashcardImageProps) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  useEffect(() => {
    const loadImage = async () => {
      if (!src) {
        return;
      }

      try {
        // If URL is already public, use it directly
        if (src.startsWith('http')) {
          setImageUrl(src);
          return;
        }

        // Otherwise, get public URL from Supabase Storage
        const { data } = supabase
          .storage
          .from('flashcard_images')
          .getPublicUrl(src);

        setImageUrl(data.publicUrl);
      } catch (error) {
        onError?.(error);
      }
    };

    loadImage();
  }, [src, onError]);

  if (!imageUrl) {
    return null;
  }

  const handleImageClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card flip when clicking image
    onOpenChange(true);
  };

  return (
    <>
      <img
        src={imageUrl}
        alt={alt}
        className={cn(
          "max-h-[200px] w-auto object-contain rounded-lg cursor-pointer transition-transform hover:scale-105",
          className
        )}
        onClick={handleImageClick}
        onError={(e) => {
          onError?.(e);
        }}
      />

      <Dialog open={isOpen} onOpenChange={onOpenChange} modal={true}>
        <DialogContent 
          className="max-w-[90vw] max-h-[90vh] w-full h-full p-6 flex flex-col items-center justify-center" 
          onClick={(e) => e.stopPropagation()}
        >
          <DialogTitle className="sr-only">
            {alt}
          </DialogTitle>
          <DialogDescription className="sr-only">
            Visualização ampliada da imagem do flashcard
          </DialogDescription>
          <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
            <img
              src={imageUrl}
              alt={alt}
              className="max-w-full max-h-[70vh] object-contain rounded-lg"
              onError={(e) => {
                onError?.(e);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
