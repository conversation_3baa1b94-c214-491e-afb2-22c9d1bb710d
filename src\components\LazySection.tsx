import React, { useState, useEffect, useRef, Suspense } from 'react';

interface LazySectionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  rootMargin?: string;
  threshold?: number;
  className?: string;
}

/**
 * Componente que carrega conteúdo apenas quando entra na viewport
 * Usa Intersection Observer para lazy loading real
 */
export const LazySection: React.FC<LazySectionProps> = ({
  children,
  fallback = (
    <div className="bg-white rounded-xl p-4 sm:p-5 shadow-sm border border-gray-100">
      {/* Header skeleton */}
      <div className="flex items-center justify-between mb-4">
        <div className="h-5 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-28"></div>
        <div className="h-6 w-6 bg-gradient-to-r from-purple-300 to-purple-200 rounded-full animate-pulse"></div>
      </div>

      {/* Content skeleton */}
      <div className="space-y-3">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-3 rounded-lg border border-gray-100">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-purple-300 rounded-lg animate-pulse"></div>
            <div className="flex-1">
              <div className="h-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-full mb-2"></div>
              <div className="h-3 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-2/3"></div>
            </div>
            <div className="h-6 w-20 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  ),
  rootMargin = '-50px',
  threshold = 0.3,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [canLoad, setCanLoad] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Delay inicial para evitar carregamento imediato
  useEffect(() => {
    const timer = setTimeout(() => {
      setCanLoad(true);
    }, 4000); // 4 segundos de delay

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!canLoad) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          // Desconectar observer após carregar
          observer.disconnect();
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold, hasLoaded, canLoad]);

  return (
    <div ref={ref} className={className}>
      {isVisible ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  );
};

export default LazySection;
