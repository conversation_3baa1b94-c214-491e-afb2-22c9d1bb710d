import React from 'react';
import type { Topic } from '@/types/flashcard';

export interface TopicHierarchyTreeProps {
  topics: Topic[];
  onSelect?: (topic: Topic) => void;
}

export const TopicHierarchyTree: React.FC<TopicHierarchyTreeProps> = ({
  topics,
  onSelect
}) => {
  return (
    <ul>
      {topics.map(topic => (
        <li key={topic.id} onClick={() => onSelect?.(topic)}>
          {topic.name}
        </li>
      ))}
    </ul>
  );
};
