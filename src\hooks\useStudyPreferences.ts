import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface StudyPreferences {
  target_specialty: string | null;
  study_months: number | null;
  preferences_completed: boolean;
  target_institutions_unknown: boolean;
  target_institutions: Array<{
    id: string;
    name: string;
  }>;
}

export interface StudyPreferencesFormData {
  target_specialty: string;
  specialty_unknown: boolean;
  study_months: number;
  target_institutions: string[];
  institutions_unknown: boolean;
}

export const useStudyPreferences = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query para buscar preferências do usuário
  const {
    data: preferences,
    isLoading,
    error
  } = useQuery({
    queryKey: ['study-preferences-data', user?.id], // ✅ CHAVE ÚNICA
    queryFn: async (): Promise<StudyPreferences | null> => {
      if (!user?.id) return null;

      // Buscar dados das preferências
      const { data: userPrefs, error: prefsError } = await supabase
        .from('user_preferences')
        .select('target_specialty, study_months, preferences_completed, target_institutions_unknown')
        .eq('user_id', user.id)
        .single();

      if (prefsError && prefsError.code !== 'PGRST116') {
        throw prefsError;
      }

      // Se não existe registro de preferências, retornar valores padrão
      // (não criar automaticamente para evitar conflitos)
      if (!userPrefs) {
        return {
          target_specialty: null,
          study_months: null,
          preferences_completed: false,
          target_institutions_unknown: false,
          target_institutions: []
        };
      }

      // Buscar instituições alvo
      const { data: institutions, error: institutionsError } = await supabase
        .from('user_target_institutions')
        .select(`
          institution_id,
          exam_locations!inner(id, name)
        `)
        .eq('user_id', user.id);

      if (institutionsError) {
        console.warn('Erro ao buscar instituições:', institutionsError);
        // Não falhar se não conseguir buscar instituições
      }

      return {
        target_specialty: userPrefs.target_specialty,
        study_months: userPrefs.study_months,
        preferences_completed: userPrefs.preferences_completed || false,
        target_institutions_unknown: userPrefs.target_institutions_unknown || false,
        target_institutions: institutions?.map(item => ({
          id: item.exam_locations.id,
          name: item.exam_locations.name
        })) || []
      };
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 30 * 60 * 1000, // 30 minutos
  });

  // Mutation para salvar preferências
  const savePreferences = useMutation({
    mutationFn: async (data: StudyPreferencesFormData) => {
      if (!user?.id) throw new Error('Usuário não autenticado');

      // Validar se todos os campos foram respondidos
      const hasSpecialty = data.specialty_unknown || (data.target_specialty && data.target_specialty.trim().length > 0);
      const hasInstitutions = data.institutions_unknown || data.target_institutions.length > 0;
      const hasStudyTime = data.study_months && data.study_months > 0;

      if (!hasSpecialty || !hasInstitutions || !hasStudyTime) {
        throw new Error('Todos os campos devem ser preenchidos');
      }

      // Atualizar preferências usando upsert para garantir consistência
      const prefsUpdate = {
        user_id: user.id,
        target_specialty: data.specialty_unknown ? null : data.target_specialty,
        study_months: data.study_months,
        preferences_completed: true,
        target_institutions_unknown: data.institutions_unknown,
        updated_at: new Date().toISOString()
      };

      console.log('🔍 [useStudyPreferences] Saving:', prefsUpdate);

      // Usar upsert para garantir que funciona sempre
      const { error: prefsError } = await supabase
        .from('user_preferences')
        .upsert(prefsUpdate, {
          onConflict: 'user_id'
        });

      if (prefsError) {
        console.error('🔍 [useStudyPreferences] Upsert error:', prefsError);
        throw prefsError;
      }

      // Limpar instituições existentes
      const { error: deleteError } = await supabase
        .from('user_target_institutions')
        .delete()
        .eq('user_id', user.id);

      if (deleteError) {
        throw deleteError;
      }

      // Adicionar novas instituições (se não for "não sei")
      if (!data.institutions_unknown && data.target_institutions.length > 0) {
        const institutionsToInsert = data.target_institutions.map(institutionId => ({
          user_id: user.id,
          institution_id: institutionId
        }));

        const { error: insertError } = await supabase
          .from('user_target_institutions')
          .insert(institutionsToInsert);

        if (insertError) {
          throw insertError;
        }
      }

      return data;
    },
    onSuccess: () => {
      // Invalidar todos os caches relacionados
      queryClient.invalidateQueries({ queryKey: ['study-preferences-data'] });
      queryClient.invalidateQueries({ queryKey: ['user-preferences'] });
      queryClient.invalidateQueries({ queryKey: ['user-preferences-completed'] });
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });

      toast({
        title: "Preferências salvas!",
        description: "Suas preferências de estudo foram salvas com sucesso.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar suas preferências. Tente novamente.",
        variant: "destructive"
      });
      console.error('Erro ao salvar preferências:', error);
    }
  });

  // Query para buscar todas as instituições disponíveis (apenas com questões)
  const {
    data: availableInstitutions,
    isLoading: isLoadingInstitutions
  } = useQuery({
    queryKey: ['available-institutions'],
    queryFn: async () => {
      // Usar função SQL para buscar apenas instituições com questões
      const { data, error } = await supabase.rpc('get_institutions_with_questions');

      if (error) {
        throw error;
      }

      return data || [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora (dados estáticos)
    cacheTime: 2 * 60 * 60 * 1000, // 2 horas
  });

  return {
    preferences,
    isLoading,
    error,
    availableInstitutions,
    isLoadingInstitutions,
    savePreferences: savePreferences.mutateAsync,
    isSaving: savePreferences.isPending,
    hasCompletedPreferences: preferences?.preferences_completed || false
  };
};
