import React from 'react';
import { useState } from 'react';
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

interface QuestionImagesProps {
  images?: string[];
  className?: string;
}

export const QuestionImages: React.FC<QuestionImagesProps> = ({ images, className }) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className={cn("mt-4 space-y-4", className)}>
      <div className="flex flex-wrap gap-4 justify-center">
        {images.map((imageUrl, index) => (
          <Dialog key={index}>
            <DialogTrigger asChild>
              <div 
                className="relative cursor-pointer overflow-hidden rounded-md border border-gray-200 hover:border-primary transition-all"
                onClick={() => setSelectedImageIndex(index)}
              >
                <img
                  src={imageUrl}
                  alt={`Imagem ${index + 1} da questão`}
                  className="h-32 w-auto object-cover hover:scale-105 transition-transform"
                  loading="lazy"
                />
              </div>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <div className="flex flex-col items-center justify-center">
                <img
                  src={imageUrl}
                  alt={`Imagem ${index + 1} da questão (ampliada)`}
                  className="max-h-[calc(100vh-200px)] w-auto object-contain"
                />
                <div className="mt-4 text-sm text-gray-500">
                  {`Imagem ${index + 1} de ${images.length}`}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </div>
  );
};

export default QuestionImages;
