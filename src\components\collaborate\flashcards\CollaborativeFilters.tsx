import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, SortDesc } from "lucide-react";

interface CollaborativeFiltersProps {
  onFilterChange: (filters: any) => void;
  onSortChange: (sort: string) => void;
}

export const CollaborativeFilters = ({ onFilterChange, onSortChange }: CollaborativeFiltersProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [specialty, setSpecialty] = useState("");
  const [theme, setTheme] = useState("");
  const [focus, setFocus] = useState("");
  const [extraFocus, setExtraFocus] = useState("");
  const [sortBy, setSortBy] = useState("recent");

  const handleFilterChange = () => {
    onFilterChange({
      searchTerm,
      specialty,
      theme,
      focus,
      extraFocus
    });
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    onSortChange(value);
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Pesquisar flashcards..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            handleFilterChange();
          }}
          className="pl-10"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Select value={specialty} onValueChange={(value) => {
          setSpecialty(value);
          handleFilterChange();
        }}>
          <SelectTrigger>
            <SelectValue placeholder="Especialidade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todas</SelectItem>
            {/* Adicionar opções dinamicamente */}
          </SelectContent>
        </Select>

        <Select value={theme} onValueChange={(value) => {
          setTheme(value);
          handleFilterChange();
        }}>
          <SelectTrigger>
            <SelectValue placeholder="Tema" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            {/* Adicionar opções dinamicamente */}
          </SelectContent>
        </Select>

        <Select value={focus} onValueChange={(value) => {
          setFocus(value);
          handleFilterChange();
        }}>
          <SelectTrigger>
            <SelectValue placeholder="Foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            {/* Adicionar opções dinamicamente */}
          </SelectContent>
        </Select>

        <Select value={extraFocus} onValueChange={(value) => {
          setExtraFocus(value);
          handleFilterChange();
        }}>
          <SelectTrigger>
            <SelectValue placeholder="Extra Foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            {/* Adicionar opções dinamicamente */}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end">
        <Select value={sortBy} onValueChange={handleSortChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Ordenar por" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Mais recentes</SelectItem>
            <SelectItem value="likes">Mais curtidos</SelectItem>
            <SelectItem value="oldest">Mais antigos</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </Card>
  );
};