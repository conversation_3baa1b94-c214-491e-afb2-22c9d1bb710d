import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.0";

// Get environment variables
const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// Initialize Supabase client with the service role key for admin access
const supabase = createClient(supabaseUrl || '', supabaseServiceKey || '');

// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request: Request): string => {
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }

  return 'https://medevo.com.br'; // fallback
};

const getCorsHeaders = (request: Request) => ({
  'Access-Control-Allow-Origin': getAllowedOrigin(request),
  'Access-Control-Allow-Headers': 'authorization, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
});

// Improved function to extract JSON from various text formats
const extractJSONFromText = (text) => {
  console.log(`🔍 [flashcards-ai-generate] Attempting to extract JSON from text (${text.length} chars)`);

  // First clean the text - remove markdown formatting if present
  let cleanText = text.replace(/```(json)?\s*/g, '').replace(/\s*```$/g, '');

  // Try direct JSON parse first (most common case)
  try {
    const parsed = JSON.parse(cleanText);
    console.log(`✅ [flashcards-ai-generate] Parsed JSON directly: ${typeof parsed} with ${Array.isArray(parsed) ? parsed.length + ' items' : 'object structure'}`);

    // If we parsed an array successfully, return it
    if (Array.isArray(parsed)) {
      return parsed;
    }

    // If we parsed an object with a data/items/cards/flashcards field, return that
    if (parsed.flashcards) return parsed.flashcards;
    if (parsed.cards) return parsed.cards;
    if (parsed.items) return parsed.items;
    if (parsed.data) return parsed.data;
    if (parsed.generated) return parsed.generated;

    // Return the parsed object wrapped in an array as fallback
    return [parsed];
  } catch (directError) {
    console.warn(`⚠️ [flashcards-ai-generate] Direct parse failed: ${directError.message}`);
  }

  // Try finding JSON arrays in the text
  const arrayMatch = cleanText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (arrayMatch) {
    try {
      const parsed = JSON.parse(arrayMatch[0]);
      console.log(`✅ [flashcards-ai-generate] Extracted and parsed array JSON: ${parsed.length} items`);
      return parsed;
    } catch (arrayError) {
      console.warn(`⚠️ [flashcards-ai-generate] Array extraction failed: ${arrayError.message}`);
    }
  }

  // Try to extract individual JSON objects and combine them
  const objects = [];
  const objectMatches = cleanText.matchAll(/\{[^{}]*\}/g);
  for (const match of objectMatches) {
    try {
      const obj = JSON.parse(match[0]);
      if (obj.front && obj.back) {
        objects.push(obj);
      }
    } catch {
      // Ignore parsing errors for individual objects
    }
  }

  if (objects.length > 0) {
    console.log(`✅ [flashcards-ai-generate] Extracted ${objects.length} individual JSON objects`);
    return objects;
  }

  console.error(`❌ [flashcards-ai-generate] Failed to extract any valid JSON from the response`);
  // Last resort: try to extract front/back pairs manually
  try {
    const frontBackPairs = cleanText.match(/["']front["']\s*:\s*["']([^"']+)["']\s*,\s*["']back["']\s*:\s*["']([^"']+)["']/g);
    if (frontBackPairs && frontBackPairs.length > 0) {
      console.log(`⚠️ [flashcards-ai-generate] Attempting manual extraction of ${frontBackPairs.length} front/back pairs`);
      return frontBackPairs.map(pair => {
        const front = pair.match(/["']front["']\s*:\s*["']([^"']+)["']/)[1];
        const back = pair.match(/["']back["']\s*:\s*["']([^"']+)["']/)[1];
        return { front, back };
      });
    }
  } catch (e) {
    console.error(`❌ [flashcards-ai-generate] Manual extraction failed: ${e.message}`);
  }

  return null;
};

serve(async (req) => {
  const corsHeaders = getCorsHeaders(req);

  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("🔄 [flashcards-ai-generate] Função Edge inicializando");

    if (!openAIApiKey) {
      console.error("❌ [flashcards-ai-generate] Chave da API não configurada");
      return new Response(
        JSON.stringify({
          error: "Serviço de geração de flashcards temporariamente indisponível. Tente novamente em alguns minutos."
        }),
        {
          status: 503,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("❌ [flashcards-ai-generate] Credenciais do Supabase não configuradas");
      return new Response(
        JSON.stringify({
          error: "Credenciais do Supabase não configuradas no servidor."
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const requestData = await req.json();
    const {
      prompt,
      specialty,
      theme,
      focus,
      extrafocus,
      quantity = 5,
      fcType = "cloze",
      questionIds,
      directQuestions
    } = requestData;

    if (!prompt) {
      console.error("❌ [flashcards-ai-generate] Prompt ausente na requisição");
      return new Response(
        JSON.stringify({ error: "Prompt ausente na requisição" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    console.log(`🔍 [flashcards-ai-generate] Request params:`, {
      specialty,
      theme,
      focus,
      extrafocus: extrafocus || "não fornecido",
      quantity,
      fcType,
      hasQuestionIds: questionIds ? `Sim (${questionIds.length} questões)` : "Não",
      hasDirectQuestions: directQuestions ? `Sim (${directQuestions.length} questões)` : "Não",
    });

    let questionsData;

    if (directQuestions && directQuestions.length > 0) {
      console.log(`✅ [flashcards-ai-generate] Usando ${directQuestions.length} questões enviadas diretamente`);

      // Normalizar a estrutura dos dados vindos do frontend
      questionsData = directQuestions.map(q => {
        // Log para debug da estrutura
        console.log(`🔍 [flashcards-ai-generate] Estrutura da questão ${q.id}:`, Object.keys(q));

        return {
          id: q.id,
          question_content: q.question_content || q.statement, // Fallback para compatibilidade
          response_choices: q.response_choices || q.alternatives,
          correct_choice: q.correct_choice || q.correct_answer,
          exam_year: q.exam_year || q.year,
          specialty_id: q.specialty_id || q.specialty?.id,
          theme_id: q.theme_id || q.theme?.id,
          focus_id: q.focus_id || q.focus?.id,
          source_id: q.id
        };
      });

      console.log(`🔍 [flashcards-ai-generate] Primeira questão normalizada:`, questionsData[0]);
    }
    else if (questionIds && questionIds.length > 0) {
      console.log(`🔍 [flashcards-ai-generate] Buscando ${questionIds.length} questões pelos IDs fornecidos`);
      console.log(`🔍 [flashcards-ai-generate] IDs das questões:`, questionIds);

      // Primeiro, fazer uma query simples para testar
      const { data: testData, error: testError } = await supabase
        .from('questions')
        .select('id, question_content')
        .limit(1);

      console.log(`🔍 [flashcards-ai-generate] Teste de query simples:`, { testData, testError });

      const { data, error } = await supabase
        .from('questions')
        .select(`
          id,
          question_content,
          response_choices,
          correct_choice,
          exam_year,
          specialty_id,
          theme_id,
          focus_id
        `)
        .in('id', questionIds);

      console.log(`🔍 [flashcards-ai-generate] Resultado da query principal:`, { data: data?.length, error });

      if (error) {
        console.error('❌ [flashcards-ai-generate] Erro ao buscar questões pelos IDs:', error);
        return new Response(
          JSON.stringify({ error: `Erro ao buscar questões: ${error.message}` }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      if (!data || data.length === 0) {
        console.warn("⚠️ [flashcards-ai-generate] Nenhuma questão encontrada com os IDs fornecidos");
        return new Response(
          JSON.stringify({
            error: "Nenhuma questão encontrada com os IDs fornecidos."
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      questionsData = data;
      console.log(`✅ [flashcards-ai-generate] Encontradas ${questionsData.length} questões pelos IDs fornecidos`);

      // Validar se as questões têm o conteúdo necessário
      const invalidQuestions = questionsData.filter(q => !q.question_content);
      if (invalidQuestions.length > 0) {
        console.warn(`⚠️ [flashcards-ai-generate] ${invalidQuestions.length} questões sem conteúdo encontradas`);
        invalidQuestions.forEach(q => console.warn(`Questão sem conteúdo: ID ${q.id}`));
      }
    }
    else {
      console.log(`🔍 [flashcards-ai-generate] Nenhuma questão direta ou ID fornecido, usando filtros tradicionais`);
      
      // Debug: primeiro vamos ver quantas questões temos no total
      const { data: totalQuestions, error: totalError } = await supabase
        .from('questions')
        .select('id, knowledge_domain', { count: 'exact' })
        .limit(5);

      console.log(`🔍 [flashcards-ai-generate] Total questions check:`, { 
        totalQuestions: totalQuestions?.length, 
        totalError, 
        sampleDomains: totalQuestions?.map(q => q.knowledge_domain) 
      });

      // Debug: verificar questões com conhecimento pediatria
      const { data: pediatricQuestions, error: pediatricError } = await supabase
        .from('questions')
        .select('id, knowledge_domain', { count: 'exact' })
        .eq('knowledge_domain', 'pediatria')
        .limit(5);

      console.log(`🔍 [flashcards-ai-generate] Pediatric questions check:`, { 
        pediatricQuestions: pediatricQuestions?.length, 
        pediatricError 
      });

      // Build the query - remove the pediatria filter first to see if we get any questions
      let query = supabase
        .from('questions')
        .select(`
          id,
          question_content,
          response_choices,
          correct_choice,
          exam_year,
          specialty_id,
          theme_id,
          focus_id,
          knowledge_domain
        `)
        .limit(quantity);

      // Apply filters using the correct column names
      if (specialty) {
        console.log(`🔍 [flashcards-ai-generate] Aplicando filtro specialty_id: ${specialty}`);
        query = query.eq('specialty_id', specialty);
      }
      if (theme) {
        console.log(`🔍 [flashcards-ai-generate] Aplicando filtro theme_id: ${theme}`);
        query = query.eq('theme_id', theme);
      }
      if (focus) {
        console.log(`🔍 [flashcards-ai-generate] Aplicando filtro focus_id: ${focus}`);
        query = query.eq('focus_id', focus);
      }
      
      // Temporarily comment out the pediatria filter to see if we get questions
      // query = query.eq('knowledge_domain', 'pediatria');

      const { data, error } = await query;

      console.log(`🔍 [flashcards-ai-generate] Query result:`, { 
        data: data?.length, 
        error,
        firstQuestion: data?.[0] ? {
          id: data[0].id,
          domain: data[0].knowledge_domain,
          specialty_id: data[0].specialty_id,
          theme_id: data[0].theme_id,
          focus_id: data[0].focus_id
        } : null
      });

      if (error) {
        console.error('❌ [flashcards-ai-generate] Erro ao buscar questões pelos filtros:', error);
        return new Response(
          JSON.stringify({ error: `Erro ao buscar questões: ${error.message}` }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      if (!data || data.length === 0) {
        console.warn("⚠️ [flashcards-ai-generate] Nenhuma questão encontrada com os filtros fornecidos");
        
        // Try a simpler query without any filters to see if we have any questions at all
        const { data: anyQuestions, error: anyError } = await supabase
          .from('questions')
          .select('id, knowledge_domain, specialty_id, theme_id, focus_id')
          .limit(5);

        console.log(`🔍 [flashcards-ai-generate] Any questions available:`, { 
          anyQuestions: anyQuestions?.length, 
          anyError,
          samples: anyQuestions 
        });

        return new Response(
          JSON.stringify({
            error: "Nenhuma questão encontrada com esses filtros. Tente outros filtros ou reduza a especificidade.",
            debug: {
              totalQuestionsAvailable: anyQuestions?.length || 0,
              appliedFilters: { specialty, theme, focus }
            }
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      questionsData = data;
      console.log(`✅ [flashcards-ai-generate] Encontradas ${questionsData.length} questões pelos filtros tradicionais`);

      const invalidQuestions = questionsData.filter(q => !q.question_content);
      if (invalidQuestions.length > 0) {
        console.warn(`⚠️ [flashcards-ai-generate] ${invalidQuestions.length} questões sem conteúdo encontradas`);
        invalidQuestions.forEach(q => console.warn(`Questão sem conteúdo: ID ${q.id}`));
      }
    }

    const questionsWithIds = questionsData.map((q, i) => {
      const categoryInfo =
        `[Q${i+1} ID: ${q.id}] ` +
        `[Especialidade ID: ${q.specialty_id || 'N/A'}] ` +
        `[Tema ID: ${q.theme_id || 'N/A'}] ` +
        `[Foco ID: ${q.focus_id || 'N/A'}]`;

      return categoryInfo;
    }).join('\n');

    const questionIdentifierInstruction = `
MUITO IMPORTANTE: Para cada flashcard gerado, você DEVE incluir o ID da questão que o inspirou no formato #QID#ID_DA_QUESTÃO# no início da propriedade "front".
Este ID será usado para rastrear a origem de cada flashcard e garantir que ele seja associado à categoria correta.

Exemplo de JSON correto:
{
  "front": "#QID#12345# A principal causa de [...] é [...]",
  "back": "resposta"
}

SEM ESTE ID ESPECÍFICO, NÃO SERÁ POSSÍVEL RASTREAR CORRETAMENTE A ORIGEM DO FLASHCARD.
    `;

    questionsData.forEach((question, index) => {
      console.log(`📝 [flashcards-ai-generate] Questão ${index + 1}:`);
      console.log(`ID: ${question.id}`);
      console.log(`Enunciado: ${question.question_content ? question.question_content.substring(0, 100) + '...' : 'N/A'}`);
      console.log(`Especialidade ID: ${question.specialty_id || 'N/A'}`);
      console.log(`Tema ID: ${question.theme_id || 'N/A'}`);
      console.log(`Foco ID: ${question.focus_id || 'N/A'}`);
      console.log(`Ano: ${question.exam_year || 'N/A'}`);

      if (question.response_choices) {
        if (Array.isArray(question.response_choices)) {
          console.log(`Alternativas: ${question.response_choices.length}`);
        } else if (typeof question.response_choices === 'object') {
          console.log(`Alternativas: ${Object.keys(question.response_choices).length}`);
        }
      }

      console.log('-'.repeat(50));
    });

    const questionsText = questionsData.map((q, i) => {
      const categoryInfo = `Especialidade ID: ${q.specialty_id || 'N/A'}, Tema ID: ${q.theme_id || 'N/A'}, Foco ID: ${q.focus_id || 'N/A'}`;

      const alternatives = Array.isArray(q.response_choices)
        ? q.response_choices.map((alt, j) => `${String.fromCharCode(65 + j)}) ${alt}`).join('\n')
        : Object.values(q.response_choices || {}).map((alt, j) => `${String.fromCharCode(65 + j)}) ${alt}`).join('\n');

      const questionContent = q.question_content || 'Conteúdo da questão não disponível';

      return `Q${i + 1} [ID:${q.id}]: ${questionContent}\n${alternatives}` +
        (q.exam_year ? ` [Ano: ${q.exam_year}]` : "") +
        `\n[Categorias: ${categoryInfo}]`;
    }).join('\n\n');

    const enhancedPrompt = `
${prompt}

${questionIdentifierInstruction}

INSTRUÇÕES ESPECÍFICAS PARA CATEGORIZAÇÃO:
1. Cada flashcard deve ser baseado EXCLUSIVAMENTE em UMA questão específica.
2. O ID da questão (#QID#) deve corresponder exatamente ao ID de uma das questões abaixo.
3. Não misture conteúdos de diferentes questões em um único flashcard.
4. Não invente categorias - use apenas as informações fornecidas nas questões.
5. IMPORTANTE: Crie EXATAMENTE ${quantity} flashcards no total.
6. Formate a resposta como um ARRAY JSON de ${quantity} objetos, cada um com 'front' e 'back'.

FORMATO DE RESPOSTA:
Por favor, retorne EXATAMENTE ${quantity} flashcards em formato JSON puro, SEM marcações markdown. Apenas o array JSON com os flashcards.

Exemplo correto de formatação (formate EXATAMENTE assim):
[
  {
    "front": "#QID#id-da-questao-1# Texto da frente com [...] lacuna",
    "back": "resposta 1"
  },
  {
    "front": "#QID#id-da-questao-2# Outro texto com [...] lacuna",
    "back": "resposta 2"
  }
]

Utilize as questões abaixo como base de inspiração para formular os flashcards:

${questionsText}
    `.trim();

    const openaiRequestBody = {
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'Você é um assistente especialista em criar bons flashcards para estudantes de medicina se preparando para residência médica. Você DEVE sempre retornar um array JSON com a quantidade exata de flashcards solicitada.'
        },
        {
          role: 'user',
          content: enhancedPrompt
        }
      ],
      temperature: 0.30,
      max_tokens: 2000,
    };

    console.log("📤 [flashcards-ai-generate] Enviando solicitação para OpenAI");
    console.log(`🔍 [flashcards-ai-generate] Enviando prompt para OpenAI (${enhancedPrompt.length} caracteres)`);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(openaiRequestBody),
    });

    const responseStatus = `${response.status} ${response.statusText}`;
    console.log(`🔍 [flashcards-ai-generate] Resposta recebida da OpenAI, status: ${responseStatus}`);

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`❌ [flashcards-ai-generate] Erro na API: ${responseStatus}`, errorData);
      return new Response(
        JSON.stringify({
          error: "Erro interno do servidor. Tente novamente em alguns minutos."
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const data = await response.json();
    console.log(`✅ [flashcards-ai-generate] Resposta recebida da OpenAI com sucesso`);

    if (!data.choices || data.choices.length === 0) {
      console.error("❌ [flashcards-ai-generate] Resposta inválida da OpenAI:", data);
      return new Response(
        JSON.stringify({ error: "Resposta inválida da OpenAI" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const generatedContent = data.choices[0]?.message?.content;
    if (!generatedContent) {
      console.error("❌ [flashcards-ai-generate] Conteúdo ausente na resposta da OpenAI");
      return new Response(
        JSON.stringify({ error: "Conteúdo ausente na resposta da OpenAI" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    console.log(`📝 [flashcards-ai-generate] CONTEÚDO GERADO PELA OPENAI:`);
    console.log(generatedContent);

    let generatedFlashcards;

    try {
      generatedFlashcards = extractJSONFromText(generatedContent);

      if (!generatedFlashcards || generatedFlashcards.length === 0) {
        throw new Error("Não foi possível extrair JSON válido da resposta ou o array está vazio");
      }

      console.log(`✅ [flashcards-ai-generate] Extraídos ${generatedFlashcards.length} flashcards do texto`);

      const questionMetadataMap = questionsData.reduce((acc, q) => {
        acc[q.id] = {
          specialty_id: q.specialty_id,
          theme_id: q.theme_id,
          focus_id: q.focus_id,
          hierarchy: {
            specialty: { name: 'N/A' },
            theme: { name: 'N/A' },
            focus: { name: 'N/A' }
          }
        };
        return acc;
      }, {});

      generatedFlashcards = generatedFlashcards.map(card => {
        let sourceId = null;

        if (card.front && typeof card.front === 'string') {
          const idMatch = card.front.match(/#QID#([^#]+)#/);
          if (idMatch && idMatch[1]) {
            sourceId = idMatch[1];
            card.front = card.front.replace(/#QID#[^#]+#\s*/, '');

            const metadata = questionMetadataMap[sourceId];
            if (metadata) {
              card.specialty_id = metadata.specialty_id;
              card.theme_id = metadata.theme_id;
              card.focus_id = metadata.focus_id;
              card.hierarchy = metadata.hierarchy;
              card.source_question_id = sourceId;
            }
          } else {
            console.warn(`⚠️ [flashcards-ai-generate] Não foi possível encontrar QID em:`, card.front.substring(0, 50));

            const firstQuestionId = questionsData[0].id;
            const metadata = questionMetadataMap[firstQuestionId];
            card.specialty_id = metadata.specialty_id;
            card.theme_id = metadata.theme_id;
            card.focus_id = metadata.focus_id;
            card.hierarchy = metadata.hierarchy;
            card.source_question_id = firstQuestionId;

            console.log(`⚠️ [flashcards-ai-generate] Usando ID de fallback: ${firstQuestionId}`);
          }
        }

        return card;
      });

      console.log(`🎯 [flashcards-ai-generate] Retornando ${generatedFlashcards.length} flashcards para o cliente`);

      return new Response(
        JSON.stringify({
          generated: generatedFlashcards,
          generatedText: generatedContent,
          fcType,
          source: directQuestions ? 'direct_questions' : (questionIds ? 'question_ids' : 'filters')
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (e) {
      console.error(`❌ [flashcards-ai-generate] Erro ao processar resposta: ${e.message}`, e.stack);
      return new Response(
        JSON.stringify({ error: e.message }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (e) {
    console.error(`❌ [flashcards-ai-generate] Erro: ${e.message}`, e.stack);
    return new Response(
      JSON.stringify({ error: e.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
