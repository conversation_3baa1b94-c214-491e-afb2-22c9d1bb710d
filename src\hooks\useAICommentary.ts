
import { useState, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { AICommentaryResponse } from "@/types/question";
import { useNavigationLock } from "@/contexts/NavigationLockContext";

export const useAICommentary = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [commentary, setCommentary] = useState<AICommentaryResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const currentQuestionIdRef = useRef<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const commentaryCache = useRef<Map<string, AICommentaryResponse>>(new Map());
  const { toast } = useToast();
  const { lockNavigation, unlockNavigation } = useNavigationLock();

  const resetCommentary = () => {
    // Cancelar requisição em andamento se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setCommentary(null);
    setError(null);
  };

  const generateCommentary = async (
    questionId: string,
    statement: string,
    alternatives: string[],
    correctAnswer: number,
    sessionId?: string,
    specialty?: string
  ) => {
    // Cancelar requisição anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Criar novo AbortController para esta requisição
    abortControllerRef.current = new AbortController();

    if (currentQuestionIdRef.current !== questionId) {
      resetCommentary();
      currentQuestionIdRef.current = questionId;
    }

    // Verificar se já temos o comentário em cache
    const cachedCommentary = commentaryCache.current.get(questionId);
    if (cachedCommentary) {
      setCommentary(cachedCommentary);
      setIsLoading(false);
      return cachedCommentary;
    }

    setIsLoading(true);
    setError(null);

    // 🔒 BLOQUEAR NAVEGAÇÃO durante a geração
    lockNavigation(`Gerando análise da IA para questão ${questionId.substring(0, 8)}...`);

    try {
      const cleanStatement = statement.replace(/<[^>]+>/g, '');
      const cleanAlternatives = alternatives.map(alt => alt.replace(/<[^>]+>/g, ''));

      const { data, error: supabaseError } = await supabase.functions.invoke(
        'question-commentary',
        {
          body: {
            statement: cleanStatement,
            alternatives: cleanAlternatives,
            correctAnswer,
            specialty: specialty || 'Medicina Geral'
          }
        }
      );

      if (supabaseError) {
        throw new Error('AI_SERVICE_ERROR');
      }

      if (!data) {
        throw new Error('AI_NO_DATA');
      }

      // 🛡️ BULLETPROOF DATA PROCESSING: Handle any response format
      const processedData: AICommentaryResponse = sanitizeCommentaryData(data, alternatives);

      if (currentQuestionIdRef.current === questionId) {
        setCommentary(processedData);
        // Adicionar ao cache
        commentaryCache.current.set(questionId, processedData);
      }

      if (sessionId) {
        try {
          const { data: existingAnswers, error: checkError } = await supabase
            .from('user_answers')
            .select('id')
            .eq('question_id', questionId)
            .eq('session_id', sessionId);

          if (checkError) throw checkError;

          if (existingAnswers && existingAnswers.length > 0) {
            // Update all answers for this question in this session
            for (const answer of existingAnswers) {
              const { error: updateError } = await supabase
                .from('user_answers')
                .update({
                  ai_commentary: processedData as any
                })
                .eq('id', answer.id);

              // Erro silencioso ao atualizar resposta
            }
          }
        } catch (err) {
          // Falha silenciosa ao tentar salvar o comentário
        }
      }

      toast({
        title: "Análise gerada",
        description: "A análise da questão foi gerada com sucesso."
      });

      return processedData;
    } catch (err: any) {
      // Se a requisição foi cancelada, não mostrar erro
      if (err.name === 'AbortError' || err.message?.includes('aborted')) {
        return null;
      }

      // ✅ Mensagens de erro sempre amigáveis - NUNCA mostrar detalhes técnicos
      let friendlyMessage = "Não foi possível gerar a análise no momento.";

      if (err.message === 'AI_SERVICE_ERROR' || err.message?.includes('Edge Function') || err.message?.includes('non-2xx')) {
        friendlyMessage = "Nosso sistema de IA está temporariamente indisponível.";
      } else if (err.message === 'AI_NO_DATA') {
        friendlyMessage = "A IA não conseguiu processar esta questão.";
      } else if (err.message?.includes('timeout') || err.message?.includes('network')) {
        friendlyMessage = "Problema de conexão detectado.";
      } else if (err.message?.includes('rate limit') || err.message?.includes('quota')) {
        friendlyMessage = "Muitas solicitações simultâneas. Aguarde um momento.";
      }

      if (currentQuestionIdRef.current === questionId) {
        setError(friendlyMessage);
        setCommentary(null);
      }

      toast({
        title: "Análise temporariamente indisponível",
        description: friendlyMessage,
        variant: "destructive"
      });

      return null;
    } finally {
      if (currentQuestionIdRef.current === questionId) {
        setIsLoading(false);
      }
      // 🔓 DESBLOQUEAR NAVEGAÇÃO após conclusão (sucesso ou erro)
      unlockNavigation();
    }
  };

  return {
    generateCommentary,
    commentary,
    isLoading,
    error,
    resetCommentary,
    currentQuestionId: currentQuestionIdRef.current
  };
};

// 🛡️ BULLETPROOF DATA SANITIZER: Ensures frontend never breaks with malformed data
function sanitizeCommentaryData(data: any, alternatives: string[]): AICommentaryResponse {


  // Handle completely invalid data
  if (!data || typeof data !== 'object') {
    console.warn('⚠️ [sanitizeCommentaryData] Invalid data object, creating fallback');
    return createFallbackCommentary(alternatives);
  }

  // Sanitize alternativas
  let sanitizedAlternativas: Array<{texto: string, comentario: string, correta: boolean}> = [];

  if (data.alternativas && Array.isArray(data.alternativas)) {
    sanitizedAlternativas = data.alternativas.map((alt: any, index: number) => ({
      texto: (alt?.texto && typeof alt.texto === 'string') ? alt.texto.trim() : alternatives[index] || `Alternativa ${index + 1}`,
      comentario: (alt?.comentario && typeof alt.comentario === 'string') ? alt.comentario.trim() : "Análise não disponível.",
      correta: Boolean(alt?.correta)
    }));
  } else {
    // Create default alternatives if missing
    sanitizedAlternativas = alternatives.map((texto, index) => ({
      texto: texto,
      comentario: "Análise não disponível.",
      correta: false // Will be set correctly by backend
    }));
  }

  // Ensure we have the right number of alternatives
  while (sanitizedAlternativas.length < alternatives.length) {
    const index = sanitizedAlternativas.length;
    sanitizedAlternativas.push({
      texto: alternatives[index] || `Alternativa ${index + 1}`,
      comentario: "Análise não disponível.",
      correta: false
    });
  }

  // Sanitize comentario_final
  let sanitizedComentarioFinal = "";
  if (data.comentario_final && typeof data.comentario_final === 'string') {
    sanitizedComentarioFinal = data.comentario_final.trim();
  } else {
    sanitizedComentarioFinal = "Análise completa não disponível.";
  }

  // Sanitize boolean fields
  const sanitizedPossivelErro = Boolean(data.possivel_erro_no_gabarito);
  const sanitizedJustificativa = (data.justificativa_erro_gabarito && typeof data.justificativa_erro_gabarito === 'string')
    ? data.justificativa_erro_gabarito.trim()
    : "";

  const result: AICommentaryResponse = {
    alternativas: sanitizedAlternativas,
    comentario_final: sanitizedComentarioFinal,
    possivel_erro_no_gabarito: sanitizedPossivelErro,
    justificativa_erro_gabarito: sanitizedJustificativa
  };


  return result;
}

// 🆘 FALLBACK CREATOR: Creates valid commentary when all else fails
function createFallbackCommentary(alternatives: string[]): AICommentaryResponse {


  return {
    alternativas: alternatives.map((texto, index) => ({
      texto: texto,
      comentario: "Análise temporariamente indisponível.",
      correta: false
    })),
    comentario_final: "Análise completa temporariamente indisponível. Tente novamente em alguns instantes.",
    possivel_erro_no_gabarito: false,
    justificativa_erro_gabarito: ""
  };
}
