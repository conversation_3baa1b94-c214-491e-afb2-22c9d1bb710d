import { useState, useCallback, useRef } from 'react';
import { useUser } from '@supabase/auth-helpers-react';
import { useScheduleManagement } from './useScheduleManagement';
import { useStudyTopics } from './useStudyTopics';
import { useAISchedule } from './useAISchedule';
import type { WeeklySchedule, GenerationStats } from '@/types/study-schedule';

// ✅ Funções auxiliares para atualizações incrementais
const updateTopicInSchedule = (schedule: WeeklySchedule | null, updatedTopic: StudyTopic): WeeklySchedule | null => {
  if (!schedule) return schedule;

  return {
    ...schedule,
    recommendations: schedule.recommendations.map(day => ({
      ...day,
      topics: day.topics.map(topic =>
        topic.id === updatedTopic.id ? { ...topic, ...updatedTopic } : topic
      )
    }))
  };
};

const markTopicAsStudiedInSchedule = (schedule: WeeklySchedule | null, topicId: string): WeeklySchedule | null => {
  if (!schedule) return schedule;

  return {
    ...schedule,
    recommendations: schedule.recommendations.map(day => ({
      ...day,
      topics: day.topics.map(topic =>
        topic.id === topicId ? {
          ...topic,
          study_status: 'completed',
          isStudied: true,
          last_revision_date: new Date().toISOString()
        } : topic
      )
    }))
  };
};

const removeTopicFromSchedule = (schedule: WeeklySchedule | null, topicId: string): WeeklySchedule | null => {
  if (!schedule) return schedule;

  return {
    ...schedule,
    recommendations: schedule.recommendations.map(day => ({
      ...day,
      topics: day.topics.filter(topic => topic.id !== topicId)
    }))
  };
};

const removeWeekFromSchedule = (schedule: WeeklySchedule | null, weekNumber: number): WeeklySchedule | null => {
  if (!schedule) return schedule;

  return {
    ...schedule,
    recommendations: schedule.recommendations.filter(day => day.weekNumber !== weekNumber)
  };
};
import { useToast } from '@/hooks/use-toast';

/**
 * Hook otimizado para cronograma com paginação e lazy loading
 */
export const useOptimizedSchedule = () => {
  console.log('🏗️ [useOptimizedSchedule] Hook sendo criado/recriado');

  const [isLoading, setIsLoading] = useState(false);
  const [weeklySchedule, setWeeklySchedule] = useState<WeeklySchedule | null>(null);
  const [generationStats, setGenerationStats] = useState<GenerationStats | null>(null);
  const [hasMoreWeeks, setHasMoreWeeks] = useState(true);
  const [loadedWeeksCount, setLoadedWeeksCount] = useState(0);

  const { toast } = useToast();
  const user = useUser();
  const loadingRef = useRef(false);

  console.log('👤 [useOptimizedSchedule] User:', user?.id);
  console.log('📊 [useOptimizedSchedule] Estado atual:', { isLoading, weeklySchedule: !!weeklySchedule, hasMoreWeeks, loadedWeeksCount });

  const {
    loadCurrentSchedule: fetchSchedule,
    loadMoreWeeks: fetchMoreWeeks,
    addWeeks,
    deleteWeek,
    deleteAllWeeks,
    updateTopic,
    isLoading: isScheduleLoading
  } = useScheduleManagement();

  const {
    markTopicAsStudied,
    deleteTopic,
    isLoading: isTopicLoading
  } = useStudyTopics();

  const {
    generateAISchedule,
    isLoading: isAILoading
  } = useAISchedule(
    setGenerationStats,
    addWeeks
  ); // ✅ REMOVIDO CALLBACK QUE CAUSAVA RELOAD COMPLETO!

  /**
   * Carrega o cronograma inicial (primeiras semanas + semana atual)
   */
  const loadInitialSchedule = useCallback(async (forceReload: boolean = false) => {
    console.log('🔄 [loadInitialSchedule] INÍCIO - forceReload:', forceReload);
    console.log('🔒 [loadInitialSchedule] loadingRef.current:', loadingRef.current);

    if (loadingRef.current) {
      console.log('⏸️ [loadInitialSchedule] JÁ CARREGANDO - saindo...');
      return;
    }

    try {
      console.log('🚀 [loadInitialSchedule] Iniciando carregamento...');
      loadingRef.current = true;
      setIsLoading(true);

      // ✅ Só resetar se for um reload forçado
      if (forceReload) {
        console.log('💥 [loadInitialSchedule] FORCE RELOAD - resetando estado...');
        setWeeklySchedule({ recommendations: [] });
      } else {
        console.log('🔄 [loadInitialSchedule] Carregamento normal - mantendo estado atual');
      }

      console.log('📡 [loadInitialSchedule] Buscando dados do servidor...');
      const schedule = await fetchSchedule(4, 0, true); // Carregar 4 semanas priorizando a atual
      console.log('📊 [loadInitialSchedule] Dados recebidos:', schedule);

      if (schedule) {
        console.log('✅ [loadInitialSchedule] Aplicando novo schedule...');
        setWeeklySchedule(schedule);
        const weeksLoaded = schedule.recommendations.length / 7;
        setLoadedWeeksCount(weeksLoaded);
        console.log('📈 [loadInitialSchedule] Semanas carregadas:', weeksLoaded);

        // Verificar se há mais semanas para carregar
        if (weeksLoaded < 4) {
          setHasMoreWeeks(false);
          console.log('🏁 [loadInitialSchedule] Não há mais semanas para carregar');
        }
      } else {
        console.log('❌ [loadInitialSchedule] Nenhum schedule recebido - definindo vazio');
        setWeeklySchedule({ recommendations: [] });
        setLoadedWeeksCount(0);
        setHasMoreWeeks(false);
      }
    } catch (error: any) {
      console.error('❌ [loadInitialSchedule] ERRO:', error);
      toast({
        title: "Erro ao carregar cronograma",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      console.log('🏁 [loadInitialSchedule] Finalizando...');
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [fetchSchedule, toast]);

  /**
   * Carrega mais semanas (lazy loading)
   */
  const loadMoreWeeks = useCallback(async () => {
    if (loadingRef.current || !hasMoreWeeks) return;
    
    try {
      loadingRef.current = true;
      setIsLoading(true);
      
      const moreSchedule = await fetchMoreWeeks(loadedWeeksCount, 4);
      
      if (moreSchedule && moreSchedule.recommendations.length > 0) {
        setWeeklySchedule(prev => {
          if (!prev) return moreSchedule;
          
          return {
            ...prev,
            recommendations: [...prev.recommendations, ...moreSchedule.recommendations]
          };
        });
        
        const newWeeksLoaded = moreSchedule.recommendations.length / 7;
        setLoadedWeeksCount(prev => prev + newWeeksLoaded);
        
        // Se carregou menos que o solicitado, não há mais semanas
        if (newWeeksLoaded < 4) {
          setHasMoreWeeks(false);
        }
      } else {
        setHasMoreWeeks(false);
      }
    } catch (error: any) {
      console.error('[useOptimizedSchedule] Error loading more weeks:', error);
      toast({
        title: "Erro ao carregar mais semanas",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [fetchMoreWeeks, loadedWeeksCount, hasMoreWeeks, toast]);

  /**
   * Recarrega o cronograma completo (usado apenas em caso de erro)
   */
  const reloadSchedule = useCallback(async () => {
    console.log('💥 [reloadSchedule] RELOAD COMPLETO INICIADO!');
    console.log('🔄 [reloadSchedule] Resetando contadores...');
    setLoadedWeeksCount(0);
    setHasMoreWeeks(true);
    console.log('🚀 [reloadSchedule] Chamando loadInitialSchedule com forceReload=true...');
    await loadInitialSchedule(true); // ✅ Força reload completo
    console.log('✅ [reloadSchedule] RELOAD COMPLETO FINALIZADO!');
  }, [loadInitialSchedule]);

  /**
   * Handlers otimizados para ações do cronograma
   */
  const handleMarkStudied = useCallback(async (topicId: string, revisionNumber?: number) => {
    console.log('🎯 [handleMarkStudied] INÍCIO - topicId:', topicId);
    console.log('📊 [handleMarkStudied] Estado atual weeklySchedule:', weeklySchedule);

    try {
      console.log('⚡ [handleMarkStudied] Aplicando atualização otimista...');

      // ✅ Atualização otimista: atualizar UI primeiro
      setWeeklySchedule(prev => {
        console.log('📝 [handleMarkStudied] Estado anterior:', prev);
        const newState = markTopicAsStudiedInSchedule(prev, topicId);
        console.log('📝 [handleMarkStudied] Novo estado:', newState);
        return newState;
      });

      console.log('🌐 [handleMarkStudied] Sincronizando com servidor...');
      // Sincronizar com servidor
      await markTopicAsStudied(topicId);
      console.log('✅ [handleMarkStudied] Sincronização concluída com sucesso');

    } catch (error: any) {
      console.error('❌ [handleMarkStudied] ERRO:', error);
      console.log('🔄 [handleMarkStudied] Recarregando schedule devido ao erro...');

      // ❌ Se falhar, recarregar para reverter
      await reloadSchedule();
      toast({
        title: "Erro ao marcar como estudado",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [markTopicAsStudied, reloadSchedule, toast, weeklySchedule]);

  const handleUpdateTopic = useCallback(async (topic: any) => {
    console.log('🎯 [handleUpdateTopic] INÍCIO - topic:', topic);
    console.log('📊 [handleUpdateTopic] Estado atual weeklySchedule:', weeklySchedule);

    try {
      console.log('⚡ [handleUpdateTopic] Aplicando atualização otimista...');

      // ✅ Atualização otimista: atualizar UI primeiro
      setWeeklySchedule(prev => {
        console.log('📝 [handleUpdateTopic] Estado anterior:', prev);
        const newState = updateTopicInSchedule(prev, topic);
        console.log('📝 [handleUpdateTopic] Novo estado:', newState);
        return newState;
      });

      console.log('🌐 [handleUpdateTopic] Sincronizando com servidor...');
      // Sincronizar com servidor
      await updateTopic(topic);
      console.log('✅ [handleUpdateTopic] Sincronização concluída com sucesso');

    } catch (error: any) {
      console.error('❌ [handleUpdateTopic] ERRO:', error);
      console.log('🔄 [handleUpdateTopic] Recarregando schedule devido ao erro...');

      // ❌ Se falhar, recarregar para reverter
      await reloadSchedule();
      toast({
        title: "Erro ao atualizar tópico",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [updateTopic, reloadSchedule, toast, weeklySchedule]);

  const handleDeleteWeek = useCallback(async (weekNumber: number) => {
    try {
      // ✅ Atualização otimista: remover da UI primeiro
      setWeeklySchedule(prev => removeWeekFromSchedule(prev, weekNumber));

      // Sincronizar com servidor
      await deleteWeek(weekNumber);
    } catch (error: any) {
      // ❌ Se falhar, recarregar para reverter
      await reloadSchedule();
      toast({
        title: "Erro ao deletar semana",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [deleteWeek, reloadSchedule, toast]);

  const handleDeleteAllWeeks = useCallback(async () => {
    try {
      await deleteAllWeeks();
      setWeeklySchedule({ recommendations: [] });
      setLoadedWeeksCount(0);
      setHasMoreWeeks(false);
    } catch (error: any) {
      toast({
        title: "Erro ao deletar todas as semanas",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [deleteAllWeeks, toast]);

  const handleDeleteTopic = useCallback(async (topicId: string) => {
    try {
      // ✅ Atualização otimista: remover da UI primeiro
      setWeeklySchedule(prev => removeTopicFromSchedule(prev, topicId));

      // Sincronizar com servidor
      await deleteTopic(topicId);
    } catch (error: any) {
      // ❌ Se falhar, recarregar para reverter
      await reloadSchedule();
      toast({
        title: "Erro ao deletar tópico",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [deleteTopic, reloadSchedule, toast]);

  return {
    // Estado
    isLoading: isLoading || isScheduleLoading || isTopicLoading || isAILoading,
    weeklySchedule,
    generationStats,
    hasMoreWeeks,
    loadedWeeksCount,
    
    // Funções de carregamento
    loadInitialSchedule,
    loadMoreWeeks,
    reloadSchedule,
    
    // Handlers
    handleMarkStudied,
    handleUpdateTopic,
    handleDeleteWeek,
    handleDeleteAllWeeks,
    handleDeleteTopic,
    
    // Funções originais
    generateAISchedule,
    addWeeks
  };
};
