
import { Comment } from "@/types/question";
import { Json } from "@/integrations/supabase/types/json";

// Convert JSON data to Comment array
export const convertJsonToComments = (jsonData: Json | null): Comment[] => {
  if (!jsonData) return [];
  
  // If it's already an array, return it typed as Comment[]
  if (Array.isArray(jsonData)) {
    return jsonData as Comment[];
  }
  
  // If it's a JSON object, try to parse it
  if (typeof jsonData === 'object') {
    try {
      return Object.values(jsonData);
    } catch (e) {
      console.error('Error converting JSON to Comments:', e);
      return [];
    }
  }
  
  // If it's a string, try to parse it
  if (typeof jsonData === 'string') {
    try {
      return JSON.parse(jsonData) as Comment[];
    } catch (e) {
      console.error('Error parsing JSON string to Comments:', e);
      return [];
    }
  }
  
  return [];
};

// Convert Comment array to JSON format for database storage
export const convertCommentsToJson = (comments: Comment[]): Json => {
  return comments as unknown as Json;
};
