
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Flashcard } from "@/types/flashcard";
import type { ReviewMetricsInfo } from "./types";
import { ReviewButton } from "./buttons/ReviewButton";
import { useReviewMetrics } from "./hooks/useReviewMetrics";
import type { FlashcardResponse } from "./types";

const buttonConfigs = [
  {
    response: 'error' as const,
    label: 'De novo',
    className: 'bg-red-500 hover:bg-red-600 border-red-700'
  },
  {
    response: 'hard' as const,
    label: 'Difícil',
    className: 'bg-orange-500 hover:bg-orange-600 border-orange-700'
  },
  {
    response: 'medium' as const,
    label: 'Médio',
    className: 'bg-purple-500 hover:bg-purple-600 border-purple-700'
  },
  {
    response: 'easy' as const,
    label: 'Fácil',
    className: 'bg-green-500 hover:bg-green-600 border-green-700'
  }
];

interface ReviewButtonsProps {
  currentCard: Flashcard;
  onResponse: (response: FlashcardResponse, metricsInfo?: ReviewMetricsInfo) => void;
  isDisabled?: boolean;
}

export const ReviewButtons = ({ currentCard, onResponse, isDisabled }: ReviewButtonsProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { currentReview, preCalculatedMetrics } = useReviewMetrics(currentCard);

  const handleButtonClick = async (response: FlashcardResponse) => {
    if (!preCalculatedMetrics) {
      //console.error('❌ [ReviewButtons] Métricas não calculadas');
      return;
    }

    setIsSubmitting(true);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error("Erro ao obter usuário:", userError);
        return;
      }

      const metrics = preCalculatedMetrics[response];
      const isCorrect = response === 'easy' || response === 'medium';

      const updateData = {
        user_id: user.id,
        card_id: currentCard.id,
        stability: metrics.stability,
        difficulty: metrics.difficulty,
        retrievability: metrics.retrievability,
        intervalindays: metrics.intervalInDays,
        last_review_date: new Date().toISOString(),
        next_review_date: metrics.nextReviewDate.toISOString(),
        total_reviews: currentReview ? currentReview.total_reviews + 1 : 1,
        correct_reviews: currentReview ? 
          (isCorrect ? currentReview.correct_reviews + 1 : currentReview.correct_reviews) : 
          (isCorrect ? 1 : 0)
      };

      const { error: updateError } = await supabase
        .from('flashcards_reviews')
        .upsert(updateData, {
          onConflict: 'user_id,card_id'
        });

      if (updateError) {
        throw updateError;
      }

      onResponse(response, {
        nextDate: metrics.nextReviewDate,
        intervalInDays: metrics.intervalInDays,
        metrics
      });
    } catch (e) {
      console.error("❌ [ReviewButtons] Erro inesperado:", e);
      toast.error("Erro ao processar revisão");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!preCalculatedMetrics) return null;

  return (
    <div className="grid grid-cols-4 gap-4">
      {buttonConfigs.map((config) => (
        <ReviewButton
          key={config.response}
          config={config}
          metrics={preCalculatedMetrics[config.response]}
          onClick={() => handleButtonClick(config.response)}
          isDisabled={isDisabled || isSubmitting}
        />
      ))}
    </div>
  );
};
