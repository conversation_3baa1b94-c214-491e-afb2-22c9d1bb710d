import type { FlashcardResponse } from "@/types/flashcard";

export interface ButtonConfig {
  response: FlashcardResponse;
  label: string;
  className: string;
}

export interface ReviewButtonInfo {
  nextDate?: Date;
  intervalInDays?: number;
  metrics?: {
    stability: number;
    difficulty: number;
    retrievability: number;
  };
}

export interface ReviewMetricsInfo {
  nextDate: Date;
  intervalInDays: number;
  metrics: {
    stability: number;
    difficulty: number;
    retrievability: number;
  };
}