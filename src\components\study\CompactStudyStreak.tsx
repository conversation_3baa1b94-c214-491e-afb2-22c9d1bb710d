import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CheckCircle, Star, Trophy } from 'lucide-react';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const CompactStudyStreak: React.FC = () => {
  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading
  } = useStreakSystem();

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-4 border-2 border-orange-200 shadow-md hover:shadow-lg transition-all duration-300"
    >
      {/* Header Compacto */}
      <div className="flex items-center gap-3 mb-4">
        <div className="bg-gradient-to-br from-orange-400 to-orange-600 p-2 rounded-full border border-orange-300">
          <Flame className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-lg font-bold text-orange-600">
                {isLoading ? '...' : currentStreak}
              </span>
              <span className="text-xs text-orange-500 font-medium ml-1">
                {currentStreak === 1 ? 'dia' : 'dias'}
              </span>
            </div>
            {maxStreak > 0 && (
              <div className="flex items-center gap-1 bg-amber-50 px-2 py-1 rounded-full border border-amber-200">
                <Trophy className="h-3 w-3 text-amber-600" />
                <span className="text-xs font-bold text-amber-700">{maxStreak}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Calendário Semanal Compacto */}
      <div className="flex items-center justify-between gap-1">
        {weekDays.map((day, index) => (
          <motion.div
            key={day.key}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.05 * index }}
            className="flex flex-col items-center"
          >
            {/* Letra do dia */}
            <span className={`text-xs font-bold mb-1 ${
              day.isCurrentDay 
                ? 'text-blue-600' 
                : day.isActive 
                  ? 'text-orange-500' 
                  : 'text-gray-500'
            }`}>
              {day.shortName}
            </span>
            
            {/* Círculo do dia */}
            <div className={`
              w-5 h-5 rounded-full flex items-center justify-center border transition-all duration-300
              ${day.isActive
                ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white border-orange-300'
                : day.isCurrentDay
                  ? 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 border-blue-300'
                  : 'bg-gray-100 text-gray-400 border-gray-300'
              }
            `}>
              {day.isActive ? (
                <CheckCircle className="h-2.5 w-2.5" />
              ) : day.isCurrentDay ? (
                <Star className="h-2 w-2" />
              ) : null}
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default CompactStudyStreak;
