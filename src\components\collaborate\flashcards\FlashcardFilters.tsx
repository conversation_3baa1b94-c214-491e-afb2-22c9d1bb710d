
import { useState, useEffect } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";

interface FlashcardFiltersProps {
  onFilterChange: (filters: {
    specialty?: string;
    theme?: string;
    focus?: string;
    extrafocus?: string;
  }) => void;
  onSearchChange: (search: string) => void;
}

export const FlashcardFilters = ({ onFilterChange, onSearchChange }: FlashcardFiltersProps) => {
  const [specialties, setSpecialties] = useState<any[]>([]);
  const [themes, setThemes] = useState<any[]>([]);
  const [focuses, setFocuses] = useState<any[]>([]);
  const [extraFocuses, setExtraFocuses] = useState<any[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [selectedFocus, setSelectedFocus] = useState<string>("");
  const [selectedExtraFocus, setSelectedExtraFocus] = useState<string>("");

  useEffect(() => {
    loadSpecialties();
  }, []);

  useEffect(() => {
    if (selectedSpecialty && selectedSpecialty !== "_all") {
      loadThemes(selectedSpecialty);
      setSelectedTheme("");
      setSelectedFocus("");
      setSelectedExtraFocus("");
    } else {
      setThemes([]);
      setSelectedTheme("");
      setSelectedFocus("");
      setSelectedExtraFocus("");
    }
  }, [selectedSpecialty]);

  useEffect(() => {
    if (selectedTheme && selectedTheme !== "_all") {
      loadFocuses(selectedTheme);
      setSelectedFocus("");
      setSelectedExtraFocus("");
    } else {
      setFocuses([]);
      setSelectedFocus("");
      setSelectedExtraFocus("");
    }
  }, [selectedTheme]);

  useEffect(() => {
    if (selectedFocus && selectedFocus !== "_all") {
      loadExtraFocuses(selectedFocus);
      setSelectedExtraFocus("");
    } else {
      setExtraFocuses([]);
      setSelectedExtraFocus("");
    }
  }, [selectedFocus]);

  useEffect(() => {
    onFilterChange({
      specialty: selectedSpecialty === "_all" || !selectedSpecialty ? undefined : selectedSpecialty,
      theme: selectedTheme === "_all" || !selectedTheme ? undefined : selectedTheme,
      focus: selectedFocus === "_all" || !selectedFocus ? undefined : selectedFocus,
      extrafocus: selectedExtraFocus === "_all" || !selectedExtraFocus ? undefined : selectedExtraFocus,
    });
  }, [selectedSpecialty, selectedTheme, selectedFocus, selectedExtraFocus, onFilterChange]);

  const loadSpecialties = async () => {
    // ... same as before ...
    const { data } = await supabase
      .from('flashcards_specialty')
      .select('*')
      .order('name');
    if (data) setSpecialties(data);
  };

  const loadThemes = async (specialtyId: string) => {
    const { data } = await supabase
      .from('flashcards_theme')
      .select('*')
      .eq('specialty_id', specialtyId)
      .order('name');
    if (data) setThemes(data);
  };

  const loadFocuses = async (themeId: string) => {
    const { data } = await supabase
      .from('flashcards_focus')
      .select('*')
      .eq('theme_id', themeId)
      .order('name');
    if (data) setFocuses(data);
  };

  const loadExtraFocuses = async (focusId: string) => {
    const { data } = await supabase
      .from('flashcards_extrafocus')
      .select('*')
      .eq('focus_id', focusId)
      .order('name');
    if (data) setExtraFocuses(data);
  };

  return (
    <div className="space-y-4 mb-6">
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Pesquisar nos flashcards..."
          className="pl-10"
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
          <SelectTrigger>
            <SelectValue placeholder="Especialidade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todas as especialidades</SelectItem>
            {specialties.map((specialty) => (
              <SelectItem key={specialty.id} value={specialty.id}>
                {specialty.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedTheme} onValueChange={setSelectedTheme} disabled={!selectedSpecialty || selectedSpecialty === "_all"}>
          <SelectTrigger>
            <SelectValue placeholder="Tema" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os temas</SelectItem>
            {themes.map((theme) => (
              <SelectItem key={theme.id} value={theme.id}>
                {theme.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedFocus} onValueChange={setSelectedFocus} disabled={!selectedTheme || selectedTheme === "_all"}>
          <SelectTrigger>
            <SelectValue placeholder="Foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os focos</SelectItem>
            {focuses.map((focus) => (
              <SelectItem key={focus.id} value={focus.id}>
                {focus.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedExtraFocus} onValueChange={setSelectedExtraFocus} disabled={!selectedFocus || selectedFocus === "_all"}>
          <SelectTrigger>
            <SelectValue placeholder="Extra Foco" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="_all">Todos os extra focos</SelectItem>
            {extraFocuses.map((extraFocus) => (
              <SelectItem key={extraFocus.id} value={extraFocus.id}>
                {extraFocus.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
