import React, { useState, useEffect, useRef } from 'react';
import StudyStreak from './StudyStreak';

/**
 * Wrapper lazy para StudyStreak que só carrega dados quando visível
 */
const LazyStudyStreak: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [canLoad, setCanLoad] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Delay inicial para evitar carregamento imediato
  useEffect(() => {
    const timer = setTimeout(() => {
      setCanLoad(true);
    }, 2000); // 2 segundos de delay

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!canLoad) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          // Desconectar observer após carregar
          observer.disconnect();
        }
      },
      {
        rootMargin: '-100px', // Só carrega quando realmente visível
        threshold: 0.3
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasLoaded, canLoad]);

  return (
    <div ref={ref}>
      {isVisible ? (
        <StudyStreak enabled={true} />
      ) : (
        <div className="bg-white rounded-xl p-4 sm:p-5 shadow-sm border border-gray-100 w-full max-w-md mx-auto lg:mx-0">
          {/* Header skeleton */}
          <div className="flex items-center justify-between mb-4">
            <div className="h-5 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-24"></div>
            <div className="h-6 w-6 bg-gradient-to-r from-orange-300 to-orange-200 rounded-full animate-pulse"></div>
          </div>

          {/* Streak counter skeleton */}
          <div className="text-center mb-4">
            <div className="h-8 w-16 bg-gradient-to-r from-orange-400 to-orange-300 rounded-lg animate-pulse mx-auto mb-2"></div>
            <div className="h-3 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-20 mx-auto"></div>
          </div>

          {/* Week days skeleton */}
          <div className="flex justify-between items-center gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="flex flex-col items-center flex-1">
                <div className="h-2.5 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-3 mb-1.5"></div>
                <div className="w-5 h-5 bg-gradient-to-r from-gray-400 to-gray-300 rounded-full animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LazyStudyStreak;
