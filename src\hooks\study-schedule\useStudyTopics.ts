
import { useState } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { StudyResult } from "./types";
import { calculateRevisionDateAndWeek, createRevisionItem } from './useRevisions';
import { ensureWeeksExist } from './useScheduleDates';

export const useStudyTopics = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const markTopicAsStudied = async (topicId: string): Promise<StudyResult> => {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: schedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('*')
        .order('week_start_date', { ascending: true });

      if (schedulesError) throw schedulesError;

      const { data: currentTopic } = await supabase
        .from('study_schedule_items')
        .select('*, study_schedules!inner(*)')
        .eq('id', topicId)
        .single();

      if (!currentTopic) throw new Error('Topic not found');

      const scheduleStartDate = new Date(currentTopic.study_schedules.week_start_date);
      const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
      const dayIndex = weekDays.indexOf(currentTopic.day_of_week.toLowerCase());

      const topicDate = new Date(scheduleStartDate);
      topicDate.setDate(scheduleStartDate.getDate() + dayIndex);

      const currentRevisionNumber = currentTopic.revision_number || 0;
      let daysToAdd = 3;

      if (currentTopic.type === 'revision') {
        switch (currentRevisionNumber) {
          case 0: daysToAdd = 3; break;
          case 1: daysToAdd = 7; break;
          case 2: daysToAdd = 30; break;
          case 3: daysToAdd = 90; break;
          default: return {
            success: true,
            message: "This topic has already been through all revisions",
            isLastRevision: true
          };
        }
      }

      // Before calculating the next revision date, make sure we have weeks in the future
      const nextRevisionDate = new Date(topicDate);
      nextRevisionDate.setDate(nextRevisionDate.getDate() + daysToAdd);

      // Ensure weeks exist up to the next revision date
      // await ensureWeeksExist(nextRevisionDate, user.id, addWeeks); // Removido para evitar recarregamentos

      // Re-fetch schedules after potentially adding new weeks
      const { data: updatedSchedules } = await supabase
        .from('study_schedules')
        .select('*')
        .order('week_start_date', { ascending: true });

      const revisionInfo = await calculateRevisionDateAndWeek(
        topicDate,
        daysToAdd,
        updatedSchedules || schedules
      );

      const updates: any = {
        study_status: 'completed',
        last_revision_date: topicDate.toISOString(),
        revision_number: currentRevisionNumber
      };

      if (revisionInfo) {
        updates.next_revision_date = revisionInfo.date.toISOString();
      }

      const { error: updateError } = await supabase
        .from('study_schedule_items')
        .update(updates)
        .eq('id', topicId);

      if (updateError) throw updateError;

      if (currentRevisionNumber >= 3) {
        // await loadCurrentSchedule(); // Removido para evitar recarregamentos
        return {
          success: true,
          message: "All revisions for this topic have been completed!",
          isLastRevision: true
        };
      }

      if (revisionInfo) {
        const revisionChain = currentTopic.revision_chain || [];
        revisionChain.push(topicId);

        await createRevisionItem(
          currentTopic,
          revisionInfo.date,
          currentRevisionNumber + 1,
          topicId,
          revisionChain,
          updatedSchedules || schedules
        );

        // await loadCurrentSchedule(); // Removido para evitar recarregamentos

        return {
          success: true,
          message: "Topic marked as studied successfully!",
          nextRevision: {
            date: revisionInfo.formattedDate,
            dayOfWeek: revisionInfo.dayOfWeek,
            revisionNumber: currentRevisionNumber + 1,
            daysUntil: daysToAdd
          },
          isLastRevision: false
        };
      }

      // await loadCurrentSchedule(); // Removido para evitar recarregamentos
      return {
        success: true,
        message: "Topic marked as studied, but the next revision could not be scheduled.",
        isLastRevision: false
      };

    } catch (error: any) {
      console.error('Error updating topic:', error);
      toast({
        title: "Error updating topic",
        description: error.message,
        variant: "destructive"
      });
      return {
        success: false,
        message: error.message
      };
    }
  };

  const deleteTopic = async (topicId: string) => {
    setIsLoading(true);
    try {
      // Delete the topic
      const { error } = await supabase
        .from('study_schedule_items')
        .delete()
        .eq('id', topicId);

      if (error) throw error;

      toast({
        title: "Topic deleted",
        description: "The topic has been removed from your schedule.",
      });

      // await loadCurrentSchedule(); // Removido para evitar recarregamentos
      return true;
    } catch (error: any) {
      console.error('Error deleting topic:', error);
      toast({
        title: "Error deleting topic",
        description: error.message,
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    markTopicAsStudied,
    deleteTopic
  };
};
