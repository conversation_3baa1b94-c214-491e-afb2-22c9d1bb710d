import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@supabase/auth-helpers-react';
import { useToast } from '@/hooks/use-toast';
import { useAISchedule } from './useAISchedule';
import { useScheduleManagement } from './useScheduleManagement';
import type { AIScheduleOptions, GenerationStats } from '@/types/study-schedule';
import { useState } from 'react';

/**
 * Hook para geração de cronograma com IA usando React Query
 */
export const useAIScheduleQuery = () => {
  const user = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // ✅ Estado para capturar estatísticas da geração
  const [generationStats, setGenerationStats] = useState<GenerationStats | null>(null);
  const [isComplete, setIsComplete] = useState(false);

  const { addWeeks } = useScheduleManagement();

  // Wrapper para addWeeks que retorna Promise<boolean>
  const addWeeksWrapper = async (numberOfWeeks: number): Promise<boolean> => {
    const result = await addWeeks(numberOfWeeks);
    return result;
  };

  const { generateAISchedule } = useAISchedule(
    setGenerationStats, // ✅ Agora captura as estatísticas
    addWeeksWrapper
  );

  // ✅ Mutation para gerar cronograma com IA
  const generateAIScheduleMutation = useMutation({
    mutationFn: async (options: AIScheduleOptions) => {
      // Reset states
      setIsComplete(false);
      setGenerationStats(null);

      const result = await generateAISchedule(options);
      return result;
    },
    onMutate: async (options: AIScheduleOptions) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
    },
    onError: (err: any, options, context) => {
      setIsComplete(false);
      setGenerationStats(null);
    },
    onSuccess: (result) => {
      // ✅ Marcar como completo quando a IA terminar
      setIsComplete(true);

      // Invalidar cache para mostrar novos tópicos
      setTimeout(() => {
        console.log('🔄 [useAIScheduleQuery] Invalidando cache do cronograma...');
        queryClient.invalidateQueries({
          queryKey: ['schedule', user?.id],
          refetchType: 'all'
        });

        // ✅ NOVO: Forçar refetch imediato
        queryClient.refetchQueries({
          queryKey: ['schedule', user?.id]
        });
      }, 500); // Aumentado para 500ms
    },
    onSettled: () => {
      // Não fazer nada aqui - deixar o dialog gerenciar o estado
    }
  });

  return {
    generateAISchedule: generateAIScheduleMutation.mutate,
    isGenerating: generateAIScheduleMutation.isPending,
    isComplete,
    generationStats,
    error: generateAIScheduleMutation.error,
    resetCompletion: () => {
      setIsComplete(false);
      setGenerationStats(null);
    }
  };
};
