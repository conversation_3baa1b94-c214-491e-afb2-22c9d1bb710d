
import React from "react";
import { Pencil, Trash2, Folder } from "lucide-react";
import { cn } from "@/lib/utils";

interface FolderCardProps {
  name: string;
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  iconColor?: string;
  className?: string;
}

export const FolderCard: React.FC<FolderCardProps> = ({
  name,
  onClick,
  onEdit,
  onDelete,
  iconColor = "#1676F3",
  className
}) => {
  return (
    <div
      className={cn(
        "rounded-xl border-2 border-gray-200 bg-white shadow-sm relative cursor-pointer flex flex-col items-center justify-center group p-6 transition-all hover:shadow-md hover:border-primary",
        "min-w-[180px] min-h-[130px] w-full sm:w-64",
        className
      )}
      onClick={onClick}
    >
      <div className="absolute right-3 top-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-all z-10">
        {onEdit && (
          <button 
            className="text-gray-500 hover:text-gray-700" 
            onClick={e => { 
              e.stopPropagation(); 
              onEdit(); 
            }}
          >
            <Pencil className="h-4 w-4" />
          </button>
        )}
        {onDelete && (
          <button 
            className="text-gray-500 hover:text-red-600" 
            onClick={e => { 
              e.stopPropagation(); 
              onDelete(); 
            }}
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}
      </div>
      <Folder size={40} color={iconColor} />
      <div className="mt-4 font-semibold text-base text-gray-800 text-center">
        {name || "Sem nome"}
      </div>
    </div>
  );
};
