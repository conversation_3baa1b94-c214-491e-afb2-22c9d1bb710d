
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, BookOpen, Check, Calendar, Trash2 } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';

interface TopicDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic;
  onMarkStudied?: (topicId: string) => void;
  onDelete?: (topic: StudyTopic) => void;
}

export const TopicDetailsDialog: React.FC<TopicDetailsDialogProps> = ({
  open,
  onOpenChange,
  topic,
  onMarkStudied,
  onDelete
}) => {
  // Format next revision date
  const formatNextRevisionDate = (date?: string) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  };
  
  // Format time
  const formatTime = (time: string) => {
    if (!time) return 'Horário não definido';
    
    if (time.includes('AM') || time.includes('PM') || time.includes(':')) {
      return time;
    }
    
    try {
      const timeDate = new Date(`2000-01-01T${time}`);
      return timeDate.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch (error) {
      return time;
    }
  };
  
  // Get title with improved handling
  const getTitle = () => {
    // If focus exists and is not "Geral", "N/A", or empty, use that
    if (topic.focus && topic.focus !== "Geral" && topic.focus !== "N/A" && topic.focus !== "") {
      return topic.focus;
    }
    // Otherwise, if theme exists and is not empty, use that
    else if (topic.theme && topic.theme !== "") {
      return topic.theme;
    }
    // Finally, if specialty exists, use that
    else if (topic.specialty && topic.specialty !== "") {
      return topic.specialty;
    }
    // Default fallback
    else {
      return "Estudo";
    }
  };
  
  // Get difficulty badge color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return 'bg-green-100 text-green-700';
      case 'Médio': return 'bg-yellow-100 text-yellow-700';
      case 'Difícil': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const handleMarkStudied = () => {
    if (onMarkStudied && topic.id) {
      onMarkStudied(topic.id);
      onOpenChange(false);
    }
  };
  
  const handleDelete = () => {
    if (onDelete) {
      onDelete(topic);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg sm:max-w-xl p-0 overflow-hidden border-0">
        {/* Background with design elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-xl"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-300 rounded-full blur-3xl opacity-10 -mr-10 -mt-10"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-green-400 rounded-full blur-3xl opacity-10 -ml-20 -mb-20"></div>
        
        <div className="relative z-10 p-6">
          <DialogHeader className="pb-2">
            <div className="flex items-center gap-3 mb-1">
              <div className="p-2 bg-blue-100 rounded-full">
                <BookOpen className="w-5 h-5 text-blue-600" />
              </div>
              <DialogTitle className="text-xl font-bold text-slate-800">
                {getTitle()}
              </DialogTitle>
            </div>
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge className={`${getDifficultyColor(topic.difficulty)}`}>
                {topic.difficulty}
              </Badge>
              
              {topic.study_status === 'completed' && (
                <Badge className="bg-green-100 text-green-700">
                  <Check className="mr-1 h-3 w-3" />
                  Estudado
                </Badge>
              )}
              
              {topic.next_revision_date && (
                <Badge className="bg-purple-100 text-purple-700">
                  <Calendar className="mr-1 h-3 w-3" />
                  Próxima revisão: {formatNextRevisionDate(topic.next_revision_date)}
                </Badge>
              )}
            </div>
            
            <div className="text-slate-600 mt-3 space-y-1">
              {topic.specialty && <div className="font-medium">Especialidade: {topic.specialty}</div>}
              {topic.theme && topic.theme !== "Geral" && (
                <div className="font-medium">Tema: {topic.theme}</div>
              )}
              {topic.focus && topic.focus !== "Geral" && topic.focus !== "N/A" && (
                <div className="font-medium">Foco: {topic.focus}</div>
              )}
            </div>
          </DialogHeader>
          
          <div className="mt-6 space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 space-y-3">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <div className="text-sm font-medium text-gray-700">
                  Horário: {formatTime(topic.startTime)}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <div className="text-sm font-medium text-gray-700">
                  Duração: {topic.duration}
                </div>
              </div>
              
              <div className="pt-3 border-t border-blue-200">
                <div className="text-sm font-semibold text-blue-700 mb-2 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Atividade:
                </div>
                <div className="text-sm text-slate-700 bg-white/60 p-3 rounded-lg border border-blue-100">
                  {topic.activity}
                </div>
              </div>
            </div>
            
            <DialogFooter className="mt-6 flex-col sm:flex-row gap-3 sm:justify-between sm:items-center pt-4 border-t border-blue-200">
              <div className="flex gap-2 order-2 sm:order-1">
                {onDelete && (
                  <Button
                    variant="outline"
                    className="gap-2 text-rose-600 border-rose-200 hover:bg-rose-50 hover:border-rose-300 transition-all duration-200 hover:scale-105"
                    onClick={handleDelete}
                  >
                    <Trash2 className="h-4 w-4" />
                    Remover
                  </Button>
                )}
              </div>

              <div className="flex gap-2 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="flex-1 sm:flex-none border-slate-300 hover:bg-slate-50 hover:border-slate-400 transition-all duration-200"
                >
                  Fechar
                </Button>

                {topic.study_status !== 'completed' && onMarkStudied && (
                  <Button
                    onClick={handleMarkStudied}
                    className="flex-1 sm:flex-none bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Marcar como estudado
                  </Button>
                )}
              </div>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
