
import { QuestionImport as ImportComponent } from "@/components/settings/QuestionImport";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { useNavigate } from "react-router-dom";

const QuestionImport = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Verificar se o usuário é admin
  if (!user) {
    navigate("/");
    return null;
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Importar Questões</CardTitle>
        </CardHeader>
        <CardContent>
          <ImportComponent />
        </CardContent>
      </Card>
    </div>
  );
};

export default QuestionImport;
