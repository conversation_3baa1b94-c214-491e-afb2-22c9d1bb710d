
import type { StudyTopic } from "@/types/study-schedule";
import type { FilterOption } from "@/components/filters/types";

export interface TopicFormData {
  specialty: string;
  theme: string;
  focus: string;
  startTime: string;
  duration: string;
  activity: string;
  manualSpecialty?: string;
  manualTheme?: string;
  manualFocus?: string;
  specialtyId?: string;
  themeId?: string;
  focusId?: string;
}

export interface TopicEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic;
  onSave: (updatedTopic: StudyTopic) => void;
  categories: FilterOption[];
  scheduleId: string;
  isCreating?: boolean;
  isManual?: boolean;
}

export interface TopicSelectionStepProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  selectedSpecialty: string;
  setSelectedSpecialty: (value: string) => void;
  selectedTheme: string;
  setSelectedTheme: (value: string) => void;
  selectedFocus: string;
  setSelectedFocus: (value: string) => void;
  filteredSpecialties: FilterOption[];
  filteredThemes: FilterOption[];
  filteredFocuses: FilterOption[];
  expandedSection: "specialty" | "theme" | "focus";
  setExpandedSection: (section: "specialty" | "theme" | "focus") => void;
  studyStats: any;
  isLoading: boolean;
  domain: string;
  shouldShowFocus: boolean;
}

export interface ManualTopicFormProps {
  manualSpecialty: string;
  setManualSpecialty: (value: string) => void;
  manualTheme: string;
  setManualTheme: (value: string) => void;
  manualFocus: string;
  setManualFocus: (value: string) => void;
  domain: string;
  shouldShowFocus: boolean;
}

export interface TimeSettingsStepProps {
  startTime: string;
  setStartTime: (value: string) => void;
  duration: string;
  setDuration: (value: string) => void;
  activity: string;
  setActivity: (value: string) => void;
}

export interface TopicReviewStepProps {
  isManual: boolean;
  manualSpecialty: string;
  manualTheme: string;
  manualFocus: string;
  selectedSpecialty: string;
  selectedTheme: string;
  selectedFocus: string;
  startTime: string;
  duration: string;
  activity: string;
  shouldShowFocus: boolean;
}
