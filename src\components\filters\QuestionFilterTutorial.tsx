import React, { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Brain, Building2, Calendar, FileText, ArrowRight, CheckCircle, Sparkles } from "lucide-react";
import { useUserPreferences } from "@/hooks/useUserPreferences";

type TutorialStep = {
  title: string;
  description: string;
  targetSelector?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  badge?: string;
};

export function QuestionFilterTutorial() {
  const { filterTutorialCompleted, markFilterTutorialAsCompleted, isLoading } = useUserPreferences();
  const [currentStep, setCurrentStep] = useState(0);
  const [showTutorial, setShowTutorial] = useState(false);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);

  const tutorialSteps: TutorialStep[] = [
    {
      title: "🎯 Filtros de Questões",
      description: "Bem-vindo aos filtros inteligentes! Aqui você pode personalizar completamente sua experiência de estudos. Vamos explorar cada seção para você dominar a plataforma.",
      icon: Sparkles,
      color: "from-blue-500 to-indigo-600",
      badge: "INÍCIO"
    },
    {
      title: "🧠 Especialidades → Temas → Focos",
      description: "Esta é a seção mais importante! Aqui você navega pela hierarquia: ESPECIALIDADE → TEMA → FOCO. Clique para expandir e explore os níveis. Esta organização ajuda você a estudar de forma direcionada e específica.",
      targetSelector: '[data-tutorial="specialty-section"]',
      icon: Brain,
      color: "from-yellow-500 to-orange-600",
      badge: "HIERÁRQUICO"
    },
    {
      title: "🏥 Instituições",
      description: "Filtre questões por instituições específicas como UNIFESP, USP, UFMG e outras. Ideal para focar em bancas que você mais estuda ou que caem na sua prova.",
      targetSelector: '[data-tutorial="institution-section"]',
      icon: Building2,
      color: "from-blue-500 to-cyan-600"
    },
    {
      title: "📅 Anos das Provas",
      description: "Selecione anos específicos das provas. Questões mais recentes podem refletir tendências atuais, enquanto questões antigas testam conceitos consolidados.",
      targetSelector: '[data-tutorial="year-section"]',
      icon: Calendar,
      color: "from-green-500 to-emerald-600"
    },
    {
      title: "🎯 Formato de Questão",
      description: "Filtre por formato específico: questões discursivas, objetivas, com imagens, casos clínicos complexos. Personalize conforme seu estilo de estudo.",
      targetSelector: '[data-tutorial="question-format-section"]',
      icon: FileText,
      color: "from-pink-500 to-rose-600"
    },
    {
      title: "🎲 Opções de Estudo",
      description: "Após filtrar, você pode: estudar questões FILTRADAS (seguindo seus critérios) ou um MIX ALEATÓRIO (variado para testar conhecimentos gerais). Ambas são estratégias válidas!",
      targetSelector: '[data-tutorial="study-options"]',
      icon: CheckCircle,
      color: "from-indigo-500 to-purple-600",
      badge: "FINAL"
    }
  ];

  // Verificar se tutorial foi completado usando hook
  useEffect(() => {
    if (isLoading) return;

    // Se não completou o tutorial, aguardar 3 segundos e mostrar
    if (!filterTutorialCompleted) {
      const timer = setTimeout(() => {
        setShowTutorial(true);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [filterTutorialCompleted, isLoading]);

  // Função para expandir seções automaticamente
  const expandSection = useCallback((selector: string) => {
    const element = document.querySelector(selector);
    if (element) {
      // Procurar por botão de accordion dentro da seção
      const accordionButton = element.querySelector('button[data-state]');
      if (accordionButton) {
        const isExpanded = accordionButton.getAttribute('data-state') === 'open';

        if (!isExpanded) {
          (accordionButton as HTMLElement).click();
        }
      }
    }
  }, []);



  // Atualizar posição quando step muda
  useEffect(() => {
    if (!showTutorial) return;

    if (currentStep === 0) {
      // Primeiro passo não tem elemento alvo
      setTargetRect(null);
      return;
    }

    // Buscar elemento alvo para outros passos
    const step = tutorialSteps[currentStep];
    if (!step.targetSelector) {
      setTargetRect(null);
      return;
    }

    const element = document.querySelector(step.targetSelector);
    if (!element) {
      setTargetRect(null);
      return;
    }

    const rect = element.getBoundingClientRect();
    if (rect.width > 0 && rect.height > 0) {
      setTargetRect(rect);
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    } else {
      expandSection(step.targetSelector);
      setTimeout(() => {
        const newRect = element.getBoundingClientRect();
        if (newRect.width > 0 && newRect.height > 0) {
          setTargetRect(newRect);
        }
      }, 300);
    }
  }, [currentStep, showTutorial, expandSection]);

  const handleNext = async () => {
    if (currentStep < tutorialSteps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
    } else {
      // Tutorial concluído
      try {
        await markFilterTutorialAsCompleted();
        setShowTutorial(false);
      } catch (error) {
        console.error("Error marking tutorial as completed:", error);
      }
    }
  };

  const handleSkip = async () => {
    try {
      await markFilterTutorialAsCompleted();
      setShowTutorial(false);
    } catch (error) {
      console.error("Error marking tutorial as completed:", error);
    }
  };

  if (isLoading || !showTutorial || filterTutorialCompleted) {
    return null;
  }

  const currentStepData = tutorialSteps[currentStep];

  // Calcular posição do tutorial próximo ao elemento
  const getTutorialPosition = () => {
    if (!targetRect) {
      // Se não há elemento alvo, centralizar
      return {
        position: 'fixed' as const,
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10000
      };
    }

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const cardWidth = 400;
    const cardHeight = 350;
    const margin = 20;

    let position = {
      position: 'fixed' as const,
      zIndex: 10000,
      width: cardWidth
    };

    // Tentar posicionar à direita do elemento
    if (targetRect.right + cardWidth + margin < viewportWidth) {
      return {
        ...position,
        top: Math.max(margin, Math.min(targetRect.top, viewportHeight - cardHeight - margin)),
        left: targetRect.right + margin
      };
    }

    // Tentar posicionar à esquerda do elemento
    if (targetRect.left - cardWidth - margin > 0) {
      return {
        ...position,
        top: Math.max(margin, Math.min(targetRect.top, viewportHeight - cardHeight - margin)),
        left: targetRect.left - cardWidth - margin
      };
    }

    // Tentar posicionar abaixo do elemento
    if (targetRect.bottom + cardHeight + margin < viewportHeight) {
      return {
        ...position,
        top: targetRect.bottom + margin,
        left: Math.max(margin, Math.min(targetRect.left, viewportWidth - cardWidth - margin))
      };
    }

    // Tentar posicionar acima do elemento
    if (targetRect.top - cardHeight - margin > 0) {
      return {
        ...position,
        top: targetRect.top - cardHeight - margin,
        left: Math.max(margin, Math.min(targetRect.left, viewportWidth - cardWidth - margin))
      };
    }

    // Fallback: centralizar
    return {
      position: 'fixed' as const,
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 10000
    };
  };

  const tutorialPosition = getTutorialPosition();

  return (
    <>
      {/* Overlay escuro com recorte para o elemento */}
      <div className="fixed inset-0 z-[9998]">
        {/* Overlay principal */}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />

        {/* Recorte para destacar o elemento */}
        {targetRect && (
          <div
            className="absolute bg-transparent"
            style={{
              top: targetRect.top - 8,
              left: targetRect.left - 8,
              width: targetRect.width + 16,
              height: targetRect.height + 16,
              boxShadow: `0 0 0 9999px rgba(0, 0, 0, 0.6)`,
              borderRadius: '12px'
            }}
          />
        )}
      </div>

      {/* Destaque do elemento alvo */}
      {targetRect && (
        <div
          className="fixed z-[9999] pointer-events-none"
          style={{
            top: targetRect.top - 4,
            left: targetRect.left - 4,
            width: targetRect.width + 8,
            height: targetRect.height + 8,
          }}
        >
          {/* Borda destacada */}
          <div className="absolute inset-0 border-4 border-yellow-400 rounded-xl shadow-2xl animate-pulse" />
        </div>
      )}

      {/* Card do tutorial - posicionado próximo ao elemento */}
      <div style={tutorialPosition}>
        <Card className="w-full bg-white rounded-2xl shadow-2xl overflow-hidden border-2 border-gray-200">
          {/* Header */}
          <div className={`bg-gradient-to-r ${tutorialSteps[currentStep].color} p-6 text-white`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {tutorialSteps[currentStep].icon && (
                  <div className="bg-white/20 p-2 rounded-lg">
                    {React.createElement(tutorialSteps[currentStep].icon!, { className: "h-6 w-6" })}
                  </div>
                )}
                <div>
                  <h3 className="text-xl font-bold">{tutorialSteps[currentStep].title}</h3>
                  {tutorialSteps[currentStep].badge && (
                    <Badge className="bg-white/20 text-white text-xs mt-1">
                      {tutorialSteps[currentStep].badge}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="bg-white/20 px-3 py-1 rounded-full">
                <span className="text-sm font-medium">
                  {currentStep + 1}/{tutorialSteps.length}
                </span>
              </div>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-6">
            <p className="text-gray-700 leading-relaxed mb-6">
              {tutorialSteps[currentStep].description}
            </p>

            {/* Barra de progresso */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
              <div
                className={`bg-gradient-to-r ${tutorialSteps[currentStep].color} h-2 rounded-full transition-all duration-500`}
                style={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
              />
            </div>

            {/* Botões */}
            <div className="flex justify-between items-center">
              <Button
                variant="ghost"
                onClick={handleSkip}
                className="text-gray-500 hover:text-gray-700"
              >
                Pular Tutorial
              </Button>

              <Button
                onClick={handleNext}
                className={`bg-gradient-to-r ${tutorialSteps[currentStep].color} hover:opacity-90 text-white font-medium px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2`}
              >
                {currentStep < tutorialSteps.length - 1 ? (
                  <>
                    Próximo
                    <ArrowRight className="h-4 w-4" />
                  </>
                ) : (
                  <>
                    Entendi!
                    <CheckCircle className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
}
