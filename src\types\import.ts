export interface ImportQuestion {
  statement_text?: string;
  statement?: string;
  question_content?: string;
  alternatives?: string[];
  response_choices?: string[];
  correct_answer?: string | number;
  correct_choice?: string | number;
  specialty: string;
  theme: string;
  focus: string;
  location?: string;
  year?: number;
  exam_year?: number;
  institution_id?: string;
  topics?: string[];
  content_tags?: string[];
  answer_type?: 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO';
  question_format?: 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO';
}

export interface ImportResults {
  success: number;
  errors: string[];
  created: {
    specialties: Map<string, CategoryResult>;
    themes: Map<string, CategoryResult>;
    focuses: Map<string, CategoryResult>;
    locations: Map<string, LocationResult>;
    years: Set<number>;
  };
}

export interface CategoryResult {
  id: string;
  name: string;
  type: string;
}

export interface LocationResult {
  id: string;
  name: string;
}
