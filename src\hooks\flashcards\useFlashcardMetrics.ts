import { FSRSMetrics } from "@/utils/fsrs/types";
import { calculateFSRSMetrics } from "@/utils/fsrs/calculator";
import { FlashcardResponse } from "@/types/flashcard";

export const useFlashcardMetrics = () => {
  const calculateNewMetrics = (
    currentMetrics: FSRSMetrics,
    response: FlashcardResponse
  ) => {
    const newMetrics = calculateFSRSMetrics(currentMetrics, response);
    return newMetrics;
  };

  return { calculateNewMetrics };
};