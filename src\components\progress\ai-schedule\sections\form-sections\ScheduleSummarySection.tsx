
import React from "react";
import { Info } from "lucide-react";
import { calculateTotalHours } from "../../utils/calculations";
import type { DayConfig, AIScheduleFormData } from "../../types";
import { UseFormReturn } from "react-hook-form";

interface ScheduleSummaryProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const ScheduleSummarySection = ({ form }: ScheduleSummaryProps) => {
  const availableDays = form.watch('availableDays');
  const scheduleOption = form.watch('scheduleOption');
  const weeksCount = form.watch('weeksCount');
  const topicDuration = form.watch('topicDuration');
  const domain = form.watch('domain');
  const totalHours = calculateTotalHours(availableDays);
  
  return (
    <div className="p-6 space-y-3 border-2 border-slate-200 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md">
      <div className="flex items-center gap-2 mb-2">
        <div className="p-2 rounded-full bg-blue-100">
          <Info className="w-5 h-5 text-blue-600" />
        </div>
        <h3 className="text-lg font-bold text-blue-800">Resumo do cronograma</h3>
      </div>
      
      <div className="flex items-center justify-between text-sm">
        <span className="text-slate-600 font-medium">Total de estudo:</span>
        <span className="font-bold text-blue-700">{totalHours.toFixed(1)} horas por semana</span>
      </div>
      
      <div className="flex items-center justify-between text-sm">
        <span className="text-slate-600 font-medium">
          Horas em {scheduleOption === "new" ? weeksCount : 1} semana(s):
        </span>
        <span className="font-bold text-blue-700">
          {(totalHours * (scheduleOption === "new" ? weeksCount : 1)).toFixed(1)} horas
        </span>
      </div>
      
      <div className="flex items-center justify-between text-sm">
        <span className="text-slate-600 font-medium">Duração dos temas:</span>
        <span className="font-bold text-blue-700">{topicDuration} minutos</span>
      </div>
      
      <div className="flex items-center justify-between text-sm">
        <span className="text-slate-600 font-medium">Temas por semana:</span>
        <span className="font-bold text-blue-700">
          ~{Math.round(totalHours * 60 / parseInt(topicDuration))}
        </span>
      </div>
      
      {domain && (
        <div className="flex items-center justify-between pt-3 mt-3 text-sm border-t-2 border-blue-100">
          <span className="text-slate-600 font-medium">Domínio:</span>
          <span className="font-bold text-blue-700">{domain}</span>
        </div>
      )}
    </div>
  );
};
