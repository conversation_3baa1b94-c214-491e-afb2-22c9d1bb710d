import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';

export const useDynamicFilters = (selectedFilters: SelectedFilters) => {
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [availableYears, setAvailableYears] = useState<string[]>([]);
  const [totalQuestions, setTotalQuestions] = useState(0);

  useEffect(() => {
    const updateAvailableOptions = async () => {
      console.log('🔄 Updating available options with filters:', selectedFilters);

      // First, get all locations and years without limit
      const [{ data: allLocations }, { data: allYearsData }] = await Promise.all([
        supabase.from('exam_locations').select('id'),
        supabase.from('questions').select('exam_year').order('exam_year').limit(100000)
      ]);

      // Extract unique years and ensure they're strings
      const uniqueYears = allYearsData
        ? [...new Set(allYearsData.filter(q => q.exam_year).map(q => q.exam_year.toString()))]
        : [];

      // Build filtered query without limit
      let query = supabase
        .from('questions')
        .select('exam_location, exam_year, id')
        .limit(100000);

      // Create OR conditions for specialties, themes, focuses and institutions
      const conditions = [];

      if (selectedFilters.specialties?.length > 0) {
        const validIds = selectedFilters.specialties.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`specialty_id.in.(${validIds.join(',')})`);
        }
      }
      if (selectedFilters.themes?.length > 0) {
        const validIds = selectedFilters.themes.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`theme_id.in.(${validIds.join(',')})`);
        }
      }
      if (selectedFilters.focuses?.length > 0) {
        const validIds = selectedFilters.focuses.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`focus_id.in.(${validIds.join(',')})`);
        }
      }

      // Apply OR conditions if they exist
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      }

      const { data: questions, error } = await query;

      if (error) {
        return;
      }

      // Update available filters data
      setAvailableLocations(allLocations?.map(loc => loc.id).filter(Boolean) || []);
      setAvailableYears(uniqueYears);
      setTotalQuestions(questions?.length || 0);
    };

    updateAvailableOptions();
  }, [selectedFilters]);

  return {
    availableLocations,
    availableYears,
    totalQuestions
  };
};