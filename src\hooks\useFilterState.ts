import { useState, useCallback } from 'react';
import { SelectedFilters, FilterOption, filterTypeToKey } from '@/components/filters/types';

const getEmptyFilters = (): SelectedFilters => ({
  specialties: [],
  themes: [],
  focuses: [],
  locations: [],
  years: [],
  question_types: [],
  question_formats: [],
  excludeAnswered: false
});

export const useFilterState = () => {
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>(getEmptyFilters());
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleToggleFilter = useCallback((id: string, type: FilterOption['type']) => {
    const filterKey = filterTypeToKey(type);

    setSelectedFilters(prev => {
      const currentFilters = prev[filterKey] || [];
      const isSelected = currentFilters.includes(id);

      const newFilters = isSelected
        ? currentFilters.filter(filterId => filterId !== id)
        : [...currentFilters, id];

      return {
        ...prev,
        [filterKey]: newFilters
      };
    });
  }, []);

  const toggleExpand = useCallback((id: string) => {
    setExpandedItems(prev => {
      const isExpanded = prev.includes(id);
      return isExpanded
        ? prev.filter(item => item !== id)
        : [...prev, id];
    });
  }, []);

  return {
    selectedFilters,
    setSelectedFilters,
    expandedItems,
    handleToggleFilter,
    toggleExpand
  };
};
