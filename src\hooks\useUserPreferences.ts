import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface UserPreferences {
  id: string;
  user_id: string;
  welcome_dialog_shown: boolean;
  tutorial_completed: boolean;
  filter_tutorial_completed: boolean;
  question_filter_tutorial_completed: boolean;
  cinematic_viewed: boolean;
  target_specialty: string | null;
  study_months: number | null;
  preferences_completed: boolean;
  target_institutions_unknown: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Hook consolidado para gerenciar preferências do usuário
 * Substitui múltiplas queries separadas por uma única query otimizada
 */
export const useUserPreferences = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Identificador único para esta instância do hook
  const hookId = React.useRef(Math.random().toString(36).substr(2, 9)).current;

  // Log essencial do estado de autenticação
  if (!user?.id) {
    console.log(`🔍 [useUserPreferences-${hookId}] No authenticated user`);
  }

  // Query consolidada para todas as preferências
  const {
    data: preferences,
    isLoading,
    error
  } = useQuery({
    queryKey: ['user-preferences', user?.id],
    queryFn: async (): Promise<UserPreferences | null> => {
      if (!user?.id) {
        return null;
      }

      try {
        const queryResult = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

        const { data, error } = queryResult;

        if (error) {
          throw error;
        }



      // Se não existe, criar com valores padrão
      if (!data) {
        const newPrefsData = {
          user_id: user.id,
          welcome_dialog_shown: false,
          tutorial_completed: false,
          filter_tutorial_completed: false,
          question_filter_tutorial_completed: false,
          cinematic_viewed: false,
          target_specialty: null,
          study_months: null,
          preferences_completed: false,
          target_institutions_unknown: false
        };

        const upsertResult = await supabase
          .from('user_preferences')
          .upsert(newPrefsData, {
            onConflict: 'user_id'
          })
          .select('*')
          .single();

        if (upsertResult.error) {
          throw upsertResult.error;
        }

        return upsertResult.data;
      }

      return data;
    } catch (error) {
      throw error;
    }
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutos - mais agressivo para dados críticos
    cacheTime: 5 * 60 * 1000, // 5 minutos - cache mais curto
    refetchOnWindowFocus: true,
    refetchOnMount: true, // ✅ Sempre buscar dados frescos na montagem
    refetchOnReconnect: true,
    keepPreviousData: false, // ✅ Não manter dados antigos
    retry: 2
  });



  // Mutation para atualizar preferências
  const updatePreferences = useMutation({
    mutationFn: async (updates: Partial<UserPreferences>) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select('*')
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      // Atualizar cache imediatamente com dados completos
      queryClient.setQueryData(['user-preferences', user?.id], data);

      // Invalidar cache para forçar refetch em outros componentes
      queryClient.invalidateQueries({ queryKey: ['user-preferences'] });
      queryClient.invalidateQueries({ queryKey: ['user-preferences-completed'] });
      queryClient.invalidateQueries({ queryKey: ['study-preferences-data'] });
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });
    }
  });

  // Funções de conveniência
  const markWelcomeAsSeen = () => {
    return updatePreferences.mutateAsync({ welcome_dialog_shown: true });
  };

  const markTutorialAsCompleted = () => {
    return updatePreferences.mutateAsync({ tutorial_completed: true });
  };

  const markFilterTutorialAsCompleted = () => {
    return updatePreferences.mutateAsync({ filter_tutorial_completed: true });
  };

  const resetWelcomeDialog = () => {
    return updatePreferences.mutateAsync({ welcome_dialog_shown: false });
  };











  // Sistema de fallback ultra-robusto com validação completa
  const createSafePreferences = () => {
    const defaults = {
      welcome_dialog_shown: false,
      tutorial_completed: false,
      filter_tutorial_completed: false,
      question_filter_tutorial_completed: false,
      cinematic_viewed: false,
      target_specialty: null,
      study_months: null,
      preferences_completed: false,
      target_institutions_unknown: false
    };

    if (!preferences) {
      return defaults;
    }

    // Validar cada campo individualmente
    const safePrefs = { ...defaults };

    Object.keys(defaults).forEach(key => {
      const value = preferences[key as keyof UserPreferences];

      if (value !== undefined && value !== null) {
        safePrefs[key as keyof typeof defaults] = value as any;
      }
    });

    return safePrefs;
  };

  const safePreferences = createSafePreferences();

  return {
    preferences: preferences || null,
    isLoading,
    error,

    // Estados específicos - usar fallback seguro
    showWelcomeDialog: !safePreferences.welcome_dialog_shown,
    tutorialCompleted: safePreferences.tutorial_completed,
    filterTutorialCompleted: safePreferences.filter_tutorial_completed,

    // Mutations
    updatePreferences: updatePreferences.mutateAsync,
    markWelcomeAsSeen,
    markTutorialAsCompleted,
    markFilterTutorialAsCompleted,

    // Status das mutations
    isUpdating: updatePreferences.isPending
  };
};
