
/**
 * Helper functions for consistent date handling with Brazil timezone (UTC-3)
 */

// Get current date in Brazil timezone - Versão simplificada e confiável
export const getCurrentBrazilDate = (): Date => {
  // Abordagem mais direta: usar apenas a data local do Brasil
  const now = new Date();

  // Obter a data no timezone do Brasil usando Intl.DateTimeFormat
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });

  const brazilDateString = formatter.format(now); // "2025-06-14"

  // Para comparação de semanas, só precisamos da data, não do horário
  // Criar uma data simples com horário zerado
  const [year, month, day] = brazilDateString.split('-').map(Number);
  const brazilDate = new Date(year, month - 1, day, 12, 0, 0, 0); // Meio-dia para evitar problemas de timezone

  return brazilDate;
};

// Format a date to the Brazilian format with proper timezone
export const formatBrazilDate = (
  date: Date | string,
  options: Intl.DateTimeFormatOptions = { day: '2-digit', month: '2-digit', year: 'numeric' },
  locale = 'pt-BR'
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const formatterOptions = {
    ...options,
    timeZone: 'America/Sao_Paulo'
  };

  const formatted = new Intl.DateTimeFormat(locale, formatterOptions).format(dateObj);

  return formatted;
};

// Find week bounds (Sunday to Saturday) for a given date
export const getWeekBounds = (date: Date): { startDate: Date, endDate: Date } => {
  // Create a copy of the date to avoid modifying the original
  const currentDate = new Date(date);

  // Ensure date has time set to midnight for consistent calculations
  currentDate.setHours(0, 0, 0, 0);

  // Get the day of week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = currentDate.getDay();

  // Calculate start date (Sunday of current week)
  const startDate = new Date(currentDate);
  startDate.setDate(currentDate.getDate() - dayOfWeek);

  // Calculate end date (Saturday of current week = Sunday + 6 days)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  return { startDate, endDate };
};

// Compare dates (ignoring time) to check if a date is within a range
export const isDateInRange = (
  dateToCheck: Date | string,
  startDate: Date | string,
  endDate: Date | string
): boolean => {
  // Convert to date objects if strings
  const checkDate = typeof dateToCheck === 'string' ? new Date(dateToCheck) : dateToCheck;
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  // Normalize all dates to YYYY-MM-DD format for comparison
  const checkStr = checkDate.toISOString().split('T')[0];
  const startStr = start.toISOString().split('T')[0];
  const endStr = end.toISOString().split('T')[0];

  const isInRange = checkStr >= startStr && checkStr <= endStr;

  return isInRange;
};

// Add days to a date
export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);

  return result;
};

// Get the week number of a date
export const getWeekNumber = (date: Date): number => {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
  const weekNum = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);

  return weekNum;
};

// Normalize day name for consistent comparison
export const normalizeDayName = (dayName: string): string => {
  const normalized = dayName.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  return normalized;
};

// Get the day of the week in Portuguese
export const getDayOfWeekPt = (date: Date): string => {
  const daysOfWeek = [
    "Domingo", "Segunda-feira", "Terça-feira", "Quarta-feira",
    "Quinta-feira", "Sexta-feira", "Sábado"
  ];

  const dayIndex = date.getDay();
  const dayName = daysOfWeek[dayIndex];

  return dayName;
};

// Get current time in Brazil for debugging purposes
export const logCurrentBrazilTime = (): void => {
  // Function available for debugging if needed
};

// Force the correct week boundaries (Sunday to Saturday)
export const forceWeekBoundaries = (weekStartDate: string, weekEndDate: string): { correctedStartDate: string, correctedEndDate: string } => {


  // Parse the dates - ensure we're using UTC to avoid timezone issues
  const startDate = new Date(weekStartDate + 'T12:00:00Z');
  const endDate = new Date(weekEndDate + 'T12:00:00Z');

  // Get day of week (0 = Sunday, 6 = Saturday)
  const startDay = startDate.getUTCDay();
  const endDay = endDate.getUTCDay();



  // Correct the start date to be Sunday
  let correctedStartDate = new Date(startDate);
  if (startDay !== 0) { // Not Sunday
    // Move back to the previous Sunday
    correctedStartDate.setUTCDate(startDate.getUTCDate() - startDay);
  }

  // Correct the end date to be Saturday
  let correctedEndDate = new Date(correctedStartDate);
  correctedEndDate.setUTCDate(correctedStartDate.getUTCDate() + 6); // Saturday is 6 days after Sunday

  const result = {
    correctedStartDate: correctedStartDate.toISOString().split('T')[0],
    correctedEndDate: correctedEndDate.toISOString().split('T')[0]
  };



  return result;
};
