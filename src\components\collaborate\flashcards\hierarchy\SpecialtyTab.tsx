import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Specialty {
  id: string;
  name: string;
}

export const SpecialtyTab = () => {
  const [specialties, setSpecialties] = useState<Specialty[]>([]);
  const [newSpecialty, setNewSpecialty] = useState("");

  useEffect(() => {
    fetchSpecialties();
  }, []);

  const fetchSpecialties = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_specialty")
      .select("id, name")
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar especialidades");
      return;
    }

    setSpecialties(data || []);
  };

  const handleCreateSpecialty = async () => {
    if (!newSpecialty.trim()) {
      toast.error("O nome da especialidade é obrigatório");
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from("flashcards_specialty")
      .insert([{
        name: newSpecialty,
        user_id: user.id
      }]);

    if (error) {
      toast.error("Erro ao criar especialidade");
      return;
    }

    toast.success("Especialidade criada com sucesso!");
    setNewSpecialty("");
    fetchSpecialties();
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Input
          placeholder="Nome da nova categoria"
          value={newSpecialty}
          onChange={(e) => setNewSpecialty(e.target.value)}
        />
        <Button onClick={handleCreateSpecialty}>
          Adicionar
        </Button>
      </div>

      <div className="grid gap-2">
        {specialties.map((specialty) => (
          <div
            key={specialty.id}
            className="p-4 rounded-lg border bg-card text-card-foreground"
          >
            {specialty.name}
          </div>
        ))}
      </div>
    </div>
  );
};