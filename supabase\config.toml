
# A string used to distinguish different Supabase projects on the same host. Defaults to the project ID.
project_id = "bxedpdmgvgatjdfxgxij"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = false

[studio]
enabled = true
port = 54323
api_url = "http://localhost"

[storage]
enabled = true
file_size_limit = "50MiB"

[auth]
enabled = true
site_url = "https://medevo.com.br"
additional_redirect_urls = ["https://medevo.com.br", "http://localhost:5173", "https://localhost:5173", "http://localhost:800", "https://localhost:800"]
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10
enable_signup = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = true
enable_confirmations = true

[inbucket]
enabled = true
port = 54324

[vector]
enabled = false

[functions]
[functions.question-commentary]
verify_jwt = false
[functions.discursive-ai-analysis]
verify_jwt = false
[functions.flashcards-ai-generate]
verify_jwt = false

[analytics]
enabled = false
port = 54327
