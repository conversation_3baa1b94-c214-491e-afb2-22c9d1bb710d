
import type { DayConfig } from "../types";

export const calculateTotalHours = (availableDays: { [key: string]: DayConfig } | undefined): number => {
  if (!availableDays) return 0;

  let totalMinutes = 0;

  Object.entries(availableDays).forEach(([_, config]) => {
    // Verificar se config existe e tem as propriedades necessárias
    if (!config || typeof config !== 'object') return;
    if (!config.enabled) return;
    if (!config.periods || !Array.isArray(config.periods) || config.periods.length === 0) return;

    const validPeriods = config.periods.filter(p => p && p.startTime && p.endTime);
    if (validPeriods.length > 0) {
      validPeriods.forEach(period => {
        if (period.startTime && period.endTime) {
          try {
            const start = new Date(`2000-01-01T${period.startTime}`);
            const end = new Date(`2000-01-01T${period.endTime}`);
            const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
            if (!isNaN(diffMinutes) && diffMinutes > 0) {
              totalMinutes += diffMinutes;
            }
          } catch (error) {
            console.warn('Erro ao calcular período:', period, error);
          }
        }
      });
    }
  });

  return Math.round(totalMinutes / 60 * 10) / 10; // Round to 1 decimal place
};
