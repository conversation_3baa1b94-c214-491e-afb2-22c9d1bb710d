import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const formSchema = z.object({
  title: z.string().min(3, {
    message: "O título deve ter pelo menos 3 caracteres.",
  }),
  description: z.string().optional(),
  specialty_id: z.string().min(1, {
    message: "Selecione uma especialidade.",
  }),
  theme_id: z.string().optional(),
  focus_id: z.string().optional(),
  extrafocus_id: z.string().optional(),
});

export function TopicEditDialog({ 
  open, 
  onOpenChange, 
  topic, 
  onSave 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  topic?: any; 
  onSave: (topic: any) => void; 
}) {
  const [specialties, setSpecialties] = useState<any[]>([]);
  const [themes, setThemes] = useState<any[]>([]);
  const [focuses, setFocuses] = useState<any[]>([]);
  const [extraFocuses, setExtraFocuses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: topic?.title || "",
      description: topic?.description || "",
      specialty_id: topic?.specialty_id || "",
      theme_id: topic?.theme_id || "",
      focus_id: topic?.focus_id || "",
      extrafocus_id: topic?.extrafocus_id || "",
    },
  });

  useEffect(() => {
    if (open) {
      loadSpecialties();
      
      if (topic) {
        form.reset({
          title: topic.title || "",
          description: topic.description || "",
          specialty_id: topic.specialty_id || "",
          theme_id: topic.theme_id || "",
          focus_id: topic.focus_id || "",
          extrafocus_id: topic.extrafocus_id || "",
        });
        
        if (topic.specialty_id) {
          loadThemes(topic.specialty_id);
        }
        
        if (topic.theme_id) {
          loadFocuses(topic.theme_id);
        }
        
        if (topic.focus_id) {
          loadExtraFocuses(topic.focus_id);
        }
      }
    }
  }, [open, topic, form]);

  const loadSpecialties = async () => {
    try {
      const { data, error } = await supabase
        .from('specialties')
        .select('*')
        .order('name');
      
      if (error) throw error;
      setSpecialties(data || []);
    } catch (error: any) {
      toast.error(`Erro ao carregar especialidades: ${error.message}`);
    }
  };

  const loadThemes = async (specialtyId: string) => {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select('*')
        .eq('specialty_id', specialtyId)
        .order('name');
      
      if (error) throw error;
      setThemes(data || []);
    } catch (error: any) {
      toast.error(`Erro ao carregar temas: ${error.message}`);
    }
  };

  const loadFocuses = async (themeId: string) => {
    try {
      const { data, error } = await supabase
        .from('focuses')
        .select('*')
        .eq('theme_id', themeId)
        .order('name');
      
      if (error) throw error;
      setFocuses(data || []);
    } catch (error: any) {
      toast.error(`Erro ao carregar focos: ${error.message}`);
    }
  };

  const loadExtraFocuses = async (focusId: string) => {
    try {
      const { data, error } = await supabase
        .from('extrafocuses')
        .select('*')
        .eq('focus_id', focusId)
        .order('name');
      
      if (error) throw error;
      setExtraFocuses(data || []);
    } catch (error: any) {
      toast.error(`Erro ao carregar subfocos: ${error.message}`);
    }
  };

  const handleSpecialtyChange = (value: string) => {
    form.setValue('specialty_id', value);
    form.setValue('theme_id', '');
    form.setValue('focus_id', '');
    form.setValue('extrafocus_id', '');
    setThemes([]);
    setFocuses([]);
    setExtraFocuses([]);
    loadThemes(value);
  };

  const handleThemeChange = (value: string) => {
    form.setValue('theme_id', value);
    form.setValue('focus_id', '');
    form.setValue('extrafocus_id', '');
    setFocuses([]);
    setExtraFocuses([]);
    loadFocuses(value);
  };

  const handleFocusChange = (value: string) => {
    form.setValue('focus_id', value);
    form.setValue('extrafocus_id', '');
    setExtraFocuses([]);
    loadExtraFocuses(value);
  };

  const handleSubmitForm = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true);
      
      const updatedTopic = {
        ...topic,
        ...values,
      };
      
      onSave(updatedTopic);
      onOpenChange(false);
    } catch (error: any) {
      toast.error(`Erro ao salvar tópico: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{topic ? 'Editar Tópico' : 'Novo Tópico'}</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmitForm)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título</FormLabel>
                  <FormControl>
                    <Input placeholder="Título do tópico" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Descrição do tópico (opcional)" 
                      className="resize-none" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="specialty_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Especialidade</FormLabel>
                  <Select 
                    onValueChange={(value) => handleSpecialtyChange(value)} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma especialidade" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {specialties.map((specialty) => (
                        <SelectItem key={specialty.id} value={specialty.id}>
                          {specialty.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {themes.length > 0 && (
              <FormField
                control={form.control}
                name="theme_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tema</FormLabel>
                    <Select 
                      onValueChange={(value) => handleThemeChange(value)} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um tema" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {themes.map((theme) => (
                          <SelectItem key={theme.id} value={theme.id}>
                            {theme.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            {focuses.length > 0 && (
              <FormField
                control={form.control}
                name="focus_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Foco</FormLabel>
                    <Select 
                      onValueChange={(value) => handleFocusChange(value)} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um foco" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {focuses.map((focus) => (
                          <SelectItem key={focus.id} value={focus.id}>
                            {focus.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            {extraFocuses.length > 0 && (
              <FormField
                control={form.control}
                name="extrafocus_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subfoco</FormLabel>
                    <Select 
                      onValueChange={(value) => form.setValue('extrafocus_id', value)} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um subfoco" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {extraFocuses.map((extrafocus) => (
                          <SelectItem key={extrafocus.id} value={extrafocus.id}>
                            {extrafocus.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
