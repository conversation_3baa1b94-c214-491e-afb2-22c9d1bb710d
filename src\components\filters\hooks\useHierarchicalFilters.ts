import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { SelectedFilters } from '@/types/question';

export const useHierarchicalFilters = (
  selectedFilters: SelectedFilters,
  setSelectedFilters: (filters: SelectedFilters) => void
) => {
  const { toast } = useToast();
  const [questionCounts, setQuestionCounts] = useState<{[key: string]: number}>({});
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const calculateHierarchicalCount = useCallback(async (categoryId: string) => {
    try {
      const { data: questions, error } = await supabase
        .from('questions')
        .select('id, specialty_id, theme_id, focus_id')
        .or(`specialty_id.eq.${categoryId},theme_id.eq.${categoryId},focus_id.eq.${categoryId}`);

      if (error) throw error;

      const { data: themes } = await supabase
        .from('study_categories')
        .select('id')
        .eq('parent_id', categoryId);

      const themeIds = themes?.map(t => t.id) || [];
      const { data: focuses } = await supabase
        .from('study_categories')
        .select('id')
        .in('parent_id', themeIds);

      const allRelatedIds = [
        categoryId,
        ...(themes?.map(t => t.id) || []),
        ...(focuses?.map(f => f.id) || [])
      ];

      const totalCount = questions?.filter(q => 
        allRelatedIds.includes(q.specialty_id) ||
        allRelatedIds.includes(q.theme_id) ||
        allRelatedIds.includes(q.focus_id)
      ).length || 0;

      setQuestionCounts(prev => ({
        ...prev,
        [categoryId]: totalCount
      }));

      return totalCount;
    } catch (error: any) {
      toast({
        title: "Erro ao calcular contagem hierárquica",
        description: error.message,
        variant: "destructive"
      });
      return 0;
    }
  }, [toast]);

  const toggleExpand = useCallback((id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  }, []);

  const handleHierarchicalToggle = useCallback(async (id: string, type: "specialty" | "theme" | "focus") => {
    const filterType = `${type}s` as keyof SelectedFilters;
    const currentFilters = selectedFilters[filterType] || [];
    const isSelected = currentFilters.includes(id);

    try {
      let childIds: string[] = [];
      if (type === "specialty" || type === "theme") {
        const { data: children } = await supabase
          .from('study_categories')
          .select('id')
          .eq('parent_id', id);
        
        childIds = children?.map(c => c.id) || [];
      }

      const newFilters = {
        ...selectedFilters,
        [filterType]: isSelected
          ? currentFilters.filter(item => item !== id && !childIds.includes(item))
          : [...currentFilters, id, ...childIds]
      };

      setSelectedFilters(newFilters);
      await calculateHierarchicalCount(id);
    } catch (error: any) {
      toast({
        title: "Erro ao atualizar filtros",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [selectedFilters, setSelectedFilters, calculateHierarchicalCount, toast]);

  useEffect(() => {
    const loadInitialCounts = async () => {
      const allCategories = [
        ...(selectedFilters.specialties || []),
        ...(selectedFilters.themes || []),
        ...(selectedFilters.focuses || [])
      ];

      for (const categoryId of allCategories) {
        await calculateHierarchicalCount(categoryId);
      }
    };

    loadInitialCounts();
  }, [selectedFilters, calculateHierarchicalCount]);

  return {
    questionCounts,
    expandedItems,
    toggleExpand,
    handleHierarchicalToggle
  };
};