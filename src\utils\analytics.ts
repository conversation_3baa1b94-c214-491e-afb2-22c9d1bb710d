// =====================================================
// SISTEMA DE ANALYTICS PARA STUDYWISE
// =====================================================

import React from 'react';
import { getAnalyticsConfig, initializeAnalytics } from './analyticsConfig';

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

interface UserProperties {
  formation_area?: string;
  is_student?: boolean;
  subscription_type?: string;
  registration_date?: string;
}

class Analytics {
  private isInitialized = false;
  private userId: string | null = null;

  // Inicializar Google Analytics com configurações otimizadas
  init(measurementId?: string) {
    const config = getAnalyticsConfig();

    if (this.isInitialized || !config.isEnabled) {
      // Log removido para reduzir ruído no console
      return;
    }

    // Usar configuração otimizada
    initializeAnalytics();
    this.isInitialized = true;
  }

  // Configurar usuário
  setUser(userId: string, properties?: UserProperties) {
    this.userId = userId;

    if (this.isInitialized && (window as any).gtag) {
      (window as any).gtag('config', import.meta.env.VITE_GA_MEASUREMENT_ID, {
        user_id: userId,
        custom_map: {
          formation_area: 'formation_area',
          is_student: 'is_student',
        }
      });

      // Definir propriedades do usuário
      if (properties) {
        (window as any).gtag('set', 'user_properties', properties);
      }
    }


  }

  // Limpar usuário (logout)
  clearUser() {
    this.userId = null;

  }

  // Rastrear evento
  trackEvent(event: AnalyticsEvent) {

    // Enviar para Google Analytics
    if (this.isInitialized && (window as any).gtag) {
      (window as any).gtag('event', event.action, {
        event_category: event.category,
        event_label: event.label,
        value: event.value,
        user_id: this.userId,
        ...event.custom_parameters,
      });
    }

    // Enviar para Supabase (opcional - para analytics próprio)
    this.trackToSupabase(event);
  }

  // Rastrear página
  trackPageView(page_title: string, page_location: string) {

    if (this.isInitialized && (window as any).gtag) {
      (window as any).gtag('event', 'page_view', {
        page_title,
        page_location,
        user_id: this.userId,
      });
    }
  }

  // Rastrear conversão
  trackConversion(conversionName: string, value?: number) {
    this.trackEvent({
      action: 'conversion',
      category: 'business',
      label: conversionName,
      value,
    });
  }

  // Rastrear erro
  trackError(error: string, category: string = 'error') {
    this.trackEvent({
      action: 'exception',
      category,
      label: error,
      custom_parameters: {
        fatal: false,
      },
    });
  }

  // Enviar para Supabase (analytics próprio)
  private async trackToSupabase(event: AnalyticsEvent) {
    try {
      // Só em produção para não poluir dados de desenvolvimento
      if (!import.meta.env.PROD) return;

      const { supabase } = await import('@/integrations/supabase/client');

      await supabase.from('analytics_events').insert({
        user_id: this.userId,
        event_action: event.action,
        event_category: event.category,
        event_label: event.label,
        event_value: event.value,
        custom_parameters: event.custom_parameters,
        timestamp: new Date().toISOString(),
        session_id: this.getSessionId(),
        user_agent: navigator.userAgent,
        page_url: window.location.href,
      });
    } catch (error) {
      // Error silently handled
    }
  }

  // Gerar ID de sessão
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }
}

// Instância singleton
export const analytics = new Analytics();

// Inicializar automaticamente com configurações otimizadas
analytics.init();

// =====================================================
// EVENTOS ESPECÍFICOS DO STUDYWISE
// =====================================================

export const trackStudyEvent = {
  // Questões
  questionAnswered: (isCorrect: boolean, category: string, timeSpent: number) => {
    analytics.trackEvent({
      action: 'question_answered',
      category: 'study',
      label: category,
      value: timeSpent,
      custom_parameters: {
        is_correct: isCorrect,
        category,
      },
    });
  },

  // Sessões
  sessionStarted: (sessionType: string, questionCount: number) => {
    analytics.trackEvent({
      action: 'session_started',
      category: 'study',
      label: sessionType,
      value: questionCount,
    });
  },

  sessionCompleted: (sessionType: string, accuracy: number, duration: number) => {
    analytics.trackEvent({
      action: 'session_completed',
      category: 'study',
      label: sessionType,
      value: duration,
      custom_parameters: {
        accuracy,
        duration,
      },
    });
  },

  // Flashcards
  flashcardReviewed: (difficulty: string, isCorrect: boolean) => {
    analytics.trackEvent({
      action: 'flashcard_reviewed',
      category: 'flashcards',
      label: difficulty,
      custom_parameters: {
        is_correct: isCorrect,
      },
    });
  },

  // Navegação
  pageVisited: (pageName: string) => {
    analytics.trackPageView(pageName, window.location.href);
  },

  // Engajamento
  featureUsed: (featureName: string, context?: string) => {
    analytics.trackEvent({
      action: 'feature_used',
      category: 'engagement',
      label: featureName,
      custom_parameters: {
        context,
      },
    });
  },

  // Conversões
  userRegistered: (formationArea: string, isStudent: boolean) => {
    analytics.trackConversion('registration');
    analytics.trackEvent({
      action: 'user_registered',
      category: 'conversion',
      custom_parameters: {
        formation_area: formationArea,
        is_student: isStudent,
      },
    });
  },

  subscriptionStarted: (planType: string, value: number) => {
    analytics.trackConversion('subscription', value);
    analytics.trackEvent({
      action: 'subscription_started',
      category: 'conversion',
      label: planType,
      value,
    });
  },
};

// Hook para usar analytics em componentes React
export const useAnalytics = () => {
  return {
    trackEvent: analytics.trackEvent.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackConversion: analytics.trackConversion.bind(analytics),
    trackError: analytics.trackError.bind(analytics),
    setUser: analytics.setUser.bind(analytics),
    clearUser: analytics.clearUser.bind(analytics),
    ...trackStudyEvent,
  };
};

// HOC para rastrear visualizações de página automaticamente
export function withPageTracking<P extends object>(
  Component: React.ComponentType<P>,
  pageName: string
) {
  return function TrackedComponent(props: P) {
    React.useEffect(() => {
      trackStudyEvent.pageVisited(pageName);
    }, []);

    return React.createElement(Component, props);
  };
}
