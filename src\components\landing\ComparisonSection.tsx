
import React from "react";
import { motion } from "framer-motion";
import { Check, X } from "lucide-react";

export function ComparisonSection() {
  const comparisonData = [
    {
      title: "Planner Automático",
      withMethod: true,
      withoutMethod: false,
    },
    {
      title: "Revisões Espaçadas",
      withMethod: true,
      withoutMethod: false,
    },
    {
      title: "Diagnóstico de Pontos Fracos",
      withMethod: true,
      withoutMethod: false,
    },
    {
      title: "Banco de Questões Comentadas",
      withMethod: true,
      withoutMethod: false,
    },
    {
      title: "Estatísticas de Progresso",
      withMethod: true,
      withoutMethod: false,
    },
  ];

  return (
    <section className="relative py-20 px-4 bg-white overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-24 h-24 bg-primary/10 rounded-full -translate-x-12 -translate-y-1/2"></div>
      <div className="absolute top-1/4 right-0 w-40 h-40 bg-yellow-200/20 rounded-full translate-x-20"></div>
      <div className="absolute bottom-0 left-1/3 w-32 h-32 bg-green-200/30 rounded-full translate-y-16"></div>
      
      {/* Zigzag pattern (Duolingo-style) */}
      <div className="absolute top-10 left-0 right-0 h-8 bg-primary/5 skew-y-3"></div>
      <div className="absolute top-16 left-0 right-0 h-8 bg-yellow-100/20 -skew-y-3"></div>
      
      <div className="container mx-auto relative">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="inline-block mb-6">
            <span className="bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              Comparativo
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
            Compare e veja a diferença
          </h2>
          <p className="text-lg text-gray-600">
            Entenda por que nosso método é a escolha ideal para sua aprovação
          </p>
          
          <div className="flex justify-center mt-6 text-sm">
            <div className="inline-flex gap-2 px-4 py-2 bg-gray-50 rounded-full items-center">
              <span>Disponível para:</span>
              <span className="font-medium text-primary">Oftalmologia</span>
              <span className="mx-1">•</span>
              <span className="font-medium text-primary">Pediatria</span>
              <span className="mx-1">•</span>
              <span className="line-through text-gray-400">Dermatologia</span>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="bg-white rounded-xl shadow-lg overflow-hidden max-w-3xl mx-auto border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="grid grid-cols-3">
            {/* Header */}
            <div className="p-4 border-b bg-gray-50 flex items-center">
              <span className="font-semibold text-gray-500">Características</span>
            </div>
            <div className="p-4 border-b bg-gradient-to-r from-primary to-primary/80 text-white text-center relative">
              <span className="font-bold">Com Nosso Método</span>
              <div className="absolute -top-1 left-0 right-0 h-2 bg-yellow-400 rounded-t-lg"></div>
            </div>
            <div className="p-4 border-b bg-gray-200 text-center">
              <span className="font-semibold text-gray-700">Estudo Tradicional</span>
            </div>
            
            {/* Comparison rows */}
            {comparisonData.map((item, index) => (
              <React.Fragment key={index}>
                <div className={`p-4 ${index < comparisonData.length - 1 ? 'border-b' : ''} hover:bg-gray-50 transition-colors`}>
                  <span className="text-gray-700">{item.title}</span>
                </div>
                <div className={`p-4 text-center ${index < comparisonData.length - 1 ? 'border-b border-primary/20' : ''} bg-primary/5`}>
                  {item.withMethod ? (
                    <div className="flex justify-center">
                      <div className="bg-green-500 rounded-full p-1">
                        <Check className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-center">
                      <div className="bg-red-500 rounded-full p-1">
                        <X className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  )}
                </div>
                <div className={`p-4 text-center ${index < comparisonData.length - 1 ? 'border-b' : ''}`}>
                  {item.withoutMethod ? (
                    <div className="flex justify-center">
                      <div className="bg-green-500 rounded-full p-1">
                        <Check className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-center">
                      <div className="bg-red-500 rounded-full p-1">
                        <X className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  )}
                </div>
              </React.Fragment>
            ))}
          </div>
        </motion.div>
        
        <motion.div 
          className="flex flex-col items-center mt-10 relative"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {/* Progress bar inspired by Duolingo */}
          <div className="w-full max-w-md h-3 bg-gray-200 rounded-full mb-8 relative overflow-hidden">
            <div className="absolute top-0 left-0 h-full w-3/4 bg-gradient-to-r from-green-400 to-green-500 rounded-full"></div>
            <div className="absolute -top-1 left-3/4 transform -translate-x-1/2 w-6 h-6 bg-white rounded-full border-2 border-green-500 shadow-md"></div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center max-w-md">
            <p className="text-lg text-gray-700">
              Mais de <span className="font-bold text-primary">10.000 aprovações</span> comprovam a eficácia do nosso método
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
