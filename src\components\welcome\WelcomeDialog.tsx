import React from 'react';
import { Dialog, DialogPortal, DialogOverlay, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Target, Brain, BarChart3, Users, Lightbulb, ArrowRight } from 'lucide-react';
import { cn } from "@/lib/utils";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface WelcomeDialogProps {
  open: boolean;
  onContinue: () => void;
}

// Componente customizado de DialogContent sem botão de fechar
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-[10001] grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-black bg-white p-6 shadow-card-sm duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-xl max-h-[90vh] overflow-y-auto",
        className
      )}
      {...props}
    >
      {children}
      {/* Sem botão de fechar */}
    </DialogPrimitive.Content>
  </DialogPortal>
));
CustomDialogContent.displayName = "CustomDialogContent";

export const WelcomeDialog: React.FC<WelcomeDialogProps> = ({ open, onContinue }) => {
  return (
    <Dialog open={open} onOpenChange={() => {}} modal>
      <CustomDialogContent
        className="w-[80dvw] max-w-[600px] max-h-[80dvh] p-0 gap-0 overflow-hidden border-2 border-blue-500 rounded-xl flex flex-col"
        style={{ zIndex: 10001 }}
      >
        {/* Hidden title and description for accessibility */}
        <VisuallyHidden>
          <DialogTitle>Bem-vindo ao MedEvo</DialogTitle>
          <DialogDescription>
            Conheça a plataforma inteligente de estudos médicos e suas funcionalidades principais
          </DialogDescription>
        </VisuallyHidden>

        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 text-center">
          <div className="flex items-center justify-center gap-3 mb-2">
            <div className="bg-white rounded-lg p-2">
              <img
                src="/logomedevo.webp"
                alt="MedEvo Logo"
                className="h-8 w-auto"
              />
            </div>
            <h1 className="text-2xl font-bold">MedEvo</h1>
            <Badge className="bg-orange-500 text-white text-xs px-2 py-1">BETA</Badge>
          </div>
          <p className="text-blue-100 text-sm">
            Sua plataforma inteligente de estudos médicos
          </p>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Descrição do Projeto */}
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-3">
              Bem-vindo à revolução dos estudos médicos! 🎓
            </h2>
            <p className="text-gray-600 leading-relaxed">
              O <strong>MedEvo</strong> é uma plataforma inovadora que combina inteligência artificial
              com metodologias comprovadas de aprendizado para maximizar seu desempenho em concursos médicos.
            </p>
          </div>

          {/* Funcionalidades Principais */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Funcionalidades Principais
            </h3>

            <div className="grid gap-3">
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-gray-800">Banco de Questões Inteligente</h4>
                  <p className="text-sm text-gray-600">Milhares de questões organizadas por especialidade, tema e foco específico</p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                <Brain className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-gray-800">Análise IA de Questões</h4>
                  <p className="text-sm text-gray-600">Comentários detalhados gerados por IA para cada questão com linguagem didática</p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <BarChart3 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-gray-800">Analytics Avançado</h4>
                  <p className="text-sm text-gray-600">Acompanhe seu progresso com métricas detalhadas e insights personalizados</p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                <Users className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-gray-800">Cronograma Personalizado</h4>
                  <p className="text-sm text-gray-600">Organize seus estudos com cronogramas adaptativos e lembretes inteligentes</p>
                </div>
              </div>
            </div>
          </div>

          {/* Roadmap */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              Roadmap de Desenvolvimento
            </h3>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0"></div>
                <span className="text-sm text-gray-700"><strong>Fase Atual (Beta):</strong> Banco de questões + IA + Analytics básico</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full flex-shrink-0"></div>
                <span className="text-sm text-gray-700"><strong>Próxima:</strong> Liberação Flashcards + Simulados adaptativos + Gamificação</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full flex-shrink-0"></div>
                <span className="text-sm text-gray-700"><strong>Futuro:</strong> Comunidade + Mentoria IA + Realidade Virtual</span>
              </div>
            </div>
          </div>

          {/* Importância do Feedback */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              🚀 Seu Feedback é Fundamental!
            </h3>
            <p className="text-sm text-gray-700 leading-relaxed mb-3">
              Como estamos em <strong>fase Beta</strong>, cada sugestão, bug reportado ou ideia compartilhada
              nos ajuda a construir a melhor plataforma de estudos médicos do Brasil.
            </p>
            <p className="text-sm text-gray-700 leading-relaxed">
              <strong>Juntos</strong>, vamos revolucionar a forma como os médicos estudam e se preparam
              para concursos e residências! 💪
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <Button
            onClick={onContinue}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
          >
            Começar Jornada de Estudos
            <ArrowRight className="h-4 w-4" />
          </Button>
          <p className="text-xs text-gray-500 text-center mt-2">
            Este dialog aparece apenas uma vez. Boa sorte nos estudos! 🎯
          </p>
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};
