
import { FlashcardManager } from "./FlashcardManager";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";

export const FlashcardsStudy = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in">
        {/* Header Section */}
        <div className="relative">
          <div className="inline-block transform -rotate-2">
            <div 
              className="bg-hackathon-yellow border-2 border-black px-4 py-1 
              text-black font-bold tracking-wide text-sm 
              shadow-card-sm hover:shadow-card transition-all"
            >
              ÁREA DE ESTUDO
            </div>
          </div>
          
          <div>
            <h1 className="text-5xl font-black mt-4 mb-2 relative">
              <span 
                className="inline-block bg-black text-white px-6 py-3 
                transform -rotate-1 shadow-card-light 
                border-2 border-white 
                relative z-10 
                hover:scale-[1.02] transition-transform"
              >
                Flashcards
              </span>
              <div 
                className="absolute inset-0 top-1 left-1 
                bg-hackathon-red/50 rounded-lg 
                -z-10 blur-sm 
                opacity-50 hover:opacity-70 transition-all"
              />
            </h1>
            
            <p 
              className="text-xl text-gray-600 max-w-2xl mt-4 
              bg-gradient-to-r from-gray-600 via-gray-800 to-black 
              bg-clip-text text-transparent 
              font-semibold tracking-wide"
            >
              Gerencie e estude com seus flashcards personalizados
            </p>
          </div>
        </div>

        {/* Custom Study Section */}
        <div 
          className="bg-white border-2 border-black rounded-xl 
          shadow-card-sm p-8 relative overflow-hidden 
          group hover:shadow-card transition-shadow"
        >
          <div 
            className="absolute top-0 right-0 w-64 h-64 
            bg-hackathon-yellow/20 rounded-full 
            transform translate-x-32 -translate-y-32 
            group-hover:rotate-12 transition-transform"
          />
          <div className="relative space-y-6">
            <div>
              <h2 
                className="text-2xl font-bold mb-2 
                bg-gradient-to-r from-black via-gray-800 to-gray-600 
                bg-clip-text text-transparent"
              >
                Nova Sessão de Estudo
              </h2>
              <p 
                className="text-gray-600 
                border-l-4 border-hackathon-yellow pl-4"
              >
                Escolha os tópicos que você deseja estudar e crie uma sessão personalizada.
              </p>
            </div>
            <FlashcardManager />
          </div>
        </div>
      </div>
    </div>
  );
};
