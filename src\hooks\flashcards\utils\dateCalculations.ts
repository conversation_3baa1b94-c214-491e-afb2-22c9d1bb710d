
import type { FlashcardResponse } from "@/types/flashcard";
import type { FSRSMetrics } from "@/utils/fsrs/types";
import { calculateFSRSMetrics } from "@/utils/fsrs/calculator";
import { adjustToBrazilTimezone } from "@/utils/formatTime";

export const calculateNextReviewDate = (
  response: FlashcardResponse,
  currentMetrics: FSRSMetrics
): Date => {
  const metrics = calculateFSRSMetrics(currentMetrics, response);

  // Calcular intervalo baseado na estabilidade e recuperabilidade
  const intervalInDays = metrics.stability * metrics.retrievability;
  
  // Aplicar ajustes baseados na resposta
  const adjustedInterval = response === 'easy' ? intervalInDays * 1.3 : 
                         response === 'medium' ? intervalInDays : 
                         response === 'hard' ? intervalInDays * 0.7 : 
                         intervalInDays * 0.3;

  // Use Brazil's timezone for the calculation
  const now = adjustToBrazilTimezone(new Date());
  const nextDate = new Date(now.getTime() + adjustedInterval * 24 * 60 * 60 * 1000);

  console.log('📅 [dateCalculations] Cálculo finalizado:', {
    response,
    metrics,
    intervalInDays: adjustedInterval,
    nextDate
  });

  return nextDate;
};

export const formatInterval = (days: number): string => {
  if (days < 1) {
    const hours = Math.round(days * 24);
    return `${hours} ${hours === 1 ? 'hora' : 'horas'}`;
  }
  if (days === 1) return '1 dia';
  if (days > 30) {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    return months === 1 
      ? `1 mês${remainingDays > 0 ? ` e ${remainingDays} dias` : ''}`
      : `${months} meses${remainingDays > 0 ? ` e ${remainingDays} dias` : ''}`;
  }
  return `${Math.round(days)} dias`;
};
