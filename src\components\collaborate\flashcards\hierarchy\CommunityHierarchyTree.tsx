import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Search, Filter } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { FolderCard } from "../components/FolderCard";
import { FlashcardPreviewCard } from "../components/FlashcardPreviewCard";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import type { FlashcardWithHierarchy } from "@/types/flashcardCollaborate";

interface HierarchyNode {
  id: string;
  name: string;
  level: number;
  children?: HierarchyNode[];
  flashcards?: FlashcardWithHierarchy[];
  count: number;
}

type SelectionPath = {
  specialty?: string;
  theme?: string;
  focus?: string;
  extraFocus?: string;
};

interface CommunityHierarchyTreeProps {
  flashcards: FlashcardWithHierarchy[];
  onImportCards: (cardIds: string[]) => Promise<void>;
  isLoading?: boolean;
  importedCardIds?: string[];
}

export const CommunityHierarchyTree: React.FC<CommunityHierarchyTreeProps> = ({
  flashcards,
  onImportCards,
  isLoading = false,
  importedCardIds = []
}) => {
  const [selection, setSelection] = useState<SelectionPath>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [importingCards, setImportingCards] = useState(false);
  const [hierarchy, setHierarchy] = useState<HierarchyNode[]>([]);
  const [filteredFlashcards, setFilteredFlashcards] = useState<FlashcardWithHierarchy[]>([]);

  useEffect(() => {
    buildHierarchy(flashcards);
    setFilteredFlashcards(flashcards);
  }, [flashcards]);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredFlashcards(flashcards);
      return;
    }

    const filtered = flashcards.filter(card => 
      card.front.toLowerCase().includes(searchTerm.toLowerCase()) || 
      card.back.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.hierarchy.specialty?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.hierarchy.theme?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.hierarchy.focus?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.hierarchy.extraFocus?.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredFlashcards(filtered);
  }, [searchTerm, flashcards]);

  const buildHierarchy = (cards: FlashcardWithHierarchy[]) => {
    const specialties: {[id: string]: HierarchyNode} = {};

    for (const card of cards) {
      if (!card.hierarchy.specialty) continue;

      const sId = card.hierarchy.specialty.id;
      const sName = card.hierarchy.specialty.name;
      
      if (!specialties[sId]) {
        specialties[sId] = { 
          id: sId, 
          name: sName, 
          level: 0, 
          children: [],
          count: 0
        };
      }

      specialties[sId].count++;

      if (card.hierarchy.theme) {
        const tId = card.hierarchy.theme.id;
        const tName = card.hierarchy.theme.name;
        
        let themeNode = specialties[sId].children!.find(t => t.id === tId);
        if (!themeNode) {
          themeNode = { 
            id: tId, 
            name: tName, 
            level: 1, 
            children: [],
            count: 0
          };
          specialties[sId].children!.push(themeNode);
        }

        themeNode.count++;

        if (card.hierarchy.focus) {
          const fId = card.hierarchy.focus.id;
          const fName = card.hierarchy.focus.name;
          
          let focusNode = themeNode.children!.find(f => f.id === fId);
          if (!focusNode) {
            focusNode = { 
              id: fId, 
              name: fName, 
              level: 2, 
              children: [],
              count: 0
            };
            themeNode.children!.push(focusNode);
          }

          focusNode.count++;
          
          if (card.hierarchy.extraFocus) {
            const eId = card.hierarchy.extraFocus.id;
            const eName = card.hierarchy.extraFocus.name;
            
            let extraFocusNode = focusNode.children!.find(e => e.id === eId);
            if (!extraFocusNode) {
              extraFocusNode = { 
                id: eId, 
                name: eName, 
                level: 3,
                count: 0
              };
              (focusNode.children as HierarchyNode[]).push(extraFocusNode);
            }
            
            extraFocusNode.count++;
          }
        }
      }
    }

    const hierarchyArray = Object.values(specialties).sort((a, b) => a.name.localeCompare(b.name));
    
    hierarchyArray.forEach(specialty => {
      if (specialty.children) {
        specialty.children.sort((a, b) => a.name.localeCompare(b.name));
        
        specialty.children.forEach(theme => {
          if (theme.children) {
            theme.children.sort((a, b) => a.name.localeCompare(b.name));
            
            theme.children.forEach(focus => {
              if (focus.children) {
                focus.children.sort((a, b) => a.name.localeCompare(b.name));
              }
            });
          }
        });
      }
    });

    setHierarchy(hierarchyArray);
  };

  function getCurrentLayerAndFlashcards(): { nodes: HierarchyNode[], flashcards: FlashcardWithHierarchy[] } {
    let nodes = hierarchy;
    let currentFlashcards: FlashcardWithHierarchy[] = [];
    
    const filterCards = () => {
      return filteredFlashcards.filter(card => {
        if (selection.extraFocus) {
          return card.hierarchy.extraFocus?.id === selection.extraFocus;
        } 
        
        if (selection.focus) {
          return card.hierarchy.focus?.id === selection.focus && !card.hierarchy.extraFocus;
        }
        
        if (selection.theme) {
          return card.hierarchy.theme?.id === selection.theme && !card.hierarchy.focus;
        }
        
        if (selection.specialty) {
          return card.hierarchy.specialty?.id === selection.specialty && !card.hierarchy.theme;
        }
        
        return !card.hierarchy.specialty;
      });
    };
    
    if (selection.specialty) {
      const s = nodes.find(n => n.id === selection.specialty);
      if (!s) return { nodes: [], flashcards: [] };
      
      currentFlashcards = filterCards();
      
      if (selection.theme) {
        const t = s.children?.find(n => n.id === selection.theme);
        if (!t) return { nodes: [], flashcards: currentFlashcards };
        
        if (selection.focus) {
          const f = t.children?.find(n => n.id === selection.focus);
          if (!f) return { nodes: [], flashcards: currentFlashcards };
          
          if (selection.extraFocus) {
            const e = f.children?.find(n => n.id === selection.extraFocus);
            return e
              ? { nodes: [], flashcards: currentFlashcards }
              : { nodes: [], flashcards: currentFlashcards };
          }
          return { nodes: f.children ?? [], flashcards: currentFlashcards };
        }
        return { nodes: t.children ?? [], flashcards: currentFlashcards };
      }
      return { nodes: s.children ?? [], flashcards: currentFlashcards };
    }
    return { nodes, flashcards: currentFlashcards };
  }

  function handleSelectNode(node: HierarchyNode) {
    setSelection(sel => {
      if (node.level === 0) return { specialty: node.id };
      if (node.level === 1) return { specialty: sel.specialty, theme: node.id };
      if (node.level === 2) return { specialty: sel.specialty, theme: sel.theme, focus: node.id };
      if (node.level === 3) return { specialty: sel.specialty, theme: sel.theme, focus: sel.focus, extraFocus: node.id };
      return {};
    });
  }

  function handleBack() {
    if (selection.extraFocus) setSelection(s => ({ ...s, extraFocus: undefined }));
    else if (selection.focus) setSelection(s => ({ ...s, focus: undefined }));
    else if (selection.theme) setSelection(s => ({ ...s, theme: undefined }));
    else if (selection.specialty) setSelection({});
  }

  function toggleCardSelection(cardId: string) {
    if (importedCardIds.includes(cardId) || (flashcards.find(c => c.id === cardId)?.isImported)) {
      return;
    }
    setSelectedCards(prev => 
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  }

  async function handleImportCards() {
    if (selectedCards.length === 0) {
      toast.error("Selecione pelo menos um flashcard para importar");
      return;
    }

    try {
      setImportingCards(true);
      await onImportCards(selectedCards);
      
      setSelectedCards([]);
    } catch (error) {
      console.error("Erro ao importar flashcards:", error);
    } finally {
      setImportingCards(false);
    }
  }

  function handleSelectAll() {
    const { flashcards: currentFlashcards } = getCurrentLayerAndFlashcards();
    if (currentFlashcards.length === 0) return;

    const notImportedCards = currentFlashcards.filter(
      card => !importedCardIds.includes(card.id) && !card.isImported
    );

    const allSelected = notImportedCards.every(card => selectedCards.includes(card.id));
    
    if (allSelected) {
      setSelectedCards(prev => 
        prev.filter(id => !notImportedCards.some(card => card.id === id))
      );
    } else {
      const newSelectedIds = notImportedCards
        .map(card => card.id)
        .filter(id => !selectedCards.includes(id));

      setSelectedCards(prev => [...prev, ...newSelectedIds]);
    }
  }

  const { nodes, flashcards: currentFlashcards } = getCurrentLayerAndFlashcards();
  const currentLevel = selection.extraFocus ? 3 
    : selection.focus ? 2 
    : selection.theme ? 1 
    : selection.specialty ? 0 
    : -1;

  const getCurrentFolderName = () => {
    const LEVEL_LABELS = ["Especialidades", "Temas", "Focos", "Extra Focos"];
    
    if (selection.extraFocus) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      if (!t) return "Pasta";
      const f = t.children?.find(n => n.id === selection.focus);
      if (!f) return "Pasta";
      const e = f.children?.find(n => n.id === selection.extraFocus);
      return e?.name || "Pasta";
    } else if (selection.focus) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      if (!t) return "Pasta";
      const f = t.children?.find(n => n.id === selection.focus);
      return f?.name || "Pasta";
    } else if (selection.theme) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      return t?.name || "Pasta";
    } else if (selection.specialty) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      return s?.name || "Pasta";
    }
    return LEVEL_LABELS[0];
  };

  const notImportedFlashcards = currentFlashcards.filter(
    card => !importedCardIds.includes(card.id) && !card.isImported
  );
  
  const allSelected = notImportedFlashcards.length > 0 && 
    notImportedFlashcards.every(card => selectedCards.includes(card.id));

  function handleLike(cardId: string) {
    console.log("Like clicked on card:", cardId);
  }
  
  function handleDislike(cardId: string) {
    console.log("Dislike clicked on card:", cardId);
  }

  return (
    <Card className="p-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row gap-4 items-center mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Pesquisar flashcards..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1"
            onClick={handleSelectAll}
          >
            {allSelected ? "Desmarcar Todos" : "Selecionar Todos"}
          </Button>
          <Button
            onClick={handleImportCards}
            disabled={selectedCards.length === 0 || importingCards}
            className="bg-hackathon-yellow hover:bg-hackathon-yellow/90 text-black border-2 border-black font-bold shadow-button"
          >
            {importingCards ? (
              "Importando..."
            ) : (
              `Importar Selecionados (${selectedCards.length})`
            )}
          </Button>
        </div>
      </div>

      <div className="mb-5 flex items-center">
        {currentLevel > -1 && (
          <Button variant="outline" size="sm" onClick={handleBack} className="mr-3 flex items-center gap-1">
            <ChevronLeft className="h-4 w-4" /> Voltar
          </Button>
        )}
        <span className="font-bold text-lg text-gray-700">
          {getCurrentFolderName()}
        </span>
        {selectedCards.length > 0 && (
          <Badge variant="outline" className="ml-2">
            {selectedCards.length} selecionados
          </Badge>
        )}
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Skeleton key={i} className="h-32 w-full rounded-xl" />
          ))}
        </div>
      ) : (
        <div>
          {nodes.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
              {nodes.map(node => (
                <FolderCard
                  key={node.id}
                  name={`${node.name} (${node.count})`}
                  onClick={() => handleSelectNode(node)}
                  className="h-full"
                  iconColor="#1676F3"
                />
              ))}
            </div>
          )}

          {nodes.length > 0 && currentFlashcards.length > 0 && (
            <Separator className="my-6" />
          )}

          {currentFlashcards.length > 0 ? (
            <div>
              <h3 className="text-md font-medium mb-4 text-gray-600">
                Flashcards {currentLevel > -1 ? "nesta pasta" : "compartilhados"}
                <span className="text-sm font-normal ml-2 text-gray-500">
                  ({currentFlashcards.length})
                </span>
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {currentFlashcards.map(card => {
                  const isImported = importedCardIds.includes(card.id) || card.isImported;
                  return (
                    <div 
                      key={card.id} 
                      className={cn(
                        "relative",
                        isImported && "opacity-75"
                      )}
                    >
                      <div 
                        className="absolute top-3 left-3 z-10"
                        onClick={e => {
                          e.stopPropagation();
                          if (!isImported) {
                            toggleCardSelection(card.id);
                          }
                        }}
                        style={isImported ? { opacity: 0.5, cursor: "not-allowed" } : undefined}
                        title={isImported ? "Já importado" : "Selecionar"}
                      >
                        <Checkbox
                          checked={selectedCards.includes(card.id)}
                          disabled={isImported}
                          className="h-5 w-5 border-2 border-black data-[state=checked]:bg-hackathon-yellow data-[state=checked]:text-black"
                        />
                      </div>
                      {isImported && (
                        <span className="absolute top-3 right-3 bg-green-200 text-green-800 px-2 text-xs rounded font-bold border border-green-400 z-10">
                          Importado
                        </span>
                      )}
                      <FlashcardPreviewCard
                        card={{
                          ...card,
                          isImported: isImported
                        }}
                        onLike={handleLike}
                        onDislike={handleDislike}
                        disableInteractions={isImported}
                        hideImportedBadge={true}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            nodes.length === 0 && (
              <div className="text-gray-400 text-center p-8 border border-dashed rounded-lg">
                {searchTerm ? "Nenhum resultado encontrado." : "Nenhum flashcard compartilhado encontrado."}
              </div>
            )
          )}
        </div>
      )}
    </Card>
  );
};
