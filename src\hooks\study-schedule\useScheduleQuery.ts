import React, { useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@supabase/auth-helpers-react';
import { useToast } from '@/hooks/use-toast';
import { useScheduleManagement } from './useScheduleManagement';
import { useStudyTopics } from './useStudyTopics';
import type { WeeklySchedule, StudyTopic } from '@/types/study-schedule';

/**
 * Hook com React Query para gerenciamento de estado do cronograma
 * Elimina re-renders desnecessários e mantém estado consistente
 */
export const useScheduleQuery = () => {
  // Removido log excessivo: console.log('🔥 [useScheduleQuery] HOOK CALLED/RE-CALLED');

  const user = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Logs de debug removidos - problema resolvido com sistema independente
  
  const {
    loadCurrentSchedule: fetchSchedule,
    updateTopic: updateTopicAPI,
    deleteWeek: deleteWeekAPI,
    deleteAllWeeks: deleteAllWeeksAPI,
    addWeeks
  } = useScheduleManagement();
  
  const {
    markTopicAsStudied: markTopicAsStudiedAPI,
    deleteTopic: deleteTopicAPI
  } = useStudyTopics();

  // ✅ Query principal do cronograma
  const {
    data: weeklySchedule,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['schedule', user?.id],
    queryFn: async () => {
      // Logs removidos - problema resolvido com sistema independente
      const schedule = await fetchSchedule(50, 0, true); // Aumentado para 50 semanas
      return schedule;
    },
    enabled: !!user?.id,
    staleTime: 1 * 60 * 1000, // 1 minuto - mais responsivo para mudanças
    cacheTime: 15 * 60 * 1000, // 15 minutos
    refetchOnWindowFocus: false,
    retry: 2
  });

  // ✅ Mutation para atualizar tópico
  const updateTopicMutation = useMutation({
    mutationFn: async (topicData: StudyTopic) => {
      return await updateTopicAPI(topicData);
    },
    onMutate: async (topicData: StudyTopic) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });

      // Snapshot do estado anterior
      const previousSchedule = queryClient.getQueryData(['schedule', user?.id]);

      // Atualização otimista
      queryClient.setQueryData(['schedule', user?.id], (old: WeeklySchedule | undefined) => {
        if (!old) return old;

        return {
          ...old,
          recommendations: old.recommendations.map(day => ({
            ...day,
            topics: day.topics.map(topic =>
              topic.id === topicData.id ? { ...topic, ...topicData } : topic
            )
          }))
        };
      });

      return { previousSchedule };
    },
    onError: (err, topicData, context) => {
      // Reverter para estado anterior
      if (context?.previousSchedule) {
        queryClient.setQueryData(['schedule', user?.id], context.previousSchedule);
      }

      toast({
        title: "Erro ao atualizar tópico",
        description: "Tente novamente em alguns instantes",
        variant: "destructive"
      });
    },
    onSettled: () => {
      // Invalidar query para garantir sincronização
      queryClient.invalidateQueries({ queryKey: ['schedule', user?.id] });
    }
  });

  // ✅ Mutation para marcar como estudado
  const markStudiedMutation = useMutation({
    mutationFn: async (topicId: string) => {
      return await markTopicAsStudiedAPI(topicId);
    },
    onMutate: async (topicId: string) => {
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
      const previousSchedule = queryClient.getQueryData(['schedule', user?.id]);

      queryClient.setQueryData(['schedule', user?.id], (old: WeeklySchedule | undefined) => {
        if (!old) return old;

        return {
          ...old,
          recommendations: old.recommendations.map(day => ({
            ...day,
            topics: day.topics.map(topic =>
              topic.id === topicId ? {
                ...topic,
                study_status: 'completed',
                isStudied: true,
                last_revision_date: new Date().toISOString()
              } : topic
            )
          }))
        };
      });

      return { previousSchedule };
    },
    onError: (err, topicId, context) => {
      if (context?.previousSchedule) {
        queryClient.setQueryData(['schedule', user?.id], context.previousSchedule);
      }

      toast({
        title: "Erro ao marcar como estudado",
        description: "Tente novamente em alguns instantes",
        variant: "destructive"
      });
    }
  });

  // ✅ Mutation para deletar tópico
  const deleteTopicMutation = useMutation({
    mutationFn: async (topicId: string) => {
      return await deleteTopicAPI(topicId);
    },
    onMutate: async (topicId: string) => {
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
      const previousSchedule = queryClient.getQueryData(['schedule', user?.id]);

      queryClient.setQueryData(['schedule', user?.id], (old: WeeklySchedule | undefined) => {
        if (!old) return old;

        return {
          ...old,
          recommendations: old.recommendations.map(day => ({
            ...day,
            topics: day.topics.filter(topic => topic.id !== topicId)
          }))
        };
      });

      return { previousSchedule };
    },
    onError: (err, topicId, context) => {
      if (context?.previousSchedule) {
        queryClient.setQueryData(['schedule', user?.id], context.previousSchedule);
      }

      toast({
        title: "Erro ao deletar tópico",
        description: "Tente novamente em alguns instantes",
        variant: "destructive"
      });
    }
  });

  // ✅ Mutation para deletar semana
  const deleteWeekMutation = useMutation({
    mutationFn: async (weekNumber: number) => {
      return await deleteWeekAPI(weekNumber);
    },
    onMutate: async (weekNumber: number) => {
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
      const previousSchedule = queryClient.getQueryData(['schedule', user?.id]);

      queryClient.setQueryData(['schedule', user?.id], (old: WeeklySchedule | undefined) => {
        if (!old) return old;

        return {
          ...old,
          recommendations: old.recommendations.filter(day => day.weekNumber !== weekNumber)
        };
      });

      return { previousSchedule };
    },
    onError: (err, weekNumber, context) => {
      if (context?.previousSchedule) {
        queryClient.setQueryData(['schedule', user?.id], context.previousSchedule);
      }

      toast({
        title: "Erro ao deletar semana",
        description: "Tente novamente em alguns instantes",
        variant: "destructive"
      });
    }
  });

  // ✅ Mutation para deletar todas as semanas
  const deleteAllWeeksMutation = useMutation({
    mutationFn: async () => {
      return await deleteAllWeeksAPI();
    },
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
      const previousSchedule = queryClient.getQueryData(['schedule', user?.id]);

      queryClient.setQueryData(['schedule', user?.id], (old: WeeklySchedule | undefined) => {
        if (!old) return old;

        return {
          ...old,
          recommendations: []
        };
      });

      return { previousSchedule };
    },
    onError: (err, variables, context) => {
      if (context?.previousSchedule) {
        queryClient.setQueryData(['schedule', user?.id], context.previousSchedule);
      }

      toast({
        title: "Erro ao deletar cronograma",
        description: "Tente novamente em alguns instantes",
        variant: "destructive"
      });
    }
  });

  // ✅ Estado de loading independente (igual à IA)
  const [isAddingWeeks, setIsAddingWeeks] = React.useState(false);

  // ✅ Função para adicionar semanas (sistema independente)
  const addWeeksFunction = useCallback(async (weeks: number) => {
    setIsAddingWeeks(true);

    try {
      const result = await addWeeks(weeks);

      if (result) {
        // Invalidação inteligente para mostrar novas semanas
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ['schedule', user?.id],
            refetchType: 'active'
          });
        }, 100);

        toast({
          title: "Semanas adicionadas!",
          description: `${weeks} semana${weeks > 1 ? 's' : ''} adicionada${weeks > 1 ? 's' : ''} com sucesso.`,
        });
      } else {
        throw new Error('Failed to add weeks');
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erro ao adicionar semanas",
        description: "Não foi possível adicionar as semanas. Tente novamente.",
      });
    } finally {
      setIsAddingWeeks(false);
    }
  }, [addWeeks, toast, queryClient, user?.id]);

  // Memoizar o retorno para evitar re-criação de objetos
  return useMemo(() => ({
    // Estado
    weeklySchedule,
    isLoading: isLoading || updateTopicMutation.isPending || markStudiedMutation.isPending || deleteTopicMutation.isPending || deleteWeekMutation.isPending || deleteAllWeeksMutation.isPending || isAddingWeeks,
    error,

    // Ações
    updateTopic: updateTopicMutation.mutate,
    markTopicAsStudied: markStudiedMutation.mutate,
    deleteTopic: deleteTopicMutation.mutate,
    deleteWeek: deleteWeekMutation.mutate,
    deleteAllWeeks: deleteAllWeeksMutation.mutate,
    addWeeks: addWeeksFunction, // ✅ Função independente (igual IA)

    // Funções auxiliares
    refetch
  }), [
    weeklySchedule,
    isLoading,
    updateTopicMutation.isPending,
    markStudiedMutation.isPending,
    deleteTopicMutation.isPending,
    deleteWeekMutation.isPending,
    deleteAllWeeksMutation.isPending,
    isAddingWeeks, // ✅ Estado independente (igual IA)
    error,
    updateTopicMutation.mutate,
    markStudiedMutation.mutate,
    deleteTopicMutation.mutate,
    deleteWeekMutation.mutate,
    deleteAllWeeksMutation.mutate,
    addWeeksFunction, // ✅ Função independente (igual IA)
    refetch
  ]);
};
