import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import type { Question } from '@/types/question';

// 🎯 Interface para o contexto da questão atual
export interface CurrentQuestionContextType {
  // Estado da questão atual
  currentQuestion: Question | null;
  currentQuestionIndex: number;
  totalQuestions: number;
  sessionId: string | null;
  
  // Metadados da sessão
  sessionTitle: string | null;
  isInQuestionSession: boolean;
  
  // Funções de controle
  setCurrentQuestion: (question: Question | null) => void;
  setQuestionIndex: (index: number) => void;
  setTotalQuestions: (total: number) => void;
  setSessionId: (sessionId: string | null) => void;
  setSessionTitle: (title: string | null) => void;
  
  // Função para limpar contexto
  clearQuestionContext: () => void;
  
  // Função para atualizar contexto completo
  updateQuestionContext: (context: Partial<QuestionContextData>) => void;
}

// 🎯 Interface para dados do contexto
export interface QuestionContextData {
  question: Question | null;
  questionIndex: number;
  totalQuestions: number;
  sessionId: string | null;
  sessionTitle: string | null;
}

// 🎯 Criar o contexto
const CurrentQuestionContext = createContext<CurrentQuestionContextType | undefined>(undefined);

// 🎯 Provider do contexto
export const CurrentQuestionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Estados principais
  const [currentQuestion, setCurrentQuestionState] = useState<Question | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [totalQuestions, setTotalQuestionsState] = useState<number>(0);
  const [sessionId, setSessionIdState] = useState<string | null>(null);
  const [sessionTitle, setSessionTitleState] = useState<string | null>(null);

  // Estado derivado
  const isInQuestionSession = currentQuestion !== null && sessionId !== null;

  // 🎯 Função para definir questão atual
  const setCurrentQuestion = useCallback((question: Question | null) => {
    setCurrentQuestionState(question);
  }, []);

  // 🎯 Função para definir índice da questão
  const setQuestionIndex = useCallback((index: number) => {
    setCurrentQuestionIndex(index);
  }, []);

  // 🎯 Função para definir total de questões
  const setTotalQuestions = useCallback((total: number) => {
    setTotalQuestionsState(total);
  }, []);

  // 🎯 Função para definir ID da sessão
  const setSessionId = useCallback((id: string | null) => {
    setSessionIdState(id);
  }, []);

  // 🎯 Função para definir título da sessão
  const setSessionTitle = useCallback((title: string | null) => {
    setSessionTitleState(title);
  }, []);

  // 🎯 Função para limpar todo o contexto
  const clearQuestionContext = useCallback(() => {
    setCurrentQuestionState(null);
    setCurrentQuestionIndex(0);
    setTotalQuestionsState(0);
    setSessionIdState(null);
    setSessionTitleState(null);
  }, []);

  // 🎯 Função para atualizar contexto completo
  const updateQuestionContext = useCallback((context: Partial<QuestionContextData>) => {
    
    if (context.question !== undefined) {
      setCurrentQuestionState(context.question);
    }
    if (context.questionIndex !== undefined) {
      setCurrentQuestionIndex(context.questionIndex);
    }
    if (context.totalQuestions !== undefined) {
      setTotalQuestionsState(context.totalQuestions);
    }
    if (context.sessionId !== undefined) {
      setSessionIdState(context.sessionId);
    }
    if (context.sessionTitle !== undefined) {
      setSessionTitleState(context.sessionTitle);
    }
  }, []);

  // 🎯 Log de debug quando há mudanças importantes
  useEffect(() => {
    if (currentQuestion && sessionId) {
      // Context is active
    }
  }, [currentQuestion, currentQuestionIndex, totalQuestions, sessionId, sessionTitle]);

  // 🎯 Valor do contexto
  const contextValue: CurrentQuestionContextType = {
    // Estado
    currentQuestion,
    currentQuestionIndex,
    totalQuestions,
    sessionId,
    sessionTitle,
    isInQuestionSession,
    
    // Funções
    setCurrentQuestion,
    setQuestionIndex,
    setTotalQuestions,
    setSessionId,
    setSessionTitle,
    clearQuestionContext,
    updateQuestionContext
  };

  return (
    <CurrentQuestionContext.Provider value={contextValue}>
      {children}
    </CurrentQuestionContext.Provider>
  );
};

// 🎯 Hook para usar o contexto
export const useCurrentQuestion = (): CurrentQuestionContextType => {
  const context = useContext(CurrentQuestionContext);
  
  if (context === undefined) {
    throw new Error('useCurrentQuestion deve ser usado dentro de um CurrentQuestionProvider');
  }
  
  return context;
};

// 🎯 Hook otimizado para componentes que só precisam saber se há uma questão ativa
export const useIsInQuestionSession = (): boolean => {
  const { isInQuestionSession } = useCurrentQuestion();
  return isInQuestionSession;
};

// 🎯 Hook otimizado para obter informações básicas da questão atual
export const useCurrentQuestionInfo = () => {
  const { 
    currentQuestion, 
    currentQuestionIndex, 
    totalQuestions, 
    sessionTitle,
    isInQuestionSession 
  } = useCurrentQuestion();
  
  return {
    questionNumber: currentQuestionIndex + 1,
    totalQuestions,
    sessionTitle,
    isInQuestionSession,
    questionId: currentQuestion?.id || null,
    specialty: currentQuestion?.specialty?.name || null,
    theme: currentQuestion?.theme?.name || null,
    focus: currentQuestion?.focus?.name || null
  };
};

export default CurrentQuestionContext;
