import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Brain, Target, Crosshair } from "lucide-react";
import { SessionStats } from "@/types/studySession";
import { supabase } from "@/integrations/supabase/client";
import { SkillTreeSection } from './SkillTreeSection';

interface SessionSkillTreeProps {
  stats: SessionStats;
}

export const SessionSkillTree = ({ stats }: SessionSkillTreeProps) => {
  const [categories, setCategories] = useState<Record<string, any>>({});

  useEffect(() => {
    const loadCategories = async () => {
      // Collect all category IDs from stats
      const categoryIds = new Set([
        ...Object.keys(stats.by_specialty || {}),
        ...Object.keys(stats.by_theme || {}),
        ...Object.keys(stats.by_focus || {})
      ]);

      if (categoryIds.size === 0) {
        console.log('⚠️ No categories found in stats');
        return;
      }

      console.log('📊 [SessionSkillTree] Loading categories...');

      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .in('id', Array.from(categoryIds));

      if (error) {
        console.error('❌ Error fetching categories:', error);
        return;
      }

      console.log('✅ [SessionSkillTree] Categories loaded:', data?.length || 0);

      const categoryMap = (data || []).reduce((acc, cat) => ({
        ...acc,
        [cat.id]: cat
      }), {});

      setCategories(categoryMap);
    };

    loadCategories();
  }, [stats]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Árvore de Habilidades da Sessão</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <SkillTreeSection
          title="Especialidades"
          data={stats.by_specialty}
          categories={categories}
          icon={<Brain className="h-4 w-4" />}
        />
        <SkillTreeSection
          title="Temas"
          data={stats.by_theme}
          categories={categories}
          icon={<Target className="h-4 w-4" />}
        />
        <SkillTreeSection
          title="Focos"
          data={stats.by_focus}
          categories={categories}
          icon={<Crosshair className="h-4 w-4" />}
        />
      </CardContent>
    </Card>
  );
};