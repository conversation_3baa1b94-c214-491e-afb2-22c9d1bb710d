
import React from "react";
import { AlertCircle } from "lucide-react";

interface ValidationErrorsProps {
  errors: string[];
}

export const ValidationErrors = ({ errors }: ValidationErrorsProps) => {
  if (errors.length === 0) return null;
  
  return (
    <div className="p-5 border-2 rounded-xl bg-red-50 border-red-200 shadow-md">
      <div className="flex items-start gap-3">
        <AlertCircle className="flex-shrink-0 w-5 h-5 text-red-500 mt-0.5" />
        <div>
          <p className="font-bold text-red-800">Por favor, corrija os seguintes erros:</p>
          <ul className="mt-2 ml-5 space-y-1 text-sm list-disc text-red-700">
            {errors.map((error, i) => (
              <li key={i}>{error}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};
