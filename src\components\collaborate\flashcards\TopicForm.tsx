import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { SpecialtyTab } from "./hierarchy/SpecialtyTab";
import { ThemeTab } from "./hierarchy/ThemeTab";
import { FocusTab } from "./hierarchy/FocusTab";
import { ExtrafocusTab } from "./hierarchy/ExtrafocusTab";
import type { Topic } from "@/types/flashcard";

interface TopicFormProps {
  topics: Topic[];
  onCreateTopic: (name: string) => Promise<void>;
}

export const TopicForm = ({ topics, onCreateTopic }: TopicFormProps) => {
  return (
    <Tabs defaultValue="specialty" className="w-full">
      <TabsList className="w-full">
        <TabsTrigger value="specialty" className="flex-1">Especialidades</TabsTrigger>
        <TabsTrigger value="theme" className="flex-1">Temas</TabsTrigger>
        <TabsTrigger value="focus" className="flex-1">Focos</TabsTrigger>
        <TabsTrigger value="extrafocus" className="flex-1">Extra Focos</TabsTrigger>
      </TabsList>

      <TabsContent value="specialty">
        <SpecialtyTab />
      </TabsContent>

      <TabsContent value="theme">
        <ThemeTab />
      </TabsContent>

      <TabsContent value="focus">
        <FocusTab />
      </TabsContent>

      <TabsContent value="extrafocus">
        <ExtrafocusTab />
      </TabsContent>
    </Tabs>
  );
};