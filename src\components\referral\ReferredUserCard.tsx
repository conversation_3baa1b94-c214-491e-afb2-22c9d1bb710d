import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  User, 
  Calendar, 
  GraduationCap, 
  Stethoscope,
  BookOpen,
  Clock
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import md5 from 'md5';

interface ReferredUser {
  id: string;
  user_id: string;
  referrer_id: string;
  referral_code: string;
  referred_at: string;
  profiles?: {
    full_name: string;
    formation_area: string;
    preparation_type: string;
    specialty: string;
    is_student: boolean;
    created_at: string;
  };
}

interface ReferredUserCardProps {
  user: ReferredUser;
  index: number;
}

export const ReferredUserCard: React.FC<ReferredUserCardProps> = ({ user, index }) => {
  const getGravatarUrl = (name: string) => {
    // Usar nome para gerar avatar padrão
    const hash = md5(name.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=identicon`;
  };

  const getPreparationTypeLabel = (type: string) => {
    switch (type) {
      case 'residencia':
        return 'Residência Médica';
      case 'titulo':
        return 'Título de Especialista';
      case 'concurso':
        return 'Concurso Público';
      default:
        return 'Não informado';
    }
  };

  const getFormationAreaLabel = (area: string) => {
    switch (area) {
      case 'medicina':
        return 'Medicina';
      case 'enfermagem':
        return 'Enfermagem';
      case 'farmacia':
        return 'Farmácia';
      case 'fisioterapia':
        return 'Fisioterapia';
      case 'nutricao':
        return 'Nutrição';
      case 'psicologia':
        return 'Psicologia';
      case 'odontologia':
        return 'Odontologia';
      case 'veterinaria':
        return 'Veterinária';
      default:
        return area || 'Não informado';
    }
  };

  const getPreparationIcon = (type: string) => {
    switch (type) {
      case 'residencia':
        return <Stethoscope className="h-4 w-4" />;
      case 'titulo':
        return <GraduationCap className="h-4 w-4" />;
      case 'concurso':
        return <BookOpen className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getPreparationColor = (type: string) => {
    switch (type) {
      case 'residencia':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'titulo':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'concurso':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card className="border-2 border-black/20 hover:border-black/40 transition-colors bg-white/80">
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          {/* Avatar e número */}
          <div className="flex flex-col items-center gap-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm border border-black/20">
              {index + 1}
            </div>
            <Avatar className="w-12 h-12 border-2 border-black/20">
              <AvatarImage
                src={getGravatarUrl(user.profiles?.full_name || 'Usuario')}
                alt={user.profiles?.full_name || 'Usuário'}
              />
              <AvatarFallback className="bg-[#E6F2FF] text-blue-700 font-bold">
                {(user.profiles?.full_name || 'U').charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>

          {/* Informações do usuário */}
          <div className="flex-1 space-y-3">
            {/* Nome e status */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-lg">
                  {user.profiles?.full_name || 'Novo Usuário'}
                </h3>
                <p className="text-sm text-gray-600">
                  {user.profiles?.full_name || 'Usuário sem nome definido'}
                </p>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-600">
                Ativo
              </Badge>
            </div>

            {/* Informações acadêmicas */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {/* Área de formação */}
              <div className="flex items-center gap-2 p-3 bg-[#FEF7CD]/30 rounded-lg border border-black/10">
                <GraduationCap className="h-4 w-4 text-gray-700" />
                <div>
                  <div className="text-xs text-gray-600">Área de Formação</div>
                  <div className="text-sm font-medium text-gray-800">
                    {getFormationAreaLabel(user.profiles?.formation_area || '')}
                  </div>
                </div>
              </div>

              {/* Tipo de preparação */}
              <div className="flex items-center gap-2 p-3 bg-[#E6F2FF]/30 rounded-lg border border-black/10">
                {getPreparationIcon(user.profiles?.preparation_type || '')}
                <div>
                  <div className="text-xs text-gray-600">Preparação</div>
                  <div className="text-sm font-medium text-gray-800">
                    {getPreparationTypeLabel(user.profiles?.preparation_type || '')}
                  </div>
                </div>
              </div>
            </div>

            {/* Especialidade (se houver) */}
            {user.profiles?.specialty && (
              <div className="flex items-center gap-2 p-3 bg-[#FFE6E6]/30 rounded-lg border border-black/10">
                <Stethoscope className="h-4 w-4 text-purple-700" />
                <div>
                  <div className="text-xs text-gray-600">Especialidade</div>
                  <div className="text-sm font-medium text-gray-800">
                    {user.profiles.specialty}
                  </div>
                </div>
              </div>
            )}

            {/* Badges de status */}
            <div className="flex flex-wrap gap-2">
              <Badge 
                variant="outline" 
                className={getPreparationColor(user.profiles?.preparation_type || '')}
              >
                {getPreparationIcon(user.profiles?.preparation_type || '')}
                <span className="ml-1">
                  {getPreparationTypeLabel(user.profiles?.preparation_type || '')}
                </span>
              </Badge>
              
              {user.profiles?.is_student && (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                  <BookOpen className="h-3 w-3 mr-1" />
                  Estudante
                </Badge>
              )}
            </div>

            {/* Data do convite */}
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Calendar className="h-3 w-3" />
              <span>
                Ingressou {formatDistanceToNow(new Date(user.referred_at), {
                  addSuffix: true,
                  locale: ptBR
                })} via convite
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
