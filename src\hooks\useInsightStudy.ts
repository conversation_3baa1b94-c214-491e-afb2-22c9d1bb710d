/**
 * Hook para gerenciar estudos de insights
 * Integra o InsightCard com o StudyOptionsDialog
 */

import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { filterQuestionsByTopic } from '@/utils/questionUtils';
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import type { FocusInsight } from '@/types/insights';

interface InsightStudyState {
  isDialogOpen: boolean;
  selectedInsight: FocusInsight | null;
  maxQuestions: number;
  availableYears: number[];
  availableInstitutions: { id: string; name: string }[];
  isLoading: boolean;
}

export const useInsightStudy = () => {
  const [state, setState] = useState<InsightStudyState>({
    isDialogOpen: false,
    selectedInsight: null,
    maxQuestions: 0,
    availableYears: [],
    availableInstitutions: [],
    isLoading: false
  });

  const navigate = useNavigate();
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const user = useUser();
  const { domain } = useDomain();
  const { preferences } = useStudyPreferences();

  /**
   * Abre o dialog de estudos para um insight específico
   */
  const openStudyDialog = useCallback(async (insight: FocusInsight) => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para estudar",
        variant: "destructive"
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, selectedInsight: insight }));

    try {
      console.log('🎯 [useInsightStudy] Abrindo dialog para insight:', {
        focus_name: insight.focus_name,
        focus_id: insight.focus_id,
        temperature: insight.temperature
      });

      // ✅ CORREÇÃO: Buscar questões com filtro de instituições aplicado
      const institutionIds = preferences?.target_institutions?.map(inst => inst.id);

      const result = await filterQuestionsByTopic(
        undefined, // specialty_id - deixar undefined para buscar por focus apenas
        undefined, // theme_id - deixar undefined para buscar por focus apenas
        insight.focus_id, // focus_id correto
        1000, // limite alto para contar todas
        undefined, // sem filtro de ano específico
        institutionIds, // ✅ APLICAR filtro de instituições aqui
        false, // não randomizar
        domain,
        false, // não ocultar respondidas inicialmente
        user.id
      );

      // ✅ OTIMIZAÇÃO: Log apenas se necessário
      if (result.count === 0) {
        console.log('⚠️ [useInsightStudy] Nenhuma questão encontrada para:', insight.focus_name);
      }

      // Extrair anos únicos das questões
      const years = result.questions 
        ? [...new Set(result.questions.map(q => q.exam_year).filter(Boolean))].sort((a, b) => b - a)
        : [];

      // Usar instituições das preferências do usuário
      const institutions = preferences?.target_institutions || [];

      setState(prev => ({
        ...prev,
        isDialogOpen: true,
        maxQuestions: result.count,
        availableYears: years,
        availableInstitutions: institutions,
        isLoading: false
      }));

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao buscar questões:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível carregar as questões para este insight",
        variant: "destructive"
      });

      setState(prev => ({ ...prev, isLoading: false, selectedInsight: null }));
    }
  }, [user?.id, domain, preferences, toast]);

  /**
   * Fecha o dialog de estudos
   */
  const closeStudyDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      isDialogOpen: false,
      selectedInsight: null,
      maxQuestions: 0,
      availableYears: [],
      availableInstitutions: []
    }));
  }, []);

  /**
   * Inicia uma sessão de estudos com as configurações escolhidas
   */
  const startStudy = useCallback(async (
    quantity: number, 
    hideAnswered?: boolean, 
    institutionIds?: string[]
  ) => {
    if (!state.selectedInsight || !user?.id) return;

    try {
      console.log('🚀 [useInsightStudy] Iniciando sessão de estudos:', {
        insight: state.selectedInsight.focus_name,
        quantity,
        hideAnswered,
        institutionIds: institutionIds?.length || 'todas',
        user_id: user.id,
        domain
      });

      // ✅ CORREÇÃO: Buscar questões com os filtros aplicados
      const result = await filterQuestionsByTopic(
        undefined, // specialty_id - deixar undefined
        undefined, // theme_id - deixar undefined
        state.selectedInsight.focus_id, // focus_id correto
        quantity,
        undefined, // anos
        institutionIds, // filtro de instituições aplicado aqui
        true, // randomizar
        domain,
        hideAnswered,
        user.id
      );

      console.log('📊 [useInsightStudy] Resultado da busca de questões:', {
        count: result.count,
        questions_length: result.questions?.length || 0,
        has_questions: !!result.questions,
        first_question_id: result.questions?.[0]?.id,
        result_keys: Object.keys(result)
      });

      if (!result.questions || result.questions.length === 0) {
        console.error('❌ [useInsightStudy] Nenhuma questão encontrada:', result);
        toast({
          title: "Nenhuma questão encontrada",
          description: "Não foram encontradas questões com os filtros aplicados",
          variant: "destructive"
        });
        return;
      }

      // ✅ CORREÇÃO: Criar sessão igual aos estudos do dia
      const title = `Insight: ${state.selectedInsight.focus_name}`;
      const questionIds = result.questions.map(q => q.id);

      console.log('🎯 [useInsightStudy] Preparando criação de sessão:', {
        title,
        questionIds_length: questionIds.length,
        questionIds: questionIds.slice(0, 3), // Primeiros 3 IDs
        user_id: user.id
      });

      const session = await createSession(user.id, questionIds, title);

      console.log('📝 [useInsightStudy] Resultado da criação de sessão:', {
        session_created: !!session,
        session_id: session?.id,
        session_keys: session ? Object.keys(session) : null
      });

      if (!session) {
        console.error('❌ [useInsightStudy] Falha ao criar sessão');
        throw new Error('Erro ao criar sessão de estudo');
      }

      // Fechar dialog
      closeStudyDialog();

      console.log('🧭 [useInsightStudy] Navegando para:', `/questions/${session.id}`);

      // ✅ CORREÇÃO: Navegar para a rota correta
      navigate(`/questions/${session.id}`);

      toast({
        title: "Sessão iniciada!",
        description: `Estudando ${result.questions.length} questões sobre ${state.selectedInsight.focus_name}`,
      });

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao iniciar sessão:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível iniciar a sessão de estudos",
        variant: "destructive"
      });
    }
  }, [state.selectedInsight, user?.id, domain, createSession, navigate, toast, closeStudyDialog]);

  return {
    // Estado
    isDialogOpen: state.isDialogOpen,
    selectedInsight: state.selectedInsight,
    maxQuestions: state.maxQuestions,
    availableYears: state.availableYears,
    availableInstitutions: state.availableInstitutions,
    isLoading: state.isLoading,
    
    // Ações
    openStudyDialog,
    closeStudyDialog,
    startStudy
  };
};
