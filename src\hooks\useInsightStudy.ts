/**
 * Hook para gerenciar estudos de insights
 * Integra o InsightCard com o StudyOptionsDialog
 */

import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { filterQuestionsByTopic } from '@/utils/questionUtils';
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import type { FocusInsight } from '@/types/insights';

interface InsightStudyState {
  isDialogOpen: boolean;
  selectedInsight: FocusInsight | null;
  maxQuestions: number;
  availableYears: number[];
  availableInstitutions: { id: string; name: string }[];
  isLoading: boolean;
}

export const useInsightStudy = () => {
  const [state, setState] = useState<InsightStudyState>({
    isDialogOpen: false,
    selectedInsight: null,
    maxQuestions: 0,
    availableYears: [],
    availableInstitutions: [],
    isLoading: false
  });

  const navigate = useNavigate();
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const user = useUser();
  const { domain } = useDomain();
  const { preferences } = useStudyPreferences();

  /**
   * Abre o dialog de estudos para um insight específico
   */
  const openStudyDialog = useCallback(async (insight: FocusInsight) => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para estudar",
        variant: "destructive"
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, selectedInsight: insight }));

    try {
      console.log('🎯 [useInsightStudy] Abrindo dialog para insight:', {
        focus_name: insight.focus_name,
        focus_id: insight.focus_id,
        temperature: insight.temperature
      });

      // ✅ CORREÇÃO: Buscar questões com filtro de instituições aplicado
      const institutionIds = preferences?.target_institutions?.map(inst => inst.id);

      const result = await filterQuestionsByTopic(
        undefined, // specialty_id - deixar undefined para buscar por focus apenas
        undefined, // theme_id - deixar undefined para buscar por focus apenas
        insight.focus_id, // focus_id correto
        1000, // limite alto para contar todas
        undefined, // sem filtro de ano específico
        institutionIds, // ✅ APLICAR filtro de instituições aqui
        false, // não randomizar
        domain,
        false, // não ocultar respondidas inicialmente
        user.id
      );

      console.log('🔍 [useInsightStudy] Questões encontradas:', {
        focus_id: insight.focus_id,
        count: result.count,
        questions: result.questions?.length || 0,
        domain,
        preferences_institutions: preferences?.target_institutions?.length || 0
      });

      // Extrair anos únicos das questões
      const years = result.questions 
        ? [...new Set(result.questions.map(q => q.exam_year).filter(Boolean))].sort((a, b) => b - a)
        : [];

      // Usar instituições das preferências do usuário
      const institutions = preferences?.target_institutions || [];

      setState(prev => ({
        ...prev,
        isDialogOpen: true,
        maxQuestions: result.count,
        availableYears: years,
        availableInstitutions: institutions,
        isLoading: false
      }));

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao buscar questões:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível carregar as questões para este insight",
        variant: "destructive"
      });

      setState(prev => ({ ...prev, isLoading: false, selectedInsight: null }));
    }
  }, [user?.id, domain, preferences, toast]);

  /**
   * Fecha o dialog de estudos
   */
  const closeStudyDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      isDialogOpen: false,
      selectedInsight: null,
      maxQuestions: 0,
      availableYears: [],
      availableInstitutions: []
    }));
  }, []);

  /**
   * Inicia uma sessão de estudos com as configurações escolhidas
   */
  const startStudy = useCallback(async (
    quantity: number, 
    hideAnswered?: boolean, 
    institutionIds?: string[]
  ) => {
    if (!state.selectedInsight || !user?.id) return;

    try {
      console.log('🚀 [useInsightStudy] Iniciando sessão de estudos:', {
        insight: state.selectedInsight.focus_name,
        quantity,
        hideAnswered,
        institutionIds: institutionIds?.length || 'todas'
      });

      // ✅ CORREÇÃO: Buscar questões com os filtros aplicados
      const result = await filterQuestionsByTopic(
        undefined, // specialty_id - deixar undefined
        undefined, // theme_id - deixar undefined
        state.selectedInsight.focus_id, // focus_id correto
        quantity,
        undefined, // anos
        institutionIds, // filtro de instituições aplicado aqui
        true, // randomizar
        domain,
        hideAnswered,
        user.id
      );

      if (!result.questions || result.questions.length === 0) {
        toast({
          title: "Nenhuma questão encontrada",
          description: "Não foram encontradas questões com os filtros aplicados",
          variant: "destructive"
        });
        return;
      }

      // Criar sessão de estudos
      const sessionId = await createSession({
        questions: result.questions,
        title: `Insight: ${state.selectedInsight.focus_name}`,
        description: `Estudo baseado no insight do dia - ${state.selectedInsight.config.label}`,
        metadata: {
          source: 'insight',
          insight_focus_id: state.selectedInsight.focus_id,
          insight_temperature: state.selectedInsight.temperature,
          total_questions: result.questions.length
        }
      });

      // Fechar dialog
      closeStudyDialog();

      // Navegar para a sessão
      navigate(`/study/${sessionId}`);

      toast({
        title: "Sessão iniciada!",
        description: `Estudando ${result.questions.length} questões sobre ${state.selectedInsight.focus_name}`,
      });

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao iniciar sessão:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível iniciar a sessão de estudos",
        variant: "destructive"
      });
    }
  }, [state.selectedInsight, user?.id, domain, createSession, navigate, toast, closeStudyDialog]);

  return {
    // Estado
    isDialogOpen: state.isDialogOpen,
    selectedInsight: state.selectedInsight,
    maxQuestions: state.maxQuestions,
    availableYears: state.availableYears,
    availableInstitutions: state.availableInstitutions,
    isLoading: state.isLoading,
    
    // Ações
    openStudyDialog,
    closeStudyDialog,
    startStudy
  };
};
