/**
 * Hook para gerenciar estudos de insights
 * Integra o InsightCard com o StudyOptionsDialog
 */

import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { getQuestionsOptimized } from '@/utils/questionUtils';
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import type { FocusInsight } from '@/types/insights';

// ✅ CACHE AGRESSIVO: Cache global para insights
const INSIGHT_CACHE = new Map<string, { count: number; data: any[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

const getCachedInsightData = (cacheKey: string) => {
  const cached = INSIGHT_CACHE.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached;
  }
  return null;
};

const setCachedInsightData = (cacheKey: string, data: { count: number; data: any[] }) => {
  INSIGHT_CACHE.set(cacheKey, { ...data, timestamp: Date.now() });
};

interface InsightStudyState {
  isDialogOpen: boolean;
  selectedInsight: FocusInsight | null;
  maxQuestions: number;
  availableYears: number[];
  availableInstitutions: { id: string; name: string }[];
  isLoading: boolean;
}

export const useInsightStudy = () => {
  const [state, setState] = useState<InsightStudyState>({
    isDialogOpen: false,
    selectedInsight: null,
    maxQuestions: 0,
    availableYears: [],
    availableInstitutions: [],
    isLoading: false
  });

  const navigate = useNavigate();
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const user = useUser();
  const { domain } = useDomain();
  const { preferences } = useStudyPreferences();

  /**
   * Abre o dialog de estudos para um insight específico
   */
  const openStudyDialog = useCallback(async (insight: FocusInsight) => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para estudar",
        variant: "destructive"
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, selectedInsight: insight }));

    try {
      // ✅ LIMPEZA: Remover log desnecessário

      // ✅ CACHE AGRESSIVO: Verificar cache primeiro
      const institutionIds = preferences?.target_institutions?.map(inst => inst.id);
      const cacheKey = `insight-${insight.focus_id}-${domain}-${user.id}-${institutionIds?.join(',')}`;
      const cachedData = getCachedInsightData(cacheKey);

      let result;
      if (cachedData) {
        result = cachedData;
      } else {
        result = await getQuestionsOptimized(
          undefined, // specialty_id - deixar undefined para buscar por focus apenas
          undefined, // theme_id - deixar undefined para buscar por focus apenas
          insight.focus_id, // focus_id correto
          1000, // limite alto para contar todas
          undefined, // sem filtro de ano específico
          institutionIds, // ✅ APLICAR filtro de instituições aqui
          false, // não randomizar
          domain,
          false, // não ocultar respondidas inicialmente
          user.id,
          true // ✅ OTIMIZAÇÃO: Retornar apenas IDs
        );
        setCachedInsightData(cacheKey, result);
      }

      // ✅ LIMPEZA: Manter apenas log de erro crítico
      if (result.count === 0) {
        console.warn('⚠️ [useInsightStudy] Nenhuma questão encontrada para:', insight.focus_name);
      }

      // ✅ OTIMIZAÇÃO: Usar valores padrão em vez de extrair dos dados
      const years = [2020, 2021, 2022, 2023, 2024, 2025];
      const institutions = preferences?.target_institutions || [];

      setState(prev => ({
        ...prev,
        isDialogOpen: true,
        maxQuestions: result.count,
        availableYears: years,
        availableInstitutions: institutions,
        isLoading: false
      }));

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao buscar questões:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível carregar as questões para este insight",
        variant: "destructive"
      });

      setState(prev => ({ ...prev, isLoading: false, selectedInsight: null }));
    }
  }, [user?.id, domain, preferences, toast]);

  /**
   * Fecha o dialog de estudos
   */
  const closeStudyDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      isDialogOpen: false,
      selectedInsight: null,
      maxQuestions: 0,
      availableYears: [],
      availableInstitutions: []
    }));
  }, []);

  /**
   * Inicia uma sessão de estudos com as configurações escolhidas
   */
  const startStudy = useCallback(async (
    quantity: number, 
    hideAnswered?: boolean, 
    institutionIds?: string[]
  ) => {
    if (!state.selectedInsight || !user?.id) return;

    try {
      // ✅ LIMPEZA: Remover log desnecessário

      // ✅ OTIMIZAÇÃO: Buscar questões com filtros aplicados usando cache
      const result = await getQuestionsOptimized(
        undefined, // specialty_id - deixar undefined
        undefined, // theme_id - deixar undefined
        state.selectedInsight.focus_id, // focus_id correto
        quantity,
        undefined, // anos
        institutionIds, // filtro de instituições aplicado aqui
        true, // randomizar
        domain,
        hideAnswered,
        user.id,
        true // ✅ OTIMIZAÇÃO: Retornar apenas IDs
      );

      // ✅ LIMPEZA: Remover log desnecessário

      if (!result.data || result.data.length === 0) {
        console.error('❌ [useInsightStudy] Nenhuma questão encontrada:', result);
        toast({
          title: "Nenhuma questão encontrada",
          description: "Não foram encontradas questões com os filtros aplicados",
          variant: "destructive"
        });
        return;
      }

      // ✅ CORREÇÃO: Criar sessão igual aos estudos do dia
      const title = `Insight: ${state.selectedInsight.focus_name}`;
      const questionIds = result.data.map((q: any) => typeof q === 'string' ? q : q.id);

      // ✅ LIMPEZA: Remover logs desnecessários

      const session = await createSession(user.id, questionIds, title);

      if (!session) {
        console.error('❌ [useInsightStudy] Falha ao criar sessão');
        throw new Error('Erro ao criar sessão de estudo');
      }

      // Fechar dialog
      closeStudyDialog();

      // ✅ CORREÇÃO: Navegar para a rota correta
      navigate(`/questions/${session.id}`);

      toast({
        title: "Sessão iniciada!",
        description: `Estudando ${result.data.length} questões sobre ${state.selectedInsight.focus_name}`,
      });

    } catch (error) {
      console.error('❌ [useInsightStudy] Erro ao iniciar sessão:', error);
      
      toast({
        title: "Erro",
        description: "Não foi possível iniciar a sessão de estudos",
        variant: "destructive"
      });
    }
  }, [state.selectedInsight, user?.id, domain, createSession, navigate, toast, closeStudyDialog]);

  return {
    // Estado
    isDialogOpen: state.isDialogOpen,
    selectedInsight: state.selectedInsight,
    maxQuestions: state.maxQuestions,
    availableYears: state.availableYears,
    availableInstitutions: state.availableInstitutions,
    isLoading: state.isLoading,
    
    // Ações
    openStudyDialog,
    closeStudyDialog,
    startStudy
  };
};
