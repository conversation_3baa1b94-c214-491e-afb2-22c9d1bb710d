
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CommunityHierarchyTree } from "@/components/collaborate/flashcards/hierarchy/CommunityHierarchyTree";
import { useCollaborativeFlashcards } from "@/hooks/useCollaborativeFlashcards";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";

const SORT_OPTIONS = [
  { value: "recent", label: "Mais recentes" },
  { value: "oldest", label: "Mais antigos" },
  { value: "likes", label: "Mais curtidos" },
  { value: "dislikes_desc", label: "Mais descurtidos" },
  { value: "imported", label: "Mais importados" },
];

const CollaborativeFlashcards = () => {
  const [filters, setFilters] = useState({});
  const [sortBy, setSortBy] = useState("recent");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [isAddingToDeck, setIsAddingToDeck] = useState(false);
  const [activeTab] = useState<"tree">("tree");
  const [showImported, setShowImported] = useState(false);

  const {
    flashcards,
    isLoading,
    totalCount,
    totalPages,
    loadFlashcards,
    importedCardIds,
    refreshImportedCards,
    refreshImportedCounts,
    likeDislikeCard
  } = useCollaborativeFlashcards();

  useEffect(() => {
    loadFlashcards(currentPage, { ...filters, showImported }, sortBy, 50);
  }, [currentPage, filters, sortBy, showImported, loadFlashcards]);

  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev =>
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const handleLikeCard = async (cardId: string) => {
    console.log("🔄 [CollaborativeFlashcards] Liking card:", cardId);
    const res = await likeDislikeCard(cardId, "like");
    if (res?.error) {
      console.error("❌ [CollaborativeFlashcards] Error liking card:", res.error);
      toast.error("Não foi possível dar like neste card");
    } else {
      console.log("✅ [CollaborativeFlashcards] Successfully liked card");
      toast("Obrigado pelo feedback!");
    }
  };

  const handleDislikeCard = async (cardId: string) => {
    console.log("🔄 [CollaborativeFlashcards] Disliking card:", cardId);
    const res = await likeDislikeCard(cardId, "dislike");
    if (res?.error) {
      console.error("❌ [CollaborativeFlashcards] Error disliking card:", res.error);
      toast.error("Não foi possível dar dislike neste card");
    } else {
      console.log("✅ [CollaborativeFlashcards] Successfully disliked card");
      toast("Obrigado pelo feedback!");
    }
  };

  const addSelectedToMyDeck = async (cards?: string[]) => {
    const cardsToAdd = cards || selectedCards;

    if (cardsToAdd.length === 0) {
      toast.error("Selecione pelo menos um flashcard");
      return;
    }

    try {

      setIsAddingToDeck(true);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não autenticado");

      const { error: fnError } = await supabase
        .rpc('add_cards_to_user_deck', {
          p_user_id: user.id,
          p_card_ids: cardsToAdd
        });

      if (fnError) throw fnError;

      if (refreshImportedCards) {
        await refreshImportedCards();
      }

      if (refreshImportedCounts) {
        await refreshImportedCounts(cardsToAdd);
      }

      setSelectedCards([]);

    } catch (error: any) {

    } finally {
      setIsAddingToDeck(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in">
        <div className="relative">
          <div className="inline-block transform -rotate-2 mb-4">
            <div className="bg-hackathon-yellow border-2 border-black px-4 py-1 text-black font-bold tracking-wide text-sm shadow-card-sm">
              CARDS COMPARTILHADOS
            </div>
          </div>

          <div>
            <h1 className="text-5xl font-black leading-none mb-2">
              <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
                Cards da Comunidade
              </span>
            </h1>
            <p className="text-xl text-gray-700 max-w-lg">
              Explore e estude flashcards criados por outros estudantes
            </p>
          </div>
        </div>

        {/* Apenas navegação hierárquica + Filtro de ordenação */}
        <Tabs defaultValue="tree" onValueChange={() => {}}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <TabsList>
                <TabsTrigger value="tree">Navegação Hierárquica</TabsTrigger>
              </TabsList>
              <Select
                value={sortBy}
                onValueChange={setSortBy}
              >
                <SelectTrigger className="w-[220px] ml-2">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  {SORT_OPTIONS.map(option => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 font-medium text-sm">
                <input
                  type="checkbox"
                  checked={showImported}
                  onChange={e => setShowImported(e.target.checked)}
                />
                Mostrar importados
              </label>
            </div>
          </div>

          <TabsContent value="tree" className="mt-6">
            <CommunityHierarchyTree
              flashcards={flashcards}
              onImportCards={addSelectedToMyDeck}
              isLoading={isLoading}
              importedCardIds={importedCardIds || []}
              onLikeCard={handleLikeCard}
              onDislikeCard={handleDislikeCard}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CollaborativeFlashcards;
