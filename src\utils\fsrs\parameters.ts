// FSRS (Free Spaced Repetition Scheduler) version 5 parameters
export const FSRS_WEIGHTS = {
  w1: 1.0,     // Initial stability for new cards
  w2: 1.0,     // Initial difficulty for new cards
  w3: 1.0,     // Memory strength impact
  w4: 1.0,     // Minimum stability - mantém para não prejudicar a recuperação de estabilidade
  w5: 0.15,    // Again response multiplier (não estamos focando no "Again" neste ajuste)
  w6: 0.2,     // Hard response multiplier - reduzido para desacelerar o crescimento da estabilidade em respostas difíceis
  w7: 1.5,     // Good response multiplier - aumentou um pouco para recompensar o crescimento da estabilidade de maneira proporcional
  w8: 10.0,     // Easy response multiplier - aumentado para um crescimento expressivo da estabilidade em resposta fácil
  w9: 0.9,     // Default difficulty - levemente aumentado para dificuldade padrão
  w10: 0.05,   // Difficulty adjustment rate - ajustado para uma diferenciação mais clara
  w11: 1.01,   // Maximum difficulty increment - reduzido para evitar que dificuldade aumente muito rapidamente
  w12: 1.0,    // Minimum stability after fail
  w13: 0.2,    // Forgetting factor
  w14: 0.05,   // Stability decay rate
  w15: 0.95,   // Target retention - aumentado para melhorar a retenção e permitir intervalos mais longos após respostas positivas
  w16: 0.1,    // Difficulty penalty
  w17: 1.0,    // Stability boost
  w18: 1.0,    // Initial stability modifier
  w19: 1.0     // Initial difficulty modifier
};

// Ajuste específico para resposta "Again" (não alterado neste ajuste)
export const DEFAULT_INITIAL_VALUES = {
  stability: FSRS_WEIGHTS.w4,
  difficulty: FSRS_WEIGHTS.w9,
  retrievability: FSRS_WEIGHTS.w15
};
