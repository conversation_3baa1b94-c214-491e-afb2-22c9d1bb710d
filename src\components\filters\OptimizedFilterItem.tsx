import React, { memo, useCallback } from 'react';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useFilterItemCounts } from '@/hooks/useOptimizedFilterSelection';

interface OptimizedFilterItemProps {
  item: {
    id: string;
    name: string;
    type: string;
  };
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  hasChildren: boolean;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, type: string) => void;
  selectedFilters: any; // Para passar para o hook de contagem
  showCount?: boolean;
}

const OptimizedFilterItemComponent = ({
  item,
  level,
  isExpanded,
  isSelected,
  hasChildren,
  onToggleExpand,
  onToggleSelect,
  selectedFilters,
  showCount = true
}: OptimizedFilterItemProps) => {
  
  // Hook para buscar contagem individual (com cache agressivo)
  const { data: itemCount = 0 } = useFilterItemCounts(
    selectedFilters,
    item.id,
    item.type as 'specialty' | 'theme' | 'focus' | 'location'
  );

  const handleSelect = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    // Chamada imediata - sem delay
    onToggleSelect(item.id, item.type);
  }, [item.id, item.type, onToggleSelect]);

  const handleExpand = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onToggleExpand(item.id);
  }, [item.id, onToggleExpand]);

  // Get colors based on the hierarchy level
  const getBorderColor = () => {
    switch (level) {
      case 0: return 'border-blue-200';
      case 1: return 'border-green-200';
      case 2: return 'border-amber-200';
      default: return 'border-gray-200';
    }
  };

  const getHoverColor = () => {
    switch (level) {
      case 0: return 'hover:bg-blue-50/60';
      case 1: return 'hover:bg-green-50/60';
      case 2: return 'hover:bg-amber-50/60';
      default: return 'hover:bg-gray-50/60';
    }
  };

  const getSelectedColor = () => {
    switch (level) {
      case 0: return 'bg-blue-50';
      case 1: return 'bg-green-50';
      case 2: return 'bg-amber-50';
      default: return 'bg-[#FEF7CD]';
    }
  };

  const getBadgeColor = () => {
    if (isSelected) return 'bg-[#FF6B00] text-white border-none';
    
    switch (level) {
      case 0: return 'bg-blue-100 text-blue-700 border-blue-200';
      case 1: return 'bg-green-100 text-green-700 border-green-200';
      case 2: return 'bg-amber-100 text-amber-700 border-amber-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <div 
      className={cn(
        'flex items-center justify-between p-2 rounded-lg border transition-all duration-200',
        getBorderColor(),
        getHoverColor(),
        isSelected && getSelectedColor(),
        level > 0 && 'sm:ml-6 ml-3'
      )}
      data-filter-item="true"
    >
      <div className="flex items-center gap-2 flex-1">
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6 hover:bg-transparent"
            onClick={handleExpand}
            type="button"
          >
            <ChevronRight 
              className={cn(
                "h-4 w-4 transition-transform duration-200 text-black",
                isExpanded && "rotate-90"
              )}
            />
          </Button>
        )}
        
        <div 
          className="flex items-center gap-3 flex-1 cursor-pointer"
          onClick={handleSelect}
        >
          {/* Checkbox otimizado - mudança visual instantânea */}
          <div 
            className={cn(
              "w-5 h-5 rounded border-2 transition-all duration-150 flex items-center justify-center",
              isSelected 
                ? "bg-[#FF6B00] border-black text-white" 
                : "border-black hover:border-[#FF6B00]",
            )}
          >
            {isSelected && (
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                className="w-3.5 h-3.5"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            )}
          </div>
          
          {/* Nome do item */}
          <span className={cn(
            "text-sm transition-colors duration-150 overflow-hidden text-ellipsis",
            isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
          )}>
            {item.name}
          </span>
        </div>
      </div>

      {/* Badge com contagem - atualização em background */}
      {showCount && (
        <Badge 
          variant="secondary"
          className={cn(
            "min-w-[3rem] justify-center transition-all duration-150",
            getBadgeColor()
          )}
        >
          {itemCount}
        </Badge>
      )}
    </div>
  );
};

// Memoizar o componente para evitar re-renders desnecessários
export const OptimizedFilterItem = memo(OptimizedFilterItemComponent, (prevProps, nextProps) => {
  // Comparação customizada para otimizar re-renders
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isExpanded === nextProps.isExpanded &&
    prevProps.level === nextProps.level &&
    prevProps.hasChildren === nextProps.hasChildren
  );
});

OptimizedFilterItem.displayName = 'OptimizedFilterItem';
