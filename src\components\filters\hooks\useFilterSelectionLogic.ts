import { useState, useCallback } from 'react';
import type { SelectedFilters } from '@/types/question';

export const useFilterSelectionLogic = (
  initialFilters: SelectedFilters,
  onFiltersChange: (filters: SelectedFilters) => void
) => {
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>(initialFilters);

  const handleFilterToggle = useCallback((id: string, type: "specialty" | "theme" | "focus") => {
    setSelectedFilters(prev => {
      const filterKey = `${type}s` as keyof SelectedFilters;
      const currentFilters = [...(prev[filterKey] || [])];
      const isSelected = currentFilters.includes(id);
      
      const newFilters = {
        ...prev,
        [filterKey]: isSelected 
          ? currentFilters.filter(item => item !== id)
          : [...currentFilters, id]
      };

      onFiltersChange(newFilters);
      return newFilters;
    });
  }, [onFiltersChange]);

  return {
    selectedFilters,
    handleFilterToggle,
    setSelectedFilters
  };
};
