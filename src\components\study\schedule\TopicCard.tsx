import React, { useState } from 'react';
import { motion } from "framer-motion";
import { BookO<PERSON>, Clock, CheckCircle, Edit, Trash2, Calendar, Target, Building2 } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';
import { Badge } from '@/components/ui/badge';
import { TopicDetailsDialog } from './TopicDetailsDialog';
import { Button } from '@/components/ui/button';
import { InstitutionsBadgeDialog } from './InstitutionsBadgeDialog';

interface TopicCardProps extends StudyTopic {
  onMarkStudied?: (topicId: string) => void;
  onDelete?: (topic: StudyTopic) => void;
  onEdit?: (topic: StudyTopic) => void;
}

export const TopicCard: React.FC<TopicCardProps> = ({
  specialty,
  theme,
  focus,
  difficulty,
  activity,
  startTime,
  duration,
  study_status,
  id,
  next_revision_date,
  revision_number,
  onMarkStudied,
  onDelete,
  onEdit,
  institutions,
  focusPrevalence,
  ...rest
}) => {
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [institutionsDialogOpen, setInstitutionsDialogOpen] = useState(false);

  // 🔍 CRITICAL DEBUG: Verificar props recebidas
  console.log('🔍 [CRITICAL] TopicCard props:', {
    focus: focus.substring(0, 30) + '...',
    institutions,
    focusPrevalence,
    hasInstitutions: !!(institutions && institutions.length > 0)
  });
  
  const getDifficultyConfig = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil':
        return {
          bg: 'bg-gradient-to-r from-emerald-50 to-green-50',
          text: 'text-emerald-700',
          border: 'border-emerald-200',
          icon: '🟢'
        };
      case 'Médio':
        return {
          bg: 'bg-gradient-to-r from-amber-50 to-yellow-50',
          text: 'text-amber-700',
          border: 'border-amber-200',
          icon: '🟡'
        };
      case 'Difícil':
        return {
          bg: 'bg-gradient-to-r from-rose-50 to-red-50',
          text: 'text-rose-700',
          border: 'border-rose-200',
          icon: '🔴'
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-slate-50 to-gray-50',
          text: 'text-slate-700',
          border: 'border-slate-200',
          icon: '⚪'
        };
    }
  };

  const getTitle = () => {
    if (focus && focus !== "Geral" && focus !== "N/A" && focus !== "") {
      return focus;
    } else if (theme && theme !== "") {
      return theme;
    } else if (specialty && specialty !== "") {
      return specialty;
    } else {
      return "Estudo";
    }
  };

  const formatTime = (time: string) => {
    if (!time) return 'Horário não definido';
    
    if (time.includes('AM') || time.includes('PM') || time.includes(':')) {
      return time;
    }
    
    try {
      const timeDate = new Date(`2000-01-01T${time}`);
      return timeDate.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch (error) {
      return time;
    }
  };

  const handleCardClick = () => {
    setDetailsDialogOpen(true);
  };
  
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (onEdit) {
      const topic: StudyTopic = {
        id,
        specialty,
        theme,
        focus,
        difficulty,
        activity,
        startTime,
        duration,
        study_status,
        next_revision_date,
        revision_number,
        ...rest
      };
      console.log('🔧 [TopicCard] Editing:', specialty, '>', theme, '>', focus);
      onEdit(topic);
    }
  };
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      const topic: StudyTopic = {
        id,
        specialty,
        theme,
        focus,
        difficulty,
        activity,
        startTime,
        duration,
        study_status,
        next_revision_date,
        revision_number,
        ...rest
      };
      onDelete(topic);
    }
  };

  const topic: StudyTopic = {
    id,
    specialty,
    theme,
    focus,
    difficulty,
    activity,
    startTime,
    duration,
    study_status,
    next_revision_date,
    revision_number,
    ...rest
  };

  const difficultyConfig = getDifficultyConfig(difficulty);

  return (
    <>
      <motion.div
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 cursor-pointer group ${
          study_status === 'completed'
            ? 'bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100 border-emerald-300 shadow-emerald-100'
            : 'bg-gradient-to-br from-white via-slate-50 to-blue-50 border-slate-200 hover:border-blue-300'
        } shadow-lg hover:shadow-xl hover:shadow-blue-100/50`}
        onClick={handleCardClick}
      >
        <div className={`absolute top-0 left-0 right-0 h-1 ${
          study_status === 'completed'
            ? 'bg-gradient-to-r from-emerald-400 to-green-500'
            : 'bg-gradient-to-r from-blue-400 to-indigo-500'
        }`} />

        <div className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <div className={`p-2 rounded-lg ${
                  study_status === 'completed'
                    ? 'bg-emerald-100 border border-emerald-200'
                    : 'bg-blue-100 border border-blue-200'
                } transition-colors duration-200`}>
                  <BookOpen className={`h-4 w-4 ${
                    study_status === 'completed' ? 'text-emerald-600' : 'text-blue-600'
                  } flex-shrink-0`} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-slate-800 text-sm leading-tight break-words line-clamp-2 group-hover:text-blue-700 transition-colors">
                    {getTitle()}
                  </h3>
                  <div className="mt-1 text-xs text-slate-500 font-medium line-clamp-1 flex items-center gap-1">
                    <Target className="h-3 w-3 flex-shrink-0" />
                    {specialty} {theme && theme !== getTitle() && theme !== "Geral" ? `→ ${theme}` : ""}
                  </div>
                </div>
                {study_status === 'completed' && (
                  <div className="flex-shrink-0">
                    <div className="p-1 bg-emerald-100 rounded-full border border-emerald-200">
                      <CheckCircle className="h-4 w-4 text-emerald-600" />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-3">
            <Badge
              variant="outline"
              className={`text-xs font-semibold px-2.5 py-1 rounded-full border-2 ${difficultyConfig.bg} ${difficultyConfig.text} ${difficultyConfig.border} transition-all hover:scale-105`}
            >
              <span className="mr-1">{difficultyConfig.icon}</span>
              {difficulty}
            </Badge>

            <Badge
              variant="outline"
              className="text-xs font-semibold px-2.5 py-1 rounded-full border-2 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 transition-all hover:scale-105"
            >
              <Clock className="h-3 w-3 mr-1.5 flex-shrink-0" />
              {formatTime(startTime)}
            </Badge>

            <Badge
              variant="outline"
              className="text-xs font-semibold px-2.5 py-1 rounded-full border-2 bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 border-purple-200 transition-all hover:scale-105"
            >
              <Calendar className="h-3 w-3 mr-1.5 flex-shrink-0" />
              {duration}
            </Badge>

            {/* ✅ NOVO: Badge de prevalência */}
            {focusPrevalence && (
              <Badge
                variant="outline"
                className="text-xs font-semibold px-2.5 py-1 rounded-full border-2 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 transition-all hover:scale-105"
              >
                <Target className="h-3 w-3 mr-1.5 flex-shrink-0" />
                {focusPrevalence}% relevância
              </Badge>
            )}

            {/* ✅ NOVO: Badge de instituições */}
            {(() => {
              const shouldShow = institutions && institutions.length > 0;
              console.log('🔍 [CRITICAL] Badge condition:', { shouldShow, institutions, length: institutions?.length });
              return shouldShow;
            })() && (
              <Badge
                variant="outline"
                className="text-xs font-semibold px-2.5 py-1 rounded-full border-2 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 transition-all hover:scale-105 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setInstitutionsDialogOpen(true);
                }}
              >
                <Building2 className="h-3 w-3 mr-1.5 flex-shrink-0" />
                {institutions.length} {institutions.length === 1 ? 'Instituição' : 'Instituições'}
              </Badge>
            )}
          </div>

          <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
            <div className="text-xs text-slate-600 font-medium break-words line-clamp-2 leading-relaxed">
              {activity}
            </div>
          </div>

          {(onEdit || onDelete) && (
            <div className="mt-4 pt-3 border-t border-slate-200 flex justify-end gap-2">
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-xs font-semibold text-blue-600 hover:text-blue-700 hover:bg-blue-50 border border-blue-200 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md"
                  onClick={handleEdit}
                >
                  <Edit className="h-3.5 w-3.5 mr-1.5" />
                  Editar
                </Button>
              )}

              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-xs font-semibold text-rose-600 hover:text-rose-700 hover:bg-rose-50 border border-rose-200 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md"
                  onClick={handleDelete}
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                  Remover
                </Button>
              )}
            </div>
          )}
        </div>
      </motion.div>
      
      <TopicDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        topic={topic}
        onMarkStudied={onMarkStudied}
        onDelete={onDelete}
      />

      {/* ✅ NOVO: Dialog de instituições */}
      <InstitutionsBadgeDialog
        open={institutionsDialogOpen}
        onOpenChange={setInstitutionsDialogOpen}
        focus={focus}
        institutions={institutions || []}
        focusPrevalence={focusPrevalence}
      />
    </>
  );
};
