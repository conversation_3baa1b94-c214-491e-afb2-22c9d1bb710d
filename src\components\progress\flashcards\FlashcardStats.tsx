
import { <PERSON>, <PERSON>, Target } from "lucide-react";
import StatCard from "@/components/StatCard";

interface FlashcardStatsProps {
  totalCards: number;
  correctAnswers: number;
  timeSpent: number;
}

export const FlashcardStats = ({ totalCards, correctAnswers, timeSpent }: FlashcardStatsProps) => {
  const accuracy = totalCards > 0 ? Math.round((correctAnswers / totalCards) * 100) : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <StatCard
        title="Total de Flashcards"
        value={totalCards}
        icon={<Brain className="w-6 h-6 text-primary" />}
        className="border border-primary/20"
      />
      <StatCard
        title="Taxa de Acerto"
        value={`${accuracy}%`}
        icon={<Target className="w-6 h-6 text-green-600" />}
        className="border border-green-100"
      />
      <StatCard
        title="Tempo Total de Estudo"
        value={`${timeSpent} min`}
        icon={<Clock className="w-6 h-6 text-blue-600" />}
        className="border border-blue-100"
      />
    </div>
  );
};
