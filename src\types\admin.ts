
export interface QuestionImprovementSuggestion {
  themeId: string;
  themeName: string;
  focusId: string;
  focusName: string;
  confidence: number;
}

export interface AdminCategory {
  id: string;
  name: string;
  type: string;
  parent_id?: string;
}

export interface QuestionHierarchyStats {
  totalQuestions: number;
  questionsWithoutTheme: number;
  questionsWithoutFocus: number;
  completedImprovements: number;
}

export interface BatchAnalysisItem {
  questionId: string;
  questionStatement: string;
  questionAlternatives: string[];
  suggestion: QuestionImprovementSuggestion | null;
  isApplied: boolean;
  isProcessing: boolean;
  isError: boolean;
  errorMessage?: string;
}
