
import { useEffect, useState } from "react";
import { FlashcardDisplay } from "./FlashcardDisplay";
import type { FlashcardWithHierarchy } from "@/types/flashcardCollaborate";

interface FlashcardReviewProps {
  session: {
    id: string;
    cards: FlashcardWithHierarchy[];
    status: string;
  };
  onComplete: () => void;
  onUpdateSession: (session: any) => void;
}

export const FlashcardReview = ({ 
  session,
  onComplete,
  onUpdateSession 
}: FlashcardReviewProps) => {
  const [currentCard, setCurrentCard] = useState<FlashcardWithHierarchy | null>(null);
  const [isFlipped, setIsFlipped] = useState(false);

  useEffect(() => {
    console.log('📝 [FlashcardReview] Session cards:', session.cards);
    if (session.cards.length > 0) {
      console.log('🎴 [FlashcardReview] Setting current card:', session.cards[0]);
      setCurrentCard(session.cards[0]);
    }
  }, [session]);

  return (
    <div className="space-y-6">
      {currentCard && (
        <>
          <FlashcardDisplay
            front={currentCard.front}
            back={currentCard.back}
            frontImage={currentCard.front_image}
            backImage={currentCard.back_image}
            isFlipped={isFlipped}
            onFlip={() => setIsFlipped(!isFlipped)}
            createdAt={currentCard.created_at}
            userId={currentCard.user_id}
            currentCard={currentCard}
          />

          <div className="mt-4 flex justify-center gap-4">
            <button 
              onClick={onComplete} 
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors"
            >
              Complete Review
            </button>
            <button 
              onClick={() => onUpdateSession(session)} 
              className="px-4 py-2 bg-secondary text-white rounded hover:bg-secondary/90 transition-colors"
            >
              Next Card
            </button>
          </div>
        </>
      )}
    </div>
  );
};
