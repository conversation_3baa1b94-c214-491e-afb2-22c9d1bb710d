import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Lightbulb,
  TrendingUp,
  Clock,
  BookOpen,
  ChevronDown,
  ChevronUp,
  Sparkles
} from 'lucide-react';
import InsightTooltip from './InsightTooltip';
import type { FocusInsight } from '@/types/insights';

interface InsightCardProps {
  insight: FocusInsight | null;
  isLoading?: boolean;
  className?: string;
  compact?: boolean;
}

const InsightCard: React.FC<InsightCardProps> = ({ 
  insight, 
  isLoading = false, 
  className = '',
  compact = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-3 border border-purple-200 ${className}`}>
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-br from-purple-400 to-blue-500 p-2 rounded-full animate-pulse">
            <Lightbulb className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded animate-pulse mb-1"></div>
            <div className="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  // No insight available
  if (!insight) {
    return (
      <div className={`bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-3 border border-gray-200 ${className}`}>
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-br from-gray-400 to-gray-500 p-2 rounded-full">
            <Lightbulb className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-600">
              Nenhum insight disponível
            </div>
            <div className="text-xs text-gray-500">
              Aguardando dados dos estudos
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { config } = insight;

  const cardContent = (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`bg-gradient-to-br ${config.bgColor} to-white rounded-lg border ${config.borderColor} shadow-sm hover:shadow-md transition-all duration-300 ${className}`}
    >
      {/* Header */}
      <div className="p-3">
        <div className="flex items-center gap-3">
          {/* Icon */}
          <div className={`bg-gradient-to-br ${config.bgColor.replace('50', '400')} ${config.bgColor.replace('50', '600')} p-2 rounded-full`}>
            <Lightbulb className="h-4 w-4 text-white" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className={`text-xs font-bold px-2 py-0.5 rounded-full ${config.bgColor} ${config.textColor} border ${config.borderColor}`}>
                {config.icon} {config.label}
              </span>
              
              {!compact && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <TrendingUp className="h-3 w-3" />
                  <span>{insight.frequency}x</span>
                  <Clock className="h-3 w-3 ml-1" />
                  <span>{insight.years_since_last < 999 ? `${insight.years_since_last}a` : 'nunca'}</span>
                </div>
              )}
            </div>

            <div className="text-sm font-medium text-gray-800 truncate">
              {insight.focus_name}
            </div>
            
            {!compact && (
              <div className="text-xs text-gray-600 truncate">
                {insight.specialty_name} • {insight.theme_name}
              </div>
            )}
          </div>

          {/* Expand button */}
          {!compact && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 rounded-full hover:bg-white/50 transition-colors"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </button>
          )}
        </div>

        {/* Compact stats */}
        {compact && (
          <div className="flex items-center gap-3 mt-2 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              <span>{insight.frequency} ocorrências</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>
                {insight.years_since_last < 999 
                  ? `há ${insight.years_since_last} anos` 
                  : 'nunca registrada'
                }
              </span>
            </div>
            <div className="flex items-center gap-1">
              <BookOpen className="h-3 w-3" />
              <span>{insight.total_questions} questões</span>
            </div>
          </div>
        )}
      </div>

      {/* Expanded content */}
      <AnimatePresence>
        {isExpanded && !compact && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-3 pb-3 border-t border-gray-200/50">
              <div className="pt-3">
                {/* Stats */}
                <div className="grid grid-cols-3 gap-3 mb-3">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-800">
                      {insight.frequency}
                    </div>
                    <div className="text-xs text-gray-600">
                      Ocorrências
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-800">
                      {insight.years_since_last < 999 ? insight.years_since_last : '∞'}
                    </div>
                    <div className="text-xs text-gray-600">
                      Anos atrás
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-800">
                      {insight.total_questions}
                    </div>
                    <div className="text-xs text-gray-600">
                      Questões
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="text-xs text-gray-700 leading-relaxed">
                  {insight.config.description}
                </div>

                {/* Action hint */}
                <div className="mt-3 flex items-center gap-2 text-xs text-purple-600">
                  <Sparkles className="h-3 w-3" />
                  <span>Clique em "Estudar" para incluir este foco no seu cronograma</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  // Wrap with tooltip if not compact
  if (!compact && insight) {
    return (
      <InsightTooltip insight={insight} side="top">
        {cardContent}
      </InsightTooltip>
    );
  }

  return cardContent;
};

export default InsightCard;
