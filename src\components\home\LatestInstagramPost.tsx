import React from "react";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Instagram } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const LatestInstagramPost = () => {
  const { data: latestPost } = useQuery({
    queryKey: ['latest-instagram-post'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_site_instagram_posts')
        .select('title, link')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) throw error;
      return data;
    },
  });

  if (!latestPost) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onClick={() => window.open(latestPost.link, '_blank')}
      className="mx-auto px-4 py-2 max-w-fit bg-accent-blue/30 backdrop-blur-sm 
        rounded-full shadow-sm hover:shadow-md transition-all cursor-pointer group flex items-center gap-2"
    >
      <Instagram className="w-4 h-4 text-primary/70" />
      <span className="text-sm text-gray-600 group-hover:text-primary transition-colors">
        Última postagem: {latestPost.title}
      </span>
    </motion.div>
  );
};