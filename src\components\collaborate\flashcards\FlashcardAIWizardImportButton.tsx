
import { Button } from "@/components/ui/button";
import React from "react";

interface FlashcardAIWizardImportButtonProps {
  imported: boolean;
  importing: boolean;
  onClick: () => void;
}

export function FlashcardAIWizardImportButton({
  imported,
  importing,
  onClick
}: FlashcardAIWizardImportButtonProps) {
  return imported ? (
    <div className="mt-4 min-w-[110px] font-semibold px-4 py-2 rounded bg-green-600 text-white border-green-700 text-center">
      Importado
    </div>
  ) : (
    <Button
      size="sm"
      className="mt-4 min-w-[110px] font-semibold px-4 rounded bg-blue-600 hover:bg-blue-700 text-white border-blue-700"
      onClick={onClick}
      disabled={importing}
    >
      {importing ? "Importando..." : "Importar"}
    </Button>
  );
}
