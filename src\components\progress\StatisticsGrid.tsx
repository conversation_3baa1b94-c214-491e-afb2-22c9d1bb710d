
import { <PERSON>, <PERSON>, <PERSON>, Flame } from "lucide-react";
import StatCard from "@/components/StatCard";

interface StatisticsGridProps {
  stats: {
    totalAnswered: number;
    correctAnswers: number;
    averageTime: number;
    streakDays: number;
  };
}

export const StatisticsGrid = ({ stats }: StatisticsGridProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <StatCard
        title="Questões Respondidas"
        value={stats.totalAnswered}
        icon={<Brain className="w-6 h-6" />}
        trend={{ value: 15, isPositive: true }}
      />
      <StatCard
        title="Taxa de Acerto"
        value={`${((stats.correctAnswers / stats.totalAnswered) * 100 || 0).toFixed(1)}%`}
        icon={<Target className="w-6 h-6" />}
        trend={{ value: 5, isPositive: true }}
      />
      <StatCard
        title="Tempo Médio"
        value={`${Math.round(stats.averageTime || 0)}s`}
        icon={<Clock className="w-6 h-6" />}
      />
      <StatCard
        title="Sequência"
        value={`${stats.streakDays} dias`}
        icon={<Flame className="w-6 h-6" />}
        trend={{ value: 1, isPositive: true }}
      />
    </div>
  );
};
