
import type { DaySchedule, StudyTopic, AIScheduleOptions } from '@/types/study-schedule';

export interface RevisionInfo {
  date: Date;
  formattedDate: string;
  weekNumber: number;
  scheduleId: string;
  dayOfWeek: string;
}

export interface StudyResult {
  success: boolean;
  message: string;
  nextRevision?: {
    date: string;
    dayOfWeek: string;
    revisionNumber: number;
    daysUntil: number;
  };
  isLastRevision?: boolean;
}

export interface GenerationStats {
  topicsCreated: number;
  specialties: string[];
  timeSpent: number;
}

export interface WeeklySchedule {
  recommendations: DaySchedule[];
  currentScheduleId?: string;
}
