
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useToast } from "@/components/ui/use-toast";
import Header from "@/components/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdminMenu } from "@/components/admin/AdminMenu";

const UsersManagement = () => {
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        console.log("🔐 [UsersManagement] Verificando status de administrador...");
        toast("Verificando permissões de administrador");
        
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          uiToast({
            title: "Acesso negado",
            description: "Você precisa estar logado para acessar esta página",
            variant: "destructive",
          });
          navigate("/");
          return;
        }
        
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error || !profile || !profile.is_admin) {
          uiToast({
            title: "Acesso restrito",
            description: "Esta área é restrita a administradores",
            variant: "destructive",
          });
          navigate("/plataformadeestudos");
          return;
        }
        
        toast.success("Acesso de administrador confirmado");
        setIsVerifying(false);
        
      } catch (error) {
        toast.error("Erro ao verificar permissões de administrador");
        navigate("/plataformadeestudos");
      }
    };

    checkAdminStatus();
  }, [navigate, uiToast]);

  if (isVerifying) {
    return (
      <>
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
            <h2 className="text-xl font-semibold mb-2">Verificando permissões</h2>
            <p className="text-muted-foreground">Aguarde enquanto verificamos seu acesso...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-8">Gerenciamento de Usuários</h1>
        
        <AdminMenu />
        
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Lista de Usuários</CardTitle>
            <CardDescription>
              Gerencie os usuários da plataforma
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center p-6 min-h-[200px]">
              <div className="text-center">
                <p className="text-muted-foreground mb-2">Funcionalidade em desenvolvimento</p>
                <p className="text-sm">Esta funcionalidade estará disponível em breve.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default UsersManagement;
