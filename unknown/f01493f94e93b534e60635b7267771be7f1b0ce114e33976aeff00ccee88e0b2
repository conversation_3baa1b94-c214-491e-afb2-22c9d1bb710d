
import React, { useState, useEffect } from "react";
import { DayCard } from "./DayCard";
import { motion } from "framer-motion";
import type { DaySchedule } from "@/types/study-schedule";
import { ChevronDown, ChevronUp, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DeleteWeekDialog } from "./DeleteWeekDialog";
import { forceWeekBoundaries } from "@/utils/dateUtils";

interface WeekCardProps {
  weekNumber: number;
  days: DaySchedule[];
  totalHours: number;
  weekStartDate: string;
  weekEndDate: string;
  isCurrentWeek: boolean;
  defaultExpanded?: boolean;
  isHighestWeek?: boolean;
  onAddTopic?: (dayOfWeek: string, weekNumber: number, scheduleId: string, source: 'platform' | 'manual') => void;
  onDeleteWeek?: (weekNumber: number) => void;
  onDeleteAllWeeks?: () => void;
  onDeleteTopic?: (topicId: string) => void;
  currentDate?: Date;
}

export const WeekCard = ({
  weekNumber,
  days,
  totalHours,
  weekStartDate,
  weekEndDate,
  isCurrentWeek,
  defaultExpanded = false,
  isHighestWeek = false,
  onAddTopic,
  onDeleteWeek,
  onDeleteAllWeeks,
  onDeleteTopic,
  currentDate
}: WeekCardProps) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [formattedDateRange, setFormattedDateRange] = useState("");

  useEffect(() => {
    // Force the start date to always be a Sunday and end date to always be a Saturday
    const { correctedStartDate, correctedEndDate } = forceWeekBoundaries(weekStartDate, weekEndDate);

    // Format the date range using the simple split and reverse method to avoid timezone issues
    // Garantir que as datas estejam no formato correto (dd/mm)
    const startParts = correctedStartDate.split('-');
    const endParts = correctedEndDate.split('-');

    // Formato: dd/mm
    const formattedStart = `${startParts[2]}/${startParts[1]}`;
    const formattedEnd = `${endParts[2]}/${endParts[1]}`;
    const range = `${formattedStart} - ${formattedEnd}`;
    setFormattedDateRange(range);

    // Log dates for debugging
    console.log(`📆 [WeekCard] Week ${weekNumber} dates:`, {
      originalStartDate: weekStartDate,
      originalEndDate: weekEndDate,
      correctedStartDate,
      correctedEndDate,
      formattedStart,
      formattedEnd,
      formattedRange: range
    });
  }, [weekStartDate, weekEndDate, weekNumber]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setDeleteDialogOpen(true);
  };

  const handleDelete = () => {
    if (onDeleteWeek) {
      onDeleteWeek(weekNumber);
    }
  };

  const handleDeleteAll = () => {
    if (onDeleteAllWeeks) {
      onDeleteAllWeeks();
    }
  };

  const orderedDays = [...days].sort((a, b) => {
    const dayOrder = ["domingo", "segunda", "terça", "quarta", "quinta", "sexta", "sábado"];
    return dayOrder.indexOf(a.day.toLowerCase()) - dayOrder.indexOf(b.day.toLowerCase());
  });

  const handleAddTopicToDay = (dayOfWeek: string, weekNum: number, scheduleId: string, source: 'platform' | 'manual') => {
    if (onAddTopic) {
      console.log("📋 WeekCard - Forwarding add topic:", { dayOfWeek, weekNum, scheduleId, source });
      onAddTopic(dayOfWeek, weekNum, scheduleId, source);
    }
  };

  // Função para formatar as horas
  const formatHours = (minutes: number) => {
    // Converter para horas (dividindo por 60 minutos)
    const hours = minutes / 60;
    // Format to 1 decimal place
    const formattedValue = hours.toFixed(1);
    // Remove o ".0" se o valor for um número inteiro de horas
    return formattedValue.endsWith('.0') ? formattedValue.slice(0, -2) : formattedValue;
  };

  return (
    <div className={`border-2 rounded-xl overflow-hidden ${
      isCurrentWeek
        ? "border-[#58CC02] bg-white/90"
        : "border-gray-300 bg-white/80"
    }`}>
      <div
        className={`p-4 flex justify-between items-center cursor-pointer ${
          isCurrentWeek ? "bg-[#F2FFEA]" : ""
        }`}
        onClick={toggleExpanded}
      >
        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
          <h3 className="font-bold text-lg text-gray-900 flex items-center">
            Semana {weekNumber}
            {isCurrentWeek && (
              <Badge variant="default" className="ml-2 bg-[#58CC02] text-white text-xs">
                Atual
              </Badge>
            )}
          </h3>
          <div className="text-sm text-gray-600 font-medium">{formattedDateRange}</div>
        </div>

        <div className="flex items-center gap-3">
          <div className="hidden sm:block text-sm font-medium">
            {formatHours(totalHours)}h de estudo
          </div>
          <div className="flex items-center gap-2">
            {onDeleteWeek && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-500 hover:text-red-600 hover:bg-red-50"
                onClick={handleDeleteClick}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-500" />
            )}
          </div>
        </div>
      </div>

      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="p-4 space-y-4">
            {orderedDays.map((day) => (
              <DayCard
                key={day.day}
                day={day}
                isInCurrentWeek={isCurrentWeek}
                weekNumber={weekNumber}
                onAddTopic={handleAddTopicToDay}
              />
            ))}
          </div>
        </motion.div>
      )}

      <DeleteWeekDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        weekNumber={weekNumber}
        isHighestWeek={isHighestWeek}
        onDelete={handleDelete}
        onDeleteAll={handleDeleteAll}
      />
    </div>
  );
};
