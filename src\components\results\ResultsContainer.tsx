
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { SessionSummary } from '@/components/study-session/SessionSummary';
import { ArrowRight, BookOpen, ChevronDown, Info, Calendar, Zap, Brain } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import type { QuestionStats } from '@/types/question';
import { FlashcardGenerationPanel } from './FlashcardGenerationPanel';
import { AvailableFlashcardsPanel } from './AvailableFlashcardsPanel';
import { motion } from "framer-motion";

interface ResultsContainerProps {
  stats: QuestionStats;
}

export const ResultsContainer = ({ stats }: ResultsContainerProps) => {
  const navigate = useNavigate();
  const [showFlashcards, setShowFlashcards] = useState(false);
  const [showInfoDialog, setShowInfoDialog] = useState(false);

  const answeredQuestions = stats.correct_answers + stats.incorrect_answers;
  const accuracy = answeredQuestions > 0
    ? Math.round((stats.correct_answers / answeredQuestions) * 100)
    : 0;

  // Função para formatar tempo corretamente
  const formatTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeLabel = (seconds: number) => {
    return seconds < 60 ? '' : 'min';
  };

  return (
    <div className="container mx-auto px-4 pt-4 pb-8 space-y-6 max-w-4xl">
      {/* Espaço para menu desktop */}
      <div className="hidden sm:block h-4"></div>

      {/* Header Compacto */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-green-50 via-blue-50 to-purple-50 rounded-xl p-6 text-center border-2 border-green-200 shadow-lg"
      >
        <div className="text-center">
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            🎉 Parabéns!
          </h1>
          <p className="text-gray-600 text-sm sm:text-base mt-2">
            Sessão de estudos concluída com sucesso
          </p>
        </div>

        {/* Botão Flashcard Compacto */}
        <div className="flex justify-center mt-6">
          <div className="relative">
            <Button
              disabled
              className="flex items-center gap-2 px-4 py-2 rounded-lg opacity-60 cursor-not-allowed
                        bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-md text-sm"
            >
              <BookOpen className="h-4 w-4" />
              <span>Aprenda com flashcards</span>
              <span className="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-semibold">
                Em breve
              </span>
            </Button>

            <Button
              onClick={() => setShowInfoDialog(true)}
              size="sm"
              variant="outline"
              className="absolute -top-2 -right-2 h-8 w-8 rounded-full p-0 bg-blue-500 hover:bg-blue-600 text-white border-2 border-white shadow-lg"
            >
              <Info className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Desempenho Compacto em Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-4"
      >
        {/* Taxa de Acerto */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-4 rounded-xl border border-blue-200 shadow-md">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-sm font-semibold text-blue-700 uppercase tracking-wide">Taxa de Acerto</h3>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-blue-600">{accuracy}%</span>
                {accuracy >= 70 && <span className="text-yellow-500">⭐</span>}
              </div>
            </div>
            <div className="text-right text-xs text-blue-600">
              <div>✅ {stats.correct_answers} corretas</div>
              <div>❌ {stats.incorrect_answers} incorretas</div>
            </div>
          </div>
          <Progress value={accuracy} className="h-2 bg-blue-100" />
        </div>

        {/* Tempo da Sessão */}
        <div className="bg-gradient-to-br from-purple-50 to-pink-100 p-4 rounded-xl border border-purple-200 shadow-md">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-sm font-semibold text-purple-700 uppercase tracking-wide">Tempo Total</h3>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-purple-600">
                  {formatTime(stats.time_spent)}
                </span>
                <span className="text-sm text-purple-500">{getTimeLabel(stats.time_spent)}</span>
              </div>
            </div>
            <div className="text-right text-xs text-purple-600">
              {answeredQuestions > 0 && (
                <div>⚡ {Math.round(stats.time_spent / answeredQuestions)}s por questão</div>
              )}
              <div className="mt-1">
                {stats.time_spent < 60
                  ? "🚀 Super rápido!"
                  : stats.time_spent < 300
                    ? "⚡ Bom ritmo!"
                    : stats.time_spent < 600
                      ? "🎯 Dedicado!"
                      : "🏆 Muito dedicado!"}
              </div>
            </div>
          </div>
          <div className="h-2 w-full bg-purple-100 rounded-full overflow-hidden">
            <div
              className="h-full bg-purple-400 rounded-full transition-all duration-1000"
              style={{ width: `${Math.min(100, (stats.time_spent / 1800) * 100)}%` }}
            ></div>
          </div>
        </div>
      </motion.div>

      {/* Análise Detalhada */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-5"
      >
        <div className="flex items-center gap-2 mb-4">
          <Brain className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-bold text-gray-800">Análise por Categorias</h2>
        </div>

        <SessionSummary
          stats={{
            correct_answers: stats.correct_answers,
            incorrect_answers: stats.incorrect_answers,
            time_spent: stats.time_spent,
            by_specialty: stats.by_specialty,
            by_theme: stats.by_theme,
            by_focus: stats.by_focus
          }}
          totalQuestions={answeredQuestions}
        />
      </motion.div>

      {/* Botões de Ação Compactos */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex flex-col sm:flex-row gap-3 justify-center pt-2"
      >
        <Button
          onClick={() => navigate('/questions')}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
        >
          <ArrowRight className="h-5 w-5" />
          Continuar Estudando
        </Button>

        <Button
          onClick={() => navigate('/plataformadeestudos')}
          variant="outline"
          className="flex items-center gap-2 px-6 py-3 border-2 border-gray-300 text-gray-700 hover:bg-gray-100 rounded-xl font-medium transition-all duration-200"
        >
          <Calendar className="h-5 w-5" />
          Voltar ao Painel
        </Button>
      </motion.div>

      {/* Info Dialog */}
      <Dialog open={showInfoDialog} onOpenChange={setShowInfoDialog}>
        <DialogContent className="max-w-[95vw] sm:max-w-lg mx-auto max-h-[90dvh] overflow-y-auto rounded-2xl">
          <DialogHeader className="text-center pr-0">
            <DialogTitle className="flex items-center justify-center gap-2 text-xl sm:text-2xl pr-12">
              <Brain className="h-6 w-6 text-blue-500" />
              Flashcards Inteligentes
            </DialogTitle>
            <DialogDescription>
              Sistema de estudo inteligente com IA e repetição espaçada para maximizar seu aprendizado
            </DialogDescription>
          </DialogHeader>
          <div className="text-center space-y-4 pt-4">
            <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center justify-center gap-2">
                <Zap className="h-5 w-5" />
                O que são Flashcards?
              </h4>
              <p className="text-blue-700 text-sm leading-relaxed">
                Sistema de estudo inteligente que transforma suas <strong>questões incorretas</strong> em cartões de revisão personalizados,
                utilizando <strong>Inteligência Artificial</strong> e técnicas de <strong>repetição espaçada</strong> para maximizar sua retenção e aprendizado.
              </p>
            </div>

            <div className="bg-green-50 p-4 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 text-center">🎯 Como Funciona:</h4>
              <div className="text-green-700 text-sm space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">1.</span>
                  <span><strong>Análise IA:</strong> Identifica automaticamente suas questões incorretas e padrões de erro</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">2.</span>
                  <span><strong>Geração Inteligente:</strong> Cria flashcards focados nos seus pontos fracos específicos</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">3.</span>
                  <span><strong>Tipos Variados:</strong> Múltipla escolha, Cloze (completar lacunas), Verdadeiro/Falso</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">4.</span>
                  <span><strong>FSRS Algorithm:</strong> Sistema de repetição espaçada baseado no algoritmo FSRS-5</span>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-xl border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-3 text-center">🚀 Recursos Avançados:</h4>
              <div className="text-purple-700 text-sm space-y-1">
                <div>• <strong>Sessões Adaptativas:</strong> Duração e dificuldade ajustadas ao seu desempenho</div>
                <div>• <strong>Métricas Detalhadas:</strong> Acompanhamento de retenção e progresso</div>
                <div>• <strong>Revisão Inteligente:</strong> Prioriza conceitos com maior chance de esquecimento</div>
                <div>• <strong>Integração Total:</strong> Sincronizado com seu histórico de questões</div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center justify-center gap-2">
                <Calendar className="h-5 w-5" />
                Status do Desenvolvimento
              </h4>
              <p className="text-green-700 text-sm leading-relaxed">
                A funcionalidade está <strong>pronta</strong> e aguardando apenas os <strong>testes finais</strong>
                das ferramentas de questões. Seguindo nosso roadmap, será lançada em breve para oferecer
                a melhor experiência de aprendizado com flashcards inteligentes.
              </p>
            </div>

            <div className="text-center pt-4">
              <Button
                onClick={() => setShowInfoDialog(false)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-2 rounded-xl"
              >
                Entendi, aguardo ansiosamente! 🎉
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

