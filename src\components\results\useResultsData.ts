import { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { QuestionStats } from '@/types/question';

interface SessionStatistics {
  total_time_spent: number;
  avg_response_time: number;
  total_correct: number;
  total_incorrect: number;
  by_theme: Record<string, { id: string; name: string; correct: number; total: number }>;
  by_specialty: Record<string, { id: string; name: string; correct: number; total: number }>;
  by_focus: Record<string, { id: string; name: string; correct: number; total: number }>;
}

export const useResultsData = (sessionId: string | null) => {
  const [stats, setStats] = useState<QuestionStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSessionStats = async (currentSessionId: string) => {
      try {


        // Usar a nova função que busca dados da estrutura atualizada
        const { data: sessionStats, error: statsError } = await supabase
          .rpc('get_session_statistics_v2', { p_session_id: currentSessionId as any });



        if (statsError) {
          console.error('❌ [useResultsData] Error fetching session statistics:', statsError);
          // Se o erro for relacionado à tabela não existir, usar dados padrão
          if (statsError.code === '42P01') {
            console.warn('⚠️ [useResultsData] Tabela não encontrada, usando dados padrão');
            const defaultStats: QuestionStats = {
              time_spent: 0,
              correct_answers: 0,
              incorrect_answers: 0,
              by_theme: {},
              by_specialty: {},
              by_focus: {}
            };
            setStats(defaultStats);
            return;
          }
          throw statsError;
        }

        if (!sessionStats?.length) {
          console.warn('⚠️ [useResultsData] No statistics found, using default values');
          const defaultStats: QuestionStats = {
            time_spent: 0,
            correct_answers: 0,
            incorrect_answers: 0,
            by_theme: {},
            by_specialty: {},
            by_focus: {}
          };
          setStats(defaultStats);
          return;
        }

        const rawStats = sessionStats[0] as SessionStatistics;

        const convertedStats: QuestionStats = {
          time_spent: rawStats.total_time_spent || 0, // Usar tempo total em vez de tempo médio
          correct_answers: rawStats.total_correct || 0,
          incorrect_answers: rawStats.total_incorrect || 0,
          by_theme: convertCategoryStatsV2(rawStats.by_theme),
          by_specialty: convertCategoryStatsV2(rawStats.by_specialty),
          by_focus: convertCategoryStatsV2(rawStats.by_focus)
        };



        setStats(convertedStats);
      } catch (error: any) {
        console.error('❌ [useResultsData] Erro ao carregar estatísticas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (sessionId) {
      fetchSessionStats(sessionId);
    }
  }, [sessionId, toast]);

  return { stats, isLoading };
};

// Nova função de conversão que não precisa do categoryMap
const convertCategoryStatsV2 = (categoryData: Record<string, { id: string; name: string; correct: number; total: number }> | null) => {
  if (!categoryData) return {};

  return Object.entries(categoryData).reduce((acc, [id, data]) => {
    return {
      ...acc,
      [id]: {
        name: data.name || 'Nome não encontrado',
        correct: data.correct || 0,
        total: data.total || 0
      }
    };
  }, {});
};

const convertCategoryStats = (
  stats: Record<string, { id: string; correct: number; total: number }> | undefined,
  categoryMap: Record<string, string>
) => {
  if (!stats) return {};

  return Object.entries(stats).reduce((acc, [id, data]) => ({
    ...acc,
    [id]: {
      name: categoryMap[id] || 'Unknown',
      correct: data.correct,
      total: data.total
    }
  }), {});
};
