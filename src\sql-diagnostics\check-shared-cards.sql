
-- Verifica quantos flashcards compartilhados existem no total
SELECT COUNT(*) 
FROM flashcards_cards 
WHERE is_shared = true;

-- Verifica flashcards compartilhados com detalhes
SELECT 
  id, 
  user_id, 
  SUBSTRING(front, 1, 50) as front_preview,
  created_at, 
  current_state,
  is_shared
FROM flashcards_cards 
WHERE is_shared = true
ORDER BY created_at DESC
LIMIT 20;

-- Verifica flashcards compartilhados por estado
SELECT 
  current_state, 
  COUNT(*) 
FROM flashcards_cards 
WHERE is_shared = true
GROUP BY current_state;

-- Consulta adicional: todos os flashcards do sistema
SELECT
  id,
  user_id,
  SUBSTRING(front, 1, 30) as front_preview,
  is_shared,
  current_state
FROM flashcards_cards
ORDER BY created_at DESC
LIMIT 10;

-- Consulta adicional: status dos cartões
SELECT
  current_state,
  COUNT(*)
FROM flashcards_cards
GROUP BY current_state;
