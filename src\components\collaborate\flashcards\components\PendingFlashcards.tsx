import { useState } from "react";
import { FlashcardItem } from "./FlashcardItem";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { FlashcardWithHierarchy } from "@/types/flashcard";

interface PendingFlashcardsProps {
  cards: FlashcardWithHierarchy[];
  onDelete: (id: string) => Promise<void>;
  onApprove: (id: string) => Promise<void>;
  onReject: (id: string, reason: string) => Promise<void>;
}

export const PendingFlashcards = ({
  cards,
  onDelete,
  onApprove,
  onReject,
}: PendingFlashcardsProps) => {
  const [rejectionReason, setRejectionReason] = useState("");

  return (
    <div className="space-y-4">
      {cards.map((card) => (
        <div key={card.id} className="border p-4 rounded-lg space-y-4">
          <FlashcardItem 
            card={card}
            onDelete={onDelete}
          />
          
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="rejectionReason">Motivo da Rejeição</Label>
              <Input
                id="rejectionReason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Informe o motivo caso rejeite o flashcard"
              />
            </div>
            <Button
              onClick={() => onApprove(card.id)}
              className="bg-green-500 hover:bg-green-600"
            >
              Aprovar
            </Button>
            <Button
              onClick={() => onReject(card.id, rejectionReason)}
              variant="destructive"
            >
              Rejeitar
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};