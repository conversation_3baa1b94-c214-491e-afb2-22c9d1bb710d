
import { Routes, Route } from "react-router-dom";
import { FlashcardsHome } from "@/components/flashcards/FlashcardsHome";
import { FlashcardsStudy } from "@/components/flashcards/FlashcardsStudy";
import { FlashcardSession } from "@/components/flashcards/FlashcardSession";

const Flashcards = () => {
  return (
    <Routes>
      <Route path="/" element={<FlashcardsHome />} />
      <Route path="/study" element={<FlashcardsStudy />} />
      <Route path="/session/:sessionId" element={<FlashcardSession />} />
    </Routes>
  );
};

export default Flashcards;
