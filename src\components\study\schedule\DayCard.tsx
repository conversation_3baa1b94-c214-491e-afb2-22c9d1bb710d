
import { motion } from "framer-motion";
import { Calendar, Clock, Plus, ChevronDown, ChevronUp } from "lucide-react";
import type { DaySchedule, StudyTopic } from "@/types/study-schedule";
import { TopicCard } from "./TopicCard";
import { Button } from "@/components/ui/button";
import { parseDurationToMinutes, sumDurations } from "@/utils/formatTime";
import { useEffect, useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface DayCardProps {
  day: DaySchedule;
  isInCurrentWeek?: boolean;
  weekNumber: number;
  onAddTopic?: (dayOfWeek: string, weekNumber: number, scheduleId: string, source: 'platform' | 'manual') => void;
}

export const DayCard = ({ 
  day,
  isInCurrentWeek,
  weekNumber,
  onAddTopic 
}: DayCardProps) => {
  const [calculatedTotalHours, setCalculatedTotalHours] = useState<string>("0:00");
  const [isExpanded, setIsExpanded] = useState(day.topics.length > 0);
  
  // Recalculate total hours whenever topics change
  useEffect(() => {
    if (day.topics && day.topics.length > 0) {
      const totalDuration = sumDurations(day.topics.map(topic => topic.duration || "0:00"));
      console.log(`📊 [DayCard] Calculated total for ${day.day}:`, {
        topics: day.topics.length,
        durations: day.topics.map(topic => topic.duration),
        total: totalDuration
      });
      setCalculatedTotalHours(totalDuration);
      
      // Auto-expand if there are topics
      if (day.topics.length > 0) {
        setIsExpanded(true);
      }
    } else {
      setCalculatedTotalHours("0:00");
    }
  }, [day.topics, day.day]);
  
  const getDayInitial = (day: string) => {
    return day.substring(0, 1).toUpperCase();
  };

  const getDayColor = (day: string) => {
    switch(day) {
      case 'Segunda-feira': return 'bg-blue-100 text-blue-600 border-2 border-blue-600';
      case 'Terça-feira': return 'bg-purple-100 text-purple-600 border-2 border-purple-600';
      case 'Quarta-feira': return 'bg-green-100 text-green-600 border-2 border-green-600';
      case 'Quinta-feira': return 'bg-yellow-100 text-yellow-600 border-2 border-yellow-600';
      case 'Sexta-feira': return 'bg-red-100 text-red-600 border-2 border-red-600';
      case 'Sábado': return 'bg-orange-100 text-orange-600 border-2 border-orange-600';
      case 'Domingo': return 'bg-pink-100 text-pink-600 border-2 border-pink-600';
      default: return 'bg-gray-100 text-gray-600 border-2 border-gray-600';
    }
  };

  const handleAddTopic = (source: 'platform' | 'manual') => {
    if (onAddTopic) {
      console.log("🔘 Add topic button clicked for day:", {
        day: day.day,
        weekNumber,
        scheduleId: day.scheduleId
      });
      onAddTopic(day.day, weekNumber, day.scheduleId, source);
    }
  };

  // Ordenar os tópicos por horário de início para exibição cronológica
  const sortedTopics = [...day.topics].sort((a, b) => {
    if (!a.startTime || !b.startTime) return 0;
    return a.startTime.localeCompare(b.startTime);
  });

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <motion.div
      whileHover={{ scale: 1.005 }}
      className="rounded-xl border-2 border-black shadow-sm bg-white hover:shadow-md transition-all w-full"
    >
      <div 
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center shadow-sm ${getDayColor(day.day)}`}>
            <span className="text-sm font-extrabold">
              {getDayInitial(day.day)}
            </span>
          </div>
          <h4 className="font-bold text-gray-800">{day.day}</h4>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 text-sm bg-gray-50 px-2.5 py-1 rounded-full border border-gray-400">
            <Clock className="h-3.5 w-3.5 text-gray-700" />
            <span className="font-bold text-gray-700">{calculatedTotalHours}</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-700" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-700" />
          )}
        </div>
      </div>
      
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="px-4 pb-4"
        >
          {sortedTopics.length > 0 ? (
            <ScrollArea className={sortedTopics.length > 3 ? "h-[350px] pr-2" : ""}>
              <div className="space-y-3">
                {sortedTopics.map((topic, index) => (
                  <TopicCard key={index} {...topic} />
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="flex flex-col items-center justify-center py-4 text-center bg-[#FEF7CD] rounded-lg mb-3 border-2 border-dashed border-gray-400">
              <p className="text-gray-700 text-sm font-medium">Nenhum estudo agendado</p>
            </div>
          )}
          
          {/* Add topic button placed at bottom */}
          {onAddTopic && (
            <div className="mt-3">
              <Button 
                variant="outline" 
                className="w-full border-2 border-dashed border-[#1CB0F6] hover:border-[#1CB0F6] hover:bg-[#1CB0F6]/5 text-[#1CB0F6] hover:text-[#1CB0F6] transition-colors font-bold"
                onClick={() => handleAddTopic('platform')}
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar tópico
              </Button>
            </div>
          )}
        </motion.div>
      )}
    </motion.div>
  );
};
