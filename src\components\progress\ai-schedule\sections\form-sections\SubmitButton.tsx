import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Sparkles } from "lucide-react";

interface SubmitButtonProps {
  isLoading: boolean;
}

export const SubmitButton = ({ isLoading }: SubmitButtonProps) => {
  return (
    <div className="pt-4 pb-2">
      <Button
        type="submit"
        className="w-full h-12 sm:h-14 text-base sm:text-lg font-bold text-white bg-blue-600 hover:bg-blue-700 shadow-lg border-b-2 border-blue-800 rounded-xl"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 animate-spin" />
            <span className="hidden sm:inline">Gerando cronograma...</span>
            <span className="sm:hidden">Gerando...</span>
          </>
        ) : (
          <>
            <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
            <span className="hidden sm:inline">Gerar cronograma com IA</span>
            <span className="sm:hidden">Gerar com IA</span>
          </>
        )}
      </Button>
    </div>
  );
};
