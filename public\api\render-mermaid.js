// Simulação da API render-mermaid para desenvolvimento
// Em produção, isso seria substituído pela ferramenta real

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { diagram_definition, title } = req.body;

  if (!diagram_definition) {
    return res.status(400).json({ error: 'diagram_definition is required' });
  }

  // Simular renderização do Mermaid
  const mockSvg = `
    <div class="mermaid-rendered">
      <svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
        <circle cx="200" cy="150" r="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
        <text x="200" y="155" text-anchor="middle" font-family="Arial" font-size="14" fill="#1976d2">
          ${title || 'Mapa Mental'}
        </text>
        <circle cx="100" cy="80" r="40" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
        <text x="100" y="85" text-anchor="middle" font-family="Arial" font-size="10" fill="#7b1fa2">
          Tópico 1
        </text>
        <circle cx="300" cy="80" r="40" fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
        <text x="300" y="85" text-anchor="middle" font-family="Arial" font-size="10" fill="#388e3c">
          Tópico 2
        </text>
        <circle cx="100" cy="220" r="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
        <text x="100" y="225" text-anchor="middle" font-family="Arial" font-size="10" fill="#f57c00">
          Tópico 3
        </text>
        <circle cx="300" cy="220" r="40" fill="#ffebee" stroke="#d32f2f" stroke-width="2"/>
        <text x="300" y="225" text-anchor="middle" font-family="Arial" font-size="10" fill="#d32f2f">
          Tópico 4
        </text>
        <line x1="200" y1="150" x2="100" y2="80" stroke="#666" stroke-width="1"/>
        <line x1="200" y1="150" x2="300" y2="80" stroke="#666" stroke-width="1"/>
        <line x1="200" y1="150" x2="100" y2="220" stroke="#666" stroke-width="1"/>
        <line x1="200" y1="150" x2="300" y2="220" stroke="#666" stroke-width="1"/>
      </svg>
      <div class="mt-2 text-center text-sm text-gray-600">
        Mapa mental renderizado com sucesso
      </div>
    </div>
  `;

  res.setHeader('Content-Type', 'text/html');
  res.status(200).send(mockSvg);
}
