import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import type { FilterOption } from "../types";

export const useFilterData = () => {
  const [categories, setCategories] = useState<FilterOption[]>([]);
  const [locations, setLocations] = useState<{id: string, name: string}[]>([]);
  const [questionCounts, setQuestionCounts] = useState<{[key: string]: number}>({});

  useEffect(() => {
    const loadInitialData = async () => {
      const savedCategories = localStorage.getItem('study_categories');
      if (savedCategories) {
        setCategories(JSON.parse(savedCategories));
      }

      const savedLocations = localStorage.getItem('locations');
      if (savedLocations) {
        setLocations(JSON.parse(savedLocations));
      }

      // Load question counts from Supabase with limit
      const { data: questionData, error } = await supabase
        .from('questions')
        .select('specialty_id, theme_id, focus_id, location_id, year')
        .limit(1000);

      if (error) {
        console.error('Error fetching questions:', error);
        return;
      }

      if (questionData) {
        const counts: {[key: string]: number} = {};
        questionData.forEach((question) => {
          if (question.specialty_id) {
            counts[question.specialty_id] = (counts[question.specialty_id] || 0) + 1;
          }
          if (question.theme_id) {
            counts[question.theme_id] = (counts[question.theme_id] || 0) + 1;
          }
          if (question.focus_id) {
            counts[question.focus_id] = (counts[question.focus_id] || 0) + 1;
          }
          if (question.location_id) {
            counts[question.location_id] = (counts[question.location_id] || 0) + 1;
          }
          if (question.year) {
            counts[question.year.toString()] = (counts[question.year.toString()] || 0) + 1;
          }
        });
        setQuestionCounts(counts);
      }
    };

    loadInitialData();
  }, []);

  return { categories, locations, questionCounts };
};