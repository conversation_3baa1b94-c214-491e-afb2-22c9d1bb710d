import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Calendar,
  BarChart3,
  Target,
  Zap,
  AlertTriangle
} from 'lucide-react';
import { useTemporalInsights, type TemporalInsight } from '@/hooks/useTemporalInsights';

const TrendIcon = ({ trend }: { trend: 'improving' | 'declining' | 'stable' }) => {
  switch (trend) {
    case 'improving':
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    case 'declining':
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    default:
      return <BarChart3 className="h-4 w-4 text-gray-600" />;
  }
};

const TrendBadge = ({ trend, change }: { trend: 'improving' | 'declining' | 'stable'; change: number }) => {
  const getVariant = () => {
    switch (trend) {
      case 'improving': return 'default';
      case 'declining': return 'destructive';
      default: return 'secondary';
    }
  };

  const getLabel = () => {
    if (trend === 'stable') return 'Estável';
    const sign = change > 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  };

  return (
    <Badge variant={getVariant()} className="flex items-center gap-1">
      <TrendIcon trend={trend} />
      {getLabel()}
    </Badge>
  );
};

const InsightCard = ({ insight }: { insight: TemporalInsight }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 bg-white hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-medium text-gray-800">{insight.category}</h4>
          <p className="text-sm text-gray-500 capitalize">{insight.categoryType}</p>
        </div>
        <TrendBadge trend={insight.trend} change={insight.weeklyChange} />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Precisão atual</span>
          <span className="font-medium">{insight.currentAccuracy.toFixed(1)}%</span>
        </div>

        <Progress
          value={insight.currentAccuracy}
          className="h-2"
        />

        <div className="flex justify-between text-xs text-gray-500">
          <span>{insight.totalQuestions} questões</span>
          <span>Semana anterior: {insight.previousAccuracy.toFixed(1)}%</span>
        </div>
      </div>
    </motion.div>
  );
};

const HourlyPerformanceChart = ({ data }: { data: any[] }) => {
  const maxAccuracy = Math.max(...data.map(d => d.accuracy));

  return (
    <div className="space-y-4">
      <h4 className="font-medium flex items-center gap-2">
        <Clock className="h-4 w-4 text-blue-600" />
        Performance por Horário
      </h4>

      <div className="grid grid-cols-12 gap-1">
        {data.map((hour) => (
          <div key={hour.hour} className="text-center">
            <div
              className="bg-blue-100 rounded-sm mb-1 transition-all hover:bg-blue-200"
              style={{
                height: `${Math.max(4, (hour.accuracy / maxAccuracy) * 40)}px`,
                backgroundColor: hour.accuracy > 70 ? '#10b981' : hour.accuracy > 50 ? '#f59e0b' : '#ef4444'
              }}
              title={`${hour.hour}h: ${hour.accuracy.toFixed(1)}% (${hour.totalQuestions} questões)`}
            />
            <span className="text-xs text-gray-500">{hour.hour}h</span>
          </div>
        ))}
      </div>

      <div className="flex justify-center gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
          <span>Boa (&gt;70%)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
          <span>Média (50-70%)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
          <span>Baixa (&lt;50%)</span>
        </div>
      </div>
    </div>
  );
};

export const TemporalInsights = () => {
  const { data: temporalData, isLoading, error } = useTemporalInsights();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !temporalData) {
    return (
      <div className="text-center text-gray-500 p-8">
        <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
        <p>Dados insuficientes para análise temporal</p>
      </div>
    );
  }

  const {
    hourlyPerformance,
    bestStudyTime,
    improvingCategories,
    decliningCategories
  } = temporalData;

  // ✅ FILTRAR APENAS ESPECIALIDADES (hierarquia principal)
  const topImprovingSpecialties = improvingCategories
    .filter(cat => cat.categoryType === 'specialty')
    .slice(0, 3);

  const topDecliningSpecialties = decliningCategories
    .filter(cat => cat.categoryType === 'specialty')
    .slice(0, 3);

  return (
    <div className="space-y-6">
      {/* ✅ RESUMO INFORMATIVO - Dados contextualizados */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pico de Performance</p>
                <p className="text-xl font-bold text-green-600">{bestStudyTime}</p>
                <p className="text-xs text-green-700">Melhor horário para estudar</p>
              </div>
              <Zap className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tendência Positiva</p>
                <p className="text-xl font-bold text-blue-600">
                  {topImprovingSpecialties.length > 0
                    ? `+${topImprovingSpecialties[0]?.weeklyChange.toFixed(1)}%`
                    : 'Estável'
                  }
                </p>
                <p className="text-xs text-blue-700">
                  {topImprovingSpecialties.length > 0
                    ? `${topImprovingSpecialties.length} especialidade(s) melhorando`
                    : 'Performance consistente'
                  }
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Foco de Estudo</p>
                <p className="text-xl font-bold text-purple-600">
                  {topDecliningSpecialties.length > 0
                    ? topDecliningSpecialties[0]?.category.split(':')[0] || 'Revisar'
                    : 'Manter Ritmo'
                  }
                </p>
                <p className="text-xs text-purple-700">
                  {topDecliningSpecialties.length > 0
                    ? 'Área que precisa de atenção'
                    : 'Continue o bom trabalho'
                  }
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ✅ GRÁFICO COMPACTO - Performance por horário */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Seu Ritmo de Estudo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <HourlyPerformanceChart data={hourlyPerformance} />
        </CardContent>
      </Card>

      {/* ✅ APENAS ESPECIALIDADES EM EVOLUÇÃO - Máximo 3 */}
      {topImprovingSpecialties.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-green-600">
              <TrendingUp className="h-5 w-5" />
              Especialidades em Evolução
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topImprovingSpecialties.map((insight) => (
                <div key={insight.categoryId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div>
                    <h4 className="font-medium text-gray-800">{insight.category}</h4>
                    <p className="text-sm text-gray-600">{insight.currentAccuracy.toFixed(1)}% de acertos</p>
                  </div>
                  <div className="text-right">
                    <TrendBadge trend={insight.trend} change={insight.weeklyChange} />
                    <p className="text-xs text-gray-500 mt-1">{insight.totalQuestions} questões</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ APENAS ESPECIALIDADES QUE PRECISAM DE ATENÇÃO - Máximo 3 */}
      {topDecliningSpecialties.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-orange-600">
              <AlertTriangle className="h-5 w-5" />
              Especialidades que Precisam de Atenção
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topDecliningSpecialties.map((insight) => (
                <div key={insight.categoryId} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div>
                    <h4 className="font-medium text-gray-800">{insight.category}</h4>
                    <p className="text-sm text-gray-600">{insight.currentAccuracy.toFixed(1)}% de acertos</p>
                  </div>
                  <div className="text-right">
                    <TrendBadge trend={insight.trend} change={insight.weeklyChange} />
                    <p className="text-xs text-gray-500 mt-1">{insight.totalQuestions} questões</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
