
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useStaticStudyCategories } from "@/hooks/useStaticDataCache";

export interface StudyStats {
  specialty: {
    id: string;
    name: string;
    total: number;
    correct: number;
  }[];
  theme: {
    id: string;
    name: string;
    specialty_id: string;
    total: number;
    correct: number;
  }[];
  focus: {
    id: string;
    name: string;
    theme_id: string;
    total: number;
    correct: number;
  }[];
}

export const useUserStudyStats = () => {
  const { user } = useAuth();
  const { data: staticCategories } = useStaticStudyCategories();

  return useQuery({
    queryKey: ["user-study-stats", user?.id],
    queryFn: async () => {
      if (!user?.id) {
        return null; // Retornar null em vez de erro
      }

      // ✅ Usar dados estáticos em cache em vez de query separada
      if (!staticCategories) {
        return null; // Retornar null em vez de erro
      }

      // Get all user answers
      const { data: answers, error } = await supabase
        .from("user_answers")
        .select(`
          id, is_correct, question_id,
          specialty_id,
          theme_id,
          focus_id
        `)
        .eq("user_id", user.id);

      if (error) return null; // Retornar null em vez de erro

      // ✅ Criar mapeamento usando dados estáticos em cache
      const allCategories = [
        ...staticCategories.specialties,
        ...staticCategories.themes,
        ...staticCategories.focuses
      ];

      const categoryMap = allCategories.reduce((acc, cat) => ({
        ...acc,
        [cat.id]: cat
      }), {} as Record<string, any>);

      const stats: StudyStats = {
        specialty: [],
        theme: [],
        focus: []
      };

      // Process answers to build statistics
      answers.forEach(answer => {
        // Process specialty stats
        if (answer.specialty_id) {
          const specialtyData = categoryMap[answer.specialty_id];
          if (!specialtyData) return;

          const specialty = stats.specialty.find(s => s.id === answer.specialty_id);

          if (specialty) {
            specialty.total++;
            if (answer.is_correct) specialty.correct++;
          } else {
            stats.specialty.push({
              id: answer.specialty_id,
              name: specialtyData.name || 'Unknown',
              total: 1,
              correct: answer.is_correct ? 1 : 0
            });
          }
        }

        // Process theme stats
        if (answer.theme_id) {
          const themeData = categoryMap[answer.theme_id];
          if (!themeData) return;

          const theme = stats.theme.find(t => t.id === answer.theme_id);

          if (theme) {
            theme.total++;
            if (answer.is_correct) theme.correct++;
          } else {
            stats.theme.push({
              id: answer.theme_id,
              name: themeData.name || 'Unknown',
              specialty_id: themeData.parent_id || '',
              total: 1,
              correct: answer.is_correct ? 1 : 0
            });
          }
        }

        // Process focus stats
        if (answer.focus_id) {
          const focusData = categoryMap[answer.focus_id];
          if (!focusData) return;

          const focus = stats.focus.find(f => f.id === answer.focus_id);

          if (focus) {
            focus.total++;
            if (answer.is_correct) focus.correct++;
          } else {
            stats.focus.push({
              id: answer.focus_id,
              name: focusData.name || 'Unknown',
              theme_id: focusData.parent_id || '',
              total: 1,
              correct: answer.is_correct ? 1 : 0
            });
          }
        }
      });

      return stats;
    },
    enabled: !!user?.id && !!staticCategories, // ✅ Aguardar dados estáticos
    staleTime: 0, // ✅ Sempre buscar dados frescos para debug
    cacheTime: 10 * 60 * 1000, // 10 minutos
    retry: false, // Não tentar novamente em caso de erro
  });
};
