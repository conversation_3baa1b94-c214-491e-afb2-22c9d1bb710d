
import { supabase } from "@/integrations/supabase/client";
import { getOrCreateCategory } from "./import/categoryImporter";
import { getOrCreateLocation } from "./import/locationImporter";
import { getOrCreateYear } from "./import/yearImporter";
import type { ImportQuestion, ImportResults } from "@/types/import";

interface ImportData {
  questions: ImportQuestion[];
}

function normalizeAnswerType(type?: string): 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO' {
  if (!type) return 'ALTERNATIVAS';

  const normalizedType = type.toUpperCase();
  // console.log('🔍 Tipo de resposta recebido:', normalizedType);

  if (normalizedType === 'MÚLTIPLA ESCOLHA' || normalizedType === 'MULTIPLA ESCOLHA' || normalizedType === 'MULTIPLE_CHOICE') {
    return 'ALTERNATIVAS';
  }

  if (normalizedType === 'DISCURSIVE' || normalizedType === 'DISCURSIVA') {
    return 'DISSERTATIVA';
  }

  if (normalizedType === 'TRUE_OR_FALSE' || normalizedType === 'VERDADEIRO_FALSO') {
    return 'VERDADEIRO_FALSO';
  }

  switch (normalizedType) {
    case 'ALTERNATIVAS':
    case 'DISSERTATIVA':
    case 'VERDADEIRO_FALSO':
      // console.log('✅ Tipo de resposta válido:', normalizedType);
      return normalizedType;
    default:
      // console.log('⚠️ Tipo de resposta não reconhecido, usando padrão:', 'ALTERNATIVAS');
      return 'ALTERNATIVAS';
  }
}

export async function importQuestions(data: ImportData) {
  try {
    // console.log('🚀 Iniciando importação de questões...');

    const results: ImportResults = {
      success: 0,
      errors: [],
      created: {
        specialties: new Map(),
        themes: new Map(),
        focuses: new Map(),
        locations: new Map(),
        years: new Set<number>()
      }
    };

    for (const question of data.questions) {
      try {
        // Verificar se temos o statement_text ou statement
        const statement = question.statement_text || question.statement;
        if (!statement) {
          throw new Error('Questão sem enunciado');
        }

        // console.log('\n📦 Processando questão:', statement.substring(0, 100) + '...');

        // Processar especialidade
        let specialty = null;
        if (question.specialty) {
          specialty = results.created.specialties.get(`specialty:${question.specialty}`);
          if (!specialty) {
            specialty = await getOrCreateCategory(question.specialty, 'specialty');
            if (specialty) {
              results.created.specialties.set(`specialty:${question.specialty}`, specialty);
            }
          }
        }

        // Processar tema
        let theme = null;
        if (question.theme && specialty) {
          theme = results.created.themes.get(`theme:${question.theme}`);
          if (!theme) {
            theme = await getOrCreateCategory(question.theme, 'theme', specialty.id);
            if (theme) {
              results.created.themes.set(`theme:${question.theme}`, theme);
            }
          }
        }

        // Processar foco
        let focus = null;
        if (question.focus && theme) {
          focus = results.created.focuses.get(`focus:${question.focus}`);
          if (!focus) {
            focus = await getOrCreateCategory(question.focus, 'focus', theme.id);
            if (focus) {
              results.created.focuses.set(`focus:${question.focus}`, focus);
            }
          }
        }

        // Processar local de prova
        let location = null;
        if (question.institution_id) {
          location = await getOrCreateLocation(question.institution_id);
        }

        // Processar ano
        if (question.year) {
          await getOrCreateYear(question.year);
          results.created.years.add(question.year);
        }

        // Normalizar o tipo de resposta
        const answer_type = normalizeAnswerType(question.answer_type);
        // console.log('📝 Tipo de resposta normalizado:', answer_type);

        // Criar questão com novos nomes de colunas
        const questionData = {
          question_content: statement,
          response_choices: question.response_choices || question.alternatives,
          correct_choice: String(question.correct_choice || question.correct_answer),
          specialty_id: specialty?.id,
          theme_id: theme?.id,
          focus_id: focus?.id,
          exam_location: location?.id,
          exam_year: question.exam_year || question.year,
          question_format: answer_type,
          content_tags: question.content_tags || question.topics
        };

        const { error: questionError } = await supabase
          .from('questions')
          .insert(questionData)
          .select()
          .single();

        if (questionError) throw questionError;

        results.success++;

      } catch (error: any) {
        const errorMessage = `Erro ao importar questão: ${error.message}`;
        // console.error('❌', errorMessage);
        results.errors.push(errorMessage);
      }
    }

    return results;
  } catch (error: any) {
    // console.error('❌ Erro fatal na importação:', error);
    throw new Error(`Erro na importação: ${error.message}`);
  }
}
