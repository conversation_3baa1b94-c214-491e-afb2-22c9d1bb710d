
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

import { QuestionComments } from "@/components/question/QuestionComments";
import { MessageSquare, <PERSON> } from "lucide-react";
import { QuestionAlternatives } from "./QuestionAlternatives";
import { DiscursiveAnswer } from "./DiscursiveAnswer";
import { QuestionFeedback } from "./QuestionFeedback";

import { QuestionMetadata } from "./QuestionMetadata";
import { QuestionLikeButtons } from "./QuestionLikeButtons";
import type { Question, Comment } from "@/types/question";
import { TextHighlighter } from "@/components/text/TextHighlighter";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { QuestionImages } from "./QuestionImages";
import { useQueryClient } from "@tanstack/react-query";
import { FormattedContent } from "./FormattedContent";
import { FormattedQuestionContent } from "./FormattedQuestionContent";
import { convertJsonToComments, convertCommentsToJson } from "@/utils/commentHelpers";
import { Json } from "@/integrations/supabase/types/json";

interface QuestionCardProps {
  question: Question;
  selectedAnswer: string | null;
  hasAnswered: boolean;
  onSelectAnswer: (answer: string) => void;
  onSubmitAnswer?: (timeSpent: number) => Promise<void>;
  onNext?: () => void;
  userId: string;
  sessionId: string;
  isAnswered?: boolean;
  timeSpent: number;
  // ✅ Props para integração com useQuestionSolverState (questões dissertativas)
  onSetFeedback?: (questionId: string, isCorrect: boolean) => void;
  onMarkAsAnswered?: (questionId: string) => void;
}

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  selectedAnswer,
  hasAnswered,
  onSelectAnswer,
  onSubmitAnswer,
  onNext,
  userId,
  sessionId,
  isAnswered = false,
  timeSpent,
  onSetFeedback,
  onMarkAsAnswered
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [questionData, setQuestionData] = useState<Question>(question);
  const [discursiveAnswer, setDiscursiveAnswer] = useState("");
  const [internalHasAnswered, setInternalHasAnswered] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);



  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estados para análise de IA dissertativa
  const [discursiveAIResult, setDiscursiveAIResult] = useState<{
    aiAnswer: string;
    feedback: string;
    isCorrect?: boolean;
    scorePercentage?: number;
    mainPointsCovered?: string[];
    missingPoints?: string[];
  } | null>(null);
  const [loadingAI, setLoadingAI] = useState(false);
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [savedQuestionStatus, setSavedQuestionStatus] = useState<boolean | null>(null);

  useEffect(() => {
    if (hasAnswered || isAnswered) {
      setInternalHasAnswered(true);
    }
  }, [hasAnswered, isAnswered]);

  useEffect(() => {
    setQuestionData(question);
    // Limpar estado interno ao mudar de questão
    setInternalHasAnswered(false);
    setDiscursiveAnswer("");
    setShowComments(false);
    setNewComment("");
    setShowFeedback(false);
    setDiscursiveAIResult(null);
    setShowAIAnalysis(false);
    setSavedQuestionStatus(null);

    // Carregar resposta e análise salvas para questões dissertativas
    if ((question.question_format || question.answer_type) === 'DISSERTATIVA') {
      loadSavedAnswer();
    }
  }, [question.id]);

  const loadSavedAnswer = async () => {
    try {
      // Verificar se o usuário está autenticado antes de fazer a query
      if (!userId || !sessionId) {
        return;
      }

      const { data, error } = await supabase
        .from('user_answers')
        .select('text_answer, is_correct, ai_analyzed')
        .eq('question_id', question.id)
        .eq('user_id', userId)
        .eq('session_id', sessionId)
        .maybeSingle();

      if (error) {
        return;
      }

      if (data && data.text_answer) {
        setDiscursiveAnswer(data.text_answer);
        setInternalHasAnswered(true);
        setShowFeedback(true);

        // ✅ Para questões dissertativas: só mostrar resultado se já foi analisada pela IA
        if (data.ai_analyzed) {
          setSavedQuestionStatus(data.is_correct);

          // ✅ Notificar o QuestionSolver sobre questões já analisadas (para cores na navegação)
          if (onSetFeedback) {
            onSetFeedback(question.id, data.is_correct);
          }
          if (onMarkAsAnswered) {
            onMarkAsAnswered(question.id);
          }
        } else {
          setSavedQuestionStatus(null); // Aguardando análise
        }
      }
    } catch (error) {
      console.error('❌ [QuestionCard] Erro ao carregar resposta salva:', error);
    }
  };

  const handleSubmit = async () => {
    if (isSubmitting) {
      return;
    }

    const answerType = question.question_format || question.answer_type;

    if (answerType === 'DISSERTATIVA' && !discursiveAnswer.trim()) {
      // Validação silenciosa - o usuário pode ver que o campo está vazio
      return;
    }

    // Para questões de múltipla escolha, verificar se há resposta selecionada
    if (answerType !== 'DISSERTATIVA' && !selectedAnswer) {
      return;
    }

    setIsSubmitting(true);

    try {
      let success = false;

      if (answerType === 'DISSERTATIVA') {
        // Verificar se specialty_id existe, pois é NOT NULL na tabela
        if (!question.specialty?.id) {
          throw new Error("Especialidade é obrigatória para salvar a resposta");
        }

        // ✅ Verificar se já existe resposta para evitar duplicatas
        try {
          const { data: existingAnswer, error: checkError } = await supabase
            .from('user_answers')
            .select('id')
            .eq('user_id', userId)
            .eq('session_id', sessionId)
            .eq('question_id', question.id)
            .maybeSingle();

          if (checkError) {
            // Erro ao verificar resposta existente, continuar com inserção
          } else if (existingAnswer) {
            return; // Já existe resposta
          }
        } catch (error) {
          // Erro ao verificar, continuar com inserção
        }

        const { error } = await supabase
          .from('user_answers')
          .insert({
            user_id: userId,
            question_id: question.id,
            session_id: sessionId,
            text_answer: discursiveAnswer,
            is_correct: false, // ✅ Valor padrão até a análise da IA (questões dissertativas)
            ai_analyzed: false, // ✅ Indica que ainda não foi analisada pela IA
            time_spent: timeSpent,
            specialty_id: question.specialty.id,
            theme_id: question.theme?.id,
            focus_id: question.focus?.id,
            year: question.exam_year || question.year || new Date().getFullYear()
          });

        if (error) {
          throw error;
        }

        // 🔄 INVALIDAR CACHE para atualizar estatísticas
        queryClient.invalidateQueries({ queryKey: ['user-statistics'] });
        queryClient.invalidateQueries({ queryKey: ['correct-questions'] });

        success = true;
        setInternalHasAnswered(true);
        setShowFeedback(true);

        // ✅ Notificar o QuestionSolver que a questão foi respondida (para questões dissertativas)
        if (onMarkAsAnswered) {
          onMarkAsAnswered(question.id);
        }
      } else {
        // Para questões de múltipla escolha, delegar para o QuestionSolver
        if (onSubmitAnswer) {
          await onSubmitAnswer(timeSpent);
          success = true;
          setInternalHasAnswered(true);
          setShowFeedback(true);
        } else {
          console.error('❌ [QuestionCard] onSubmitAnswer não está definido!');
        }
      }
    } catch (error) {
      console.error('❌ [QuestionCard] Erro durante o processo de submissão:', error);
      // Erro silencioso - usuário pode tentar novamente
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGenerateAnalysis = async () => {
    if (loadingAI || !discursiveAnswer.trim()) return;
    setLoadingAI(true);
    setDiscursiveAIResult(null);
    setShowAIAnalysis(true);

    try {
      const requestData = {
        specialty: question.specialty?.name || "",
        theme: question.theme?.name || "",
        focus: question.focus?.name || "",
        statement: question.question_content || question.statement,
        userAnswer: discursiveAnswer,
      };

      console.log('🚀 [QuestionCard] Enviando para IA:', requestData);

      const res = await fetch(
        `https://bxedpdmgvgatjdfxgxij.functions.supabase.co/discursive-ai-analysis`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestData),
        }
      );

      if (!res.ok) {
        throw new Error(`API error: ${res.status} - ${(await res.text())}`);
      }

      const data = await res.json();

      console.log('📥 [QuestionCard] Resposta da IA recebida:', data);

      // Estrutura da resposta da IA atualizada
      const analysisResult = {
        aiAnswer: data.ai_answer || "",
        feedback: data.feedback || "",
        isCorrect: data.is_correct || false,
        scorePercentage: data.score_percentage || 0,
        mainPointsCovered: data.main_points_covered || [],
        missingPoints: data.missing_points || []
      };

      console.log('🔍 [QuestionCard] Resultado processado:', analysisResult);

      setDiscursiveAIResult(analysisResult);

      // 🎯 ATUALIZAR ESTADO LOCAL IMEDIATAMENTE
      setSavedQuestionStatus(analysisResult.isCorrect);

      // 💾 SALVAR ANÁLISE NO BANCO DE DADOS
      await saveAnalysisToDatabase(analysisResult);

    } catch (error) {
      console.error("❌ [QuestionCard] Error generating AI analysis:", error);
      toast({
        title: "Erro ao gerar análise",
        description: "Não foi possível gerar a análise da IA. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoadingAI(false);
    }
  };

  const saveAnalysisToDatabase = async (analysisResult: any) => {
    try {
      // 💾 ESTRATÉGIA SIMPLIFICADA: Salvar apenas o resultado (certo/errado)
      // Não salvar a análise completa para economizar espaço no banco
      const { error: updateError } = await supabase
        .from('user_answers')
        .update({
          is_correct: analysisResult.isCorrect,
          ai_analyzed: true // ✅ Marcar como analisada pela IA
          // ai_commentary removido para economizar dados
        })
        .eq('question_id', question.id)
        .eq('user_id', userId)
        .eq('session_id', sessionId);

      if (updateError) {
        throw updateError;
      }

      // Resultado salvo com sucesso

      // 🔄 INVALIDAR CACHE para atualizar estatísticas
      queryClient.invalidateQueries({ queryKey: ['user-statistics'] });
      queryClient.invalidateQueries({ queryKey: ['correct-questions'] });

      // ✅ Notificar o QuestionSolver sobre o resultado da análise (para cores na navegação)
      if (onSetFeedback) {
        onSetFeedback(question.id, analysisResult.isCorrect);
      }

      toast({
        title: "Análise concluída",
        description: `Resposta ${analysisResult.isCorrect ? 'correta' : 'incorreta'}${analysisResult.scorePercentage ? ` (${analysisResult.scorePercentage}%)` : ''}`,
        variant: analysisResult.isCorrect ? "default" : "destructive"
      });

    } catch (error) {
      console.error('❌ [QuestionCard] Erro ao salvar resultado:', error);
      // Não mostrar erro para o usuário - análise ainda é exibida
    }
  };

  const handleNext = async () => {
    if (onNext) {
      onNext();
      setShowFeedback(false);
      setShowComments(false);
      setNewComment("");
    }
  };

  const handleAddComment = async (text: string) => {
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('comments')
        .eq('id', question.id)
        .single();

      if (error) throw error;

      // Convert JSON to Comment array
      const commentsArray = data.comments ?
        (Array.isArray(data.comments) ? data.comments : []) : [];

      setQuestionData(prev => ({
        ...prev,
        comments: commentsArray as Comment[]
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error fetching updated comments:', error);
    }
  };

  const handleReplyComment = async (commentId: string | number, replyText: string) => {
    try {
      const newReply: Comment = {
        id: crypto.randomUUID(),
        text: replyText,
        user: userId,
        timestamp: new Date().toISOString(),
        likes: 0,
        dislikes: 0,
        likedBy: [],
        dislikedBy: []
      };

      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply]
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error adding reply:', error);
    }
  };

  const handleCommentLike = async (commentId: string | number, isLike: boolean) => {
    try {
      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          const alreadyLiked = comment.likedBy?.includes(userId);
          const alreadyDisliked = comment.dislikedBy?.includes(userId);

          let newLikedBy = comment.likedBy || [];
          let newDislikedBy = comment.dislikedBy || [];
          let newLikes = comment.likes || 0;
          let newDislikes = comment.dislikes || 0;

          if (alreadyLiked) {
            newLikedBy = newLikedBy.filter(id => id !== userId);
            newLikes--;
          }
          if (alreadyDisliked) {
            newDislikedBy = newDislikedBy.filter(id => id !== userId);
            newDislikes--;
          }

          if (isLike && !alreadyLiked) {
            newLikedBy.push(userId);
            newLikes++;
          } else if (!isLike && !alreadyDisliked) {
            newDislikedBy.push(userId);
            newDislikes++;
          }

          return {
            ...comment,
            likes: newLikes,
            dislikes: newDislikes,
            likedBy: newLikedBy,
            dislikedBy: newDislikedBy
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      // Reação registrada - feedback visual através da mudança de cor do botão

    } catch (error) {
      console.error('❌ [QuestionCard] Error updating comment like:', error);
      // Erro silencioso - usuário pode tentar novamente
    }
  };

  const handleReplyLike = async (commentId: string | number, replyId: string | number, isLike: boolean) => {
    try {
      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          const updatedReplies = (comment.replies || []).map(reply => {
            if (reply.id === replyId) {
              const alreadyLiked = reply.likedBy?.includes(userId);
              const alreadyDisliked = reply.dislikedBy?.includes(userId);

              let newLikedBy = reply.likedBy || [];
              let newDislikedBy = reply.dislikedBy || [];
              let newLikes = reply.likes || 0;
              let newDislikes = reply.dislikes || 0;

              if (alreadyLiked) {
                newLikedBy = newLikedBy.filter(id => id !== userId);
                newLikes--;
              }
              if (alreadyDisliked) {
                newDislikedBy = newDislikedBy.filter(id => id !== userId);
                newDislikes--;
              }

              if (isLike && !alreadyLiked) {
                newLikedBy.push(userId);
                newLikes++;
              } else if (!isLike && !alreadyDisliked) {
                newDislikedBy.push(userId);
                newDislikes++;
              }

              return {
                ...reply,
                likes: newLikes,
                dislikes: newDislikes,
                likedBy: newLikedBy,
                dislikedBy: newDislikedBy
              };
            }
            return reply;
          });

          return {
            ...comment,
            replies: updatedReplies
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      // Reação registrada - feedback visual através da mudança de cor do botão

    } catch (error) {
      console.error('❌ [QuestionCard] Error updating reply like:', error);
      // Erro silencioso - usuário pode tentar novamente
    }
  };

  const currentQuestionAnswered = internalHasAnswered || hasAnswered || isAnswered;


  return (
    <Card className="bg-white rounded-xl shadow-lg overflow-hidden border-0">
      <div className="p-6 space-y-6">
        <div className="mb-4">
          <QuestionMetadata question={question} />
        </div>

        <div className="prose max-w-none mb-6 bg-gray-50 p-4 rounded-lg" data-highlighter="statement">
          <FormattedQuestionContent content={question.question_content || question.statement} />
        </div>

      {(question.media_attachments || question.images) && (question.media_attachments || question.images)?.length > 0 && (
        <QuestionImages images={question.media_attachments || question.images} />
      )}

      {(question.question_format || question.answer_type) === 'DISSERTATIVA' ? (
        <>
          <DiscursiveAnswer
            value={discursiveAnswer}
            onChange={setDiscursiveAnswer}
            onSubmit={handleSubmit}
            onAnalyze={currentQuestionAnswered && savedQuestionStatus === null ? handleGenerateAnalysis : undefined}
            hasAnswered={currentQuestionAnswered}
            readOnly={currentQuestionAnswered}
            isAnalyzing={loadingAI}
            hasBeenEvaluated={savedQuestionStatus !== null}
          />

          {currentQuestionAnswered && (
            <>
              {/* 📊 INDICADOR DE STATUS DA QUESTÃO RESPONDIDA */}
              {savedQuestionStatus !== null && !showAIAnalysis && (
                <Card className={`animate-fade-in border-2 shadow-lg mt-4 ${
                  savedQuestionStatus ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                }`}>
                  <div className="py-3 px-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className={`text-lg ${savedQuestionStatus ? 'text-green-700' : 'text-red-700'}`}>
                        {savedQuestionStatus ? '✅' : '❌'}
                      </span>
                      <span className={`font-bold ${savedQuestionStatus ? 'text-green-800' : 'text-red-800'}`}>
                        Questão {savedQuestionStatus ? 'Correta' : 'Incorreta'}
                      </span>
                    </div>
                    <span className={`text-sm ${savedQuestionStatus ? 'text-green-600' : 'text-red-600'}`}>
                      Resposta já avaliada
                    </span>
                  </div>
                </Card>
              )}

              <QuestionFeedback
                question={{...question, discursiveAnswer}}
                selectedAnswer={null}
                onNext={onNext}
                isLastQuestion={false}
                sessionId={sessionId}
                discursiveAnswer={discursiveAnswer}
              />

              {showAIAnalysis && discursiveAIResult && (
                <Card className="animate-fade-in border-2 border-black shadow-lg mt-4">
                  <div className={`border-b-2 border-black py-4 px-6 flex items-center justify-between ${
                    discursiveAIResult.isCorrect ? 'bg-green-50' : 'bg-red-50'
                  }`}>
                    <div className="flex items-center gap-2">
                      <Brain className={`h-5 w-5 ${discursiveAIResult.isCorrect ? 'text-green-700' : 'text-red-700'}`} />
                      <span className={`font-bold ${discursiveAIResult.isCorrect ? 'text-green-900' : 'text-red-900'}`}>
                        Análise da IA
                      </span>
                    </div>
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-bold ${
                      discursiveAIResult.isCorrect
                        ? 'bg-green-200 text-green-800'
                        : 'bg-red-200 text-red-800'
                    }`}>
                      {discursiveAIResult.isCorrect ? '✅ Correta' : '❌ Incorreta'}
                      {discursiveAIResult.scorePercentage !== undefined && discursiveAIResult.scorePercentage > 0 && (
                        <span className="ml-1">({discursiveAIResult.scorePercentage}%)</span>
                      )}
                    </div>
                  </div>
                  <div className="space-y-4 p-4">
                    {discursiveAIResult.aiAnswer && (
                      <div className="rounded-lg p-4 border-2 border-green-200 bg-green-50/50 hover:bg-green-50">
                        <h4 className="font-bold mb-2 text-green-800">Resposta Esperada</h4>
                        <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                          {discursiveAIResult.aiAnswer}
                        </div>
                      </div>
                    )}

                    {discursiveAIResult.feedback && (
                      <div className={`rounded-lg p-4 border-2 ${
                        discursiveAIResult.isCorrect
                          ? 'border-blue-200 bg-blue-50/50 hover:bg-blue-50'
                          : 'border-orange-200 bg-orange-50/50 hover:bg-orange-50'
                      }`}>
                        <h4 className={`font-bold mb-2 flex items-center gap-2 ${
                          discursiveAIResult.isCorrect ? 'text-blue-800' : 'text-orange-800'
                        }`}>
                          <Brain className="h-4 w-4" />
                          Feedback sobre sua resposta
                        </h4>
                        <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                          {discursiveAIResult.feedback}
                        </div>
                      </div>
                    )}

                    {discursiveAIResult.mainPointsCovered && discursiveAIResult.mainPointsCovered.length > 0 && (
                      <div className="rounded-lg p-4 border-2 border-green-200 bg-green-50/50">
                        <h4 className="font-bold mb-2 text-green-800">✅ Pontos Abordados</h4>
                        <ul className="list-disc list-inside space-y-1 text-gray-700">
                          {discursiveAIResult.mainPointsCovered.map((point: string, index: number) => (
                            <li key={index}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {discursiveAIResult.missingPoints && discursiveAIResult.missingPoints.length > 0 && (
                      <div className="rounded-lg p-4 border-2 border-orange-200 bg-orange-50/50">
                        <h4 className="font-bold mb-2 text-orange-800">⚠️ Pontos Não Abordados</h4>
                        <ul className="list-disc list-inside space-y-1 text-gray-700">
                          {discursiveAIResult.missingPoints.map((point: string, index: number) => (
                            <li key={index}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </Card>
              )}
            </>
          )}
        </>
      ) : (
        <>
          <QuestionAlternatives
            alternatives={
              (question.response_choices && question.response_choices.length > 0)
                ? question.response_choices
                : (question.alternatives && question.alternatives.length > 0)
                ? question.alternatives
                : []
            }
            selectedAnswer={selectedAnswer}
            setSelectedAnswer={!currentQuestionAnswered ? onSelectAnswer : undefined}
            hasAnswered={currentQuestionAnswered}
            correct_answer={parseInt((question.correct_choice || question.correct_answer).toString())}
            statistics={question.statistics}
            alternativeComments={question.alternativeComments}
            questionId={question.id}
          />

          {!currentQuestionAnswered && (
            <div className="flex justify-end gap-4">
              <Button
                onClick={handleSubmit}
                disabled={!selectedAnswer || isSubmitting}
                className="w-full md:w-auto"
              >
                {isSubmitting ? "Enviando..." : "Confirmar Resposta"}
              </Button>
            </div>
          )}

          {currentQuestionAnswered && (
            <QuestionFeedback
              question={question}
              selectedAnswer={selectedAnswer}
              onNext={onNext}
              isLastQuestion={false}
              sessionId={sessionId}
            />
          )}
        </>
      )}

      <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-4">
        <QuestionLikeButtons
          questionId={question.id}
          userId={userId}
          initialLikes={question.likes || 0}
          initialDislikes={question.dislikes || 0}
          likedBy={question.liked_by || []}
          dislikedBy={question.disliked_by || []}
        />

        {currentQuestionAnswered && (
          <Button
            variant="outline"
            onClick={() => setShowComments(!showComments)}
            className="flex items-center gap-2"
          >
            <MessageSquare className="h-4 w-4" />
            {showComments ? "Ocultar Comentários" : "Ver Comentários"}
          </Button>
        )}
      </div>

      {showComments && currentQuestionAnswered && (
        <div className="animate-fade-in">
          <QuestionComments
            comments={questionData.comments || []}
            newComment={newComment}
            setNewComment={setNewComment}
            onAddComment={handleAddComment}
            onReplyComment={handleReplyComment}
            onLikeComment={handleCommentLike}
            onReplyLike={handleReplyLike}
            userId={userId}
            questionId={question.id}
          />
        </div>
      )}
      </div>
    </Card>
  );
};

export default QuestionCard;
