import { useEffect, useRef } from 'react';

interface StudyTimerProps {
  isActive: boolean;
  initialSeconds: number;
  onTimeUpdate: (elapsedSeconds: number) => void;
  currentElapsed?: number;
}

export const StudyTimer = ({
  isActive,
  initialSeconds,
  onTimeUpdate,
  currentElapsed = 0,
}: StudyTimerProps) => {
  const intervalRef = useRef<number>();
  const startTimeRef = useRef<number>();
  const elapsedRef = useRef<number>(currentElapsed);

  // Load persisted time when component mounts
  useEffect(() => {
    const persistedData = localStorage.getItem('pomodoro_timer_state');
    if (persistedData) {
      const { startTime, elapsed } = JSON.parse(persistedData);
      if (startTime && elapsed) {
        startTimeRef.current = startTime;
        elapsedRef.current = elapsed;
      }
    }
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Save current state when page is hidden
        if (startTimeRef.current && isActive) {
          const currentElapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
          localStorage.setItem('pomodoro_timer_state', JSON.stringify({
            startTime: startTimeRef.current,
            elapsed: currentElapsed
          }));
        }
      } else {
        // Restore state when page becomes visible
        const persistedData = localStorage.getItem('pomodoro_timer_state');
        if (persistedData && isActive) {
          const { startTime, elapsed } = JSON.parse(persistedData);
          if (startTime && elapsed) {
            const currentElapsed = Math.floor((Date.now() - startTime) / 1000);
            elapsedRef.current = currentElapsed;
            onTimeUpdate(currentElapsed);
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isActive, onTimeUpdate]);

  useEffect(() => {
    if (!isActive) {
      // Save state when timer is paused
      localStorage.setItem('pomodoro_timer_state', JSON.stringify({
        startTime: startTimeRef.current,
        elapsed: elapsedRef.current
      }));
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
      return;
    }

    if (isActive && !intervalRef.current) {
      // Resume from saved state or start new
      const persistedData = localStorage.getItem('pomodoro_timer_state');
      let initialElapsed = 0;
      
      if (persistedData) {
        const { elapsed } = JSON.parse(persistedData);
        initialElapsed = elapsed || 0;
      }

      startTimeRef.current = Date.now() - (initialElapsed * 1000);
      elapsedRef.current = initialElapsed;

      console.log(`⏱️ Timer initializing with:`, {
        initialSeconds,
        currentElapsed: initialElapsed,
        startTime: new Date(startTimeRef.current).toISOString()
      });
      
      intervalRef.current = window.setInterval(() => {
        const now = Date.now();
        const startTime = startTimeRef.current || now;
        const elapsedSeconds = Math.floor((now - startTime) / 1000);
        
        elapsedRef.current = elapsedSeconds;
        console.log(`⏱️ Timer update:`, {
          elapsedSeconds,
          timeLeft: initialSeconds - elapsedSeconds,
          currentTime: new Date(now).toISOString()
        });
        
        onTimeUpdate(elapsedSeconds);

        // Persist current state
        localStorage.setItem('pomodoro_timer_state', JSON.stringify({
          startTime: startTimeRef.current,
          elapsed: elapsedSeconds
        }));
      }, 1000);

      console.log('▶️ Timer started/resumed with initial seconds:', initialSeconds);
    }

    return () => {
      if (intervalRef.current) {
        console.log('🛑 Timer cleanup at:', {
          elapsedSeconds: elapsedRef.current,
          timeLeft: initialSeconds - elapsedRef.current,
          currentTime: new Date().toISOString()
        });
        clearInterval(intervalRef.current);
        intervalRef.current = undefined;
      }
    };
  }, [isActive, onTimeUpdate, initialSeconds]);

  return null;
};