
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AdminMenu } from "@/components/admin/AdminMenu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Check, AlertTriangle } from "lucide-react";
import { QuestionHierarchyStats, QuestionImprovementSuggestion, BatchAnalysisItem } from "@/types/admin";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import QuestionDetailsDialog from "@/components/admin/question-hierarchy/QuestionDetailsDialog";
import BatchProcessingTable from "@/components/admin/question-hierarchy/BatchProcessingTable";

interface Question {
  id: string;
  statement: string;
  alternatives: string[];
  specialty_id: string;
}

interface Category {
  id: string;
  name: string;
  type: string;
}

const QuestionHierarchyImprovement = () => {
  const { toast: uiToast } = useToast();
  const [specialties, setSpecialties] = useState<Category[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [themes, setThemes] = useState<Category[]>([]);
  const [focuses, setFocuses] = useState<Category[]>([]);
  const [hierarchyStats, setHierarchyStats] = useState<QuestionHierarchyStats>({
    totalQuestions: 0,
    questionsWithoutTheme: 0,
    questionsWithoutFocus: 0,
    completedImprovements: 0
  });
  const [batchSize, setBatchSize] = useState<number>(10);
  const [processingBatch, setProcessingBatch] = useState(false);
  const [batchProgress, setBatchProgress] = useState(0);
  const [batchTotal, setBatchTotal] = useState(0);
  const [realTimeBatchItems, setRealTimeBatchItems] = useState<BatchAnalysisItem[]>([]);
  
  // State for question details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  
  useEffect(() => {
    loadSpecialties();
  }, []);

  useEffect(() => {
    if (selectedSpecialty) {
      fetchThemes(selectedSpecialty);
      loadQuestionsWithoutHierarchy();
      fetchHierarchyStats();
    }
  }, [selectedSpecialty]);

  const loadSpecialties = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('*')
        .eq('type', 'specialty')
        .order('name');

      if (error) throw error;
      setSpecialties(data || []);
    } catch (error) {
      console.error('Error loading specialties:', error);
      toast.error('Erro ao carregar especialidades');
    }
  };

  const fetchHierarchyStats = async () => {
    try {
      // Get total questions count for this specialty
      const { count: totalCount, error: totalError } = await supabase
        .from('questions')
        .select('id', { count: 'exact', head: true })
        .eq('specialty_id', selectedSpecialty);

      // Get count of questions without theme
      const { count: withoutThemeCount, error: themeError } = await supabase
        .from('questions')
        .select('id', { count: 'exact', head: true })
        .eq('specialty_id', selectedSpecialty)
        .is('theme_id', null);

      // Get count of questions without focus
      const { count: withoutFocusCount, error: focusError } = await supabase
        .from('questions')
        .select('id', { count: 'exact', head: true })
        .eq('specialty_id', selectedSpecialty)
        .is('focus_id', null);

      if (totalError || themeError || focusError) throw new Error("Error fetching stats");

      // Calculate completed improvements
      const completedImprovements = totalCount && withoutThemeCount !== null
        ? totalCount - withoutThemeCount
        : 0;

      setHierarchyStats({
        totalQuestions: totalCount || 0,
        questionsWithoutTheme: withoutThemeCount || 0,
        questionsWithoutFocus: withoutFocusCount || 0,
        completedImprovements: completedImprovements
      });

    } catch (error) {
      console.error('Error fetching hierarchy stats:', error);
      toast.error('Erro ao carregar estatísticas de hierarquia');
    }
  };

  const fetchThemes = async (specialtyId: string) => {
    try {
      const { data: themesData, error: themesError } = await supabase
        .from('study_categories')
        .select('*')
        .eq('type', 'theme')
        .eq('parent_id', specialtyId)
        .order('name');

      if (themesError) throw themesError;
      setThemes(themesData || []);

      // Load all focuses associated with these themes
      if (themesData && themesData.length > 0) {
        const themeIds = themesData.map(theme => theme.id);
        
        const { data: focusData, error: focusError } = await supabase
          .from('study_categories')
          .select('*')
          .eq('type', 'focus')
          .in('parent_id', themeIds)
          .order('name');

        if (focusError) throw focusError;
        setFocuses(focusData || []);
      }
    } catch (error) {
      console.error('Error loading hierarchy data:', error);
      toast.error('Erro ao carregar temas e focos');
    }
  };

  const loadQuestionsWithoutHierarchy = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('id, statement, alternatives, specialty_id')
        .eq('specialty_id', selectedSpecialty)
        .is('theme_id', null)
        .is('focus_id', null)
        .limit(50);

      if (error) throw error;
      
      if (data && data.length > 0) {
        setQuestions(data);
        setCurrentQuestionIndex(0);
        setRealTimeBatchItems([]);
      } else {
        toast.info('Não foram encontradas questões sem hierarquia para esta especialidade.');
        setQuestions([]);
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error('Erro ao carregar questões');
    } finally {
      setLoading(false);
    }
  };

  const analyzeQuestion = async (questionIndex: number) => {
    if (questions.length === 0 || questionIndex >= questions.length) return null;
    
    const currentQuestion = questions[questionIndex];

    try {      
      // This would typically call an edge function to do AI analysis
      const themesData = themes.map(theme => ({
        id: theme.id,
        name: theme.name
      }));
      
      const focusesData = focuses.map(focus => ({
        id: focus.id,
        name: focus.name,
        themeId: focus.parent_id
      }));
      
      // Call edge function for AI analysis
      const { data, error } = await supabase.functions.invoke('analyze-question-hierarchy', {
        body: {
          questionText: currentQuestion.statement,
          alternatives: currentQuestion.alternatives,
          themes: themesData,
          focuses: focusesData
        }
      });

      if (error) {
        console.error('Error analyzing question:', error);
        throw error;
      }
      
      if (data && data.suggestion) {
        const suggestion = {
          themeId: data.suggestion.themeId,
          themeName: data.suggestion.themeName,
          focusId: data.suggestion.focusId,
          focusName: data.suggestion.focusName,
          confidence: data.suggestion.confidence
        };
        
        return suggestion;
      } else {
        console.error('No suggestion returned from AI:', data);
        return null;
      }
    } catch (error) {
      console.error('Error analyzing question:', error);
      throw error;
    }
  };

  const applySuggestion = async (questionIndex: number, suggestionToApply: QuestionImprovementSuggestion) => {
    if (!suggestionToApply || questions.length === 0 || questionIndex >= questions.length) return false;
    
    const currentQuestion = questions[questionIndex];
    
    try {      
      const { error } = await supabase
        .from('questions')
        .update({
          theme_id: suggestionToApply.themeId,
          focus_id: suggestionToApply.focusId
        })
        .eq('id', currentQuestion.id);

      if (error) throw error;
      
      return true;
    } catch (error) {
      console.error('Error applying suggestion:', error);
      return false;
    }
  };

  const showQuestionDetails = (question: Question) => {
    setSelectedQuestion(question);
    setDetailsDialogOpen(true);
  };

  const processBatch = async () => {
    if (questions.length === 0 || processingBatch) return;
    
    setProcessingBatch(true);
    setBatchProgress(0);
    setRealTimeBatchItems([]);
    
    // Determine how many questions to process (either batchSize or remaining questions)
    const questionsToProcess = Math.min(batchSize, questions.length - currentQuestionIndex);
    setBatchTotal(questionsToProcess);
    
    toast.info(`Iniciando processamento em lote de ${questionsToProcess} questões...`);
    
    // Initialize all batch items with processing state
    const initialBatchItems: BatchAnalysisItem[] = [];
    for (let i = 0; i < questionsToProcess; i++) {
      const questionIndex = currentQuestionIndex + i;
      if (questionIndex >= questions.length) break;
      
      const question = questions[questionIndex];
      initialBatchItems.push({
        questionId: question.id,
        questionStatement: question.statement,
        questionAlternatives: question.alternatives,
        suggestion: null,
        isApplied: false,
        isProcessing: true,
        isError: false
      });
    }
    
    setRealTimeBatchItems(initialBatchItems);
    
    // Process each question sequentially
    for (let i = 0; i < questionsToProcess; i++) {
      const questionIndex = currentQuestionIndex + i;
      if (questionIndex >= questions.length) break;
      
      // Update progress
      setBatchProgress(i + 1);
      
      // Analyze the question
      const question = questions[questionIndex];
      try {
        // Update UI to show this item is being processed
        setRealTimeBatchItems(prev => {
          const updated = [...prev];
          updated[i] = {
            ...updated[i],
            isProcessing: true
          };
          return updated;
        });
        
        const suggestion = await analyzeQuestion(questionIndex);
        
        // Update UI with results
        setRealTimeBatchItems(prev => {
          const updated = [...prev];
          if (suggestion) {
            updated[i] = {
              ...updated[i],
              suggestion,
              isProcessing: false,
              isError: false
            };
          } else {
            updated[i] = {
              ...updated[i],
              isProcessing: false,
              isError: true,
              errorMessage: "Não foi possível gerar uma sugestão"
            };
          }
          return updated;
        });
      } catch (error) {
        console.error("Error processing batch item:", error);
        
        // Update UI to show error
        setRealTimeBatchItems(prev => {
          const updated = [...prev];
          updated[i] = {
            ...updated[i],
            isProcessing: false,
            isError: true,
            errorMessage: error instanceof Error ? error.message : "Erro desconhecido"
          };
          return updated;
        });
      }
      
      // Small delay to prevent overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Update current index for the next batch
    setCurrentQuestionIndex(prev => Math.min(prev + questionsToProcess, questions.length - 1));
    
    setProcessingBatch(false);
    setBatchProgress(0);
    
    const successfulSuggestions = realTimeBatchItems.filter(item => item.suggestion && !item.isError).length;
    toast.success(`Análise em lote concluída! ${successfulSuggestions} sugestões disponíveis para revisão.`);
  };

  const applyBatchSuggestion = async (index: number) => {
    const item = realTimeBatchItems[index];
    if (!item || item.isApplied || item.isError || !item.suggestion) return;
    
    try {
      toast.info(`Aplicando sugestão para questão ${index + 1}...`);
      
      const success = await applySuggestion(currentQuestionIndex + index - realTimeBatchItems.length, item.suggestion);
      
      if (success) {
        // Update the item status
        setRealTimeBatchItems(prev => {
          const updated = [...prev];
          updated[index] = {
            ...updated[index],
            isApplied: true
          };
          return updated;
        });
        
        toast.success(`Sugestão ${index + 1} aplicada com sucesso!`);
        
        // Update stats
        await fetchHierarchyStats();
      } else {
        throw new Error("Falha ao aplicar sugestão");
      }
      
    } catch (error) {
      console.error('Error applying batch suggestion:', error);
      toast.error(`Erro ao aplicar sugestão ${index + 1}`);
    }
  };

  const applyAllBatchSuggestions = async () => {
    const unappliedSuggestions = realTimeBatchItems.filter(
      item => !item.isApplied && !item.isError && item.suggestion
    );
    
    if (unappliedSuggestions.length === 0) {
      toast.info('Todas as sugestões já foram aplicadas ou possuem erros.');
      return;
    }
    
    try {
      toast.info(`Aplicando todas as ${unappliedSuggestions.length} sugestões pendentes...`);
      
      for (let i = 0; i < realTimeBatchItems.length; i++) {
        const item = realTimeBatchItems[i];
        if (!item.isApplied && !item.isError && item.suggestion) {
          await applyBatchSuggestion(i);
        }
      }
      
      toast.success('Todas as sugestões foram aplicadas com sucesso!');
      
      // Opcionalmente, carregar mais questões
      if (currentQuestionIndex >= questions.length - 1) {
        await loadQuestionsWithoutHierarchy();
      }
      
    } catch (error) {
      console.error('Error applying all batch suggestions:', error);
      toast.error('Erro ao aplicar todas as sugestões');
    }
  };

  const viewBatchItemDetails = (index: number) => {
    const item = realTimeBatchItems[index];
    if (!item) return;
    
    // Find the corresponding question in the questions array
    const question = questions.find(q => q.id === item.questionId);
    if (question) {
      showQuestionDetails(question);
    }
  };

  const calculateProgress = () => {
    if (hierarchyStats.totalQuestions === 0) return 0;
    return (hierarchyStats.completedImprovements / hierarchyStats.totalQuestions) * 100;
  };

  return (
    <>
      <Header />
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-8">Melhoria de Hierarquia de Questões</h1>
        
        <AdminMenu />
        
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Selecione uma Especialidade</CardTitle>
            <CardDescription>
              Escolha a especialidade para encontrar questões que precisam de hierarquia
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Select 
                value={selectedSpecialty}
                onValueChange={(value) => {
                  setSelectedSpecialty(value);
                  setRealTimeBatchItems([]);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Selecione uma especialidade" />
                </SelectTrigger>
                <SelectContent>
                  {specialties.map((specialty) => (
                    <SelectItem key={specialty.id} value={specialty.id}>
                      {specialty.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button 
                onClick={loadQuestionsWithoutHierarchy} 
                className="w-full"
                disabled={!selectedSpecialty || loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Carregando...
                  </>
                ) : (
                  'Buscar Questões'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {selectedSpecialty && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Estatísticas de Hierarquia</CardTitle>
              <CardDescription>
                Progresso de classificação das questões para esta especialidade
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium">Progresso</span>
                    <span className="text-sm font-medium">{calculateProgress().toFixed(1)}%</span>
                  </div>
                  <Progress value={calculateProgress()} className="h-2" />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-muted/10 p-4 rounded-lg">
                    <p className="text-sm text-muted-foreground">Total de Questões</p>
                    <p className="text-2xl font-bold">{hierarchyStats.totalQuestions}</p>
                  </div>
                  <div className="bg-muted/10 p-4 rounded-lg">
                    <p className="text-sm text-muted-foreground">Sem Tema/Foco</p>
                    <p className="text-2xl font-bold">{hierarchyStats.questionsWithoutTheme}</p>
                  </div>
                  <div className="bg-muted/10 p-4 rounded-lg">
                    <p className="text-sm text-muted-foreground">Classificadas</p>
                    <p className="text-2xl font-bold">{hierarchyStats.completedImprovements}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {selectedSpecialty && questions.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Processamento em Lote</CardTitle>
              <CardDescription>
                Analise várias questões de uma vez para agilizar a classificação
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row md:items-center gap-4 mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm whitespace-nowrap">Quantidade:</span>
                    <Input
                      type="number"
                      min="1"
                      max="50"
                      value={batchSize}
                      onChange={(e) => setBatchSize(parseInt(e.target.value) || 10)}
                      disabled={processingBatch}
                      className="max-w-[100px]"
                    />
                    <span className="text-sm text-muted-foreground whitespace-nowrap">
                      (Max: {Math.min(50, questions.length - currentQuestionIndex)})
                    </span>
                  </div>
                </div>
                
                <Button
                  onClick={processBatch}
                  disabled={processingBatch || questions.length === 0}
                  className="min-w-[140px]"
                >
                  {processingBatch ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    'Processar Lote'
                  )}
                </Button>
              </div>
              
              {processingBatch && (
                <div className="space-y-2 mb-6">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Progresso: {batchProgress} de {batchTotal}</span>
                    <span>{Math.round((batchProgress / batchTotal) * 100)}%</span>
                  </div>
                  <Progress 
                    value={(batchProgress / batchTotal) * 100} 
                    className="h-2" 
                  />
                </div>
              )}

              {/* Display batch processing results */}
              <div className="mt-4">
                <BatchProcessingTable 
                  items={realTimeBatchItems}
                  onViewDetails={viewBatchItemDetails}
                  onApplySuggestion={applyBatchSuggestion}
                  onApplyAll={applyAllBatchSuggestions}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {selectedSpecialty && questions.length === 0 && !loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-8 text-center space-y-3">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <p className="text-lg font-medium">
                  Não foram encontradas questões sem hierarquia
                </p>
                <p className="text-muted-foreground">
                  Todas as questões desta especialidade já foram classificadas ou não existem questões cadastradas.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Question details dialog */}
      {selectedQuestion && (
        <QuestionDetailsDialog
          isOpen={detailsDialogOpen}
          onClose={() => setDetailsDialogOpen(false)}
          statement={selectedQuestion.statement}
          alternatives={selectedQuestion.alternatives}
        />
      )}
    </>
  );
};

export default QuestionHierarchyImprovement;
