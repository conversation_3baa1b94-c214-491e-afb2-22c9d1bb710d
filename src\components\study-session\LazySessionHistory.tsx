import React, { useState, useEffect, useRef } from 'react';
import { SessionHistory } from './SessionHistory';
import { History, BookOpen, Zap } from 'lucide-react';

/**
 * Wrapper lazy para SessionHistory que só carrega dados quando visível
 */
const LazySessionHistory: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [canLoad, setCanLoad] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Delay inicial para evitar carregamento imediato
  useEffect(() => {
    const timer = setTimeout(() => {
      setCanLoad(true);
    }, 4000); // 4 segundos de delay (1 segundo a mais que TodayStudies)

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!canLoad) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          // Desconectar observer após carregar
          observer.disconnect();
        }
      },
      {
        rootMargin: '-50px', // Só carrega quando realmente visível
        threshold: 0.3
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasLoaded, canLoad]);

  return (
    <div ref={ref}>
      {isVisible ? (
        <SessionHistory />
      ) : (
        <div className="bg-white rounded-lg border-2 border-black shadow-card-sm">
          {/* Header skeleton */}
          <div className="flex items-center justify-between p-4 sm:p-6 border-b-2 border-black">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-gradient-to-r from-red-400 to-red-300 rounded-full border-2 border-black animate-pulse">
                <History className="h-5 w-5 sm:h-6 sm:w-6 text-white opacity-70" />
              </div>
              <div className="h-5 sm:h-6 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-40 sm:w-48"></div>
            </div>
          </div>

          {/* Tabs skeleton */}
          <div className="flex w-full gap-2 px-4 sm:px-6 py-4 sm:py-6 border-b border-gray-200">
            <div className="flex-1 py-2 px-2 sm:px-4 rounded-lg border-2 border-black bg-gradient-to-r from-yellow-300 to-yellow-200 animate-pulse">
              <div className="flex items-center justify-center gap-2">
                <BookOpen className="h-4 w-4 text-gray-600 opacity-70" />
                <div className="h-4 w-16 bg-gray-500 rounded animate-pulse opacity-70"></div>
              </div>
            </div>
            <div className="flex-1 py-2 px-2 sm:px-4 rounded-lg border-2 border-black bg-gray-100 animate-pulse">
              <div className="flex items-center justify-center gap-2">
                <Zap className="h-4 w-4 text-gray-500 opacity-70" />
                <div className="h-4 w-20 bg-gray-400 rounded animate-pulse opacity-70"></div>
              </div>
            </div>
          </div>

          {/* Content skeleton */}
          <div className="p-4 sm:p-6 space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="p-4 bg-white border-2 border-black rounded-lg shadow-card-sm animate-pulse">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                  <div className="flex flex-col space-y-3 flex-1">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-blue-300 rounded-full animate-pulse border-2 border-black"></div>
                      <div className="h-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-32"></div>
                      <div className="h-6 bg-gradient-to-r from-green-400 to-green-300 rounded-full animate-pulse w-20 border border-green-500"></div>
                    </div>
                    <div className="h-3 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-24"></div>
                    <div className="h-2 bg-gradient-to-r from-yellow-300 to-yellow-200 rounded-full animate-pulse w-full border border-gray-300"></div>
                    <div className="flex gap-2">
                      <div className="h-8 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-20 border border-gray-200"></div>
                      <div className="h-8 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-20 border border-gray-200"></div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <div className="h-8 w-20 bg-gradient-to-r from-yellow-400 to-yellow-300 rounded animate-pulse border-2 border-black"></div>
                    <div className="h-8 w-8 bg-gradient-to-r from-red-300 to-red-200 rounded animate-pulse border-2 border-black"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LazySessionHistory;
