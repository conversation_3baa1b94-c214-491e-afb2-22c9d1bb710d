
import { UseFormReturn } from "react-hook-form";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON>Circle2, Info, Lightbulb } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import type { AIScheduleFormData, GenerationPhase } from "../types";
import { calculateTotalHours } from "../utils/validation";

interface ProgressSectionProps {
  isLoading: boolean;
  isComplete: boolean;
  progress: number;
  progressMessage: string;
  generationPhase: GenerationPhase;
  topicsCreatedRef: React.RefObject<number>;
  handleClose: () => void;
  form: UseFormReturn<AIScheduleFormData>;
}

export const ProgressSection = ({
  isLoading,
  isComplete,
  progress,
  progressMessage,
  generationPhase,
  topicsCreatedRef,
  handleClose,
  form
}: ProgressSectionProps) => {
  const { watch } = form;
  const availableDays = watch('availableDays');
  const scheduleOption = watch('scheduleOption');
  const weeksCount = watch('weeksCount');
  const topicDuration = watch('topicDuration');

  const totalHours = calculateTotalHours(availableDays);
  const enabledDaysCount = Object.values(availableDays).filter(day => day.enabled).length;

  // Log important state changes for debugging
  console.log(`🔄 [ProgressSection] Current state: isLoading=${isLoading}, isComplete=${isComplete}, progress=${progress}%`);
  console.log(`🔄 [ProgressSection] Current phase: ${generationPhase}, topicsCreated=${topicsCreatedRef.current}`);

  return (
    <div className="py-4 space-y-8 animate-in fade-in">
      {/* Warning not to close during generation */}
      {isLoading && !isComplete && (
        <div className="flex p-4 space-x-3 border-2 rounded-lg shadow-md bg-amber-50 border-amber-200">
          <AlertTriangle className="flex-shrink-0 w-5 h-5 text-amber-500 mt-0.5" />
          <div className="space-y-1">
            <h4 className="font-bold text-amber-800">Atenção! Não feche esta janela</h4>
            <p className="text-sm text-amber-700">
              A geração do cronograma está em andamento. Fechar agora interromperá o processo.
            </p>
          </div>
        </div>
      )}
      
      {/* Progress bar and status */}
      <div className="p-6 rounded-xl bg-white shadow-lg border-2 border-white">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-base font-bold text-blue-700">{progressMessage}</p>
            <span className="px-3 py-1 text-sm font-bold text-blue-700 rounded-full bg-blue-100">{Math.round(progress)}%</span>
          </div>
          
          <Progress 
            value={progress} 
            className="h-3 w-full bg-blue-100" 
          />
          
          <div className="pt-2 space-y-2 text-sm text-slate-600">
            <p className="flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
              Analisando {enabledDaysCount} dias da semana
            </p>
            <p className="flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
              Preparando {scheduleOption === "new" ? weeksCount : 1} semana(s) de estudos
            </p>
            <p className="flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
              Tópicos com duração de {topicDuration} minutos
            </p>
            <p className="flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
              Total de horas: {totalHours.toFixed(1)} horas por semana
            </p>
          </div>
        </div>
        
        {/* Generation phases tracker */}
        <div className="mt-8 pt-6 border-t-2 border-slate-100">
          <h4 className="mb-4 text-base font-bold text-blue-700">Progresso da geração:</h4>
          
          <div className="space-y-4">
            {/* Creation phase */}
            <div className="flex items-center">
              <div className={cn(
                "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                generationPhase === "creating_weeks"
                  ? "bg-blue-100"
                  : generationPhase > "creating_weeks"
                    ? "bg-green-100"
                    : "bg-slate-100"
              )}>
                {generationPhase === "creating_weeks" ? (
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                ) : generationPhase > "creating_weeks" ? (
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                ) : (
                  <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                )}
              </div>
              <span className={cn(
                "text-sm font-medium",
                generationPhase === "creating_weeks"
                  ? "text-blue-700"
                  : generationPhase > "creating_weeks"
                    ? "text-green-700"
                    : "text-slate-500"
              )}>
                Criando estrutura de semanas
              </span>
            </div>
            
            {/* Analysis phase */}
            <div className="flex items-center">
              <div className={cn(
                "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                generationPhase === "analyzing_specialties"
                  ? "bg-blue-100"
                  : generationPhase > "analyzing_specialties"
                    ? "bg-green-100"
                    : "bg-slate-100"
              )}>
                {generationPhase === "analyzing_specialties" ? (
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                ) : generationPhase > "analyzing_specialties" ? (
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                ) : (
                  <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                )}
              </div>
              <span className={cn(
                "text-sm font-medium",
                generationPhase === "analyzing_specialties"
                  ? "text-blue-700"
                  : generationPhase > "analyzing_specialties"
                    ? "text-green-700"
                    : "text-slate-500"
              )}>
                Analisando especialidades disponíveis
              </span>
            </div>
            
            {/* Topic generation phase */}
            <div className="flex items-center">
              <div className={cn(
                "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                generationPhase === "generating_topics"
                  ? "bg-blue-100"
                  : generationPhase > "generating_topics"
                    ? "bg-green-100"
                    : "bg-slate-100"
              )}>
                {generationPhase === "generating_topics" ? (
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                ) : generationPhase > "generating_topics" ? (
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                ) : (
                  <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                )}
              </div>
              <span className={cn(
                "text-sm font-medium",
                generationPhase === "generating_topics"
                  ? "text-blue-700"
                  : generationPhase > "generating_topics"
                    ? "text-green-700"
                    : "text-slate-500"
              )}>
                Gerando tópicos de estudo
              </span>
            </div>
            
            {/* Completion phase */}
            <div className="flex items-center">
              <div className={cn(
                "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                generationPhase === "completing"
                  ? "bg-blue-100"
                  : generationPhase > "completing"
                    ? "bg-green-100"
                    : "bg-slate-100"
              )}>
                {generationPhase === "completing" ? (
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                ) : generationPhase > "completing" ? (
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                ) : (
                  <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                )}
              </div>
              <span className={cn(
                "text-sm font-medium",
                generationPhase === "completing"
                  ? "text-blue-700"
                  : generationPhase > "completing"
                    ? "text-green-700"
                    : "text-slate-500"
              )}>
                Finalizando cronograma
              </span>
            </div>
          </div>
          
          {topicsCreatedRef.current > 0 && (
            <div className="flex items-center gap-2 px-4 py-3 mt-6 bg-blue-50 border-2 border-blue-100 rounded-lg">
              <Lightbulb className="w-5 h-5 text-blue-600" />
              <p className="text-sm font-bold text-blue-700">
                {topicsCreatedRef.current} tópicos gerados
                {generationPhase !== "completed" && " até o momento"}
              </p>
            </div>
          )}
        </div>
      </div>
      
      {/* Success message */}
      {isComplete ? (
        <div className="p-8 rounded-xl border-2 border-green-200 bg-gradient-to-br from-green-50 to-green-100 shadow-lg">
          <div className="flex gap-6">
            <div className="flex-shrink-0">
              <div className="p-4 bg-white rounded-full shadow-md">
                <CheckCircle2 className="w-8 h-8 text-green-600" />
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold text-green-800">Cronograma gerado com sucesso!</h3>
                <p className="text-base text-green-700 mt-1">Seu cronograma personalizado foi criado e está disponível para visualização.</p>
              </div>
              <Button 
                onClick={handleClose} 
                className="px-8 py-6 text-base font-bold text-white bg-green-600 hover:bg-green-700 shadow-md border-b-2 border-green-800 rounded-lg"
              >
                Visualizar Cronograma
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-3 p-4 border-2 rounded-lg bg-white border-slate-100 shadow-md">
          <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
          <span className="font-bold text-slate-700">
            {generationPhase === 'not_started' ? 'Preparando...' :
             generationPhase === 'creating_weeks' ? 'Criando estrutura de semanas...' :
             generationPhase === 'analyzing_specialties' ? 'Analisando especialidades disponíveis...' :
             generationPhase === 'generating_topics' ? 'Gerando tópicos de estudo...' :
             generationPhase === 'completing' ? 'Finalizando cronograma...' :
             generationPhase === 'completed' ? 'Cronograma concluído!' :
             'Erro na geração'}
          </span>
        </div>
      )}
    </div>
  );
};
