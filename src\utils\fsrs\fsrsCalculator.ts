import { FSRS_WEIGHTS } from "./parameters";
import type { FSRSMetrics } from "./types";

// Classe para cálculo das métricas FSRS
export class FSRSCalculator {
  private metrics: FSRSMetrics;

  constructor(metrics: FSRSMetrics) {
    this.metrics = {
      stability: metrics.stability || FSRS_WEIGHTS.w1,
      difficulty: metrics.difficulty || FSRS_WEIGHTS.w2,
      retrievability: metrics.retrievability || FSRS_WEIGHTS.w17,
      intervalInDays: metrics.intervalInDays || 1, // Garantindo que intervalInDays esteja presente
      nextReviewDate: metrics.nextReviewDate || new Date(), // Garantindo que nextReviewDate esteja presente
      lastReviewDate: metrics.lastReviewDate
    };

    //console.log('🔍 [FSRSCalculator] Inicializando com métricas:', this.metrics);
    //console.log('🔧 [FSRSCalculator] Parâmetros atuais de FSRS:', FSRS_WEIGHTS);
  }

  // Método para calcular a nova estabilidade de um flashcard
calculateNewStability(response: 'easy' | 'medium' | 'hard' | 'error'): number {
  const elapsedDays = this.getElapsedDays();
  let decayFactor = Math.exp(-FSRS_WEIGHTS.w14 * elapsedDays);  // Decaimento do fator de estabilidade

  //console.log(`🔍 [FSRSCalculator] Dias passados desde a última revisão: ${elapsedDays}`);
  //console.log(`🔍 [FSRSCalculator] Fator de decaimento calculado: ${decayFactor}`);

  if (this.metrics.stability > 100) {
    decayFactor *= 0.9;  // Reduz o decaimento para cartas mais antigas
    //console.log('🔍 [FSRSCalculator] Ajustando fator de decaimento para alta estabilidade:', decayFactor);
  }

  if (response === 'error') {
    // Reduz drasticamente a estabilidade para "Again"
    const newStability = Math.max(FSRS_WEIGHTS.w4, this.metrics.stability * 0.2);
   // console.log(`📉 [FSRSCalculator] Nova estabilidade para resposta "error": ${newStability}`);
    return newStability;
  }

  const stabilityMultiplier = this.getStabilityMultiplier(response);
  const difficultyImpact = 1 + FSRS_WEIGHTS.w5 * (1 - this.metrics.difficulty / FSRS_WEIGHTS.w8);
  const retrievabilityImpact = 1 + FSRS_WEIGHTS.w6 * (this.metrics.retrievability - 0.5);

  //console.log(`🔍 [FSRSCalculator] Multiplicador de estabilidade para "${response}": ${stabilityMultiplier}`);
  //console.log(`🔍 [FSRSCalculator] Impacto da dificuldade: ${difficultyImpact}`);
  //console.log(`🔍 [FSRSCalculator] Impacto da recuperabilidade: ${retrievabilityImpact}`);

  let newStability = this.metrics.stability * stabilityMultiplier * difficultyImpact * retrievabilityImpact * decayFactor;

  // Adicionando limites para estabilidade
  //console.log(`📊 [FSRSCalculator] Estabilidade calculada antes da aplicação do limite: ${newStability}`);
  newStability = Math.max(FSRS_WEIGHTS.w4, Math.min(365, newStability));
  //console.log(`📊 [FSRSCalculator] Estabilidade final após a aplicação do limite: ${newStability}`);

  //console.log(`📈 [FSRSCalculator] Nova estabilidade calculada para "${response}": ${newStability}`);

  return newStability;
}

  // Método para calcular a nova dificuldade
  calculateNewDifficulty(response: 'easy' | 'medium' | 'hard' | 'error'): number {
    let difficultyChange: number;
  
    if (response === 'error') {
      difficultyChange = FSRS_WEIGHTS.w11 * 2;  // Dobrar a dificuldade para erros
    } else {
      difficultyChange = this.getDifficultyChange(response);
    }
  
    // Amplificação da mudança de dificuldade proporcional à estabilidade
    const amplificationFactor = 1 + (this.metrics.stability / 50);
    const adjustedDifficultyChange = difficultyChange * amplificationFactor;
  
    let newDifficulty = this.metrics.difficulty + adjustedDifficultyChange;
  
    // Garantindo limites para dificuldade
    newDifficulty = Math.max(1, Math.min(10, newDifficulty));
    //console.log(`🎯 [FSRSCalculator] Ajuste da dificuldade para resposta "${response}": ${difficultyChange}`);
    //console.log(`🎯 [FSRSCalculator] Fator de amplificação aplicado: ${amplificationFactor}`);
    //console.log(`🎯 [FSRSCalculator] Nova dificuldade calculada: ${newDifficulty}`);
  
    return newDifficulty;
  }

  // Método para calcular a nova recuperabilidade (retrievability)
calculateNewRetrievability(): number {
  const elapsedDays = this.getElapsedDays();
  let newRetrievability = Math.exp(-elapsedDays / this.metrics.stability);

  // Garantindo limites para recuperabilidade
  newRetrievability = Math.max(0.1, Math.min(1, newRetrievability));

  //console.log(`🔄 [FSRSCalculator] Dias passados para calcular recuperabilidade: ${elapsedDays}`);
  //console.log(`🔄 [FSRSCalculator] Nova recuperabilidade calculada: ${newRetrievability}`);

  return newRetrievability;
}

  // Método para calcular o novo intervalo em dias com base na resposta
  calculateNewIntervalInDays(response: 'easy' | 'medium' | 'hard' | 'error', stability: number): number {
    let multiplier: number;
  
    switch (response) {
      case 'error':
        multiplier = 5 / (24 * 60);  // 5 minutos para erros
        break;
      case 'hard':
        multiplier = 2;  // Intervalo mais curto para difícil
        break;
      case 'medium':
        multiplier = 3;  // Intervalo médio para "medium"
        break;
      case 'easy':
        multiplier = 4;  // Intervalo maior para "fácil"
        break;
      default:
        multiplier = 1;
        break;
    }
  
    //console.log(`🔍 [FSRSCalculator] Multiplicador para resposta "${response}": ${multiplier}`);
  
    let intervalInDays = stability * multiplier;
  
    // Garantindo limites para intervalo
    intervalInDays = Math.max(5 / (24 * 60), Math.min(365, intervalInDays));
    //console.log(`📅 [FSRSCalculator] Novo intervalo em dias calculado para "${response}": ${intervalInDays}`);
  
    return Math.round(intervalInDays);
  }

  // Método para calcular as novas métricas de um flashcard
  calculateNewMetrics(response: 'easy' | 'medium' | 'hard' | 'error'): FSRSMetrics {
    const newStability = this.calculateNewStability(response);
    const newDifficulty = this.calculateNewDifficulty(response);
    const newRetrievability = Math.exp(-1 / newStability);
    const intervalInDays = this.calculateNewIntervalInDays(response, newStability);

    const nextReviewDate = new Date(new Date().getTime() + (intervalInDays * 24 * 60 * 60 * 1000));

    // console.log(`✨ [FSRSCalculator] Métricas finais para resposta "${response}":`, {
    //   newStability,
    //   newDifficulty,
    //   newRetrievability,
    //   intervalInDays,
    //   nextReviewDate
    // });

    return {
      stability: Math.max(FSRS_WEIGHTS.w4, Math.min(365, newStability)),
      difficulty: Math.max(1, Math.min(10, newDifficulty)),
      retrievability: Math.max(0.1, Math.min(1, newRetrievability)),
      intervalInDays: intervalInDays,
      nextReviewDate: nextReviewDate,
      lastReviewDate: new Date() // Atualizando a data da última revisão
    };
  }

  // Método para obter os dias desde a última revisão
  private getElapsedDays(): number {
    if (!this.metrics.lastReviewDate) return 0;
    const lastReview = new Date(this.metrics.lastReviewDate);
    const elapsedDays = (new Date().getTime() - lastReview.getTime()) / (1000 * 60 * 60 * 24);
    //console.log(`🕒 [FSRSCalculator] Dias passados desde a última revisão: ${elapsedDays}`);
    return elapsedDays;
  }

  // Método para obter o multiplicador de estabilidade baseado na resposta do usuário
  private getStabilityMultiplier(response: 'easy' | 'medium' | 'hard' | 'error'): number {
  const multiplier = {
    easy: 1 + FSRS_WEIGHTS.w13,        // Promover crescimento controlado.
    medium: 1.2,                       // Aumentar ligeiramente para premiar respostas boas (antes era 1).
    hard: 0.8 * FSRS_WEIGHTS.w4,       // Reduzir para penalizar respostas difíceis (antes era FSRS_WEIGHTS.w4 * (1 - FSRS_WEIGHTS.w14)).
    error: 0.2                         // Reduz drasticamente a estabilidade.
  }[response];

  //console.log(`🔍 [FSRSCalculator] Multiplicador de estabilidade calculado para "${response}": ${multiplier}`);
  return multiplier;
}

  // Método para calcular o ajuste de dificuldade baseado na resposta
  private getDifficultyChange(response: 'easy' | 'medium' | 'hard' | 'error'): number {
    const difficultyChange = {
      easy: -FSRS_WEIGHTS.w11,
      medium: 0,
      hard: FSRS_WEIGHTS.w11,
      error: FSRS_WEIGHTS.w11 * 2
    }[response];

    //console.log(`🔧 [FSRSCalculator] Mudança de dificuldade para resposta "${response}": ${difficultyChange}`);
    return difficultyChange;
  }
}
