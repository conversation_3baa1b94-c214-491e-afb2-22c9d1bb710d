
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import Header from "@/components/Header";
import { AdminMenu } from "@/components/admin/AdminMenu";
import { AdminStats } from "@/components/admin/AdminStats";
import { useAdminData } from "@/hooks/useAdminData";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();

  // ✅ Usar hook centralizado para admin
  const { isAdmin, isLoading: adminLoading, error: adminError } = useAdminData();

  useEffect(() => {
    // ✅ Verificação de admin simplificada e segura
    if (!adminLoading) {
      if (adminError) {
        console.error("❌ [AdminDashboard] Erro ao verificar admin:", adminError);
        uiToast({
          title: "Erro de verificação",
          description: "Erro ao verificar permissões de administrador",
          variant: "destructive",
        });
        navigate("/plataformadeestudos");
        return;
      }

      if (!isAdmin) {
        console.log("❌ [AdminDashboard] Acesso negado: usuário não é administrador");
        uiToast({
          title: "Acesso restrito",
          description: "Esta área é restrita a administradores",
          variant: "destructive",
        });
        navigate("/plataformadeestudos");
        return;
      }

      console.log("✅ [AdminDashboard] Acesso de administrador confirmado");
    }
  }, [isAdmin, adminLoading, adminError, navigate, uiToast]);

  if (adminLoading) {
    return (
      <>
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
            <h2 className="text-xl font-semibold mb-2">Verificando permissões</h2>
            <p className="text-muted-foreground">Aguarde enquanto verificamos seu acesso...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-8">Painel Administrativo</h1>

        {/* Menu administrativo */}
        <AdminMenu />

        {/* Estatísticas */}
        <AdminStats />

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Opções de Administração</CardTitle>
            <CardDescription>
              Gerencie os principais recursos da plataforma
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="users">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="users">Usuários</TabsTrigger>
                <TabsTrigger value="questions">Questões</TabsTrigger>
                <TabsTrigger value="settings">Configurações</TabsTrigger>
              </TabsList>
              <TabsContent value="users" className="p-4">
                <h3 className="text-lg font-medium mb-2">Gerenciamento de Usuários</h3>
                <p className="text-muted-foreground mb-4">
                  Adicione, remova ou edite usuários da plataforma. Gerencie permissões e acesso.
                </p>
                <div className="border rounded-md p-4 bg-muted/20">
                  <p className="text-center text-muted-foreground">
                    Funcionalidade em desenvolvimento
                  </p>
                </div>
              </TabsContent>
              <TabsContent value="questions" className="p-4">
                <h3 className="text-lg font-medium mb-2">Gerenciamento de Questões</h3>
                <p className="text-muted-foreground mb-4">
                  Aprove novas questões, edite ou remova questões existentes.
                </p>
                <div className="border rounded-md p-4 bg-muted/20">
                  <p className="text-center text-muted-foreground">
                    Funcionalidade em desenvolvimento
                  </p>
                </div>
              </TabsContent>
              <TabsContent value="settings" className="p-4">
                <h3 className="text-lg font-medium mb-2">Configurações do Sistema</h3>
                <p className="text-muted-foreground mb-4">
                  Configure parâmetros globais, categorias e outras opções do sistema.
                </p>
                <div className="border rounded-md p-4 bg-muted/20">
                  <p className="text-center text-muted-foreground">
                    Funcionalidade em desenvolvimento
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default AdminDashboard;
