
import { Json } from "@/integrations/supabase/types/json";

export interface Comment extends Record<string, Json> {
  id: string | number;
  user: string;
  text: string;
  timestamp: string;
  replies?: Comment[];
  likes?: number;
  dislikes?: number;
  likedBy?: string[];
  dislikedBy?: string[];
}
export interface CommentListProps {
  comments: Comment[];
  newComment: string;
  setNewComment: (comment: string) => void;
  onAddComment: (text: string) => void;
  onReplyComment: (commentId: string | number, replyText: string) => void;
  onLikeComment: (commentId: string | number, isLike: boolean) => void;
}
