
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Search } from "lucide-react";

interface FilterHeaderProps {
  questionCount: number;
  isLoading: boolean;
  onStartStudy: () => void;
}

export function FilterHeader({ questionCount, isLoading, onStartStudy }: FilterHeaderProps) {
  return (
    <motion.div 
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6"
    >
      <div className="relative rounded-xl overflow-hidden mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B00] via-[#FF8800] to-[#FF4D00] rounded-lg"></div>
        <motion.div 
          className="relative p-6 md:p-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 p-2 rounded-full">
                  <Search className="h-7 w-7 text-white" />
                </div>
                <h1 className="text-3xl md:text-4xl font-bold text-white">
                  Banco de Questões
                </h1>
              </div>
              <p className="text-white/90 text-base md:text-lg max-w-2xl leading-relaxed">
                Explore nossa coleção de questões e filtre de acordo com suas necessidades
              </p>
            </div>
            
            <div className="flex items-center gap-4 w-full md:w-auto">
              <div className="flex flex-col items-center justify-center bg-[#FEF7CD] p-4 rounded-xl shadow-md border-2 border-black w-full md:w-auto">
                <span className="text-sm text-gray-700 mb-1">
                  {isLoading ? 'Carregando...' : 'Total de questões filtradas'}
                </span>
                <span className="text-2xl font-extrabold text-gray-800">
                  {isLoading ? '-' : questionCount.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
