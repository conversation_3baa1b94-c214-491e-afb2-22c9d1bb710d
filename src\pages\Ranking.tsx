
import React from 'react';
import { Trophy, Clock, Calendar, BookOpen, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import StudyNavBar from '@/components/study/StudyNavBar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const RankingIntro = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />
      <StudyNavBar className="mb-8" />

      <div className="container max-w-6xl mx-auto px-4 pt-16 md:pt-8 space-y-8 animate-fade-in">
        <div className="relative mb-12">
          <div className="inline-block transform -rotate-2 mb-4">
            <div className="bg-hackathon-red border-2 border-black px-4 py-1 text-white font-bold tracking-wide text-sm shadow-card-sm">
              RANKING
            </div>
          </div>

          <h1 className="text-5xl font-black leading-none mb-6">
            <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
              Ranking de Estudantes
            </span>
            <span className="block text-hackathon-red">Desafie-se e Destaque-se!</span>
          </h1>

          <div className="grid md:grid-cols-2 gap-8 mt-12">
            <div className="space-y-6">
              <Card className="p-8 border-2 border-black bg-white shadow-card relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 transform translate-x-16 -translate-y-16">
                  <div className="w-full h-full bg-hackathon-yellow rounded-full opacity-20"></div>
                </div>
                <h2 className="text-2xl font-bold mb-4">Ranking de Questões 📚</h2>
                <p className="text-gray-700 mb-6">
                  Compare seu desempenho com outros estudantes baseado no número de questões respondidas.
                  Pratique consistentemente para subir no ranking!
                </p>
                <Button 
                  onClick={() => navigate('/ranking/questions')}
                  className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 text-black border-2 border-black font-bold shadow-card-sm transform transition-transform hover:-translate-y-1"
                >
                  Ver Ranking de Questões
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Card>

              <Card className="p-8 border-2 border-black bg-white shadow-card relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 transform translate-x-16 -translate-y-16">
                  <div className="w-full h-full bg-hackathon-green rounded-full opacity-20"></div>
                </div>
                <h2 className="text-2xl font-bold mb-4">Períodos Disponíveis ⏱️</h2>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-gray-700">
                    <Clock className="h-5 w-5 text-hackathon-red" />
                    <span>Todo Período - Seu desempenho geral</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-700">
                    <Calendar className="h-5 w-5 text-hackathon-red" />
                    <span>Semanal - Sua dedicação nos últimos 7 dias</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-700">
                    <BookOpen className="h-5 w-5 text-hackathon-red" />
                    <span>Diário - Seu foco nas últimas 24 horas</span>
                  </div>
                </div>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="p-8 border-2 border-black bg-white shadow-card">
                <h2 className="text-2xl font-bold mb-4">Por que Participar? 🎯</h2>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 flex items-center justify-center bg-hackathon-yellow rounded-full border-2 border-black shrink-0">
                      <Trophy className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">Competição Saudável</h3>
                      <p className="text-gray-700">
                        Compare seu progresso com outros estudantes e mantenha-se motivado para estudar mais.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 flex items-center justify-center bg-hackathon-green rounded-full border-2 border-black shrink-0">
                      <Trophy className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">Consistência</h3>
                      <p className="text-gray-700">
                        Desenvolva uma rotina regular de estudos para manter sua posição no ranking.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 flex items-center justify-center bg-hackathon-red rounded-full border-2 border-black shrink-0">
                      <Trophy className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">Reconhecimento</h3>
                      <p className="text-gray-700">
                        Destaque-se entre os melhores estudantes e inspire outros com seu desempenho.
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-8 border-2 border-black bg-white shadow-card">
                <h2 className="text-2xl font-bold mb-4">Como Funciona? 🤔</h2>
                <div className="space-y-4 text-gray-700">
                  <p className="flex items-center gap-2">
                    <span className="w-6 h-6 rounded-full bg-hackathon-yellow border-2 border-black flex items-center justify-center text-sm font-bold">1</span>
                    Responda questões regularmente para acumular pontos
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="w-6 h-6 rounded-full bg-hackathon-yellow border-2 border-black flex items-center justify-center text-sm font-bold">2</span>
                    Acompanhe sua posição em diferentes períodos de tempo
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="w-6 h-6 rounded-full bg-hackathon-yellow border-2 border-black flex items-center justify-center text-sm font-bold">3</span>
                    Compare seu desempenho com outros estudantes
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RankingIntro;
