
import React, { memo, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { Trophy, Clock, Target, BookO<PERSON>, <PERSON><PERSON>hart, ArrowUp, ArrowDown, Brain, Info, HelpCircle, Zap } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { TimeExplanationDialog } from './TimeExplanationDialog';
import { useStreakSystem } from '@/hooks/useOptimizedStreakStats';

interface LearningInsightsProps {
  stats: {
    accuracy: number;
    averageTime: number;
    totalQuestions: number; // Questões únicas
    totalAnswers?: number; // Total de respostas
    totalTimeSpent: number;
    weeklyChange: {
      accuracy: number;
      questions: number;
    };
  };
}

const LearningInsights = memo(({ stats }: LearningInsightsProps) => {
  // Usar sistema de sequência otimizado
  const { currentStreak, maxStreak, isLoading: streakLoading } = useStreakSystem();

  // ✅ Otimizar formatTime com useMemo
  const formattedTime = useMemo(() => {
    const seconds = stats.totalStudyTime; // ✅ CORRIGIDO: usar totalStudyTime
    if (!seconds || isNaN(seconds)) return "0 minutos";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes} minutos`;
  }, [stats.totalStudyTime]);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  // Componente de diálogo de ajuda para sequências
  const StreakHelpDialog = () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 rounded-full hover:bg-gray-100 transition-colors"
          title="Como funciona a sequência?"
        >
          <HelpCircle className="h-4 w-4 text-gray-500" />
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[80dvw] max-w-md max-h-[80dvh] overflow-y-auto rounded-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-orange-500" />
            Sistema de Sequência de Estudos
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">🔥 O que conta como "Dia de Estudo"?</h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Responder 1+ questão</strong>
                  <p className="text-gray-600">Qualquer questão respondida na plataforma</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Completar 1+ sessão</strong>
                  <p className="text-gray-600">Simulados, revisões ou estudos dirigidos</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Marcar cronograma como estudado</strong>
                  <p className="text-gray-600">Itens do seu cronograma pessoal</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-800 mb-2">📊 Como funciona o cálculo?</h4>
            <div className="space-y-2">
              <div>
                <strong className="text-orange-600">Sequência Atual:</strong>
                <p className="text-gray-600">Dias consecutivos de estudo (incluindo hoje ou ontem)</p>
              </div>
              <div>
                <strong className="text-amber-600">Recorde:</strong>
                <p className="text-gray-600">Sua maior sequência histórica (nunca diminui)</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-800 mb-2">⏰ Regras de Tempo</h4>
            <div className="space-y-1 text-gray-600">
              <p>• Baseado no seu fuso horário local</p>
              <p>• Dia = 00:00 às 23:59 (horário local)</p>
              <p>• Múltiplas atividades no mesmo dia = 1 dia</p>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <p className="text-blue-800 text-sm">
              <strong>💡 Dica:</strong> Responder apenas 1 questão por dia já mantém sua sequência ativa!
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="space-y-6"
    >
      {/* Section header with new design */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 sm:p-3 bg-hackathon-red rounded-full border-2 border-black">
            <Brain className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
          </div>
          <h2 className="text-lg sm:text-xl font-bold text-gray-800">Suas Estatísticas</h2>
        </div>
      </div>

      {/* Streak section with new styling */}
      <motion.div variants={item} className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-hackathon-yellow" />
            <h3 className="text-lg font-semibold">Sua Sequência</h3>
          </div>
          <StreakHelpDialog />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-hackathon-yellow p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-black">Sequência Atual</h3>
                <Trophy className="h-5 w-5 text-black" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="text-3xl font-bold text-black">
                {streakLoading ? '...' : `${currentStreak} ${currentStreak === 1 ? 'dia' : 'dias'}`}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Continue estudando para aumentar sua sequência!
              </p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-hackathon-green p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-black">Recorde</h3>
                <BookOpen className="h-5 w-5 text-black" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="text-3xl font-bold text-black">
                {streakLoading ? '...' : `${maxStreak} ${maxStreak === 1 ? 'dia' : 'dias'}`}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Seu melhor recorde de sequência
              </p>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Performance overview with new styling */}
      <motion.div variants={item} className="space-y-4">
        <div className="flex items-center gap-2">
          <BarChart className="h-5 w-5 text-hackathon-red" />
          <h3 className="text-lg font-semibold">Seu Desempenho</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-hackathon-green p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-black">Precisão</h3>
                <Target className="h-5 w-5 text-black" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="flex items-end justify-between mb-2">
                <div className="text-3xl font-bold text-black">
                  {stats.accuracy.toFixed(1)}%
                </div>
                <div className="flex items-center text-sm">
                  {stats.weeklyChange.accuracy > 0 ? (
                    <span className="text-green-600 flex items-center">
                      <ArrowUp className="h-4 w-4 mr-1" />
                      {Math.abs(stats.weeklyChange.accuracy).toFixed(1)}%
                    </span>
                  ) : stats.weeklyChange.accuracy < 0 ? (
                    <span className="text-red-600 flex items-center">
                      <ArrowDown className="h-4 w-4 mr-1" />
                      {Math.abs(stats.weeklyChange.accuracy).toFixed(1)}%
                    </span>
                  ) : (
                    <span className="text-gray-500">0%</span>
                  )}
                </div>
              </div>
              <Progress
                value={stats.accuracy}
                className="h-2 border border-black/20"
              />
              <p className="text-xs text-gray-600 mt-2">
                Comparado à semana anterior
              </p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-hackathon-yellow p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-black">Questões</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-black/70 hover:text-black transition-colors" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <div className="space-y-2">
                          <p className="font-medium">Como funciona o contador:</p>
                          <p className="text-sm">• <strong>Questões únicas:</strong> Questões diferentes que você já respondeu</p>
                          <p className="text-sm">• <strong>Total de respostas:</strong> Inclui questões repetidas</p>
                          <p className="text-sm text-blue-600">💡 Responda questões novas para aumentar o número principal!</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <BookOpen className="h-5 w-5 text-black" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="space-y-3">
                {/* Número principal - Questões únicas */}
                <div className="flex items-end justify-between">
                  <div className="text-3xl font-bold text-black">
                    {stats.totalQuestions}
                  </div>
                  <div className="flex items-center text-sm">
                    {stats.weeklyChange.questions > 0 ? (
                      <span className="text-green-600 flex items-center">
                        <ArrowUp className="h-4 w-4 mr-1" />
                        {Math.abs(stats.weeklyChange.questions)}
                      </span>
                    ) : stats.weeklyChange.questions < 0 ? (
                      <span className="text-red-600 flex items-center">
                        <ArrowDown className="h-4 w-4 mr-1" />
                        {Math.abs(stats.weeklyChange.questions)}
                      </span>
                    ) : (
                      <span className="text-gray-500">0</span>
                    )}
                  </div>
                </div>

                {/* Label principal */}
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-hackathon-yellow rounded-full"></div>
                  <p className="text-sm font-medium text-gray-800">
                    Questões únicas dominadas
                  </p>
                </div>

                {/* Estatística adicional - Total de respostas */}
                {stats.totalAnswers && stats.totalAnswers > stats.totalQuestions && (
                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">Total de respostas</span>
                      </div>
                      <span className="text-lg font-semibold text-blue-600">
                        {stats.totalAnswers}
                      </span>
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      +{stats.totalAnswers - stats.totalQuestions} questões repetidas
                    </div>
                  </div>
                )}

                {/* Barra de progresso visual */}
                {stats.totalAnswers && stats.totalAnswers > stats.totalQuestions && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Progresso de questões únicas</span>
                      <span>{((stats.totalQuestions / stats.totalAnswers) * 100).toFixed(1)}% únicas</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-hackathon-yellow to-hackathon-green h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(stats.totalQuestions / stats.totalAnswers) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-hackathon-red p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-white">Tempo</h3>
                <Clock className="h-5 w-5 text-white" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="text-3xl font-bold text-black">
                {formattedTime}
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Tempo total de estudo
              </p>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Time analysis section with both metrics */}
      <motion.div variants={item} className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-hackathon-yellow" />
            <h3 className="text-lg font-semibold">Análise de Tempo</h3>
          </div>
          <TimeExplanationDialog>
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
              <HelpCircle className="h-4 w-4" />
            </Button>
          </TimeExplanationDialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 max-w-md mx-auto">
          {/* Tempo por Questão - Card único */}
          <Card className="overflow-hidden border-2 border-black bg-white shadow-card-sm">
            <div className="bg-blue-500 p-4 border-b-2 border-black">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-white">Tempo por Questão</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-white/70 hover:text-white transition-colors" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p className="text-sm">Tempo médio que você gasta para responder cada questão</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Clock className="h-5 w-5 text-white" />
              </div>
            </div>
            <CardContent className="pt-4">
              <div className="text-3xl font-bold text-blue-600">
                {Math.round(stats.averageTime)}s
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Tempo médio por questão
              </p>
              <div className="mt-2 text-xs text-blue-700 bg-blue-50 p-2 rounded">
                {(() => {
                  const avgTime = Math.round(stats.averageTime);
                  if (avgTime <= 15) return '⚡ Muito rápido - cuidado com chutes';
                  if (avgTime <= 45) return '✅ Ritmo ideal para questões';
                  if (avgTime <= 90) return '⏱️ Ritmo normal, pode acelerar';
                  return '🐌 Muito lento, precisa treinar';
                })()}
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </motion.div>
  );
});

LearningInsights.displayName = 'LearningInsights';

export { LearningInsights };
