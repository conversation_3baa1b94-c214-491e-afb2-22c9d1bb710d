import React, { useState, useCallback, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useFlashcardGeneration } from '@/components/collaborate/flashcards/hooks/useFlashcardGeneration';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { useFlashcardFilters } from '@/components/flashcards/hooks/useFlashcardFilters';
import { FlashcardPanelStepOne } from "./FlashcardPanelStepOne";
import { FlashcardPanelStepTwo } from "./FlashcardPanelStepTwo";
import { usePanelProcessingMessages } from "./usePanelProcessingMessages";
import { AvailableFlashcardsPanel } from "./AvailableFlashcardsPanel";
import { PlayCircle } from "lucide-react";

interface FlashcardGenerationPanelProps {
  totalQuestions: number;
  incorrectQuestions: number;
}

const LoadingMessages = [
  "Dr. Will está analisando suas questões...",
  "Comparando padrões de erro e acerto...",
  "Gerando flashcards personalizados...",
  "Organizando o conteúdo para melhor aprendizado...",
  "Finalizando a preparação dos seus flashcards..."
];

export const FlashcardGenerationPanel = ({ totalQuestions, incorrectQuestions }: FlashcardGenerationPanelProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedType, setSelectedType] = useState("all");
  const [quantity, setQuantity] = useState(5);
  const [flashcardType, setFlashcardType] = useState<"cloze"|"vf"|"option"|"qa">("cloze");
  const [step, setStep] = useState(1);

  // Armazena todos os IDs dos cartões importados (tanto dos gerados quanto dos disponíveis)
  const [allImportedCardIds, setAllImportedCardIds] = useState<string[]>([]);
  const [availableCardIds, setAvailableCardIds] = useState<string[]>([]);
  const [hasImportedAll, setHasImportedAll] = useState(false);

  const { sessionId } = useParams();
  const { generateFlashcardsFromQuestions, suggested, setSuggested, loading, loadingMessage: generationLoadingMessage } = useFlashcardGeneration();
  const { createSession } = useFlashcardFilters();

  const incrementQuantity = () => {
    if (quantity < 20) setQuantity(quantity + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) setQuantity(quantity - 1);
  };

  const handleReset = () => {
    setStep(1);
    setSelectedType("all");
    setQuantity(5);
    setFlashcardType("cloze");
    setHasImportedAll(false);
    setSuggested([]);
    // Não resetamos os cartões importados, eles devem ser acumulativos
  };

  const handleGenerateFlashcards = async () => {
    if (!sessionId) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "ID da sessão não encontrada"
      });
      return;
    }

    setStep(2);

    try {
      const { data: events, error: eventsError } = await supabase
        .from('session_events')
        .select('question_id')
        .eq('session_id', sessionId);

      if (eventsError) {
        console.error('❌ [FlashcardGenerationPanel] Erro ao buscar eventos da sessão:', eventsError);
        throw eventsError;
      }

      if (!events || events.length === 0) {
        throw new Error("Nenhuma questão respondida encontrada na sessão");
      }

      const answeredQuestionIds = [...new Set(events.map(event => event.question_id))];

      if (selectedType === "incorrect") {
        const { data: incorrectEvents, error: incorrectEventsError } = await supabase
          .from('session_events')
          .select('question_id')
          .eq('session_id', sessionId)
          .eq('response_status', false)
          .limit(100);

        if (incorrectEventsError) {
          console.error('❌ [FlashcardGenerationPanel] Erro ao buscar eventos incorretos da sessão:', incorrectEventsError);
          throw incorrectEventsError;
        }

        if (!incorrectEvents || incorrectEvents.length === 0) {
          throw new Error("Nenhuma questão incorreta encontrada na sessão");
        }

        const incorrectQuestionIds = [...new Set(incorrectEvents.map(event => event.question_id))];


        await generateFlashcardsFromQuestions(incorrectQuestionIds, quantity, flashcardType);
      } else {

        await generateFlashcardsFromQuestions(answeredQuestionIds, quantity, flashcardType);
      }
    } catch (err: any) {
      console.error("❌ [FlashcardGenerationPanel] Erro:", err);
      toast({
        variant: "destructive",
        title: "Erro ao gerar flashcards",
        description: err.message || "Erro desconhecido"
      });
      setStep(1);
    }
  };

  // Função para adicionar um cartão importado
  const handleImportCard = useCallback((cardId: string) => {
    if (!cardId) {
      console.error("❌ [FlashcardGenerationPanel] Tentativa de importar cartão com ID inválido:", cardId);
      return;
    }

    console.log(`📝 [FlashcardGenerationPanel] Importando cartão: ${cardId}`);

    setAllImportedCardIds(prev => {
      if (prev.includes(cardId)) return prev;
      return [...prev, cardId];
    });
  }, []);

  // Função para importar todos os cartões sugeridos de uma vez
  const handleImportAll = useCallback(async () => {
    if (!suggested || !Array.isArray(suggested) || suggested.length === 0) {
      console.error("❌ [FlashcardGenerationPanel] Não há flashcards para importar");
      return;
    }

    // Armazena temporariamente os IDs que serão importados
    const newImportedIds: string[] = [];

    // Para cada cartão sugerido, vamos importá-lo
    const importPromises = suggested.map(async (card) => {
      try {
        if (!card) return;

        // Validate required fields
        if (!card.front || !card.back) {
          console.error('❌ [FlashcardGenerationPanel] Cartão inválido - faltam campos obrigatórios:', { 
            front: card.front, 
            back: card.back 
          });
          return;
        }

        console.log('🔄 [FlashcardGenerationPanel] Importando cartão gerado por AI');

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          toast({
            variant: "destructive",
            title: "Erro de autenticação",
            description: "Você precisa estar autenticado para importar flashcards"
          });
          return;
        }

        // Always set is_shared to true for AI-generated cards
        const baseCardData = {
          user_id: user.id,
          front: card.front || '',
          back: card.back || '',
          front_image: card.front_image || null,
          back_image: card.back_image || null,
          specialty_id: card.specialty_id || (card.hierarchy?.specialty ? card.hierarchy.specialty.id : null),
          theme_id: card.theme_id || (card.hierarchy?.theme ? card.hierarchy.theme.id : null),
          focus_id: card.focus_id || (card.hierarchy?.focus ? card.hierarchy.focus.id : null),
          current_state: "available",
          is_shared: true,  // Always set to true for AI-generated cards
          origin_id: card.id || null
        };

        // Verifica se o cartão já existe
        const checkQuery = supabase
          .from("flashcards_cards")
          .select("id, origin_id")
          .eq("user_id", user.id);

        // Se card tem ID, verifica por origin_id, caso contrário, verifica pelo conteúdo
        if (card.id) {
          checkQuery.eq("origin_id", card.id);
        } else {
          checkQuery.eq("front", card.front).eq("back", card.back);
        }

        const { data: existing, error: checkError } = await checkQuery.maybeSingle();

        if (checkError && checkError.code !== 'PGRST116') {
          console.error('❌ [FlashcardGenerationPanel] Erro ao verificar cartão:', checkError);
          return;
        }

        if (existing) {
          console.log(`✅ [FlashcardGenerationPanel] Cartão já existe: ${existing.id}`);
          newImportedIds.push(existing.id);
          return;
        }

        // Insere o novo cartão
        const { data: newCard, error } = await supabase
          .from("flashcards_cards")
          .insert(baseCardData)
          .select("id")
          .single();

        if (error) {
          console.error('❌ [FlashcardGenerationPanel] Erro ao inserir cartão:', error);
          return;
        }

        // Se não tinha origin_id, define o próprio ID como origin_id
        if (!card.id) {
          const { error: updateError } = await supabase
            .from("flashcards_cards")
            .update({ origin_id: newCard.id })
            .eq("id", newCard.id);

          if (updateError) {
            console.error('❌ [FlashcardGenerationPanel] Erro ao atualizar origin_id:', updateError);
          } else {
            console.log(`✅ [FlashcardGenerationPanel] Definiu próprio ID como origem: ${newCard.id}`);
          }
        }

        newImportedIds.push(newCard.id);
        console.log(`✅ [FlashcardGenerationPanel] Cartão importado com sucesso: ${newCard.id}`);

      } catch (error) {
        console.error('❌ [FlashcardGenerationPanel] Erro ao importar cartão:', error);
      }
    });

    // Aguarda todas as importações terminarem
    Promise.all(importPromises).then(() => {
      // Atualiza os IDs importados
      if (newImportedIds.length > 0) {
        setAllImportedCardIds(prev => {
          const uniqueIds = [...prev];

          // Adiciona somente IDs que ainda não existem
          newImportedIds.forEach(id => {
            if (!uniqueIds.includes(id)) {
              uniqueIds.push(id);
            }
          });

          return uniqueIds;
        });

        // Marca que todos foram importados
        setHasImportedAll(true);
        toast({
          title: "Sucesso!",
          description: `${newImportedIds.length} flashcards importados com sucesso!`
        });

        // Limpa os sugeridos
        setSuggested([]);
      }
    });
  }, [suggested, setSuggested]);

  const handleImportFromAvailable = useCallback((count: number, cardIds: string[]) => {
    if (!cardIds.length) {
      return;
    }

    // Adiciona os novos IDs aos disponíveis
    setAvailableCardIds(prev => {
      const uniqueNewIds = cardIds.filter(id => !prev.includes(id));
      return [...prev, ...uniqueNewIds];
    });

    // Adiciona também à lista geral de IDs importados
    setAllImportedCardIds(prev => {
      const uniqueNewIds = cardIds.filter(id => !prev.includes(id));
      return [...prev, ...uniqueNewIds];
    });
  }, []);

  const startFlashcardSession = async () => {
    // Combina todos os IDs de cartões importados
    const allCards = [...allImportedCardIds, ...availableCardIds]
      .filter(Boolean) // Remove valores undefined/null/empty
      .filter((id, index, self) => self.indexOf(id) === index); // Remove duplicatas

    if (allCards.length === 0) {
      toast({
        variant: "destructive",
        title: "Nenhum flashcard",
        description: "Importe alguns flashcards antes de iniciar uma sessão"
      });
      return;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não autenticado");



      const session = await createSession(
        user.id,
        {
          specialties: [],
          themes: [],
          focuses: []
        },
        allCards
      );

      if (!session) throw new Error("Erro ao criar sessão");


      navigate(`/flashcards/session/${session.id}`);
    } catch (error: any) {
      console.error("❌ [FlashcardGenerationPanel] Erro ao iniciar sessão:", error);
      toast({
        variant: "destructive",
        title: "Erro ao iniciar sessão",
        description: error.message || "Erro desconhecido"
      });
    }
  };

  const {
    loadingMessageIndex,
    setLoadingMessageIndex,
    startProcessingMessages,
    stopProcessingMessages,
    loadingMessage,
  } = usePanelProcessingMessages(LoadingMessages);

  // Total de cards únicos importados
  const totalUniqueCards = Array.from(new Set([...allImportedCardIds, ...availableCardIds].filter(Boolean))).length;

  return (
    <div className="mt-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-lg p-6 shadow-sm border border-primary/10"
      >
        <div className="space-y-8">
          <div className="border-2 border-dashed border-[#aab6ff] rounded-xl p-6 bg-[#f8faff]">
            <div className="text-center mb-4">
              <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Flashcards Disponíveis
              </h3>
            </div>
            <AvailableFlashcardsPanel
              stats={{ totalQuestions, incorrectQuestions }}
              onImport={handleImportFromAvailable}
            />
          </div>

          {step === 1 ? (
            <div className="border-2 border-dashed border-[#aab6ff] rounded-xl p-6 bg-[#f8faff]">
              <div className="text-center mb-4">
                <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
                  Criar Novos Flashcards
                </h3>
              </div>
              <div className="space-y-4">
                <p className="text-sm font-semibold text-gray-700 uppercase tracking-wider text-center">
                  Fonte dos Flashcards
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <button
                    className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-300 ${
                      selectedType === "all"
                        ? "border-primary bg-primary/10 shadow-sm"
                        : "border-gray-200 hover:border-primary/50 hover:bg-primary/5"
                    }`}
                    onClick={() => setSelectedType("all")}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        selectedType === "all"
                          ? "bg-primary text-white"
                          : "bg-gray-200 text-gray-500"
                      }`}>
                        {totalQuestions}
                      </div>
                      <p className="font-semibold text-gray-800">Todas as Questões Respondidas</p>
                    </div>
                    <p className="text-xs text-gray-600 mt-2">
                      Criar flashcards baseados em todas as {totalQuestions} questões respondidas nesta sessão
                    </p>
                  </button>

                  <button
                    className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-300 ${
                      selectedType === "incorrect"
                        ? "border-red-500 bg-red-50/50 shadow-sm"
                        : "border-gray-200 hover:border-red-500/50 hover:bg-red-50/20"
                    }`}
                    onClick={() => setSelectedType("incorrect")}
                    disabled={incorrectQuestions === 0}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        selectedType === "incorrect"
                          ? "bg-red-500 text-white"
                          : "bg-gray-200 text-gray-500"
                      }`}>
                        {incorrectQuestions}
                      </div>
                      <p className="font-semibold text-gray-800">Questões Incorretas</p>
                    </div>
                    <p className="text-xs text-gray-600 mt-2">
                      {incorrectQuestions > 0
                        ? `Criar flashcards baseados nas ${incorrectQuestions} questões com erro`
                        : "Sem questões incorretas nesta sessão"}
                    </p>
                  </button>
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <p className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
                  Tipo e Quantidade
                </p>
                <div className="flex flex-col sm:flex-row gap-4 items-stretch">
                  <div className="flex-1">
                    <label className="sr-only" htmlFor="flashcard-type-select">Tipo de Flashcard</label>
                    <select
                      id="flashcard-type-select"
                      value={flashcardType}
                      onChange={(e) => setFlashcardType(e.target.value as any)}
                      className="w-full rounded-xl border-2 border-[#aab6ff] bg-[#eef2fa] py-3 px-4 text-base font-medium text-[#373f60] focus:outline-none focus:ring-2 focus:ring-[#aab6ff] transition-shadow shadow-sm"
                    >
                      <option value="cloze">Lacunas</option>
                      <option value="vf">V/F</option>
                      <option value="option">Múltipla</option>
                      <option value="qa">Pergunta</option>
                    </select>
                  </div>
                  <div className="flex items-center justify-between bg-[#eef2fa] rounded-xl px-4 py-2 border-2 border-[#aab6ff] min-w-[140px]">
                    <button
                      onClick={decrementQuantity}
                      className="p-2 rounded-full hover:bg-[#dde3fb] transition-colors"
                      disabled={quantity <= 1}
                      aria-label="Diminuir quantidade"
                      type="button"
                    >
                      <span className="sr-only">Diminuir</span>
                      <svg className="h-5 w-5 text-[#7482be]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                        <path d="M5 12h14" />
                      </svg>
                    </button>
                    <span className="text-lg font-bold text-[#35395e]">{quantity}</span>
                    <button
                      onClick={incrementQuantity}
                      className="p-2 rounded-full hover:bg-[#dde3fb] transition-colors"
                      disabled={quantity >= 20}
                      aria-label="Aumentar quantidade"
                      type="button"
                    >
                      <span className="sr-only">Aumentar</span>
                      <svg className="h-5 w-5 text-[#7482be]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                        <path d="M5 12h14" />
                        <path d="M12 5v14" />
                      </svg>
                    </button>
                  </div>
                </div>
                <p className="text-xs text-gray-500 text-right">
                  Máximo: 20 flashcards por geração
                </p>
              </div>

              {/* Fix: Contain buttons within the div by adding overflow-hidden to ensure buttons don't overflow */}
              <div className="mt-8 flex justify-end space-x-3 overflow-hidden">
                <Button variant="ghost" className="px-4 py-2 text-sm">
                  Cancelar
                </Button>
                <Button
                  onClick={handleGenerateFlashcards}
                  disabled={incorrectQuestions === 0 && selectedType === "incorrect"}
                  variant="default"
                  className="px-4 py-2 text-sm"
                >
                  Gerar Flashcards
                </Button>
              </div>
            </div>
          ) : (
            <FlashcardPanelStepTwo
              loading={loading}
              loadingMessage={generationLoadingMessage || loadingMessage}
              suggested={suggested}
              hasImportedAll={hasImportedAll}
              handleImportAll={handleImportAll}
              handleImportCard={handleImportCard}
              totalImportedCards={totalUniqueCards}
              handleReset={handleReset}
              startFlashcardSession={startFlashcardSession}
              setStep={setStep}
            />
          )}

          {totalUniqueCards > 0 && step === 2 && !loading && (
            <div className="flex flex-col items-center gap-4">
              <Button
                onClick={startFlashcardSession}
                className="bg-green-600 hover:bg-green-700 text-white gap-2 w-fit"
              >
                <PlayCircle className="w-5 h-5" />
                Iniciar Sessão ({totalUniqueCards})
              </Button>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};
