
import { useState, useEffect } from "react";
import { ThumbsUp, ThumbsDown, Flag, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";


interface LikeButtonsProps {
  cardId: string;
  initialLikes: number;
  initialDislikes: number;
  initialLikedBy: string[];
  initialDislikedBy: string[];
  onLikeImported?: () => void;
}

export const LikeButtons = ({
  cardId,
  initialLikes,
  initialDislikes,
  initialLikedBy,
  initialDislikedBy,
  onLikeImported,
}: LikeButtonsProps) => {
  const [likes, setLikes] = useState(initialLikes);
  const [dislikes, setDislikes] = useState(initialDislikes);
  const [likedBy, setLikedBy] = useState<string[]>(initialLikedBy || []);
  const [dislikedBy, setDislikedBy] = useState<string[]>(initialDislikedBy || []);
  const [isLoading, setIsLoading] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserId(user.id);
      }
    };
    getUser();
  }, []);

  // Collaborative hook is not available in this environment
  // Removing the problematic require() call that causes ReferenceError
  const likeDislikeCardHook: null = null;

  const handleVote = async (isLike: boolean) => {
    try {
      setIsLoading(true);


      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {

        return;
      }

      // Direct database update approach (collaborative hook not available)

      const currentUserId = user.id;
      const hasLiked = likedBy.includes(currentUserId);
      const hasDisliked = dislikedBy.includes(currentUserId);

      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = likes;
      let newDislikes = dislikes;

      if (hasLiked) {
        newLikedBy = newLikedBy.filter(id => id !== currentUserId);
        newLikes--;
      }
      if (hasDisliked) {
        newDislikedBy = newDislikedBy.filter(id => id !== currentUserId);
        newDislikes--;
      }

      if (isLike && !hasLiked) {
        newLikedBy.push(currentUserId);
        newLikes++;
      } else if (!isLike && !hasDisliked) {
        newDislikedBy.push(currentUserId);
        newDislikes++;
      }

      // Get the card details first to check if it's an imported card
      const { data: cardData, error: cardError } = await supabase
        .from('flashcards_cards')
        .select('origin_id')
        .eq('id', cardId)
        .single();

      if (cardError) {

        throw cardError;
      }

      // Update the current card
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', cardId);

      if (updateError) throw updateError;

      // If this is an imported card (has origin_id), also update the original card
      if (cardData?.origin_id) {


        // First get the current state of the original card
        const { data: originalCard, error: originalCardError } = await supabase
          .from('flashcards_cards')
          .select('liked_by, disliked_by, likes, dislikes')
          .eq('id', cardData.origin_id)
          .single();

        if (originalCardError) {

        } else {
          // Update liked_by and disliked_by arrays on the original card
          let originalLikedBy = originalCard.liked_by || [];
          let originalDislikedBy = originalCard.disliked_by || [];

          if (isLike) {
            // Add user to liked_by if not already there
            if (!originalLikedBy.includes(currentUserId)) {
              originalLikedBy.push(currentUserId);
            }
            // Remove from disliked_by if present
            originalDislikedBy = originalDislikedBy.filter(id => id !== currentUserId);
          } else {
            // Add user to disliked_by if not already there
            if (!originalDislikedBy.includes(currentUserId)) {
              originalDislikedBy.push(currentUserId);
            }
            // Remove from liked_by if present
            originalLikedBy = originalLikedBy.filter(id => id !== currentUserId);
          }

          // Update the original card
          const { error: originalUpdateError } = await supabase
            .from('flashcards_cards')
            .update({
              likes: originalLikedBy.length,
              dislikes: originalDislikedBy.length,
              liked_by: originalLikedBy,
              disliked_by: originalDislikedBy
            })
            .eq('id', cardData.origin_id);

          if (originalUpdateError) {
          }
        }
      }

      setLikes(newLikes);
      setDislikes(newDislikes);
      setLikedBy(newLikedBy);
      setDislikedBy(newDislikedBy);


    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  const handleReport = () => {
    // Report functionality placeholder
  };

  const handleBury = () => {
    // Bury functionality placeholder
  };

  return (
    <div className="flex items-center gap-4">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote(true)}
        disabled={isLoading}
        className={`flex items-center gap-2 ${userId && likedBy.includes(userId) ? 'text-green-500' : ''}`}
      >
        <ThumbsUp className={`h-4 w-4 ${userId && likedBy.includes(userId) ? 'text-green-500 fill-green-500' : ''}`} />
        <span>{likes}</span>
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote(false)}
        disabled={isLoading}
        className={`flex items-center gap-2 ${userId && dislikedBy.includes(userId) ? 'text-red-500' : ''}`}
      >
        <ThumbsDown className={`h-4 w-4 ${userId && dislikedBy.includes(userId) ? 'text-red-500 fill-red-500' : ''}`} />
        <span>{dislikes}</span>
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleReport}
        className="flex items-center gap-2 text-yellow-500"
      >
        <Flag className="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleBury}
        className="flex items-center gap-2 text-gray-500"
      >
        <XCircle className="h-4 w-4" />
      </Button>
    </div>
  );
};
