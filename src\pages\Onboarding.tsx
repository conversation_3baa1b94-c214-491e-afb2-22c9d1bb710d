
import { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/integrations/supabase/client";
import InitialQuestionnaire from "@/components/onboarding/InitialQuestionnaire";
import { GraduationCap, Sparkles } from "lucide-react";
import { motion } from "framer-motion";

const Onboarding = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const checkOnboardingStatus = async () => {
      if (!user?.id) {
        if (isMounted) setIsLoading(false);
        return;
      }

      if (hasChecked) {
        return;
      }

      if (isMounted) setHasChecked(true);

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('preparation_type, specialty, full_name, created_at, premium, premium_requested')
          .eq('id', user.id)
          .single();

        if (!isMounted) return;

        if (error) {
          setIsLoading(false);
          return;
        }

        // Se já tem preparation_type, redirecionar imediatamente
        if (data?.preparation_type) {
          navigate('/plataformadeestudos', { replace: true });
          return;
        } else {
          setNeedsOnboarding(true);
          setIsLoading(false);
        }

      } catch (error) {
        if (isMounted) setIsLoading(false);
      }
    };

    checkOnboardingStatus();

    return () => {
      isMounted = false;
    };
  }, [user?.id]);

  const handleComplete = () => {
    // O redirecionamento já é feito no InitialQuestionnaire
    // Não fazer nada aqui para evitar conflitos
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#FEF7CD]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando perfil...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  // Se não precisa de onboarding, não renderizar nada (já redirecionou)
  if (!needsOnboarding) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#FEF7CD]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecionando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#FEF7CD] py-12 relative overflow-hidden">
      {/* Static decorative elements instead of animated ones for better performance */}
      <div className="absolute top-20 left-10 opacity-20 hidden md:block">
        <GraduationCap size={120} className="text-black transform -rotate-12" />
      </div>
      <div className="absolute bottom-20 right-10 opacity-20 hidden md:block">
        <Sparkles size={100} className="text-black transform rotate-12" />
      </div>

      {/* Simplified animated circles with reduced motion */}
      <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-hackathon-yellow opacity-20"></div>
      <div className="absolute -bottom-20 -right-20 w-60 h-60 rounded-full bg-hackathon-red opacity-10"></div>

      <div className="container max-w-4xl mx-auto px-4 relative z-10">
        <div className="mb-10">
          <div className="flex justify-center mb-6">
            <div className="bg-white border-2 border-black px-5 py-2 shadow-card-sm relative">
              <span className="font-bold text-2xl tracking-tight">MedEvo</span>
              <div className="absolute -right-2 -top-2">
                <span className="bg-hackathon-red text-white text-xs px-2 py-0.5 rounded border border-black font-bold">
                  beta
                </span>
              </div>
            </div>
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          key="onboarding-questionnaire" // Key para evitar re-renders
        >
          <InitialQuestionnaire
            userId={user.id}
            onComplete={handleComplete}
          />
        </motion.div>

        <div className="mt-8 flex justify-center">
          <div className="flex -space-x-2">
            <div className="w-10 h-10 rounded-full border-2 border-black bg-blue-100 flex items-center justify-center text-blue-600 font-bold">M</div>
            <div className="w-10 h-10 rounded-full border-2 border-black bg-green-100 flex items-center justify-center text-green-600 font-bold">D</div>
            <div className="w-10 h-10 rounded-full border-2 border-black bg-purple-100 flex items-center justify-center text-purple-600 font-bold">R</div>
          </div>
          <span className="text-gray-700 font-medium ml-3">Junte-se a mais de 1.000 estudantes</span>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
