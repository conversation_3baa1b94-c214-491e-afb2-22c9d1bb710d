
import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, HelpCircle, Info, Search } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { TopicSelectionStepProps } from "./types";

export const TopicSelectionStep = ({
  searchTerm,
  setSearchTerm,
  selectedSpecialty,
  setSelectedSpecialty,
  selectedTheme,
  setSelectedTheme,
  selectedFocus,
  setSelectedFocus,
  filteredSpecialties,
  filteredThemes,
  filteredFocuses,
  expandedSection,
  setExpandedSection,
  studyStats,
  isLoading,
  domain,
  shouldShowFocus
}: TopicSelectionStepProps) => {

  // LOGS AGRESSIVOS PARA DEBUG
  console.log('🔥 [TopicSelectionStep] RENDERED WITH:', {
    selectedSpecialty,
    selectedTheme,
    selectedFocus,
    filteredSpecialtiesCount: filteredSpecialties.length,
    filteredThemesCount: filteredThemes.length,
    filteredFocusesCount: filteredFocuses.length,
    shouldShowFocus,
    expandedSection
  });

  if (filteredFocuses.length > 0) {
    console.log('🔥 [TopicSelectionStep] FILTERED FOCUSES:', filteredFocuses.map(f => f.name));
  }

  // Helper functions for study stats and badges
  const isItemStudied = (name: string, type: "specialty" | "theme" | "focus"): boolean => {
    if (!studyStats) return false;

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    return statsMap.some((stat: any) => stat.name === name);
  };

  const getStatsText = (name: string, type: "specialty" | "theme" | "focus") => {
    if (!studyStats) return "";

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    const stats = statsMap.find((s: any) => s.name === name);
    if (!stats) return "";

    return ` (${stats.correct}/${stats.total})`;
  };

  const getBadgeStyle = (name: string, type: "specialty" | "theme" | "focus") => {
    const studied = isItemStudied(name, type);
    return studied
      ? "bg-green-100 text-green-800 hover:bg-green-200"
      : "bg-blue-100 text-blue-800 hover:bg-blue-200";
  };

  const getStatusEmoji = (name: string, type: "specialty" | "theme" | "focus") => {
    const studied = isItemStudied(name, type);
    return studied ? "✅" : "🆕";
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-none p-4">
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`Buscar em ${domain?.toLowerCase() || ''}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 border-2 focus:border-green-500"
          />
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-100 rounded-lg mx-4 p-4 mb-4 flex items-start gap-3">
        <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
        <p className="text-sm text-blue-700 break-words">
          {shouldShowFocus 
            ? `Escolha um tópico de ${domain?.toLowerCase() || ''} para estudar. Selecione primeiro a especialidade, depois o tema e por fim o foco específico.`
            : `Escolha um tópico de ${domain?.toLowerCase() || ''} para estudar. Selecione a especialidade e depois o tema.`}
        </p>
      </div>

      <div className="p-4 space-y-6">
        <div className={`border rounded-lg p-4 bg-white ${expandedSection === "specialty" ? "border-green-500" : ""}`}>
          <Button 
            variant="ghost" 
            className="w-full flex justify-between items-center mb-2"
            onClick={() => setExpandedSection("specialty")}
          >
            <h3 className="font-semibold flex items-center text-left">
              <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">1</span>
              Especialidade {selectedSpecialty && <span className="ml-2 text-green-600">({selectedSpecialty})</span>}
            </h3>
            <span className="text-gray-400">{expandedSection === "specialty" ? "▼" : "▶"}</span>
          </Button>
          
          {expandedSection === "specialty" && (
            <ScrollArea className="h-[200px] w-full mt-2">
              <div className="space-y-1 pr-4">
                {filteredSpecialties.length > 0 ? (
                  filteredSpecialties.map((specialty) => (
                    <Button
                      key={specialty.id}
                      variant={selectedSpecialty === specialty.name ? "secondary" : "ghost"}
                      className="w-full justify-start mb-1"
                      onClick={() => {
                       // console.log("🔍 Especialidade selecionada:", specialty.name);
                        setSelectedSpecialty(specialty.name);
                        setSelectedTheme("");
                        setSelectedFocus("");
                        setExpandedSection("theme");
                      }}
                    >
                      {selectedSpecialty === specialty.name && (
                        <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                      )}
                      <span className="break-words text-left">{specialty.name}</span>
                      <Badge
                        className={`ml-2 ${getBadgeStyle(
                          specialty.name,
                          "specialty"
                        )}`}
                      >
                        {getStatusEmoji(specialty.name, "specialty")}
                        {getStatsText(specialty.name, "specialty")}
                      </Badge>
                    </Button>
                  ))
                ) : (
                  <p className="text-gray-500 text-sm py-2">Nenhuma especialidade encontrada com o termo buscado.</p>
                )}
              </div>
            </ScrollArea>
          )}
        </div>

        {selectedSpecialty && (
          <div className={`border rounded-lg p-4 bg-white ${expandedSection === "theme" ? "border-green-500" : ""}`}>
            <Button 
              variant="ghost" 
              className="w-full flex justify-between items-center mb-2"
              onClick={() => setExpandedSection("theme")}
              disabled={!selectedSpecialty}
            >
              <h3 className="font-semibold flex items-center text-left">
                <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">2</span>
                Tema {selectedTheme && <span className="ml-2 text-green-600">({selectedTheme})</span>}
              </h3>
              <span className="text-gray-400">{expandedSection === "theme" ? "▼" : "▶"}</span>
            </Button>
            
            {expandedSection === "theme" && (
              <ScrollArea className="h-[200px] w-full mt-2">
                <div className="space-y-1 pr-4">
                  {filteredThemes.length === 0 ? (
                    <p className="text-gray-500 text-sm py-2">Nenhum tema encontrado para esta especialidade.</p>
                  ) : (
                    filteredThemes.map((theme) => (
                      <Button
                        key={theme.id}
                        variant={selectedTheme === theme.name ? "secondary" : "ghost"}
                        className="w-full justify-start mb-1"
                        onClick={() => {
                        //  console.log("🔍 Tema selecionado:", theme.name);
                          setSelectedTheme(theme.name);
                          setSelectedFocus("");
                          if (shouldShowFocus) {
                            setExpandedSection("focus");
                          }
                        }}
                      >
                        {selectedTheme === theme.name && (
                          <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                        )}
                        <span className="break-words text-left">{theme.name}</span>
                        <Badge
                          className={`ml-2 ${getBadgeStyle(
                            theme.name,
                            "theme"
                          )}`}
                        >
                          {getStatusEmoji(theme.name, "theme")}
                          {getStatsText(theme.name, "theme")}
                        </Badge>
                      </Button>
                    ))
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        )}

        {selectedTheme && shouldShowFocus && (
          <div className={`border rounded-lg p-4 bg-white ${expandedSection === "focus" ? "border-green-500" : ""}`}>
            <Button 
              variant="ghost" 
              className="w-full flex justify-between items-center mb-2"
              onClick={() => setExpandedSection("focus")}
              disabled={!selectedTheme}
            >
              <h3 className="font-semibold flex items-center text-left">
                <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">3</span>
                Foco {selectedFocus && <span className="ml-2 text-green-600">({selectedFocus})</span>}
              </h3>
              <span className="text-gray-400">{expandedSection === "focus" ? "▼" : "▶"}</span>
            </Button>
            
            {expandedSection === "focus" && (
              <ScrollArea className="h-[200px] w-full mt-2">
                <div className="space-y-1 pr-4">
                  {filteredFocuses.length === 0 ? (
                    <p className="text-gray-500 text-sm py-2">Nenhum foco encontrado para este tema.</p>
                  ) : (
                    filteredFocuses.map((focus) => (
                      <Button
                        key={focus.id}
                        variant={selectedFocus === focus.name ? "secondary" : "ghost"}
                        className="w-full justify-start mb-1"
                        onClick={() => {
                          console.log("🔍 Foco selecionado:", focus.name);
                          setSelectedFocus(focus.name);
                        }}
                      >
                        {selectedFocus === focus.name && (
                          <Check className="mr-2 h-4 w-4 flex-shrink-0" />
                        )}
                        <span className="break-words text-left">{focus.name}</span>
                        <Badge
                          className={`ml-2 ${getBadgeStyle(
                            focus.name,
                            "focus"
                          )}`}
                        >
                          {getStatusEmoji(focus.name, "focus")}
                          {getStatsText(focus.name, "focus")}
                        </Badge>
                      </Button>
                    ))
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        )}
      </div>

      <div className="border rounded-lg p-3 bg-slate-50">
        <div className="flex items-center gap-1 mb-1">
          <Info className="h-4 w-4 text-blue-500" />
          <span className="text-sm font-medium text-slate-700">Legenda:</span>
        </div>
        <div className="text-xs text-slate-600 space-y-1">
          <div className="flex items-center gap-1">
            <span>✅</span>
            <span>Tópico já estudado (questões corretas/total)</span>
          </div>
          <div className="flex items-center gap-1">
            <span>🆕</span>
            <span>Tópico ainda não estudado</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopicSelectionStep;
