import { useState, useCallback } from 'react';
import type { SelectedFilters, FilterOption } from '@/types/question';

export const useFilterSelection = (
  initialFilters: SelectedFilters,
  onFiltersChange: (filters: SelectedFilters) => void
) => {
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>(initialFilters);

  const handleFilterToggle = useCallback((id: string, type: FilterOption['type']) => {
    setSelectedFilters(prev => {
      const filterKeyMap = {
        specialty: 'specialties',
        theme: 'themes',
        focus: 'focuses',
        location: 'locations',
        year: 'years'
      };

      const filterKey = filterKeyMap[type] as keyof SelectedFilters;
      const currentFilters = [...(prev[filterKey] || [])];
      const isSelected = currentFilters.includes(id);

      const newFilters = {
        ...prev,
        [filterKey]: isSelected
          ? currentFilters.filter(item => item !== id)
          : [...currentFilters, id]
      };

      onFiltersChange(newFilters);
      return newFilters;
    });
  }, [onFiltersChange]);

  return {
    selectedFilters,
    handleFilterToggle,
    setSelectedFilters
  };
};