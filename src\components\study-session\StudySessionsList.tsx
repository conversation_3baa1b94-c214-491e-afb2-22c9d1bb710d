
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { SessionCard } from "./SessionCard";
import { But<PERSON> } from "@/components/ui/button";
import { History, Book, BookOpenCheck } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";

const SESSIONS_PER_PAGE = 3;

interface Session {
  id: string;
  started_at: string;
  total_questions: number;
  current_question_index: number;
  questions: string[];
  stats: {
    correct_answers: number;
    total_questions: number;
  };
  status: 'in_progress' | 'completed' | 'abandoned';
  specialty_name?: string;
  title?: string;
  domain?: string;
}

export const StudySessionsList = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();
  const { user } = useAuth(); // ✅ Usar hook de auth em vez de supabase.auth.getUser()

  // Função para buscar sessões (otimizada com RPC)
  const fetchSessionsData = async (page: number) => {
    if (!user?.id) throw new Error('User not authenticated');

    // Usar função RPC otimizada
    const { data: sessionsData, error: sessionsError } = await supabase
      .rpc('get_user_sessions_optimized', {
        user_id_param: user.id,
        page_number: page,
        items_per_page: SESSIONS_PER_PAGE
      });

    // Para o count total, usar uma query separada mais simples (apenas quando necessário)
    const { count, error: countError } = await supabase
      .from("study_sessions")
      .select("id", { count: 'exact', head: true })
      .eq("user_id", user.id)
      .in("status", ["completed", "in_progress", "abandoned"]);

    if (sessionsError) {
      // Se o erro for relacionado à tabela não existir ou permissão negada, retorna dados vazios
      if (sessionsError.code === '42P01') {
        console.warn('⚠️ [StudySessionsList] Tabela study_sessions não encontrada:', sessionsError);
        return { sessions: [], totalSessions: 0 };
      } else if (sessionsError.code === '42501') {
        console.warn('⚠️ [StudySessionsList] Permissão negada para acessar a tabela study_sessions:', sessionsError);
        return { sessions: [], totalSessions: 0 };
      } else {
        throw sessionsError;
      }
    }

    if (countError) {
      console.warn('⚠️ [StudySessionsList] Erro ao contar sessões, usando dados disponíveis:', countError);
    }

    // Processar sessões otimizadas (dados já vêm da RPC)
    const processedSessions = (sessionsData || []).map((session) => {
      return {
        id: session.id,
        started_at: session.started_at,
        completed_at: session.completed_at,
        total_questions: session.total_questions || 0,
        current_question_index: Number(session.answered_questions) || 0, // Questões respondidas
        questions: [], // Não carregamos questões no histórico
        status: session.status as 'in_progress' | 'completed' | 'abandoned',
        stats: {
          correct_answers: session.correct_answers || 0,
          total_questions: session.total_questions || 0
        },
        specialty_name: session.specialty_name,
        title: session.title,
        domain: session.knowledge_domain,
        total_time_spent: session.total_time_spent
      };
    });

    return {
      sessions: processedSessions,
      totalSessions: count || 0
    };
  };

  // React Query para cache e otimização
  const { data, isLoading, error } = useQuery({
    queryKey: ['study-sessions', currentPage, user?.id],
    queryFn: () => fetchSessionsData(currentPage),
    enabled: !!user?.id, // ✅ Só executar quando user estiver disponível
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
    retry: 1,
    onError: (error: any) => {
      console.error("❌ [StudySessionsList] Erro ao carregar sessões:", error);
      toast({
        title: "Erro ao carregar sessões",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const sessions = data?.sessions || [];
  const totalSessions = data?.totalSessions || 0;

  const handleDelete = async (sessionId: string) => {
    try {
      // Primeiro deletar os eventos da sessão
      try {
        const { error: eventsError } = await supabase
          .from("session_events")
          .delete()
          .eq("session_id", sessionId);

        if (eventsError) {
          // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
          if (eventsError.code === '42P01') {
            console.warn('⚠️ [StudySessionsList] Tabela session_events não encontrada. Continuando sem deletar eventos:', eventsError);
          } else if (eventsError.code === '42501') {
            console.warn('⚠️ [StudySessionsList] Permissão negada para acessar a tabela session_events. Continuando sem deletar eventos:', eventsError);
          } else {
            console.error('❌ [StudySessionsList] Erro ao deletar eventos da sessão:', eventsError);
            throw eventsError;
          }
        }
      } catch (error) {
        console.error('❌ [StudySessionsList] Erro ao processar a deleção de eventos, mas continuando:', error);
      }

      // Depois deletar a sessão
      const { error } = await supabase
        .from("study_sessions")
        .delete()
        .eq("id", sessionId);

      if (error) {
        // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
        if (error.code === '42P01') {
          console.warn('⚠️ [StudySessionsList] Tabela study_sessions não encontrada. Continuando sem deletar a sessão:', error);
          return;
        } else if (error.code === '42501') {
          console.warn('⚠️ [StudySessionsList] Permissão negada para acessar a tabela study_sessions. Continuando sem deletar a sessão:', error);
          return;
        } else {
          console.error('❌ [StudySessionsList] Erro ao deletar sessão:', error);
          throw error;
        }
      }

      toast({
        title: "Sessão removida",
        description: "A sessão foi removida com sucesso"
      });

      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['study-sessions'] });

      // Se a página atual ficar vazia, voltar para a página anterior
      if (sessions.length === 1 && currentPage > 1) {
        setCurrentPage(prev => prev - 1);
      }
    } catch (error: any) {
      console.error("❌ [StudySessionsList] Erro ao deletar sessão:", error);
      toast({
        title: "Erro ao deletar sessão",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const totalPages = Math.ceil(totalSessions / SESSIONS_PER_PAGE);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4 bg-white border-2 border-black rounded-lg shadow-card-sm">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div className="flex flex-col space-y-3 flex-1">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
                  <div className="h-6 bg-gray-200 rounded-full animate-pulse w-20"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-24"></div>
                <div className="h-2 bg-gray-200 rounded-full animate-pulse w-full"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-gray-200 rounded animate-pulse w-20"></div>
                  <div className="h-8 bg-gray-200 rounded animate-pulse w-20"></div>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="h-8 w-20 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (sessions.length === 0 && currentPage === 1) {
    return (
      <div className="flex flex-col items-center justify-center py-6 sm:py-8 text-center">
        <div className="p-3 sm:p-4 rounded-full bg-gray-50 mb-3 sm:mb-4 border-2 border-black">
          <Book className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400" />
        </div>
        <h3 className="text-lg sm:text-xl font-medium text-gray-700 mb-2">
          Nenhuma sessão de estudo encontrada
        </h3>
        <p className="text-sm sm:text-base text-gray-500 mb-4 sm:mb-6 max-w-md">
          Inicie sua jornada de estudos respondendo questões e acompanhe seu progresso.
        </p>
        <Button
          onClick={() => navigate("/questions")}
          className="bg-black hover:bg-black/90 text-white font-bold border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
        >
          <BookOpenCheck className="h-4 w-4 mr-2" />
          Iniciar primeira sessão
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <div className="space-y-3 sm:space-y-4">
        {sessions.map((session, index) => (
          <motion.div
            key={session.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
          >
            <SessionCard
              session={session}
              onDelete={handleDelete}
              onNavigate={(id) => navigate(`/questions/${id}`)}
            />
          </motion.div>
        ))}
      </div>

      {totalPages > 1 && (
        <div className="mt-4 sm:mt-6 flex justify-center pt-4 sm:pt-6 border-t-2 border-black">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  aria-disabled={currentPage === 1}
                  className={`${currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}
                />
              </PaginationItem>
              <PaginationItem className="flex items-center px-2 sm:px-4">
                <span className="text-xs sm:text-sm font-medium text-gray-700">
                  {isMobile ? `${currentPage}/${totalPages}` : `Página ${currentPage} de ${totalPages}`}
                </span>
              </PaginationItem>
              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  aria-disabled={currentPage === totalPages}
                  className={`${currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"} border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </motion.div>
  );
};
