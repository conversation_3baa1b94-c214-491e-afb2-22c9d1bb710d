
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ubar<PERSON>ontent, 
  <PERSON>ubar<PERSON>tem, 
  MenubarMenu, 
  MenubarTrigger 
} from "@/components/ui/menubar";
import {
  Database,
  Grid3x3,
  List,
  Settings,
  Shield,
  Users,
  Merge,
  Sparkles,
  Target,
  BarChart3
} from "lucide-react";

interface AdminMenuOption {
  name: string;
  path: string;
  icon: React.ReactNode;
  description?: string;
}

const adminMenuOptions: AdminMenuOption[] = [
  {
    name: "Arrumar Hierarquia Questões",
    path: "/admin/question-hierarchy",
    icon: <Grid3x3 className="h-4 w-4" />,
    description: "Revise e melhore a hierarquia e organização das questões"
  },
  {
    name: "Consolidar Focos por Ano",
    path: "/admin/focus-consolidation-by-year",
    icon: <Merge className="h-4 w-4" />,
    description: "Consolide focos de 2025 para focos pré-existentes usando IA"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    path: "/admin/focus-optimization",
    icon: <Sparkles className="h-4 w-4" />,
    description: "Analise e otimize focos para eliminar duplicatas e melhorar organização"
  },
  {
    name: "Categorizar Questões",
    path: "/admin/question-categorization",
    icon: <Target className="h-4 w-4" />,
    description: "Categorize questões órfãs (sem tema ou foco) usando IA"
  },
  {
    name: "Gerenciamento de Categorias",
    path: "/admin/category-management",
    icon: <Database className="h-4 w-4" />,
    description: "Visualize e delete especialidades, temas e focos"
  },
  {
    name: "Consolidação de Focos",
    path: "/admin/focus-consolidation",
    icon: <List className="h-4 w-4" />,
    description: "Agrupe e consolide focos semelhantes"
  },
  {
    name: "Gerenciamento de Usuários",
    path: "/admin/users",
    icon: <Users className="h-4 w-4" />,
    description: "Administre usuários e permissões"
  },
  {
    name: "Análise de Prevalência",
    path: "/admin/prevalence",
    icon: <BarChart3 className="h-4 w-4" />,
    description: "Analise prevalência de temas por instituição"
  },
  {
    name: "Configurações",
    path: "/admin/settings",
    icon: <Settings className="h-4 w-4" />,
    description: "Configure parâmetros do sistema"
  }
];

export const AdminMenu = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <Card className="mb-6 border-2 border-black shadow-card-sm">
      <Menubar className="border-0 p-0 bg-transparent">
        <MenubarMenu>
          <MenubarTrigger className="font-bold p-4 cursor-pointer hover:bg-muted data-[state=open]:bg-muted transition-colors">
            Opções Administrativas
          </MenubarTrigger>
          <MenubarContent className="bg-white border-2 border-black shadow-card-sm min-w-[250px]">
            {adminMenuOptions.map((option) => (
              <MenubarItem
                key={option.path}
                className="cursor-pointer p-3 hover:bg-muted focus:bg-muted"
                onClick={() => handleNavigate(option.path)}
              >
                <div className="flex items-center gap-2">
                  <div className="bg-muted rounded-full p-1.5">
                    {option.icon}
                  </div>
                  <div>
                    <div className="font-medium">{option.name}</div>
                    {option.description && (
                      <div className="text-xs text-muted-foreground">{option.description}</div>
                    )}
                  </div>
                </div>
              </MenubarItem>
            ))}
          </MenubarContent>
        </MenubarMenu>
      </Menubar>
    </Card>
  );
};
