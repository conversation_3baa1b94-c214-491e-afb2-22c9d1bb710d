import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogPortal, DialogOverlay } from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Info } from "lucide-react";
import { useState, useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { formatDate } from "@/hooks/study-schedule/useScheduleDates";
import { cn } from "@/lib/utils";

// Custom DialogContent without automatic close button
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-black bg-white p-6 shadow-card-sm duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-xl max-h-[90vh] overflow-y-auto",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
))
CustomDialogContent.displayName = DialogPrimitive.Content.displayName;

interface AddWeeksDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (numberOfWeeks: number) => void;
  isLoading?: boolean;
  onAdded?: () => void;
}

export const AddWeeksDialog = ({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  onAdded
}: AddWeeksDialogProps) => {
  const [numberOfWeeks, setNumberOfWeeks] = useState(1);
  const [previewWeeks, setPreviewWeeks] = useState<string>("");
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0);
  const [existingWeeks, setExistingWeeks] = useState<any[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();


    onSubmit(numberOfWeeks);

    // REMOVIDO: Timeout desnecessário que causava re-renders
    // O React Query já cuida da invalidação e atualização
    // setTimeout(() => {
    //   console.log('🔥 [AddWeeksDialog] TIMEOUT CALLBACK - refreshing');
    //   setLastRefreshTime(Date.now());
    //   if (onAdded) {
    //     console.log('🔥 [AddWeeksDialog] Calling onAdded');
    //     onAdded();
    //   }
    // }, 1000);
  };

  useEffect(() => {
    // Limpar o estado quando o diálogo é fechado
    if (!open) {
      setExistingWeeks([]);
      return;
    }

    if (numberOfWeeks <= 0) {
      setPreviewWeeks("");
      return;
    }

    setIsLoadingPreview(true);
    let isMounted = true;

    // Forçar uma nova verificação a cada vez que o diálogo é aberto
    const generatePreview = async () => {
      try {

        const { data: { user } } = await supabase.auth.getUser();
        if (!user || !isMounted) return;

        // Verificar se existem semanas no banco de dados
        const { data: weeksData, error } = await supabase
          .from('study_schedules')
          .select('week_number, week_start_date, week_end_date')
          .eq('user_id', user.id)
          .order('week_number', { ascending: true });

        if (!isMounted) return;

        // Verificar se houve erro na consulta
        if (error) {

          setPreviewWeeks("Erro ao calcular as semanas");
          return;
        }

        // Atualizar o estado com as semanas encontradas
        if (isMounted) {
          setExistingWeeks(weeksData || []);
        }

        let nextWeekNumber = 1;
        let nextWeekStartDate = new Date();

        // Verificar novamente se existem semanas
        if (!weeksData || weeksData.length === 0) {

          // Se não existem semanas, começar da semana 1 e ajustar a data para o domingo da semana atual
          nextWeekNumber = 1;
          const dayOfWeek = nextWeekStartDate.getDay();
          nextWeekStartDate.setDate(nextWeekStartDate.getDate() - dayOfWeek);
        }
        else if (weeksData && weeksData.length > 0) {
          // Se existem semanas, começar da próxima semana após a última
          const lastWeek = weeksData[weeksData.length - 1];
          nextWeekNumber = lastWeek.week_number + 1;


          // Obter a data de término da última semana e adicionar 1 dia para obter o domingo da próxima semana
          const lastWeekEndDate = new Date(lastWeek.week_end_date);

          // Criar uma nova data para evitar modificar a original
          nextWeekStartDate = new Date(lastWeekEndDate);

          // Adicionar 1 dia para obter o domingo da próxima semana (o dia após o sábado)
          nextWeekStartDate.setDate(lastWeekEndDate.getDate() + 1);

          // Garantir que a próxima semana comece em um domingo
          if (nextWeekStartDate.getDay() !== 0) {
            // Se não for domingo, ajustar para o próximo domingo
            const daysUntilSunday = 7 - nextWeekStartDate.getDay();
            nextWeekStartDate.setDate(nextWeekStartDate.getDate() + daysUntilSunday);

          }

        }

        let result = "";
        // Usar UTC para evitar problemas de fuso horário
        const firstWeekStartDate = new Date(Date.UTC(
          nextWeekStartDate.getFullYear(),
          nextWeekStartDate.getMonth(),
          nextWeekStartDate.getDate(),
          12, 0, 0, 0
        ));

        for (let i = 0; i < numberOfWeeks; i++) {
          const weekStart = new Date(firstWeekStartDate);
          if (i > 0) {
            weekStart.setUTCDate(firstWeekStartDate.getUTCDate() + (i * 7));
          }

          // Garantir que a semana comece no domingo
          if (weekStart.getUTCDay() !== 0) {
            const daysUntilSunday = weekStart.getUTCDay();
            weekStart.setUTCDate(weekStart.getUTCDate() - daysUntilSunday);
          }

          const weekEnd = new Date(weekStart);
          weekEnd.setUTCDate(weekStart.getUTCDate() + 6);
          const weekNumber = nextWeekNumber + i;

          // Formatar as datas no formato dd/mm/yyyy
          const startDay = weekStart.getUTCDate().toString().padStart(2, '0');
          const startMonth = (weekStart.getUTCMonth() + 1).toString().padStart(2, '0');
          const startYear = weekStart.getUTCFullYear();

          const endDay = weekEnd.getUTCDate().toString().padStart(2, '0');
          const endMonth = (weekEnd.getUTCMonth() + 1).toString().padStart(2, '0');
          const endYear = weekEnd.getUTCFullYear();

          const formattedStart = `${startDay}/${startMonth}/${startYear}`;
          const formattedEnd = `${endDay}/${endMonth}/${endYear}`;

          const weekLine = `Semana ${weekNumber}: ${formattedStart} - ${formattedEnd}\n`;
          result += weekLine;

          // Log para debug

        }

        if (isMounted) {
          setPreviewWeeks(result);
        }
      } catch (error) {
        if (isMounted) {
          setPreviewWeeks("Erro ao calcular as semanas");
        }
      } finally {
        if (isMounted) {
          setIsLoadingPreview(false);
        }
      }
    };

    generatePreview();

    return () => {
      isMounted = false;
    };
  }, [numberOfWeeks, lastRefreshTime, open]);

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        onOpenChange(newOpen);
        if (newOpen) {
          // Forçar uma atualização completa quando o diálogo é aberto
          setLastRefreshTime(Date.now());
        }
      }}
    >
      <CustomDialogContent className="w-[95vw] max-w-[425px] max-h-[85dvh] border-2 border-black rounded-xl overflow-y-auto p-0">
        <DialogHeader className="p-4 sm:p-5 border-b border-gray-200">
          <div className="flex items-center justify-between gap-3">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-lg sm:text-xl font-bold">Adicionar Semanas de Estudo</DialogTitle>
              <DialogDescription className="text-sm text-gray-600 mt-1">
                Defina quantas semanas de estudo você deseja adicionar ao seu cronograma
              </DialogDescription>
            </div>

            {/* Single close button */}
            <button
              onClick={() => onOpenChange(false)}
              className="flex-shrink-0 p-1.5 sm:p-2 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Fechar"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </DialogHeader>

        <div className="p-4 sm:p-5">

          <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
            <div className="space-y-2">
              <Label htmlFor="numberOfWeeks" className="text-sm sm:text-base font-medium">Número de Semanas</Label>
              <Input
                id="numberOfWeeks"
                type="number"
                min={1}
                max={52}
                value={numberOfWeeks}
                onChange={(e) => setNumberOfWeeks(parseInt(e.target.value) || 1)}
                className="border-2 border-gray-300 h-10 sm:h-11 text-sm sm:text-base"
              />
            </div>

            <div className="flex items-start gap-2 p-3 bg-blue-50 border-2 border-blue-200 rounded-lg text-sm">
              <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="text-blue-700 min-w-0 flex-1">
                <p className="text-xs sm:text-sm">
                  {existingWeeks.length === 0
                    ? "As novas semanas serão adicionadas começando da semana 1."
                    : `As novas semanas serão adicionadas consecutivamente após a semana ${existingWeeks.length} do seu cronograma.`
                  }
                </p>

                {numberOfWeeks > 0 && (
                  <div className="mt-2 p-2 bg-white rounded border border-blue-100 max-h-[25dvh] overflow-y-auto">
                    {isLoadingPreview ? (
                      <p className="text-center text-xs text-gray-500">Calculando semanas...</p>
                    ) : (
                      <>
                        <p className="font-medium mb-1 text-xs sm:text-sm">Previsão das semanas:</p>
                        <pre className="text-xs whitespace-pre-wrap font-mono">{previewWeeks}</pre>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-10 sm:h-12 bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-sm sm:text-base"
              disabled={isLoading}
            >
              {isLoading ? 'Adicionando...' : 'Adicionar Semanas'}
            </Button>
          </form>
        </div>
        </CustomDialogContent>
    </Dialog>
  );
};
