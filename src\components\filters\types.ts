import { Json } from '@/integrations/supabase/types/json';

export interface FilterOption {
  id: string;
  name: string;
  count?: number;
  type: "specialty" | "theme" | "focus" | "location" | "year" | "question_type" | "question_format";
  children?: FilterOption[];
  indent?: number;
  parentId?: string;
}

export interface SelectedFilters {
  specialties: string[];
  themes: string[];
  focuses: string[];
  locations: string[];
  years: string[];
  question_types: string[];
  question_formats: string[];
  excludeAnswered?: boolean;
}

export type FilterType = FilterOption['type'];
export type SelectedFilterKey = keyof SelectedFilters;

export const filterTypeToKey = (type: FilterType): SelectedFilterKey => {
  const mapping: Record<FilterType, SelectedFilterKey> = {
    specialty: 'specialties',
    theme: 'themes',
    focus: 'focuses',
    location: 'locations',
    year: 'years',
    question_type: 'question_types',
    question_format: 'question_formats'
  };
  return mapping[type];
};

export const filterKeyToType = (key: SelectedFilterKey): FilterType => {
  const mapping: Record<SelectedFilterKey, FilterType> = {
    specialties: 'specialty',
    themes: 'theme',
    focuses: 'focus',
    locations: 'location',
    years: 'year',
    question_types: 'question_type',
    question_formats: 'question_format'
  };
  return mapping[key];
};

// Helper to convert SelectedFilters to Json type
export const selectedFiltersToJson = (filters: SelectedFilters): Json => {
  return filters as unknown as Json;
};
