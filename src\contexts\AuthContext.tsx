import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const initialized = useRef(false);

  useEffect(() => {
    if (initialized.current) {
      return;
    }
    initialized.current = true;

    // Obter sessão inicial
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          // Erro ao obter sessão
        } else {
          setUser(session?.user ?? null);
        }
      } catch (error) {
        // Erro na inicialização
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Configurar listener de mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const newUserId = session?.user?.id || null;
        const currentUserId = window.__currentUserId || null;

        // Evitar múltiplas atualizações para o mesmo usuário
        if (newUserId === currentUserId && event !== 'SIGNED_OUT') {
          return;
        }

        // Atualizar referência global
        window.__currentUserId = newUserId;

        // Limpar dados quando faz logout
        if (event === 'SIGNED_OUT') {
          console.log('🚪 [AuthContext] User signed out, cleaning up');
          setUser(null);

          // Limpar todas as queries do React Query se disponível
          if (window.__queryClient) {
            console.log('🧹 [AuthContext] Clearing React Query cache on signout');
            window.__queryClient.clear();
          }

          // Limpar referência global
          window.__currentUserId = null;

          // Limpar localStorage relacionado ao usuário
          try {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('referral_notification_shown_') || key.includes('pendingReferralCode'))) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
          } catch (error) {
            // Silent error handling
          }

          // Forçar redirecionamento para página inicial após logout
          setTimeout(() => {
            console.log('🔄 [AuthContext] Forcing redirect to home page after logout');
            if (window.location.pathname !== '/') {
              window.location.href = '/';
            }
          }, 100);
        } else {
          setUser(session?.user ?? null);
        }

        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      // Limpar queries antes do logout se disponível
      if (window.__queryClient) {
        window.__queryClient.clear();
      }

      // Limpar referência global
      window.__currentUserId = null;

      // Limpar estado local imediatamente
      setUser(null);

      // Fazer logout
      await supabase.auth.signOut();

      // Forçar redirecionamento imediato para página inicial
      window.location.href = '/';

    } catch (error) {
      // Mesmo com erro, redirecionar para página inicial
      window.location.href = '/';
    }
  };

  const value = {
    user,
    loading,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
