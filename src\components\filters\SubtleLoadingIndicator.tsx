import React from 'react';
import { Loader2, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SubtleLoadingIndicatorProps {
  isLoading?: boolean;
  isFetching?: boolean;
  questionCount?: number;
  className?: string;
  showText?: boolean;
}

export const SubtleLoadingIndicator: React.FC<SubtleLoadingIndicatorProps> = ({
  isLoading = false,
  isFetching = false,
  questionCount = 0,
  className,
  showText = true
}) => {
  // Não mostrar nada se não estiver carregando
  if (!isLoading && !isFetching) {
    return null;
  }

  return (
    <div className={cn(
      "flex items-center gap-2 text-xs text-muted-foreground",
      "transition-all duration-300 ease-in-out",
      className
    )}>
      {/* Ícone de carregamento sutil */}
      {isLoading ? (
        <Loader2 className="h-3 w-3 animate-spin" />
      ) : (
        <RefreshCw className="h-3 w-3 animate-spin" />
      )}
      
      {/* Texto opcional */}
      {showText && (
        <span className="animate-pulse">
          {isLoading ? 'Carregando...' : 'Atualizando...'}
        </span>
      )}
      
      {/* Mostrar contagem se disponível */}
      {questionCount > 0 && (
        <span className="font-medium text-primary">
          {questionCount.toLocaleString()} questões
        </span>
      )}
    </div>
  );
};

// Componente para indicador na barra superior
export const TopLoadingIndicator: React.FC<{
  isVisible: boolean;
  progress?: number;
}> = ({ isVisible, progress = 0 }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div className="h-1 bg-gray-200">
        <div 
          className="h-full bg-[#FF6B00] transition-all duration-300 ease-out"
          style={{ 
            width: progress > 0 ? `${progress}%` : '30%',
            animation: progress === 0 ? 'pulse 1.5s ease-in-out infinite' : 'none'
          }}
        />
      </div>
    </div>
  );
};

// Componente para indicador no badge
export const BadgeLoadingIndicator: React.FC<{
  isLoading: boolean;
  count: number;
}> = ({ isLoading, count }) => {
  return (
    <div className="flex items-center gap-1">
      {isLoading && (
        <Loader2 className="h-3 w-3 animate-spin text-current" />
      )}
      <span>{count}</span>
    </div>
  );
};

// Componente para indicador no header do filtro
export const FilterHeaderIndicator: React.FC<{
  totalQuestions: number;
  isUpdating: boolean;
  isFetching: boolean;
}> = ({ totalQuestions, isUpdating, isFetching }) => {
  return (
    <div className="flex items-center gap-2">
      <span className="text-lg font-semibold">
        {totalQuestions.toLocaleString()} questões
      </span>
      
      {(isUpdating || isFetching) && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <RefreshCw className="h-3 w-3 animate-spin" />
          <span className="hidden sm:inline">
            {isUpdating ? 'Atualizando...' : 'Carregando...'}
          </span>
        </div>
      )}
    </div>
  );
};

// Hook para controlar indicadores de carregamento
export const useLoadingIndicators = () => {
  const [showTopIndicator, setShowTopIndicator] = React.useState(false);
  const [progress, setProgress] = React.useState(0);

  const startLoading = React.useCallback(() => {
    setShowTopIndicator(true);
    setProgress(0);
    
    // Simular progresso
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + Math.random() * 20;
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const finishLoading = React.useCallback(() => {
    setProgress(100);
    setTimeout(() => {
      setShowTopIndicator(false);
      setProgress(0);
    }, 200);
  }, []);

  return {
    showTopIndicator,
    progress,
    startLoading,
    finishLoading
  };
};
