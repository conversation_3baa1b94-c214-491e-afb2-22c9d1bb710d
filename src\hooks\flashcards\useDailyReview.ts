import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";

export const useDailyReview = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['daily-review-cards', user?.id],
    queryFn: async () => {
      if (!user) throw new Error("User not authenticated");

      const today = new Date().toISOString().split('T')[0];

      const { data: reviews, error } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .eq('next_review_date', today);

      if (error) throw error;
      return reviews || [];
    },
    enabled: !!user,
  });
};