import { useCallback, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useFilterLogic = () => {
  const [selectedFilters, setSelectedFilters] = useState({
    specialties: [] as string[],
    themes: [] as string[],
    focuses: [] as string[],
  });

  const loadCategories = useCallback(async () => {
    try {
      console.log('📊 [useFilterLogic] Loading categories...');

      const { data: categories, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .order('name');

      if (error) {
        console.error('❌ [useFilterLogic] Error:', error);
        throw error;
      }

      console.log('✅ [useFilterLogic] Categories loaded:', categories?.length || 0);

      const specialties = categories.filter(cat => !cat.parent_id);
      const themes = categories.filter(cat =>
        specialties.some(spec => spec.id === cat.parent_id)
      );
      const focuses = categories.filter(cat =>
        themes.some(theme => theme.id === cat.parent_id)
      );

      return {
        specialties,
        themes,
        focuses,
      };
    } catch (error) {
      console.error('Error loading categories:', error);
      return {
        specialties: [],
        themes: [],
        focuses: [],
      };
    }
  }, []);

  return {
    selectedFilters,
    setSelectedFilters,
    loadCategories,
  };
};