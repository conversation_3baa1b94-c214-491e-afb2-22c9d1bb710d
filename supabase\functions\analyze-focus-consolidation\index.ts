import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders } from "../_shared/cors.ts";

const openAiKey = Deno.env.get("OPENAI_API_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { sourceFocus, targetFocuses } = await req.json();

    console.log(`🎯 Analyzing consolidation for: ${sourceFocus?.name}`);
    console.log(`📊 Target focuses count: ${targetFocuses?.length || 0}`);

    if (!sourceFocus || !targetFocuses || targetFocuses.length === 0) {
      console.error("❌ Missing required parameters");
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!openAiKey) {
      return new Response(
        JSON.stringify({ error: "OpenAI API key not configured" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
    
    // Prepare the content for the OpenAI request
    const targetFocusesFormatted = targetFocuses.map((f: any) =>
      `- ID: ${f.id}, Nome: ${f.name}, Questões: ${f.questionCount}`
    ).join('\n');

    // Validate prompt size
    const promptLength = targetFocusesFormatted.length;
    console.log(`📏 Prompt length: ${promptLength} characters`);

    if (promptLength > 10000) {
      console.error("❌ Prompt too large:", promptLength);
      return new Response(
        JSON.stringify({
          error: "Too many target focuses - prompt too large",
          targetCount: targetFocuses.length,
          promptLength
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Make request to OpenAI API
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${openAiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `Você é um especialista em classificação de conteúdo médico. Sua tarefa é analisar um foco de questões médicas de 2025 e determinar qual foco pré-existente (anterior a 2025) é mais apropriado para consolidação.

Critérios para análise:
1. Similaridade semântica do conteúdo
2. Sobreposição conceitual
3. Hierarquia médica apropriada
4. Especificidade vs generalidade

Responda APENAS com um objeto JSON no formato especificado.`
          },
          {
            role: "user",
            content: `Analise o seguinte foco de 2025 e determine qual foco pré-existente é mais apropriado para consolidação:

FOCO DE 2025 PARA CONSOLIDAR:
Especialidade: ${sourceFocus.specialty}
Tema: ${sourceFocus.theme}
Foco: ${sourceFocus.name}

FOCOS PRÉ-EXISTENTES DISPONÍVEIS:
${targetFocusesFormatted}

Responda APENAS com um objeto JSON neste formato:
{
  "targetFocusId": "id-do-foco-mais-apropriado",
  "targetFocusName": "Nome do Foco Mais Apropriado",
  "confidence": 0.95,
  "reasoning": "Breve explicação da escolha (máximo 100 caracteres)"
}

A confiança deve ser um valor entre 0 e 1. Se não houver um foco claramente apropriado, use confiança baixa (< 0.5).`
          }
        ],
        temperature: 0.2,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ OpenAI API error:", response.status, errorText);
      console.error("📊 Request details:", {
        sourceFocus: sourceFocus.name,
        targetCount: targetFocuses.length,
        promptLength: targetFocusesFormatted.length
      });
      return new Response(
        JSON.stringify({
          error: "Failed to analyze focus consolidation",
          details: `OpenAI API error: ${response.status}`,
          targetCount: targetFocuses.length
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return new Response(
        JSON.stringify({ error: "Invalid response from AI" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    let result: any;
    try {
      result = JSON.parse(data.choices[0].message.content);
    } catch (parseError) {
      console.error("Failed to parse AI response:", parseError);
      return new Response(
        JSON.stringify({ error: "Failed to parse AI response" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate the result has required fields
    if (!result.targetFocusId || !result.targetFocusName || typeof result.confidence !== 'number') {
      return new Response(
        JSON.stringify({ error: "Invalid AI response format" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Verify the suggested focus ID exists in the target focuses
    const targetFocus = targetFocuses.find((f: any) => f.id === result.targetFocusId);
    if (!targetFocus) {
      return new Response(
        JSON.stringify({ error: "AI suggested an invalid focus ID" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Additional validation: ensure the suggested focus is not the same as the source
    if (result.targetFocusId === sourceFocus.id) {
      return new Response(
        JSON.stringify({ error: "AI suggested the same focus as source - invalid consolidation" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error) {
    console.error("Error in analyze-focus-consolidation:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
