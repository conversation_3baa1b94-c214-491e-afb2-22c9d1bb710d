
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileImage, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface ImageUploadProps {
  label: string;
  currentImage: string;
  onImageSelect: (url: string) => void;
  id: string;
}

export const ImageUpload = ({
  label,
  currentImage,
  onImageSelect,
  id
}: ImageUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const uploadImage = async (file: File) => {
    setIsUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('flashcard_images')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('flashcard_images')
        .getPublicUrl(filePath);

      onImageSelect(data.publicUrl);
      toast.success('Imagem carregada com sucesso!');
    } catch (error) {
      console.error('Erro ao enviar imagem:', error);
      toast.error('Erro ao enviar imagem. Tente novamente.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error('A imagem deve ter menos de 5MB');
        return;
      }
      uploadImage(file);
    }
  };

  const removeImage = () => {
    onImageSelect('');
  };

  return (
    <div className="space-y-2">
      <Label className="text-base font-bold">{label}</Label>
      <div className="flex flex-col gap-2">
        {currentImage ? (
          <div className="flex items-center gap-2">
            <img
              src={currentImage}
              alt="Preview"
              className="w-16 h-16 object-cover rounded-lg border-2 border-black/20"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={removeImage}
              className="text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        ) : (
          <Button
            type="button"
            variant="outline"
            className="border-2 border-black/20 hover:border-black/50 transition-colors"
            disabled={isUploading}
            onClick={() => document.getElementById(id)?.click()}
          >
            <FileImage className="mr-2 h-5 w-5" />
            {isUploading ? 'Enviando...' : 'Escolher imagem'}
          </Button>
        )}
        <input
          id={id}
          type="file"
          accept="image/*"
          onChange={handleImageSelect}
          className="hidden"
        />
      </div>
    </div>
  );
};
