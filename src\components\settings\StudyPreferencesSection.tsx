import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Target,
  Building2,
  Clock,
  CheckCircle,
  X,
  Search,
  Save,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useStudyPreferences, StudyPreferencesFormData } from '@/hooks/useStudyPreferences';
import { useToast } from '@/hooks/use-toast';

export const StudyPreferencesSection: React.FC = () => {
  const { 
    preferences, 
    isLoading,
    availableInstitutions, 
    isLoadingInstitutions, 
    savePreferences, 
    isSaving 
  } = useStudyPreferences();
  
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const itemsPerPage = 20;
  const [formData, setFormData] = useState<StudyPreferencesFormData>({
    target_specialty: '',
    specialty_unknown: false,
    study_months: 6,
    target_institutions: [],
    institutions_unknown: false
  });

  // Carregar dados existentes quando as preferências chegarem
  useEffect(() => {
    if (preferences) {
      setFormData({
        target_specialty: preferences.target_specialty || '',
        specialty_unknown: !preferences.target_specialty,
        study_months: preferences.study_months || 6,
        target_institutions: preferences.target_institutions.map(inst => inst.id),
        institutions_unknown: preferences.target_institutions_unknown
      });
    }
  }, [preferences]);

  const filteredInstitutions = availableInstitutions?.filter(institution =>
    institution.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Paginação
  const totalPages = Math.ceil(filteredInstitutions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedInstitutions = filteredInstitutions.slice(startIndex, endIndex);

  // Reset página quando busca muda
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleSpecialtyChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      target_specialty: value,
      specialty_unknown: false
    }));
  };

  const handleSpecialtyUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      specialty_unknown: checked,
      target_specialty: checked ? '' : prev.target_specialty
    }));
  };

  const handleInstitutionToggle = (institutionId: string) => {
    if (formData.institutions_unknown) return;

    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.includes(institutionId)
        ? prev.target_institutions.filter(id => id !== institutionId)
        : [...prev.target_institutions, institutionId]
    }));
  };

  const handleInstitutionsUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      institutions_unknown: checked,
      target_institutions: checked ? [] : prev.target_institutions
    }));
  };

  const handleStudyMonthsChange = (value: number[]) => {
    setFormData(prev => ({
      ...prev,
      study_months: value[0]
    }));
  };

  const removeInstitution = (institutionId: string) => {
    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.filter(id => id !== institutionId)
    }));
  };

  const getSelectedInstitutionNames = () => {
    return availableInstitutions
      ?.filter(inst => formData.target_institutions.includes(inst.id))
      .map(inst => inst.name) || [];
  };

  const isFormValid = () => {
    const hasSpecialty = formData.specialty_unknown || formData.target_specialty.trim().length > 0;
    const hasInstitutions = formData.institutions_unknown || formData.target_institutions.length > 0;
    const hasStudyTime = formData.study_months > 0;
    return hasSpecialty && hasInstitutions && hasStudyTime;
  };

  const handleSave = async () => {
    if (!isFormValid()) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha todos os campos para salvar suas preferências",
        variant: "destructive"
      });
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
      return;
    }

    try {
      await savePreferences(formData);
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);
      toast({
        title: "Preferências salvas!",
        description: "Suas preferências foram atualizadas com sucesso",
      });
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Carregando preferências...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 className="text-lg font-medium text-blue-800 flex items-center gap-2">
          <Target className="h-5 w-5" />
          Preferências de Estudo
        </h3>
        <p className="text-sm text-blue-700">
          Configure suas preferências para personalizar cronogramas e focar nos temas mais relevantes.
        </p>
      </div>

      {/* Status */}
      {preferences?.preferences_completed ? (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <span className="text-sm text-green-700 font-medium">Preferências configuradas</span>
        </div>
      ) : (
        <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-orange-600" />
          <span className="text-sm text-orange-700 font-medium">Configure suas preferências para cronogramas personalizados</span>
        </div>
      )}

      {/* Formulário */}
      <div className="space-y-6">
        
        {/* Especialidade */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-600" />
              Especialidade Desejada
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="specialty-unknown"
                checked={formData.specialty_unknown}
                onCheckedChange={handleSpecialtyUnknownChange}
              />
              <Label htmlFor="specialty-unknown" className="text-sm">
                Ainda não sei / Estou decidindo
              </Label>
            </div>

            {!formData.specialty_unknown && (
              <div className="space-y-2">
                <Label htmlFor="specialty" className="text-sm font-medium">
                  Digite a especialidade
                </Label>
                <Input
                  id="specialty"
                  placeholder="Ex: Pediatria, Cardiologia, Ortopedia..."
                  value={formData.target_specialty}
                  onChange={(e) => handleSpecialtyChange(e.target.value)}
                  className="border border-gray-300 focus:border-blue-500"
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instituições */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Building2 className="h-4 w-4 text-green-600" />
              Instituições Alvo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="institutions-unknown"
                checked={formData.institutions_unknown}
                onCheckedChange={handleInstitutionsUnknownChange}
              />
              <Label htmlFor="institutions-unknown" className="text-sm">
                Ainda não sei / Qualquer instituição
              </Label>
            </div>

            {!formData.institutions_unknown && (
              <>
                {/* Instituições Selecionadas */}
                {formData.target_institutions.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Instituições Selecionadas ({formData.target_institutions.length})
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {getSelectedInstitutionNames().map((name, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          <Building2 className="h-3 w-3" />
                          {name}
                          <X
                            className="h-3 w-3 cursor-pointer hover:text-red-500"
                            onClick={() => removeInstitution(formData.target_institutions[index])}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Busca */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Buscar Instituições</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Buscar instituições..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 border border-gray-300 focus:border-green-500"
                    />
                  </div>
                </div>

                {/* Lista de Instituições */}
                <div className="max-h-40 sm:max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-2 sm:p-3 space-y-1 sm:space-y-2">
                  {isLoadingInstitutions ? (
                    <div className="text-center py-4 text-gray-500 text-sm">
                      Carregando instituições...
                    </div>
                  ) : filteredInstitutions.length === 0 ? (
                    <div className="text-center py-4 text-gray-500 text-sm">
                      Nenhuma instituição encontrada
                    </div>
                  ) : (
                    paginatedInstitutions.map((institution) => (
                      <div
                        key={institution.id}
                        className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer touch-manipulation"
                        onClick={() => handleInstitutionToggle(institution.id)}
                      >
                        <Checkbox
                          checked={formData.target_institutions.includes(institution.id)}
                          onChange={() => {}} // Controlled by parent click
                        />
                        <Label className="flex-1 text-sm cursor-pointer leading-tight">
                          {institution.name}
                        </Label>
                      </div>
                    ))
                  )}
                </div>

                {/* Paginação */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between px-3 py-2 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                    <div className="text-xs text-gray-500">
                      Página {currentPage} de {totalPages} ({filteredInstitutions.length} instituições)
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="h-8 w-8 p-0"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <span className="text-xs text-gray-600 px-2">
                        {currentPage}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="h-8 w-8 p-0"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Tempo de Estudo */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-600" />
              Tempo de Estudo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <span className="text-2xl sm:text-3xl font-bold text-purple-600">
                {formData.study_months}
              </span>
              <span className="text-lg text-gray-600 ml-2">
                {formData.study_months === 1 ? 'mês' : 'meses'}
              </span>
            </div>

            <div className="px-2 sm:px-4">
              <Slider
                value={[formData.study_months]}
                onValueChange={handleStudyMonthsChange}
                max={12}
                min={1}
                step={1}
                className="w-full touch-manipulation"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>1 mês</span>
                <span>12 meses</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Botão Salvar */}
        <div className="space-y-2">
          {/* Feedback visual */}
          {saveStatus !== 'idle' && (
            <div className={`text-center text-sm py-2 px-4 rounded-lg ${
              saveStatus === 'success'
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {saveStatus === 'success' ? '✅ Preferências salvas com sucesso!' : '❌ Erro ao salvar preferências'}
            </div>
          )}

          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              disabled={!isFormValid() || isSaving}
              className={`w-full sm:w-auto min-h-[44px] touch-manipulation transition-colors ${
                saveStatus === 'success'
                  ? 'bg-green-600 hover:bg-green-700'
                  : saveStatus === 'error'
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-blue-600 hover:bg-blue-700'
              } text-white`}
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Salvar Preferências</span>
                  <span className="sm:hidden">Salvar</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
