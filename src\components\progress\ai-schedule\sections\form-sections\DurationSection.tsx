
import React from "react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Timer } from "lucide-react";
import { cn } from "@/lib/utils";
import { Controller, UseFormReturn } from "react-hook-form";
import type { AIScheduleFormData } from "../../types";

interface DurationSectionProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const DurationSection = ({ form }: DurationSectionProps) => {
  return (
    <div className="p-6 space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
      <div className="flex items-center gap-2 text-slate-800">
        <div className="p-2 rounded-full bg-amber-100">
          <Timer className="w-5 h-5 text-amber-600" />
        </div>
        <h3 className="text-lg font-bold">Duração de cada tópico</h3>
      </div>
      
      <Controller
        name="topicDuration"
        control={form.control}
        render={({ field }) => (
          <RadioGroup
            value={field.value}
            onValueChange={field.onChange}
            className="grid grid-cols-3 gap-3 mt-3"
          >
        {[
          { value: "15", label: "15 min" },
          { value: "30", label: "30 min" },
          { value: "60", label: "1 hora" }
        ].map((option) => (
          <div key={option.value} className={cn(
            "flex items-center justify-center space-x-2 border-2 rounded-lg p-4 cursor-pointer transition-colors",
            field.value === option.value
              ? "border-blue-500 bg-blue-50"
              : "border-slate-200 hover:bg-slate-50"
          )}>
            <RadioGroupItem value={option.value} id={`duration-${option.value}`} className="text-blue-600" />
            <Label htmlFor={`duration-${option.value}`} className="cursor-pointer font-bold">
              {option.label}
            </Label>
          </div>
        ))}
          </RadioGroup>
        )}
      />
    </div>
  );
};
