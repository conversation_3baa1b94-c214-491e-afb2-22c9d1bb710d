
import { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { FilterSearch } from "./filter/FilterSearch";
import { HierarchyItem } from "./filter/HierarchyItem";
import { useHierarchyData } from "./filter/useHierarchyData";
import { supabase } from "@/integrations/supabase/client";
import { useFlashcardFilters } from "@/hooks/flashcards/useFlashcardFilters";
import type { SelectedFilters } from "@/types/flashcard";

interface FlashcardFilterProps {
  onStartSession: (filters: SelectedFilters) => void;
}

export const FlashcardFilter = ({ onStartSession }: FlashcardFilterProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>({
    specialties: [],
    themes: [],
    focuses: [],
    extrafocuses: [],
    locations: [],
    years: []
  });
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const { specialties, isLoading: isHierarchyLoading, error } = useHierarchyData();
  const { fetchFilteredCards, createSession } = useFlashcardFilters();

  const toggleExpand = useCallback((id: string) => {
    setExpandedItems(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  }, []);

  const handleSelect = useCallback((id: string, type: keyof SelectedFilters) => {
    setSelectedFilters(prev => {
      const newFilters = { ...prev };
      const index = newFilters[type].indexOf(id);

      if (index === -1) {
        newFilters[type] = [...newFilters[type], id];
      } else {
        newFilters[type] = newFilters[type].filter(filterId => filterId !== id);
      }

      return newFilters;
    });
  }, []);

  const handleStartSession = async () => {
    try {
      setIsLoading(true);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      let allCards: string[] = [];
      let page = 1;
      let hasMore = true;
      const pageSize = 100;

      while (hasMore) {
        const { cards, total } = await fetchFilteredCards(user.id, selectedFilters, page, pageSize);
        allCards = [...allCards, ...cards.map(c => c.id)];

        if (allCards.length >= total) {
          hasMore = false;
        } else {
          page++;
        }
      }

      if (!allCards.length) {
        toast.error("Nenhum flashcard encontrado", {
          description: "Tente outros filtros"
        });
        return;
      }

      const session = await createSession(user.id, selectedFilters, allCards);

      navigate(`/flashcards/session/${session.id}`);



    } catch (error: any) {

    } finally {
      setIsLoading(false);
    }
  };

  // Check if any filters are selected
  const hasSelectedFilters = Object.values(selectedFilters).some(filters => filters.length > 0);

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-500">
          Erro ao carregar os dados. Por favor, tente novamente.
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 space-y-4 border">
      <FilterSearch value={searchTerm} onChange={setSearchTerm} />

      <ScrollArea className="h-[400px] overflow-hidden">
        <div className="space-y-1 pr-4">
          {specialties
            .filter(item => {
              if (!searchTerm) return true;
              return item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (item.children && item.children.some((child: any) =>
                  child.name.toLowerCase().includes(searchTerm.toLowerCase())
                ));
            })
            .map(item => (
              <HierarchyItem
                key={item.id}
                id={item.id}
                name={item.name}
                level={0}
                count={item.count || 0}
                isExpanded={expandedItems.includes(item.id)}
                isSelected={selectedFilters.specialties.includes(item.id)}
                hasChildren={item.children?.length > 0}
                expandedItems={expandedItems}
                selectedFilters={selectedFilters}
                item={item}
                onToggleExpand={toggleExpand}
                onToggleSelect={handleSelect}
              />
            ))}
        </div>
      </ScrollArea>

      <div className="flex flex-wrap justify-between items-center gap-3">
        {hasSelectedFilters && (
          <Button
            variant="outline"
            onClick={() => setSelectedFilters({
              specialties: [],
              themes: [],
              focuses: [],
              extrafocuses: [],
              locations: [],
              years: []
            })}
            className="text-sm text-[#FF6B00] hover:text-[#FF4D00] border-none p-0 h-auto"
          >
            Limpar filtros
          </Button>
        )}
        <Button
          className="w-full sm:w-auto bg-hackathon-yellow text-black hover:bg-hackathon-yellow/90 border border-black"
          onClick={handleStartSession}
          disabled={!hasSelectedFilters || isLoading}
        >
          {isLoading ? "Criando sessão..." : "Iniciar Sessão de Estudos"}
        </Button>
      </div>
    </Card>
  );
};

export default FlashcardFilter;
