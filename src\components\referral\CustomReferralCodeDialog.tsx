import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Shuffle, 
  Check, 
  X, 
  Loader2, 
  Co<PERSON>, 
  <PERSON>rkles,
  AlertCircle,
  Info
} from 'lucide-react';
import { useReferralCodeValidation } from '@/hooks/useReferralCodeValidation';
import { cn } from "@/lib/utils";

interface CustomReferralCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentCode?: string;
  onCodeUpdate: (newCode: string) => Promise<boolean>;
  isUpdating: boolean;
}

export const CustomReferralCodeDialog: React.FC<CustomReferralCodeDialogProps> = ({
  open,
  onOpenChange,
  currentCode,
  onCodeUpdate,
  isUpdating
}) => {
  const [inputCode, setInputCode] = useState('');
  const [mode, setMode] = useState<'custom' | 'random'>('custom');
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [updatePermission, setUpdatePermission] = useState<{ canUpdate: boolean; error?: string } | null>(null);
  const { validationResult, validateCode, generateUniqueCode, checkUpdatePermission } = useReferralCodeValidation();

  useEffect(() => {
    if (open) {
      if (currentCode) {
        setInputCode(currentCode);
        setMode('custom');
        // Verificar permissão de atualização apenas se já tem código
        checkUpdatePermission().then(setUpdatePermission);
      } else {
        setInputCode('');
        setMode('custom');
        setUpdatePermission({ canUpdate: true }); // Pode criar código pela primeira vez
      }
      setHasGenerated(false);
      setIsGenerating(false);
    }
  }, [open, currentCode, checkUpdatePermission]);

  const handleInputChange = async (value: string) => {
    // Converter para maiúsculas e remover caracteres inválidos
    const cleanValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setInputCode(cleanValue);
    setMode('custom');
    setHasGenerated(false);

    if (cleanValue.length >= 3) {
      await validateCode(cleanValue);
    }
  };

  const handleGenerateRandom = async () => {
    // Só gerar se ainda não gerou ou se o usuário clicar novamente
    if (hasGenerated && mode === 'random') {
      return; // Não gerar novamente
    }

    setMode('random');
    setIsGenerating(true);

    try {
      const newCode = await generateUniqueCode();
      setInputCode(newCode);
      setHasGenerated(true);
      await validateCode(newCode);
    } catch (error) {
      // Error handling
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = async () => {
    if (!validationResult.isValid || !inputCode.trim()) {
      return;
    }

    // Verificar permissão antes de salvar
    if (currentCode && updatePermission && !updatePermission.canUpdate) {
      return; // Não pode atualizar ainda
    }

    const success = await onCodeUpdate(inputCode);
    if (success) {
      onOpenChange(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(inputCode);
  };

  const getValidationIcon = () => {
    if (validationResult.isChecking) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
    if (validationResult.isValid) {
      return <Check className="h-4 w-4 text-green-500" />;
    }
    if (validationResult.error) {
      return <X className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  const getValidationMessage = () => {
    if (!inputCode) {
      return <span className="text-gray-500">Digite um código para verificar</span>;
    }
    if (validationResult.isChecking) {
      return <span className="text-blue-600">Verificando disponibilidade...</span>;
    }
    if (validationResult.isValid && validationResult.message) {
      return <span className="text-green-600">{validationResult.message}</span>;
    }
    if (validationResult.error) {
      return <span className="text-red-600">{validationResult.error}</span>;
    }
    if (inputCode.length < 3) {
      return <span className="text-gray-500">Mínimo 3 caracteres</span>;
    }
    return <span className="text-gray-500">Digite um código válido</span>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
        {/* Header */}
        <div className="w-full py-6 bg-gradient-to-br from-[#E6F2FF] to-[#FFE6E6] border-b border-black/20">
          <div className="flex flex-col items-center text-center gap-3 px-6">
            <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-black/20">
              <Sparkles className="h-8 w-8 text-purple-600" />
            </div>
            <div className="space-y-1">
              <DialogTitle className="text-xl font-bold text-gray-900">
                {currentCode ? 'Editar Código' : 'Criar Código Personalizado'}
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-700">
                Crie um código único e memorável para seus convites
              </DialogDescription>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Aviso de limitação de tempo */}
          {currentCode && updatePermission && !updatePermission.canUpdate ? (
            <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-2 text-sm">
                  <p className="text-orange-800 font-medium">Alteração temporariamente bloqueada</p>
                  <p className="text-orange-700">
                    {updatePermission.error}
                  </p>
                  <p className="text-orange-600 text-xs">
                    Esta limitação evita alterações excessivas e mantém a estabilidade dos links compartilhados.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2 text-sm">
                <p className="text-blue-800 font-medium">Dicas para um bom código:</p>
                <ul className="text-blue-700 space-y-1">
                  <li>• 3-20 caracteres (letras e números)</li>
                  <li>• Fácil de lembrar e compartilhar</li>
                  <li>• Exemplo: SEUNOME123, ESTUDOS123...</li>
                </ul>
              </div>
            </div>
            </div>
          )}

          {/* Opções de modo */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Como deseja criar seu código?</Label>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant={mode === 'custom' ? 'default' : 'outline'}
                onClick={() => {
                  setMode('custom');
                  setHasGenerated(false);
                }}
                disabled={currentCode && updatePermission && !updatePermission.canUpdate}
                className="h-auto p-4 flex flex-col gap-2"
              >
                <Sparkles className="h-5 w-5" />
                <span className="text-sm font-medium">Personalizado</span>
              </Button>
              <Button
                variant={mode === 'random' ? 'default' : 'outline'}
                onClick={handleGenerateRandom}
                disabled={isGenerating || (currentCode && updatePermission && !updatePermission.canUpdate)}
                className="h-auto p-4 flex flex-col gap-2"
              >
                {isGenerating ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Shuffle className="h-5 w-5" />
                )}
                <span className="text-sm font-medium">
                  {isGenerating ? 'Gerando...' : hasGenerated && mode === 'random' ? 'Gerado!' : 'Aleatório'}
                </span>
              </Button>
            </div>
          </div>

          {/* Input do código */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="referral-code" className="text-base font-medium">
                Seu código de referência
              </Label>
              {mode === 'random' && hasGenerated && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setHasGenerated(false);
                    handleGenerateRandom();
                  }}
                  disabled={isGenerating}
                  className="text-xs h-7 px-2"
                >
                  <Shuffle className="h-3 w-3 mr-1" />
                  Gerar Novo
                </Button>
              )}
            </div>
            <div className="relative">
              <Input
                id="referral-code"
                value={inputCode}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder={mode === 'random' ? 'Código será gerado automaticamente' : 'Digite seu código...'}
                className={cn(
                  "pr-20 text-center font-mono text-lg tracking-wider transition-colors",
                  validationResult.isValid && "border-green-500 bg-green-50",
                  validationResult.error && "border-red-500 bg-red-50",
                  validationResult.isChecking && "border-blue-500 bg-blue-50",
                  mode === 'random' && "bg-gray-50"
                )}
                maxLength={20}
                disabled={
                  isUpdating ||
                  isGenerating ||
                  (currentCode && updatePermission && !updatePermission.canUpdate)
                }
                readOnly={mode === 'random' && !hasGenerated}
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
                {inputCode && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={copyToClipboard}
                    className="h-6 w-6 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                )}
                {getValidationIcon()}
              </div>
            </div>
            
            {/* Mensagem de validação - altura fixa para evitar layout shift */}
            <div className="h-[24px] flex items-center justify-center">
              <div className="text-sm font-medium">
                {getValidationMessage()}
              </div>
            </div>
          </div>

          {/* Preview */}
          {inputCode && validationResult.isValid && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="text-center space-y-2">
                <p className="text-sm text-green-700 font-medium">Preview do seu link:</p>
                <div className="p-2 bg-white rounded border border-green-300">
                  <code className="text-xs text-green-800 break-all">
                    {window.location.origin}/?ref={inputCode}
                  </code>
                </div>
              </div>
            </div>
          )}

          {/* Botões de ação */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
              disabled={isUpdating}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={
                !validationResult.isValid ||
                isUpdating ||
                !inputCode.trim() ||
                (currentCode && updatePermission && !updatePermission.canUpdate)
              }
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Salvando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  {currentCode ? 'Atualizar' : 'Criar'} Código
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
