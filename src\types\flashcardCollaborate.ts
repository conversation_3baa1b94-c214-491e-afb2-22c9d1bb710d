
export interface FlashcardWithHierarchy {
  id: string;
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  extrafocus_id?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  current_state: 'available' | 'reviewing' | 'archived' | 'pending' | 'rejected';
  rejection_reason?: string | null;
  stability?: number;
  difficulty?: number;
  retrievability?: number;
  recall_probability?: number;
  intervalindays?: string;
  last_review_date?: string;
  next_review_date?: string;
  total_reviews?: number;
  correct_reviews?: number;
  // NOVOS CAMPOS
  origin_id?: string | null;
  likes?: number;
  dislikes?: number;
  liked_by?: string[];
  disliked_by?: string[];
  import_count?: number;
  isImported?: boolean;
  is_shared: boolean;
  hierarchy: {
    specialty: { id: string; name: string };
    theme?: { id: string; name: string };
    focus?: { id: string; name: string };
    extraFocus?: { id: string; name: string };
  };
}
