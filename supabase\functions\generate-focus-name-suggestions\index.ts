
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { focusNames } = await req.json();
    
    if (!Array.isArray(focusNames)) {
      throw new Error('focusNames deve ser um array');
    }

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `Você é um especialista em terminologia médica e otimização de nomenclaturas acadêmicas. Sua missão é revisar e aprimorar títulos de temas médicos, tornando-os mais precisos, profissionais e distintos, sem comprometer seu significado essencial.

Diretrizes:
Sempre que possível, reformule os títulos, seja por meio de ajustes na estrutura, substituição de termos ou ampliação do escopo.
Evite manter a nomenclatura original, a menos que não haja alternativa válida para aprimoramento.
O foco é diferenciar os títulos previamente existentes, garantindo originalidade e identidade própria sem criar termos artificiais.
Não adicione termos como "diagnóstico" ou "tratamento", a menos que sejam essenciais para a definição do tema.
A nomenclatura deve ser exclusivamente técnica, voltada para médicos e profissionais da saúde, sem adaptações para o público leigo.
Não replique diretamente a estrutura original—priorize variações que mantenham o conceito sem caracterizar cópia.
Reformule de maneira estratégica para que os títulos sejam informativos, específicos e alinhados à prática médica.
Regras:
Preserve a fundamentação médica e científica.
Elimine o uso de aspas desnecessárias.
Aplique capitalização apropriada e padronizada.
Garanta coerência terminológica ao longo dos títulos.
Expanda o significado quando houver margem para maior abrangência.
Evite anglicismos desnecessários, priorizando termos técnicos equivalentes em português.
Priorize clareza e precisão na nomenclatura.
Mantenha a terminologia médica relevante e de uso consolidado.
Reformule com identidade própria, diferenciando-se de padrões já existentes.
Retorne APENAS um array JSON com os novos títulos, sem explicações ou formatação markdown.
Utilize sinônimos técnicos apropriados sempre que possível.
Evite repetir exatamente o mesmo nome original.
Mantenha um tom profissional, acadêmico e adequado ao contexto médico.


            IMPORTANTE: Retorne APENAS o array JSON com os novos nomes, sem nenhuma formatação markdown ou texto adicional.`
          },
          {
            role: 'user',
            content: `Melhore estes nomes de focos, retornando APENAS um array JSON com os novos nomes na mesma ordem (sem formatação markdown ou texto adicional): ${JSON.stringify(focusNames)}`
          }
        ],
        temperature: 0.5,
      }),
    });

    const data = await response.json();
    console.log("Resposta da IA:", data.choices[0].message.content);

    // Limpar qualquer formatação markdown que possa existir
    const cleanContent = data.choices[0].message.content
      .replace(/```json\n?/, '')
      .replace(/```\n?/, '')
      .trim();

    let suggestedNames;
    try {
      suggestedNames = JSON.parse(cleanContent);
      if (!Array.isArray(suggestedNames)) {
        throw new Error('A resposta da IA não é um array válido');
      }
    } catch (parseError) {
      console.error("Erro ao fazer parse da resposta:", parseError);
      console.error("Conteúdo que causou erro:", cleanContent);
      throw new Error('Erro ao processar resposta da IA');
    }

    return new Response(JSON.stringify({ suggestedNames }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Erro ao gerar sugestões:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
