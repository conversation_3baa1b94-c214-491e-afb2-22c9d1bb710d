
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "./DashboardLayout";
import { useToast } from "./ui/use-toast";
import { useAdminData } from "@/hooks/useAdminData";


interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();

  // ✅ Usar hook centralizado para admin
  const { isAdmin, isLoading: adminLoading, error: adminError } = useAdminData();

  useEffect(() => {
    // ✅ Verificação de admin simplificada e segura
    if (!adminLoading) {
      if (adminError) {
        console.error("❌ [AdminLayout] Erro ao verificar admin:", adminError);
        uiToast({
          title: "Erro de verificação",
          description: "Erro ao verificar permissões de administrador",
          variant: "destructive",
        });
        navigate("/plataformadeestudos");
        return;
      }

      if (!isAdmin) {
        console.log("❌ [AdminLayout] Acesso negado: usuário não é administrador");
        uiToast({
          title: "Acesso restrito",
          description: "Esta área é restrita a administradores",
          variant: "destructive",
        });
        navigate("/plataformadeestudos");
        return;
      }

      console.log("✅ [AdminLayout] Acesso de administrador confirmado");
    }
  }, [isAdmin, adminLoading, adminError, navigate, uiToast]);

  if (adminLoading) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">Verificando permissões</h2>
          <p className="text-muted-foreground">Aguarde enquanto verificamos seu acesso...</p>
        </div>
      </DashboardLayout>
    );
  }

  return <DashboardLayout>{children}</DashboardLayout>;
};

export default AdminLayout;
