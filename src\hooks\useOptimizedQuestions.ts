import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';
import { useMemo } from 'react';
import { useDomain } from '@/hooks/useDomain';

interface OptimizedQuestionsParams {
  filters: SelectedFilters;
  page: number;
  pageSize: number;
  enabled?: boolean;
}

interface OptimizedQuestionsResponse {
  questions: any[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export const useOptimizedQuestions = ({
  filters,
  page = 1,
  pageSize = 50,
  enabled = true
}: OptimizedQuestionsParams) => {
  const { domain, isReady } = useDomain();

  const hasFilters = useMemo(() =>
    Object.values(filters).some(f => Array.isArray(f) ? f.length > 0 : false),
    [filters]
  );

  const queryKey = useMemo(() => [
    'optimized-questions',
    filters,
    page,
    pageSize,
    domain
  ], [filters, page, pageSize, domain]);

  return useQuery({
    queryKey,
    queryFn: async (): Promise<OptimizedQuestionsResponse> => {
      if (!hasFilters || !isReady || !domain) {
        return {
          questions: [],
          totalCount: 0,
          hasNextPage: false,
          hasPreviousPage: false
        };
      }

      // Usar a função RPC otimizada com cache de parâmetros
      const baseParams = {
        specialty_ids: filters.specialties || [],
        theme_ids: filters.themes || [],
        focus_ids: filters.focuses || [],
        location_ids: filters.locations || [],
        years: (filters.years || []).map(Number),
        question_types: filters.question_types || [],
        question_formats: filters.question_formats || [],
        page_number: page,
        items_per_page: Math.min(pageSize, 50), // Limitar tamanho da página
        domain_filter: domain
      };

      const { data, error } = await supabase.rpc('get_filtered_questions', baseParams);

      if (error) {
        console.error('❌ [useOptimizedQuestions] Error:', error);
        throw error;
      }

      if (!data) {
        throw new Error('No data returned');
      }

      const typedData = data as {
        questions: any[];
        total_count: number;
      };

      const questions = typedData.questions || [];
      const totalCount = typedData.total_count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        questions,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      };
    },
    enabled: enabled && hasFilters && isReady && !!domain,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    keepPreviousData: true, // Manter dados anteriores durante carregamento
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
};

// Hook para prefetch da próxima página
export const usePrefetchNextPage = (
  filters: SelectedFilters,
  currentPage: number,
  pageSize: number,
  hasNextPage: boolean
) => {
  const { domain, isReady } = useDomain();

  const hasFilters = useMemo(() =>
    Object.values(filters).some(f => Array.isArray(f) ? f.length > 0 : false),
    [filters]
  );

  return useQuery({
    queryKey: [
      'optimized-questions',
      filters,
      currentPage + 1,
      pageSize,
      domain
    ],
    queryFn: async () => {
      if (!hasFilters || !isReady || !domain || !hasNextPage) {
        return null;
      }

      const baseParams = {
        specialty_ids: filters.specialties || [],
        theme_ids: filters.themes || [],
        focus_ids: filters.focuses || [],
        location_ids: filters.locations || [],
        years: (filters.years || []).map(Number),
        question_types: filters.question_types || [],
        question_formats: filters.question_formats || [],
        page_number: currentPage + 1,
        items_per_page: pageSize,
        domain_filter: domain
      };

      const { data, error } = await supabase.rpc('get_filtered_questions', baseParams);

      if (error) {
        console.error('❌ [usePrefetchNextPage] Error:', error);
        return null;
      }

      return data;
    },
    enabled: hasFilters && isReady && !!domain && hasNextPage,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 1
  });
};
