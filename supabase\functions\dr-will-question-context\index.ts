import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request)=>{
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800'
  ];
  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }
  return 'https://medevo.com.br'; // fallback
};
const getCorsHeaders = (request)=>({
    'Access-Control-Allow-Origin': getAllowedOrigin(request),
    'Access-Control-Allow-Headers': 'authorization, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
  });
// FUNÇÃO PARA VALIDAR SE O CONTEÚDO É REALMENTE UMA TABELA
function validateTableContent(content) {
  // Remover prefixos
  const cleanContent = content.replace(/^\[TABLE_RESPONSE\]\s*/, '').replace(/^\[TEXT_RESPONSE\]\s*/, '');
  // Dividir em linhas
  const lines = cleanContent.split('\n');
  // Encontrar linhas que parecem ser de tabela (com |) mas não são separadores
  const tableLines = lines.filter((line)=>{
    const trimmed = line.trim();
    // Deve ter | e pelo menos 3 colunas
    if (!trimmed.includes('|') || trimmed.split('|').length < 3) {
      return false;
    }
    // Não deve ser linha separadora (só hífens, dois pontos e espaços)
    if (trimmed.match(/^\s*\|[\s\-:]+\|\s*$/) || trimmed.match(/^[\s\-:|]+$/)) {
      return false;
    }
    return true;
  });
  // Deve ter pelo menos 3 linhas de tabela (cabeçalho + separador + dados)
  if (tableLines.length < 3) {
    return false;
  }
  // Verificar consistência de colunas
  const columnCounts = tableLines.map((line)=>line.split('|').length);
  const firstCount = columnCounts[0];
  const consistentLines = columnCounts.filter((count)=>Math.abs(count - firstCount) <= 1);
  // Pelo menos 70% das linhas devem ter número similar de colunas
  const consistency = consistentLines.length / columnCounts.length;
  return consistency >= 0.7 && tableLines.length >= 3;
}
// ULTRA-ROBUST RESPONSE TYPE DETECTOR
function detectResponseType(content, userMessage) {
  // Priority 0: Check user message for Mermaid request
  if (userMessage) {
    const userLower = userMessage.toLowerCase();
    const mermaidUserKeywords = [
      'mapa mental',
      'mindmap',
      'fluxograma',
      'diagrama',
      'esquema visual',
      'organograma'
    ];
    const userRequestedMermaid = mermaidUserKeywords.some((keyword)=>userLower.includes(keyword));
    if (userRequestedMermaid) {
      return "mermaid";
    }
  }
  // Priority 1: Explicit markers
  if (content.includes('[MERMAID_RESPONSE]')) {
    return "mermaid";
  }
  if (content.includes('[TABLE_RESPONSE]')) {
    return "table";
  }
  if (content.includes('[TEXT_RESPONSE]')) {
    return "text";
  }
  // Priority 2: Content analysis for Mermaid
  const mermaidKeywords = [
    'mindmap',
    'graph',
    'flowchart',
    'mapa mental',
    'fluxograma',
    'diagrama',
    'esquema visual'
  ];
  const hasMermaidKeyword = mermaidKeywords.some((keyword)=>content.toLowerCase().includes(keyword));
  if (hasMermaidKeyword) {
    return "mermaid";
  }
  // Priority 3: Content analysis for Table
  if (validateTableContent(content)) {
    return "table";
  }
  // Default: text
  return "text";
}
serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req);
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  // Simple health check
  if (req.method === 'GET') {
    return new Response(JSON.stringify({
      status: 'Dr. Will Question Context is online',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
  try {
    const requestBody = await req.json();
    const { message, userId, conversationHistory, questionContext } = requestBody;
    if (!message || typeof message !== 'string') {
      return new Response(JSON.stringify({
        error: 'Message is required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    if (!questionContext) {
      return new Response(JSON.stringify({
        error: 'Question context is required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get Gemini API key
    const geminiKey = Deno.env.get("GEMINI_API_KEY");
    if (!geminiKey) {
      return new Response(JSON.stringify({
        error: "Serviço temporariamente indisponível. Tente novamente em alguns minutos."
      }), {
        status: 503,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get user info for personalization
    let userName = 'estudante';
    let userGreeting = 'Prezado(a) estudante';
    if (userId) {
      try {
        // Fetch user data from Supabase
        const supabaseUrl = Deno.env.get('SUPABASE_URL');
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
        if (supabaseUrl && supabaseServiceKey) {
          const userResponse = await fetch(`${supabaseUrl}/rest/v1/profiles?id=eq.${userId}&select=name,formation_area`, {
            headers: {
              'Authorization': `Bearer ${supabaseServiceKey}`,
              'apikey': supabaseServiceKey,
              'Content-Type': 'application/json'
            }
          });
          if (userResponse.ok) {
            const userData = await userResponse.json();
            if (userData && userData.length > 0) {
              const user = userData[0];
              if (user.name) {
                userName = user.name.split(' ')[0]; // Primeiro nome
                userGreeting = `Prezado(a) ${userName}`;
              }
            }
          }
        }
      } catch (error) {
        console.warn('Failed to fetch user data:', error);
      }
    }
    // Build conversation context
    let conversationContext = '';
    if (conversationHistory && Array.isArray(conversationHistory) && conversationHistory.length > 0) {
      conversationContext = '\n\nHISTÓRICO DA CONVERSA:\n';
      conversationHistory.slice(-4).forEach((msg)=>{
        const role = msg.role === 'user' ? 'Estudante' : 'Dr. Will';
        conversationContext += `${role}: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}\n`;
      });
    }

    // 🎯 CONSTRUÇÃO INTELIGENTE DO CONTEXTO DA QUESTÃO
    const buildQuestionContext = () => {
      let context = `
═══════════════════════════════════════════════════════════════════════════════
🎯 CONTEXTO DA QUESTÃO ATUAL - MODO CONTEXTUAL ATIVO
═══════════════════════════════════════════════════════════════════════════════

📊 INFORMAÇÕES DA SESSÃO:
- Questão: ${questionContext.questionNumber} de ${questionContext.totalQuestions}
- Sessão: "${questionContext.sessionTitle}"
- ID da Questão: ${questionContext.questionId}

🏥 CLASSIFICAÇÃO MÉDICA:
- Especialidade: ${questionContext.specialty || 'Não especificada'}
- Tema: ${questionContext.theme || 'Não especificado'}
- Foco: ${questionContext.focus || 'Não especificado'}`;

      // 📋 METADADOS DO EXAME (se disponíveis)
      if (questionContext.examYear || questionContext.examLocation || questionContext.assessmentType || questionContext.knowledgeDomain || questionContext.questionFormat) {
        context += `\n\n📋 METADADOS DO EXAME:`;
        if (questionContext.examYear) context += `\n- Ano: ${questionContext.examYear}`;
        if (questionContext.examLocation) context += `\n- Local: ${questionContext.examLocation}`;
        if (questionContext.assessmentType) context += `\n- Tipo de Avaliação: ${questionContext.assessmentType}`;
        if (questionContext.knowledgeDomain) context += `\n- Domínio do Conhecimento: ${questionContext.knowledgeDomain}`;
        if (questionContext.questionFormat) context += `\n- Formato: ${questionContext.questionFormat}`;
      }

      // 📝 ENUNCIADO DA QUESTÃO
      context += `\n\n📝 ENUNCIADO DA QUESTÃO:
${questionContext.statement || 'Enunciado não disponível'}`;

      // 🔤 ALTERNATIVAS
      if (questionContext.alternatives && questionContext.alternatives.length > 0) {
        context += `\n\n🔤 ALTERNATIVAS:`;
        questionContext.alternatives.forEach((alt: any, index: number) => {
          const letter = String.fromCharCode(65 + index);
          const text = alt.text || alt;
          context += `\n${letter}) ${text}`;
        });
      } else {
        context += `\n\n🔤 ALTERNATIVAS: Não disponíveis`;
      }

      // ✅ RESPOSTA CORRETA (para referência interna)
      context += `\n\n✅ RESPOSTA CORRETA (REFERÊNCIA INTERNA): ${questionContext.correctAnswer || 'Não especificada'}`;

      // 📚 EXPLICAÇÃO OFICIAL (se disponível)
      if (questionContext.explanation) {
        context += `\n\n📚 EXPLICAÇÃO OFICIAL:
${questionContext.explanation}`;
      }

      // 🤖 COMENTÁRIO DA IA (se disponível)
      if (questionContext.aiCommentary) {
        context += `\n\n🤖 COMENTÁRIO DA IA PRÉVIA:
${typeof questionContext.aiCommentary === 'string' ? questionContext.aiCommentary : JSON.stringify(questionContext.aiCommentary)}`;
      }

      // 🏷️ TAGS DE CONTEÚDO (se disponíveis)
      if (questionContext.contentTags) {
        context += `\n\n🏷️ TAGS DE CONTEÚDO:
${typeof questionContext.contentTags === 'string' ? questionContext.contentTags : JSON.stringify(questionContext.contentTags)}`;
      }

      context += `\n═══════════════════════════════════════════════════════════════════════════════`;

      return context;
    };

    const questionInfo = buildQuestionContext();



    // Enhanced system prompt for contextual chat - IGUAL AO DR. WILL PRINCIPAL
    const systemPrompt = `Você é o Dr. Will, inteligência artificial médica avançada, integrante da plataforma MedEvo, criada especificamente para auxiliar estudantes e profissionais na preparação completa para provas de residência médica. Sua missão é fornecer respostas técnicas, detalhadas, precisas, atualizadas cientificamente e didáticas, sempre em português brasileiro.

CONTEXTO ESPECIAL: O estudante está atualmente resolvendo uma questão específica e quer conversar sobre ela.

${questionInfo}

TIPOS DE RESPOSTA DISPONÍVEIS:

1. **TEXTO PADRÃO**: Para explicações, conceitos, casos clínicos
   - Inicie com: [TEXT_RESPONSE]

2. **TABELAS**: Para comparações, classificações, protocolos
   - Inicie com: [TABLE_RESPONSE]
   - Use formato Markdown simples:
   | Coluna 1 | Coluna 2 | Coluna 3 |
   | Valor 1 | Valor 2 | Valor 3 |
   | Valor 4 | Valor 5 | Valor 6 |
   - NÃO use linhas separadoras com hífens (---)
   - Máximo 50 caracteres por célula

3. **MAPAS MENTAIS**: Para tópicos complexos, revisões, conexões conceituais
   - Inicie com: [MERMAID_RESPONSE]
   - Use sintaxe Mermaid mindmap

DETECÇÃO AUTOMÁTICA:
- Se o usuário pedir "mapa mental", "mindmap", "esquema visual", "diagrama", "fluxograma" use [MERMAID_RESPONSE]
- Se precisar comparar múltiplos itens use [TABLE_RESPONSE]
- Para explicações normais use [TEXT_RESPONSE]

SISTEMA INTERNO DE RESPOSTA:
- Use [TEXT_RESPONSE] para explicações normais
- Use [TABLE_RESPONSE] apenas para tabelas reais com estrutura clara
- Use [MERMAID_RESPONSE] para mapas mentais/fluxogramas
- Estas sinalizações são internas - NUNCA as mencione ao usuário

REGRA CRÍTICA PARA MÚLTIPLOS TIPOS DE RESPOSTA:
- Se usar múltiplos marcadores ([MERMAID_RESPONSE] + [TEXT_RESPONSE] ou [TABLE_RESPONSE] + [TEXT_RESPONSE])
- NUNCA repita saudações ou cumprimentos
- Use saudação apenas na PRIMEIRA seção (antes do primeiro marcador)
- Seções subsequentes devem começar diretamente com o conteúdo, sem "Olá" ou cumprimentos
- Exemplo correto:
  Olá, [nome]! Explicação inicial...
  [MERMAID_RESPONSE]
  mindmap...
  [TEXT_RESPONSE]
  As vulvovaginites representam... (DIRETO ao conteúdo)
- Exemplo ERRADO:
  [TEXT_RESPONSE]
  Olá, wilson! As vulvovaginites... (NUNCA fazer isso)

INSTRUÇÕES ESPECÍFICAS PARA MODO CONTEXTUAL AVANÇADO:

1. 🎯 FOCO NA QUESTÃO ATUAL:
   - Suas respostas devem ser diretamente relacionadas à questão apresentada
   - Use TODOS os dados disponíveis: enunciado, alternativas, metadados do exame, especialidade, tema, foco
   - Conecte a dúvida do estudante com os conceitos abordados na questão
   - Aproveite o ano do exame para contextualizar tendências e atualizações
   - Use o formato da questão para adaptar sua abordagem pedagógica

2. 📚 ABORDAGEM EDUCACIONAL INTELIGENTE:
   - JAMAIS revele qual é a resposta correta (A, B, C, D, E)
   - MAS VOCÊ PODE AJUDAR O USUÁRIO A DISCUTIR TODAS AS ALTERNATIVAS
   - JAMAIS diga frases como "a resposta é...", "a alternativa correta é..."
   - Explique os conceitos médicos envolvidos na questão
   - Ajude o estudante a RACIOCINAR e chegar à conclusão por conta própria
   - Identifique possíveis pegadinhas ou pontos de confusão
   - Relacione com conhecimentos clínicos práticos
   - Estimule o pensamento crítico através de perguntas direcionadas
   - Use a explicação oficial (se disponível) para enriquecer sua resposta
   - Aproveite comentários da IA prévia para complementar informações

3. 🎨 ESTILO DE RESPOSTA CONTEXTUALIZADA:
   - Seja didático e claro, adaptando ao nível da especialidade
   - Use exemplos clínicos específicos da área (baseado na especialidade/tema/foco)
   - Mantenha o foco na questão, mas conecte com conhecimentos mais amplos
   - Seja encorajador e construtivo
   - Utilize **negrito** para enfatizar termos técnicos e pontos-chave
   - Referencie o contexto temporal (ano do exame) quando relevante
   - Adapte a linguagem ao tipo de avaliação (residência, concurso, etc.)

4. 🏗️ ESTRUTURA SUGERIDA APRIMORADA:
   - Responda diretamente à dúvida do estudante
   - Explique o conceito médico envolvido (considerando especialidade/tema/foco)
   - Relacione com a questão específica e suas alternativas
   - Dê dicas para questões similares da mesma especialidade/tema
   - Faça perguntas que estimulem o raciocínio
   - Use metadados para contextualizar (ano, local, tipo de prova)

5. ⚠️ REGRAS RÍGIDAS MANTIDAS:
   - JAMAIS revele qual é a resposta correta (A, B, C, D, E)
   - MAS VOCÊ PODE AJUDAR O USUÁRIO A DISCUTIR TODAS AS ALTERNATIVAS
   - JAMAIS diga frases como "a resposta é...", "a alternativa correta é..."
   - Mantenha suas respostas focadas na questão apresentada
   - Use linguagem clara e didática
   - Seja conciso mas completo
   - Evite informações que não se relacionem com a questão atual
   - Estimule o estudante a PENSAR e chegar à conclusão sozinho

6. 🚀 APROVEITAMENTO DE DADOS ENRIQUECIDOS:
   - Use a especialidade para focar em aspectos específicos da área
   - Aproveite o tema e foco para direcionar explicações
   - Referencie o ano do exame para contextualizar atualizações
   - Use o tipo de avaliação para adaptar o nível de detalhamento
   - Aproveite tags de conteúdo para identificar tópicos específicos
   - Use comentários da IA prévia como base para complementar informações

INFORMAÇÕES DO USUÁRIO:
- Nome: ${userName}
- Use o nome do usuário para personalizar suas respostas quando apropriado

PERGUNTA DO ESTUDANTE: "${message.trim()}"${conversationContext}

Responda sucinto e direto ao ponto de forma educativa e específica, focando na questão atual e na dúvida apresentada. Seja direto e útil.
`;
    // Build conversation contents with history (using working structure from dr-will-medevo)
    const contents = [];

    // Start with system prompt as first user message
    contents.push({
      role: "user",
      parts: [
        {
          text: systemPrompt
        }
      ]
    });

    // Add conversation history if provided (using the working approach)
    if (conversationHistory && Array.isArray(conversationHistory) && conversationHistory.length > 0) {
      // Add history messages directly (like the working model)
      for (const historyMessage of conversationHistory) {
        if (historyMessage.role && historyMessage.content) {
          contents.push({
            role: historyMessage.role === 'user' ? 'user' : 'model',
            parts: [
              {
                text: historyMessage.content
              }
            ]
          });
        }
      }
    }

    // Add current user message
    contents.push({
      role: "user",
      parts: [
        {
          text: message
        }
      ]
    });


    // Configurar request body com thinking config (IGUAL AO DR. WILL PRINCIPAL)
    const geminiRequestBody = {
      contents,
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192,
        topP: 0.9,
        topK: 40,
        thinkingConfig: {
          thinkingBudget: -1,      // ativa pensamento dinâmico
          includeThoughts: true    // para receber resumos de pensamentos
        }
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_ONLY_HIGH"
        }
      ]
    };
    const geminiResponse = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:streamGenerateContent", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-goog-api-key": geminiKey
      },
      body: JSON.stringify(geminiRequestBody)
    });
    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text();
      console.error('[dr-will-question-context] Gemini API error:', {
        status: geminiResponse.status,
        statusText: geminiResponse.statusText,
        errorText: errorText,
        headers: Object.fromEntries(geminiResponse.headers.entries())
      });

      return new Response(JSON.stringify({
        error: "Erro interno do servidor. Tente novamente em alguns minutos.",
        details: `Gemini API error: ${geminiResponse.status} - ${errorText}`
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Create a ReadableStream for Server-Sent Events
    const stream = new ReadableStream({
      async start (controller) {
        const reader = geminiResponse.body?.getReader();
        if (!reader) {
          controller.error(new Error("No response body"));
          return;
        }
        const decoder = new TextDecoder();
        let buffer = "";
        let chunkCount = 0;
        let totalContent = "";
        let totalThinkingContent = "";
        let responseType = "text"; // Default to text
        try {
          while(true){
            const { done, value } = await reader.read();
            chunkCount++;
            if (done) {
              // Send final message to indicate completion
              const finalData = JSON.stringify({
                done: true
              });
              controller.enqueue(new TextEncoder().encode(`data: ${finalData}\n\n`));
              break;
            }
            buffer += decoder.decode(value, {
              stream: true
            });


            // Process JSON chunks from buffer
            while(buffer.length > 0){
              try {
                // Find JSON start
                let jsonStart = -1;
                for(let i = 0; i < buffer.length; i++){
                  if (buffer[i] === '{') {
                    jsonStart = i;
                    break;
                  }
                }
                if (jsonStart === -1) {
                  break;
                }
                // Remove garbage before JSON
                if (jsonStart > 0) {
                  buffer = buffer.substring(jsonStart);
                }
                // Agora encontrar o fim do JSON
                let jsonEnd = -1;
                let braceCount = 0;
                let inString = false;
                let escaped = false;
                for(let i = 0; i < buffer.length; i++){
                  const char = buffer[i];
                  if (escaped) {
                    escaped = false;
                    continue;
                  }
                  if (char === '\\') {
                    escaped = true;
                    continue;
                  }
                  if (char === '"') {
                    inString = !inString;
                    continue;
                  }
                  if (!inString) {
                    if (char === '{') {
                      braceCount++;
                    } else if (char === '}') {
                      braceCount--;
                      if (braceCount === 0) {
                        jsonEnd = i;
                        break;
                      }
                    }
                  }
                }
                if (jsonEnd === -1) {
                  break;
                }
                // Extract complete JSON
                const jsonText = buffer.substring(0, jsonEnd + 1);
                buffer = buffer.substring(jsonEnd + 1);
                // Parse the JSON
                const jsonResponse = JSON.parse(jsonText);
                // Check for errors in the response
                if (jsonResponse.error) {
                  const errorData = JSON.stringify({
                    error: "Erro interno do servidor. Tente novamente.",
                    timestamp: new Date().toISOString()
                  });
                  controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
                  continue;
                }
                // Process candidates
                if (jsonResponse.candidates && Array.isArray(jsonResponse.candidates) && jsonResponse.candidates.length > 0) {
                  const candidate = jsonResponse.candidates[0];
                  if (candidate.content && candidate.content.parts && Array.isArray(candidate.content.parts)) {
                    for (const part of candidate.content.parts){
                      if (part.text && typeof part.text === 'string' && part.text.length > 0) {
                        // Verificar se é thinking ou resposta final (IGUAL AO DR. WILL PRINCIPAL)
                        const isThinking = part.thought === true;

                        if (isThinking) {
                          // Acumular conteúdo de thinking
                          totalThinkingContent += part.text;

                          // Enviar thinking como SSE separado
                          const thinkingData = JSON.stringify({
                            content: part.text,
                            timestamp: new Date().toISOString(),
                            isThinking: true,
                            responseType: "thinking"
                          });
                          controller.enqueue(new TextEncoder().encode(`data: ${thinkingData}\n\n`));
                        } else {
                          // Acumular conteúdo da resposta final para análise
                          totalContent += part.text;



                          // ULTRA-ROBUST RESPONSE TYPE DETECTION
                          if (chunkCount === 1) {
                            responseType = detectResponseType(totalContent, message);
                          }
                          // Validar se realmente é uma tabela
                          if (responseType === "table") {
                            const isRealTable = validateTableContent(totalContent);
                            if (!isRealTable && totalContent.length > 200) {
                              responseType = "text";
                            }
                          }
                          // Send the text chunk as SSE (resposta final)
                          const data = JSON.stringify({
                            content: part.text,
                            timestamp: new Date().toISOString(),
                            responseType: responseType,
                            isThinking: false
                          });
                          controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
                        }
                      }
                    }
                  }
                }
              } catch (parseError) {
                break;
              }
            }
          }
        } catch (error) {
          const errorData = JSON.stringify({
            error: "Erro no streaming da resposta",
            timestamp: new Date().toISOString()
          });
          controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
        } finally{
          controller.close();
        }
      }
    });
    // Return the stream with appropriate headers for SSE
    return new Response(stream, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  } catch (error) {
    console.error('[dr-will-question-context] Main catch error:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });

    return new Response(JSON.stringify({
      error: 'Erro interno do servidor',
      message: error.message,
      timestamp: new Date().toISOString(),
      errorName: error.name
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
