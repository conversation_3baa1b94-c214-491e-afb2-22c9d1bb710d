import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Specialty {
  id: string;
  name: string;
}

interface Theme {
  id: string;
  name: string;
  specialty_id: string;
}

export const ThemeTab = () => {
  const [specialties, setSpecialties] = useState<Specialty[]>([]);
  const [themes, setThemes] = useState<Theme[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
  const [newTheme, setNewTheme] = useState("");

  useEffect(() => {
    fetchSpecialties();
  }, []);

  useEffect(() => {
    if (selectedSpecialty) {
      fetchThemes(selectedSpecialty);
    }
  }, [selectedSpecialty]);

  const fetchSpecialties = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_specialty")
      .select("id, name")
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar especialidades");
      return;
    }

    setSpecialties(data || []);
  };

  const fetchThemes = async (specialtyId: string) => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_theme")
      .select("id, name, specialty_id")
      .eq("specialty_id", specialtyId)
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar temas");
      return;
    }

    setThemes(data || []);
  };

  const handleCreateTheme = async () => {
    if (!selectedSpecialty) {
      toast.error("Selecione uma especialidade");
      return;
    }

    if (!newTheme.trim()) {
      toast.error("O nome do tema é obrigatório");
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from("flashcards_theme")
      .insert([{
        name: newTheme,
        specialty_id: selectedSpecialty,
        user_id: user.id
      }]);

    if (error) {
      toast.error("Erro ao criar tema");
      return;
    }

    toast.success("Tema criado com sucesso!");
    setNewTheme("");
    fetchThemes(selectedSpecialty);
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Input
          placeholder="Nome da nova categoria"
          value={newTheme}
          onChange={(e) => setNewTheme(e.target.value)}
        />
        <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Selecione a especialidade" />
          </SelectTrigger>
          <SelectContent>
            {specialties.map((specialty) => (
              <SelectItem key={specialty.id} value={specialty.id}>
                {specialty.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={handleCreateTheme}>
          Adicionar
        </Button>
      </div>

      <div className="grid gap-2">
        {themes.map((theme) => (
          <div
            key={theme.id}
            className="p-4 rounded-lg border bg-card text-card-foreground flex justify-between items-center"
          >
            <span>{theme.name}</span>
            <span className="text-sm text-gray-500">
              ↳ {specialties.find(s => s.id === theme.specialty_id)?.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};