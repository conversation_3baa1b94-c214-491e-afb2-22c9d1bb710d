
import { Card } from "@/components/ui/card";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

const StatCard = ({ title, value, icon, trend, className }: StatCardProps) => {
  return (
    <Card className={`stat-card p-4 ${className || ''}`}>
      <div className="flex flex-col items-center text-center">
        <div className="mb-3 p-2 rounded-full bg-opacity-20 flex items-center justify-center">
          <div className="text-primary">{icon}</div>
        </div>
        <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
        <h3 className="text-2xl font-bold">{value}</h3>
        {trend && (
          <p className={`text-sm mt-2 ${trend.isPositive ? "text-green-600" : "text-red-600"}`}>
            {trend.isPositive ? "+" : "-"}{trend.value}% from last week
          </p>
        )}
      </div>
    </Card>
  );
};

export default StatCard;
