import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://medevo.com.br',
  'Access-Control-Allow-Headers': 'authorization, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
};

// Múltiplas chaves de API
const apiKeys = {
  // Gemini keys
  gemini_key1: Deno.env.get("GEMINI_API_KEY"),
  gemini_key2: Deno.env.get("GEMINI_API_KEY2"),
  gemini_key3: Deno.env.get("GEMINI_API_KEY3"),
  gemini_key4: Deno.env.get("GEMINI_API_KEY4"),
  // OpenAI key
  openai_key1: Deno.env.get("OPENAI_API_KEY")
};

interface Question {
  id: string;
  text: string;
  alternatives: string[];
  correctAnswer: string;
  examYear: number;
}

interface Category {
  id: string;
  name: string;
}

interface CategorizationSuggestion {
  questionId: string;
  questionText: string;
  suggestedTheme?: {
    id: string;
    name: string;
    confidence: number;
  };
  suggestedFocus?: {
    id: string;
    name: string;
    confidence: number;
  };
  reasoning: string;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🚀 Edge Function started");
    console.log("📥 Request method:", req.method);

    const requestBody = await req.json();
    console.log("📦 Request body keys:", Object.keys(requestBody));

    const { specialty, analysisType, questions, availableCategories, selectedApiKey } = requestBody;

    // Selecionar a chave API baseada no parâmetro ou usar a primeira disponível
    let apiKey: string | undefined;
    let keyName = 'gemini_key1'; // padrão
    let apiProvider = 'gemini'; // padrão

    if (selectedApiKey && apiKeys[selectedApiKey as keyof typeof apiKeys]) {
      apiKey = apiKeys[selectedApiKey as keyof typeof apiKeys];
      keyName = selectedApiKey;
      apiProvider = selectedApiKey.startsWith('openai') ? 'openai' : 'gemini';
    } else {
      // Usar a primeira chave disponível (priorizar Gemini)
      for (const [key, value] of Object.entries(apiKeys)) {
        if (value) {
          apiKey = value;
          keyName = key;
          apiProvider = key.startsWith('openai') ? 'openai' : 'gemini';
          break;
        }
      }
    }

    console.log(`🎯 Categorizing ${questions?.length || 0} questions for ${specialty}`);
    console.log(`📊 Analysis type: ${analysisType}`);
    console.log(`📋 Available categories: ${availableCategories?.length || 0}`);
    console.log(`🔑 Using API provider: ${apiProvider}`);

    if (!specialty || !analysisType || !questions || !availableCategories || questions.length === 0) {
      console.error("❌ Missing required parameters");
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!apiKey) {
      console.error("❌ No API key available");
      return new Response(
        JSON.stringify({
          error: "Serviço de categorização temporariamente indisponível. Tente novamente em alguns minutos."
        }),
        {
          status: 503,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Prepare questions for AI analysis
    const questionsFormatted = questions.map((q: Question) => {
      const alternativesText = q.alternatives.map((alt, index) =>
        `${String.fromCharCode(65 + index)}) ${alt}`
      ).join('\n');

      return `ID: ${q.id} | Ano: ${q.examYear}
Texto: "${q.text.substring(0, 400)}${q.text.length > 400 ? '...' : ''}"
Alternativas:
${alternativesText}
Resposta Correta: ${q.correctAnswer}`;
    }).join('\n\n---\n\n');

    // Prepare categories for AI analysis
    const categoriesFormatted = availableCategories.map((c: Category) => 
      `- ID: ${c.id}, Nome: "${c.name}"`
    ).join('\n');

    console.log(`📏 Questions text length: ${questionsFormatted.length} characters`);
    console.log(`📏 Categories text length: ${categoriesFormatted.length} characters`);
    console.log(`🤖 Using ${apiProvider} model`);

    // Prepare prompt otimizado para evitar truncamento
    const prompt = `Categorize questões médicas para ${specialty}. ${analysisType === 'theme' ? 'Escolha o TEMA' : 'Escolha o FOCO'} mais apropriado.

QUESTÕES:
${questionsFormatted}

${analysisType === 'theme' ? 'TEMAS' : 'FOCOS'} DISPONÍVEIS:
${categoriesFormatted}

Responda APENAS com JSON:
{
  "suggestions": [
    {
      "questionId": "id",
      "questionText": "resumo-breve",
      ${analysisType === 'theme' ? `"suggestedTheme": {"id": "id", "name": "nome", "confidence": 85},` : `"suggestedFocus": {"id": "id", "name": "nome", "confidence": 85},`}
      "reasoning": "motivo-breve"
    }
  ]
}

REGRAS:
- Analise TODAS as questões
- Use apenas categorias da lista
- Confiança mínima: 60%
- Reasoning máximo: 50 caracteres
- Sempre sugira algo, mesmo com baixa confiança`;

    // Make request to AI API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 segundos timeout

    try {
      let response: Response;

      if (apiProvider === 'gemini') {
        // Gemini API request
        response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          signal: controller.signal,
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.3,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 4096, // Reduzido para evitar truncamento
              responseMimeType: "application/json"
            }
          }),
        });
      } else {
        // OpenAI API request
        response = await fetch("https://api.openai.com/v1/chat/completions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${apiKey}`,
          },
          signal: controller.signal,
          body: JSON.stringify({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: "Você é um especialista em categorização de questões médicas. Responda APENAS com JSON válido no formato solicitado."
              },
              {
                role: "user",
                content: prompt
              }
            ],
            temperature: 0.3,
            max_tokens: 8192,
            response_format: { type: "json_object" }
          }),
        });
      }

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API error:`, response.status, response.statusText);
        console.error(`❌ Error details:`, errorText);

        // Diferentes tipos de erro
        let errorMessage = "Erro interno do servidor";
        if (response.status === 429) {
          errorMessage = "Muitas solicitações. Tente novamente em alguns minutos";
        } else if (response.status === 400) {
          errorMessage = "Erro na solicitação";
        } else if (response.status >= 500) {
          errorMessage = "Erro interno do servidor";
        }

        return new Response(
          JSON.stringify({
            error: errorMessage
          }),
          {
            status: response.status === 429 ? 429 : 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

    const data = await response.json();

    let result: any;
    try {
      if (apiProvider === 'gemini') {
        // Parse Gemini response
        if (!data.candidates || !data.candidates[0]) {
          console.error("❌ Invalid Gemini response structure:", data);
          return new Response(
            JSON.stringify({ error: "Invalid response from Gemini AI" }),
            {
              status: 500,
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        const candidate = data.candidates[0];

        // Check for MAX_TOKENS error
        if (candidate.finishReason === "MAX_TOKENS") {
          console.error("❌ Gemini response truncated due to MAX_TOKENS");
          console.error("❌ Usage metadata:", data.usageMetadata);
          return new Response(
            JSON.stringify({
              error: "Response truncated due to token limit",
              details: "Try reducing the number of questions per batch or use shorter prompts"
            }),
            {
              status: 413, // Payload Too Large
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        // Check if content exists
        if (!candidate.content || !candidate.content.parts || !candidate.content.parts[0] || !candidate.content.parts[0].text) {
          console.error("❌ Gemini response has no content:", candidate);
          return new Response(
            JSON.stringify({
              error: "Empty response from Gemini AI",
              finishReason: candidate.finishReason
            }),
            {
              status: 500,
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        const responseText = data.candidates[0].content.parts[0].text;
        console.log("🤖 Gemini response text:", responseText.substring(0, 200) + "...");

        // Check if JSON is truncated
        if (!responseText.trim().endsWith('}')) {
          console.error("❌ Gemini response appears to be truncated");
          console.error("❌ Response ends with:", responseText.slice(-100));
          return new Response(
            JSON.stringify({
              error: "Response truncated - incomplete JSON",
              details: "Try reducing the number of questions per batch"
            }),
            {
              status: 413, // Payload Too Large
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        result = JSON.parse(responseText);
      } else {
        // Parse OpenAI response
        if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
          console.error("❌ Invalid OpenAI response structure:", data);
          return new Response(
            JSON.stringify({ error: "Invalid response from OpenAI" }),
            {
              status: 500,
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        const responseText = data.choices[0].message.content;
        console.log("🤖 OpenAI response text:", responseText.substring(0, 200) + "...");
        result = JSON.parse(responseText);
      }
    } catch (parseError) {
      console.error(`Failed to parse ${apiProvider} response:`, parseError);
      const rawResponse = apiProvider === 'gemini'
        ? data.candidates?.[0]?.content?.parts?.[0]?.text
        : data.choices?.[0]?.message?.content;
      console.error("Raw response:", rawResponse);
      return new Response(
        JSON.stringify({ error: "Erro interno do servidor. Tente novamente." }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate the result structure
    if (!result.suggestions || !Array.isArray(result.suggestions)) {
      return new Response(
        JSON.stringify({ error: "Invalid AI response format - missing suggestions array" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`🔍 Raw AI response contains ${result.suggestions?.length || 0} suggestions`);

    // Log each suggestion for debugging
    result.suggestions?.forEach((suggestion: any, index: number) => {
      console.log(`📋 Suggestion ${index + 1}:`, {
        questionId: suggestion.questionId,
        hasTheme: !!suggestion.suggestedTheme,
        hasFocus: !!suggestion.suggestedFocus,
        hasReasoning: !!suggestion.reasoning,
        confidence: analysisType === 'theme' ? suggestion.suggestedTheme?.confidence : suggestion.suggestedFocus?.confidence
      });
    });

    // Validate and sanitize suggestions
    const validSuggestions = result.suggestions.filter((suggestion: any) => {
      const hasValidCategory = analysisType === 'theme'
        ? suggestion.suggestedTheme && suggestion.suggestedTheme.id && suggestion.suggestedTheme.name
        : suggestion.suggestedFocus && suggestion.suggestedFocus.id && suggestion.suggestedFocus.name;

      const isValid = suggestion.questionId &&
             suggestion.reasoning &&
             hasValidCategory &&
             typeof (analysisType === 'theme' ? suggestion.suggestedTheme.confidence : suggestion.suggestedFocus.confidence) === 'number';

      if (!isValid) {
        console.log(`❌ Invalid suggestion for question ${suggestion.questionId}:`, {
          hasQuestionId: !!suggestion.questionId,
          hasReasoning: !!suggestion.reasoning,
          hasValidCategory,
          hasConfidence: typeof (analysisType === 'theme' ? suggestion.suggestedTheme?.confidence : suggestion.suggestedFocus?.confidence) === 'number'
        });
      }

      return isValid;
    });

    console.log(`✅ After validation: ${validSuggestions.length} valid suggestions`);

    // Ensure all suggested categories exist in available categories
    const finalSuggestions = validSuggestions.filter((suggestion: any) => {
      const suggestedCategory = analysisType === 'theme' ? suggestion.suggestedTheme : suggestion.suggestedFocus;
      const categoryExists = availableCategories.some((cat: Category) => cat.id === suggestedCategory.id);

      if (!categoryExists) {
        console.log(`❌ Category not found for question ${suggestion.questionId}:`, {
          suggestedCategoryId: suggestedCategory.id,
          suggestedCategoryName: suggestedCategory.name,
          availableCategoryIds: availableCategories.map((c: Category) => c.id)
        });
      }

      return categoryExists;
    });

    console.log(`✅ After category validation: ${finalSuggestions.length} final suggestions`);
    console.log(`📊 Questions sent: ${questions.length}`);
    console.log(`📊 AI suggestions: ${result.suggestions?.length || 0}`);
    console.log(`📊 Valid suggestions: ${validSuggestions.length}`);
    console.log(`📊 Final suggestions: ${finalSuggestions.length}`);
    console.log(`📊 Success rate: ${Math.round((finalSuggestions.length / questions.length) * 100)}%`);

    // Log which questions didn't get suggestions
    const processedQuestionIds = finalSuggestions.map((s: any) => s.questionId);
    const unprocessedQuestions = questions.filter((q: Question) => !processedQuestionIds.includes(q.id));

    if (unprocessedQuestions.length > 0) {
      console.log(`⚠️ Questions without suggestions (${unprocessedQuestions.length}):`);
      unprocessedQuestions.forEach((q: Question) => {
        console.log(`   - ${q.id}: "${q.text.substring(0, 100)}..."`);
      });
    }

    return new Response(
      JSON.stringify({ suggestions: finalSuggestions }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError.name === 'AbortError') {
        console.error(`❌ ${apiProvider.toUpperCase()} API timeout after 60 seconds`);
        return new Response(
          JSON.stringify({
            error: "Request timeout",
            details: `${apiProvider.toUpperCase()} API request timed out after 60 seconds`
          }),
          {
            status: 408,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      console.error("❌ Fetch error:", fetchError);
      return new Response(
        JSON.stringify({
          error: "Network error",
          details: fetchError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

  } catch (error) {
    console.error("❌ Error in categorize-questions:", error);
    console.error("❌ Error details:", {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return new Response(
      JSON.stringify({
        error: "Erro interno do servidor. Tente novamente em alguns minutos."
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
