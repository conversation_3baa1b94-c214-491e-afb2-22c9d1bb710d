import { useState, useEffect } from 'react';

export interface PomodoroState {
  workTime: number;
  breakTime: number;
  timeLeft: number;
  isRunning: boolean;
  isWorkTime: boolean;
  cycles: number;
  startTime: number | null;
  lastUpdate: number | null;
}

const STORAGE_KEY = 'pomodoro_state';

export const usePomodoroState = () => {
  const loadInitialState = (): PomodoroState => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const state = JSON.parse(saved);
        if (state.isRunning && state.lastUpdate) {
          const elapsed = Math.floor((Date.now() - state.lastUpdate) / 1000);
          state.timeLeft = Math.max(0, state.timeLeft - elapsed);
          state.lastUpdate = Date.now();
        }
        return state;
      }
    } catch (error) {
      console.error('Error loading pomodoro state:', error);
    }
    
    return {
      workTime: 25,
      breakTime: 5,
      timeLeft: 25 * 60,
      isRunning: false,
      isWorkTime: true,
      cycles: 0,
      startTime: null,
      lastUpdate: null
    };
  };

  const [state, setState] = useState<PomodoroState>(loadInitialState);

  const updateState = (updates: Partial<PomodoroState>) => {
    setState(current => {
      const newState = { ...current, ...updates, lastUpdate: Date.now() };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
      return newState;
    });
  };

  const resetState = () => {
    const initialState = {
      workTime: 25,
      breakTime: 5,
      timeLeft: 25 * 60,
      isRunning: false,
      isWorkTime: true,
      cycles: 0,
      startTime: null,
      lastUpdate: null
    };
    localStorage.removeItem(STORAGE_KEY);
    setState(initialState);
  };

  return {
    state,
    updateState,
    resetState
  };
};