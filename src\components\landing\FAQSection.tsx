
import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function FAQSection() {
  const faqs = [
    {
      question: "Como funciona o planner automático?",
      answer: "Nosso planner analisa seu perfil de estudante, disponibilidade de tempo e objetivos para criar um cronograma personalizado. Ele distribui os conteúdos de forma estratégica, priorizando seus pontos fracos e programando revisões no momento ideal para fixação."
    },
    {
      question: "A plataforma é realmente gratuita?",
      answer: "Sim! O acesso à plataforma é 100% gratuito, incluindo o planner automático e banco de questões. Temos alguns recursos premium opcionais para quem deseja uma experiência ainda mais personalizada."
    },
    {
      question: "Quantas questões existem no banco?",
      answer: "Nosso banco conta com mais de 50.000 questões comentadas, abrangendo todas as especialidades e atualizadas constantemente com as provas mais recentes das principais residências do país."
    },
    {
      question: "Quanto tempo preciso dedicar aos estudos?",
      answer: "O tempo recomendado varia de acordo com seus objetivos e disponibilidade. Nosso sistema se adapta à sua rotina, seja você um estudante com dedicação integral ou um profissional com tempo limitado."
    },
    {
      question: "Como sei que o método funciona?",
      answer: "Nosso método é baseado em técnicas comprovadas de aprendizagem, com foco em revisões espaçadas e prática ativa que são cientificamente comprovadas para maximizar a retenção e o aprendizado."
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-purple-50 to-purple-100 relative overflow-hidden">
      {/* Decorative elements - Duolingo style */}
      <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/5 rounded-full"></div>
      <div className="absolute top-1/3 right-0 w-60 h-60 bg-yellow-100/30 rounded-full translate-x-1/3"></div>
      <div className="absolute -bottom-20 left-1/4 w-48 h-48 bg-green-100/20 rounded-full"></div>
      
      {/* Zigzag pattern (Duolingo style) */}
      <div className="absolute bottom-0 left-0 right-0 h-10 bg-primary/5"></div>
      <div className="absolute bottom-10 left-0 right-0 h-6 bg-yellow-100/30"></div>
      
      <div className="container mx-auto relative">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl text-primary font-bold">?</span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
            Perguntas Frequentes
          </h2>
          <p className="text-lg text-gray-600">
            Tire suas dúvidas sobre nossa plataforma e metodologia
          </p>
        </motion.div>

        <motion.div 
          className="max-w-3xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem 
                  key={index} 
                  value={`faq-${index}`}
                  className="border border-gray-200 rounded-lg overflow-hidden shadow-sm group"
                >
                  <AccordionTrigger className="px-6 py-4 hover:bg-gray-50 transition-colors text-left group-data-[state=open]:bg-primary/5">
                    <div className="flex items-center">
                      <span className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-sm font-medium">
                        {index + 1}
                      </span>
                      <span className="text-lg font-medium text-gray-800">{faq.question}</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-6 py-4 bg-gray-50">
                    <div className="pl-11">
                      <p className="text-gray-600">{faq.answer}</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </motion.div>
        
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="inline-block bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-3 rounded-full">
            <p className="text-gray-600">
              Ainda tem dúvidas? Entre em contato conosco pelo{" "}
              <a href="#" className="text-primary font-medium hover:underline flex items-center justify-center gap-1 inline-flex">
                suporte
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            </p>
          </div>
          
          <div className="mt-8">
            <div className="text-center text-sm text-gray-500">
              <p>Disponível para:</p>
              <div className="flex items-center justify-center gap-2 mt-2">
                <span className="px-3 py-1 bg-primary/10 text-primary rounded-full font-medium">Oftalmologia</span>
                <span className="px-3 py-1 bg-primary/10 text-primary rounded-full font-medium">Pediatria</span>
                <span className="px-3 py-1 bg-gray-100 text-gray-400 rounded-full font-medium line-through">Dermatologia</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
