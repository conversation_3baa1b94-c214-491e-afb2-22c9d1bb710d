
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ImageUpload } from "./ImageUpload";

interface FlashcardInputsProps {
  front: string;
  setFront: (value: string) => void;
  back: string;
  setBack: (value: string) => void;
  frontImage: string;
  setFrontImage: (value: string) => void;
  backImage: string;
  setBackImage: (value: string) => void;
}

export const FlashcardInputs = ({
  front,
  setFront,
  back,
  setBack,
  frontImage,
  setFrontImage,
  backImage,
  setBackImage,
}: FlashcardInputsProps) => {
  return (
    <div className="grid gap-6">
      {/* Front Side */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-bold">Frente *</Label>
          <Textarea
            placeholder="Digite o conteúdo da frente do cartão"
            className="mt-2 min-h-[100px] border-2 border-black/20 rounded-lg focus:border-black/50 transition-colors resize-none"
            value={front}
            onChange={(e) => setFront(e.target.value)}
            required
          />
        </div>
        <ImageUpload
          label="Imagem da frente (opcional)"
          currentImage={frontImage}
          onImageSelect={setFrontImage}
          id="front-image"
        />
      </div>

      {/* Back Side */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-bold">Verso *</Label>
          <Textarea
            placeholder="Digite o conteúdo do verso do cartão"
            className="mt-2 min-h-[100px] border-2 border-black/20 rounded-lg focus:border-black/50 transition-colors resize-none"
            value={back}
            onChange={(e) => setBack(e.target.value)}
            required
          />
        </div>
        <ImageUpload
          label="Imagem do verso (opcional)"
          currentImage={backImage}
          onImageSelect={setBackImage}
          id="back-image"
        />
      </div>
    </div>
  );
};
