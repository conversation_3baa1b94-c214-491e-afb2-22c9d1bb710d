
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BatchAnalysisItem, QuestionImprovementSuggestion } from "@/types/admin";
import { Check, Eye, Loader2, AlertTriangle, AlertCircle } from "lucide-react";

interface BatchProcessingTableProps {
  items: BatchAnalysisItem[];
  onViewDetails: (index: number) => void;
  onApplySuggestion: (index: number) => void;
  onApplyAll: () => void;
}

export const BatchProcessingTable: React.FC<BatchProcessingTableProps> = ({
  items,
  onViewDetails,
  onApplySuggestion,
  onApplyAll,
}) => {
  const pendingItems = items.filter((item) => !item.isApplied && !item.isError && item.suggestion);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Resultados do Processamento em Lote</h3>
        {pendingItems.length > 0 && (
          <Button onClick={onApplyAll} variant="default">
            <Check className="mr-2 h-4 w-4" />
            Aprovar Todos ({pendingItems.length})
          </Button>
        )}
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>#</TableHead>
              <TableHead>Questão</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Tema Sugerido</TableHead>
              <TableHead>Foco Sugerido</TableHead>
              <TableHead>Confiança</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item, index) => (
              <TableRow
                key={`${item.questionId}-${index}`}
                className={
                  item.isApplied
                    ? "bg-green-50 dark:bg-green-900/10"
                    : item.isError
                    ? "bg-red-50 dark:bg-red-900/10"
                    : ""
                }
              >
                <TableCell>{index + 1}</TableCell>
                <TableCell className="max-w-xs">
                  <div className="truncate">
                    {item.questionStatement.substring(0, 80)}
                    {item.questionStatement.length > 80 ? "..." : ""}
                  </div>
                </TableCell>
                <TableCell>
                  {item.isProcessing ? (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Processando
                    </Badge>
                  ) : item.isError ? (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      Erro
                    </Badge>
                  ) : item.isApplied ? (
                    <Badge variant="outline" className="bg-green-500 text-white flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Aplicado
                    </Badge>
                  ) : item.suggestion ? (
                    <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
                      Pronto
                    </Badge>
                  ) : (
                    <Badge variant="outline">Pendente</Badge>
                  )}
                </TableCell>
                <TableCell>
                  {item.suggestion?.themeName || (item.isError ? "—" : "")}
                </TableCell>
                <TableCell>
                  {item.suggestion?.focusName || (item.isError ? "—" : "")}
                </TableCell>
                <TableCell>
                  {item.suggestion
                    ? `${(item.suggestion.confidence * 100).toFixed(0)}%`
                    : item.isError
                    ? "—"
                    : ""}
                </TableCell>
                <TableCell className="text-right whitespace-nowrap">
                  <div className="flex justify-end gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={() => onViewDetails(index)}
                      title="Ver detalhes da questão"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    {item.suggestion && !item.isApplied && !item.isError && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onApplySuggestion(index)}
                      >
                        Aprovar
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {items.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <AlertCircle className="h-8 w-8 mb-2" />
                    <span>Nenhum item processado ainda</span>
                    <span className="text-sm">Selecione uma quantidade e clique em "Processar Lote"</span>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default BatchProcessingTable;
