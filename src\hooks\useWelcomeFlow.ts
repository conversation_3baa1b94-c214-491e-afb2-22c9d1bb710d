import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { useUserData } from '@/hooks/useUserData';

interface UserData {
  profile: {
    id: string;
    premium: boolean;
    full_name: string;
  } | null;
  preferences: {
    id: string;
    welcome_dialog_shown: boolean;
  } | null;
}

export const useWelcomeFlow = () => {
  const { user } = useAuth();
  const { preferences, isLoading: preferencesLoading, markWelcomeAsSeen } = useUserPreferences();
  const { profile, isLoading: profileLoading, isPremium } = useUserData();
  const [showWelcome, setShowWelcome] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      setShowWelcome(false);
      return;
    }

    // Usar dados já carregados do useUserData e useUserPreferences
    if (!preferencesLoading && !profileLoading && preferences && profile) {
      checkWelcomeStatus();
    } else if (!preferencesLoading && !profileLoading) {
      setIsLoading(false);
      setShowWelcome(false);
    }
  }, [user?.id, preferences?.welcome_dialog_shown, preferencesLoading, profileLoading, profile?.id, isPremium]);

  const checkWelcomeStatus = () => {
    if (!user || !preferences || !profile) {
      console.log('🔍 [WelcomeFlow] Missing user, preferences or profile, skipping check');
      setIsLoading(false);
      return;
    }

    // Mostrar welcome se:
    // 1. Tem perfil
    // 2. Ainda não viu o welcome dialog (welcome_dialog_shown = false)
    // 3. Para usuários premium, não mostrar welcome (já têm acesso)
    const shouldShowWelcome = profile && preferences && !preferences.welcome_dialog_shown && !isPremium;



    if (shouldShowWelcome) {
      setShowWelcome(true);
    }

    setIsLoading(false);
  };

  const markWelcomeAsSeenLocal = async () => {
    if (!user) return;

    try {
      // Usar a função do useUserPreferences para manter consistência
      await markWelcomeAsSeen();
      setShowWelcome(false);
    } catch (error) {
      console.error('Erro ao atualizar status de welcome:', error);
    }
  };

  const grantPremiumAccess = async () => {
    if (!user) return false;

    try {
      // ✅ Apenas atualizar premium no perfil
      // NÃO marcar welcome_dialog_shown como true - deixar false para que apareça
      const { error } = await supabase
        .from('profiles')
        .update({ premium: true })
        .eq('id', user.id);

      if (error) {
        console.error('Erro ao conceder acesso premium:', error);
        return false;
      }

      // Estado será atualizado automaticamente pelo useUserData

      setShowWelcome(false);
      return true;
    } catch (error) {
      console.error('Erro ao conceder acesso premium:', error);
      return false;
    }
  };

  return {
    showWelcome,
    isLoading,
    userData: { profile, preferences },
    markWelcomeAsSeen: markWelcomeAsSeenLocal,
    grantPremiumAccess,
    userName: profile?.full_name || user?.user_metadata?.full_name || user?.email?.split('@')[0]
  };
};
