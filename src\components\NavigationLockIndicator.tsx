import React from 'react';
import { useNavigationLock } from '@/contexts/NavigationLockContext';
import { Lock, Brain } from 'lucide-react';

export const NavigationLockIndicator: React.FC = () => {
  const { isNavigationLocked, lockReason } = useNavigationLock();

  if (!isNavigationLocked) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-[9999] bg-yellow-500 text-black px-4 py-2 shadow-lg">
      <div className="flex items-center justify-center gap-2 max-w-4xl mx-auto">
        <div className="flex items-center gap-2">
          <Lock className="h-4 w-4" />
          <Brain className="h-4 w-4 animate-pulse" />
        </div>
        <span className="font-medium text-sm">
          {lockReason?.includes('Gerando análise da IA')
            ? 'IA analisando sua resposta...'
            : lockReason?.includes('Finalizando sessão')
            ? 'Finalizando sessão de estudos...'
            : lockReason || 'Processando...'}
        </span>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-black rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};
