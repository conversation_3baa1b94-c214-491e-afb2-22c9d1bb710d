/**
 * Sistema de cache persistente para evitar perda de dados ao navegar entre abas
 */

interface CacheItem {
  data: any;
  timestamp: number;
  ttl: number; // Time to live em milliseconds
}

class PersistentCache {
  private prefix = 'studywise_cache_';

  /**
   * Salvar dados no cache com TTL
   */
  set(key: string, data: any, ttlMinutes: number = 30): void {
    try {
      const item: CacheItem = {
        data,
        timestamp: Date.now(),
        ttl: ttlMinutes * 60 * 1000
      };
      
      sessionStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn(`⚠️ [PersistentCache] Erro ao salvar cache para ${key}:`, error);
    }
  }

  /**
   * Recuperar dados do cache
   */
  get(key: string): any | null {
    try {
      const cached = sessionStorage.getItem(this.prefix + key);
      if (!cached) return null;

      const item: CacheItem = JSON.parse(cached);
      const now = Date.now();

      // Verificar se o cache expirou
      if (now - item.timestamp > item.ttl) {
        this.remove(key);
        return null;
      }


      return item.data;
    } catch (error) {
      console.warn(`⚠️ [PersistentCache] Erro ao recuperar cache para ${key}:`, error);

      return null;
    }
  }

  /**
   * Remover item do cache
   */
  remove(key: string): void {
    try {
      sessionStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.warn(`Erro ao remover cache para ${key}:`, error);
    }
  }

  /**
   * Limpar todo o cache
   */
  clear(): void {
    try {
      const keys = Object.keys(sessionStorage).filter(key => key.startsWith(this.prefix));
      keys.forEach(key => sessionStorage.removeItem(key));
    } catch (error) {
      console.warn(`Erro ao limpar cache:`, error);
    }
  }

  /**
   * Verificar se uma chave existe no cache e não expirou
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Obter estatísticas do cache
   */
  getStats(): { totalItems: number; totalSize: number; keys: string[] } {
    try {
      const keys = Object.keys(sessionStorage).filter(key => key.startsWith(this.prefix));
      let totalSize = 0;
      
      keys.forEach(key => {
        const value = sessionStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      });

      return {
        totalItems: keys.length,
        totalSize,
        keys: keys.map(key => key.replace(this.prefix, ''))
      };
    } catch (error) {
      console.warn(`⚠️ [PersistentCache] Erro ao obter estatísticas:`, error);
      return { totalItems: 0, totalSize: 0, keys: [] };
    }
  }
}

// Instância singleton
export const persistentCache = new PersistentCache();

/**
 * Hook para usar cache persistente com React Query
 */
export const usePersistentCache = (key: string, ttlMinutes: number = 30) => {
  const setCache = (data: any) => {
    persistentCache.set(key, data, ttlMinutes);
  };

  const getCache = () => {
    return persistentCache.get(key);
  };

  const removeCache = () => {
    persistentCache.remove(key);
  };

  const hasCache = () => {
    return persistentCache.has(key);
  };

  return {
    setCache,
    getCache,
    removeCache,
    hasCache
  };
};
