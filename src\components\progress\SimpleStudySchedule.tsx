import React, { memo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Calendar, Loader2 } from "lucide-react";
import { StudySchedule } from "./StudySchedule";
import { useScheduleQuery } from "@/hooks/study-schedule/useScheduleQuery";

interface SimpleStudyScheduleProps {
  onUpdateTopic?: (topic: any) => void;
  onMarkStudied?: (topicId: string, revisionNumber?: number) => void;
  onDeleteWeek?: (weekNumber: number) => void;
  onDeleteAllWeeks?: () => void;
  onDeleteTopic?: (topicId: string) => void;
}

const SimpleStudyScheduleComponent = ({
  onUpdateTopic,
  onMarkStudied,
  onDeleteWeek,
  onDeleteAllWeeks,
  onDeleteTopic
}: SimpleStudyScheduleProps) => {
  const {
    weeklySchedule,
    isLoading,
    error,
    updateTopic,
    markTopicAsStudied,
    deleteTopic,
    deleteWeek,
    deleteAllWeeks
  } = useScheduleQuery();

  // Handlers que usam as mutations do React Query
  const handleUpdateTopic = (topic: any) => {
    if (onUpdateTopic) {
      onUpdateTopic(topic);
    } else {
      updateTopic(topic);
    }
  };

  const handleMarkStudied = (topicId: string, revisionNumber?: number) => {
    if (onMarkStudied) {
      onMarkStudied(topicId, revisionNumber);
    } else {
      markTopicAsStudied(topicId);
    }
  };

  const handleDeleteTopic = (topicId: string) => {
    if (onDeleteTopic) {
      onDeleteTopic(topicId);
    } else {
      deleteTopic(topicId);
    }
  };

  const handleDeleteWeek = (weekNumber: number) => {
    if (onDeleteWeek) {
      onDeleteWeek(weekNumber);
    } else {
      deleteWeek(weekNumber);
    }
  };

  const handleDeleteAllWeeks = () => {
    if (onDeleteAllWeeks) {
      onDeleteAllWeeks();
    } else {
      deleteAllWeeks();
    }
  };

  // Loading inicial
  if (isLoading && !weeklySchedule) {
    return (
      <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
        <CardHeader className="pb-2 border-b border-gray-100">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-[#58CC02] p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800">Cronograma de Estudos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#58CC02] mb-4" />
            <p className="text-gray-600 text-center">
              Carregando cronograma...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Erro
  if (error) {
    return (
      <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
        <CardHeader className="pb-2 border-b border-gray-100">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-red-500 p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800">Erro no Cronograma</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="text-center py-12">
            <p className="text-red-600 text-center">
              Erro ao carregar cronograma. Tente recarregar a página.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sem cronograma
  if (!weeklySchedule || weeklySchedule.recommendations.length === 0) {
    return (
      <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
        <CardHeader className="pb-2 border-b border-gray-100">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-[#58CC02] p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800">Cronograma de Estudos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] rounded-full flex items-center justify-center mb-4 shadow-lg border-2 border-black">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-gray-800">
              Nenhum cronograma encontrado
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Você ainda não possui um cronograma de estudos criado. 
              Gere um novo cronograma para começar a organizar seus estudos.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Componente principal do cronograma */}
      <StudySchedule
        weeklyPlan={weeklySchedule.recommendations}
        onUpdateTopic={handleUpdateTopic}
        onMarkStudied={handleMarkStudied}
        onDeleteWeek={handleDeleteWeek}
        onDeleteAllWeeks={handleDeleteAllWeeks}
        onDeleteTopic={handleDeleteTopic}
      />

      {/* Indicador de loading para mutations */}
      {isLoading && weeklySchedule && (
        <div className="flex items-center justify-center py-4">
          <div className="flex items-center gap-3 text-gray-600">
            <Loader2 className="h-4 w-4 animate-spin text-[#58CC02]" />
            <span className="text-sm font-medium">Salvando alterações...</span>
          </div>
        </div>
      )}

      {/* Estatísticas de desenvolvimento */}
      {process.env.NODE_ENV === 'development' && weeklySchedule && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-blue-700">
              <span>Total de dias: {weeklySchedule.recommendations.length}</span>
              <span>Semanas: {Math.ceil(weeklySchedule.recommendations.length / 7)}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// ✅ Memoização simples - só re-renderiza se props mudarem
export const SimpleStudySchedule = memo(SimpleStudyScheduleComponent);

export default SimpleStudySchedule;
