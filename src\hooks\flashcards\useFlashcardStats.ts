import { useState } from 'react';

interface SessionStats {
  totalCards: number;
  correctAnswers: number;
  totalTime: number;
  startTime: Date;
}

export const useFlashcardStats = () => {
  const [sessionStats, setSessionStats] = useState<SessionStats>({
    totalCards: 0,
    correctAnswers: 0,
    totalTime: 0,
    startTime: new Date(),
  });

  const updateStats = (isCorrect: boolean) => {
    setSessionStats(prev => ({
      ...prev,
      correctAnswers: prev.correctAnswers + (isCorrect ? 1 : 0),
      totalTime: Math.floor((new Date().getTime() - prev.startTime.getTime()) / 1000)
    }));
  };

  return { sessionStats, updateStats };
};