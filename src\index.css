
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom theme colors for the question filters */
    --brand-yellow: #FEF7CD;
    --brand-orange: #FF6B00;
    --brand-orange-hover: #FF8800;
    --brand-red: #FF4D00;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-[#FEF7CD] text-foreground;
  }
}

/* Special styles for the design elements */
.text-outline {
  -webkit-text-stroke: 2px black;
  text-stroke: 2px black;
}

.card-border {
  border: 2px solid black;
}

.skewed-background {
  transform: skew(-5deg);
}

.slanted-title {
  transform: rotate(-2deg);
  display: inline-block;
}

.raised-button {
  transform: translateY(-2px);
  box-shadow: 0 4px 0 0 rgba(0, 0, 0, 0.2);
  transition: transform 0.1s, box-shadow 0.1s;
}

.raised-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2);
}

.decorated-badge {
  position: relative;
}

.decorated-badge::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(
    from 0deg at 50% 50%,
    #FF3B30,
    #FFD60A,
    #4ADE80,
    #0EA5E9,
    #FF3B30
  );
  border-radius: inherit;
  z-index: -1;
}

/* Create the dot pattern background */
.dot-pattern {
  background-image: radial-gradient(#D1D5DB 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0;
}

/* Falling element animations */
.falling-element {
  transform: rotate(-2deg);
}

.bounce-element {
  animation: bounce 1s ease infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Icon container styling */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  border: 2px solid #000;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.1);
  aspect-ratio: 1 / 1;
  width: 2.5rem;
  height: 2.5rem;
}

/* Question filter specific styles */
.filter-header {
  background: linear-gradient(to right, #FF6B00, #FF4D00);
  border: 2px solid black;
}

.filter-card {
  background-color: #FEF7CD;
  border: 2px solid black;
  border-radius: 0.5rem;
}

/* Study page layout improvements */
.study-container {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile */
}

.study-header {
  padding-top: clamp(2rem, 8vw, 4rem);
}

.study-title {
  font-size: clamp(2rem, 6vw, 3rem);
  line-height: 1.1;
}

.study-description {
  font-size: clamp(1rem, 3vw, 1.25rem);
  line-height: 1.6;
}

/* Study streak widget improvements */
.study-streak-widget {
  width: 100%;
  max-width: 28rem;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .study-streak-widget {
    margin: 0;
    width: auto;
    min-width: 24rem;
  }
}

/* Study tools grid improvements */
.study-tools-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .study-tools-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .study-tools-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Ensure proper spacing and overflow handling */
.study-card {
  min-height: 200px;
  overflow: hidden;
}

.study-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.study-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.filter-badge {
  background-color: #FF6B00;
  color: white;
  border: 1px solid black;
}

.filter-button {
  background-color: #FF6B00;
  color: white;
  border: 2px solid black;
}

.filter-button:hover {
  background-color: #FF8800;
}

/* Custom checkbox styling */
.custom-checkbox {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid black;
  border-radius: 0.25rem;
  background-color: white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.custom-checkbox:checked {
  background-color: #FF6B00;
  border-color: black;
}

.custom-checkbox:checked::after {
  content: '✓';
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

/* Flashcard flip animation utilities */
.backface-hidden {
  backface-visibility: hidden;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.perspective-1000 {
  perspective: 1000px;
}

/* Disable zoom and double-tap on mobile */
@media (max-width: 768px) {
  * {
    /* Disable double-tap zoom */
    touch-action: manipulation;

    /* Disable text selection on double-tap */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    /* Disable tap highlight */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* Re-enable text selection for specific elements */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Disable zoom on input focus (iOS) */
  input, select, textarea {
    font-size: 16px !important;
    transform-origin: left top;
    transform: scale(1);
  }

  /* Prevent zoom on double-tap for specific elements */
  button, a, [role="button"] {
    touch-action: manipulation;
  }
}

/* Global touch-action for better mobile experience */
html, body {
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* Disable zoom on all interactive elements */
button, input, select, textarea, a, [role="button"], [tabindex] {
  touch-action: manipulation;
}

/* Specific mobile optimizations */
@media (max-width: 768px) {
  /* Disable zoom on cards and clickable elements */
  .study-card, .card, [data-clickable], .clickable {
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Disable zoom on dialog and modal content */
  [role="dialog"], [role="alertdialog"], .modal, .dialog {
    touch-action: manipulation;
  }

  /* Prevent zoom on form elements */
  .form-control, .input, .button {
    touch-action: manipulation;
  }

  /* Disable zoom on navigation elements */
  nav, .nav, .navigation, .menu {
    touch-action: manipulation;
  }

  /* Disable zoom on floating action buttons */
  .floating-button, .fab, .fixed {
    touch-action: manipulation;
  }
}

