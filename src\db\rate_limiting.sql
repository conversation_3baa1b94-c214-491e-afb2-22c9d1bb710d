-- =====================================================
-- SISTEMA DE RATE LIMITING PARA STUDYWISE
-- =====================================================

-- Tabela para armazenar rate limits por usuário
CREATE TABLE IF NOT EXISTS user_rate_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL, -- 'questions', 'sessions', 'flashcards', 'ai_requests'
    request_count INTEGER DEFAULT 0,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, action_type)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_action ON user_rate_limits(user_id, action_type);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window ON user_rate_limits(window_start);

-- RLS para segurança
ALTER TABLE user_rate_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only see their own rate limits" 
ON user_rate_limits FOR ALL 
USING (user_id = auth.uid());

-- =====================================================
-- FUNÇÃO PARA VERIFICAR E APLICAR RATE LIMITING
-- =====================================================

CREATE OR REPLACE FUNCTION check_rate_limit(
    p_user_id UUID,
    p_action_type TEXT,
    p_max_requests INTEGER DEFAULT 100,
    p_window_minutes INTEGER DEFAULT 60
) RETURNS JSONB AS $$
DECLARE
    v_current_count INTEGER := 0;
    v_window_start TIMESTAMP WITH TIME ZONE;
    v_current_time TIMESTAMP WITH TIME ZONE := NOW();
    v_window_duration INTERVAL;
    v_allowed BOOLEAN := TRUE;
    v_reset_time TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calcular duração da janela
    v_window_duration := (p_window_minutes || ' minutes')::INTERVAL;
    
    -- Buscar ou criar registro de rate limit
    SELECT request_count, window_start 
    INTO v_current_count, v_window_start
    FROM user_rate_limits 
    WHERE user_id = p_user_id AND action_type = p_action_type;
    
    -- Se não existe registro, criar
    IF NOT FOUND THEN
        INSERT INTO user_rate_limits (user_id, action_type, request_count, window_start)
        VALUES (p_user_id, p_action_type, 1, v_current_time);
        
        v_current_count := 1;
        v_window_start := v_current_time;
    ELSE
        -- Verificar se a janela expirou
        IF v_current_time > (v_window_start + v_window_duration) THEN
            -- Reset da janela
            UPDATE user_rate_limits 
            SET request_count = 1, 
                window_start = v_current_time,
                updated_at = v_current_time
            WHERE user_id = p_user_id AND action_type = p_action_type;
            
            v_current_count := 1;
            v_window_start := v_current_time;
        ELSE
            -- Incrementar contador
            v_current_count := v_current_count + 1;
            
            -- Verificar se excedeu o limite
            IF v_current_count > p_max_requests THEN
                v_allowed := FALSE;
            ELSE
                UPDATE user_rate_limits 
                SET request_count = v_current_count,
                    updated_at = v_current_time
                WHERE user_id = p_user_id AND action_type = p_action_type;
            END IF;
        END IF;
    END IF;
    
    -- Calcular quando o limite será resetado
    v_reset_time := v_window_start + v_window_duration;
    
    -- Retornar resultado
    RETURN jsonb_build_object(
        'allowed', v_allowed,
        'current_count', v_current_count,
        'max_requests', p_max_requests,
        'window_minutes', p_window_minutes,
        'reset_time', v_reset_time,
        'remaining_requests', GREATEST(0, p_max_requests - v_current_count)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNÇÕES ESPECÍFICAS PARA CADA TIPO DE AÇÃO
-- =====================================================

-- Rate limit para questões (100 por hora)
CREATE OR REPLACE FUNCTION check_questions_rate_limit(p_user_id UUID)
RETURNS JSONB AS $$
BEGIN
    RETURN check_rate_limit(p_user_id, 'questions', 100, 60);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Rate limit para sessões de estudo (20 por hora)
CREATE OR REPLACE FUNCTION check_sessions_rate_limit(p_user_id UUID)
RETURNS JSONB AS $$
BEGIN
    RETURN check_rate_limit(p_user_id, 'sessions', 20, 60);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Rate limit para flashcards (200 por hora)
CREATE OR REPLACE FUNCTION check_flashcards_rate_limit(p_user_id UUID)
RETURNS JSONB AS $$
BEGIN
    RETURN check_rate_limit(p_user_id, 'flashcards', 200, 60);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Rate limit para requisições de IA (10 por hora)
CREATE OR REPLACE FUNCTION check_ai_rate_limit(p_user_id UUID)
RETURNS JSONB AS $$
BEGIN
    RETURN check_rate_limit(p_user_id, 'ai_requests', 10, 60);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FUNÇÃO PARA LIMPAR REGISTROS ANTIGOS
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_old_rate_limits()
RETURNS void AS $$
BEGIN
    -- Remover registros mais antigos que 24 horas
    DELETE FROM user_rate_limits 
    WHERE window_start < NOW() - INTERVAL '24 hours';
    
    -- Log da limpeza
    RAISE NOTICE 'Rate limits cleanup completed at %', NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TRIGGER PARA ATUALIZAR updated_at
-- =====================================================

CREATE OR REPLACE FUNCTION update_rate_limits_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_rate_limits_updated_at ON user_rate_limits;
CREATE TRIGGER trigger_update_rate_limits_updated_at
    BEFORE UPDATE ON user_rate_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_rate_limits_updated_at();

-- =====================================================
-- CONFIGURAR LIMPEZA AUTOMÁTICA (CRON JOB)
-- =====================================================

-- Nota: No Supabase, você pode configurar um cron job para executar:
-- SELECT cron.schedule('cleanup-rate-limits', '0 2 * * *', 'SELECT cleanup_old_rate_limits();');

-- =====================================================
-- GRANTS DE PERMISSÃO
-- =====================================================

-- Permitir que usuários autenticados executem as funções
GRANT EXECUTE ON FUNCTION check_rate_limit TO authenticated;
GRANT EXECUTE ON FUNCTION check_questions_rate_limit TO authenticated;
GRANT EXECUTE ON FUNCTION check_sessions_rate_limit TO authenticated;
GRANT EXECUTE ON FUNCTION check_flashcards_rate_limit TO authenticated;
GRANT EXECUTE ON FUNCTION check_ai_rate_limit TO authenticated;

-- =====================================================
-- EXEMPLOS DE USO
-- =====================================================

/*
-- Verificar rate limit para questões
SELECT check_questions_rate_limit(auth.uid());

-- Resultado exemplo:
{
  "allowed": true,
  "current_count": 15,
  "max_requests": 100,
  "window_minutes": 60,
  "reset_time": "2024-01-01T15:00:00Z",
  "remaining_requests": 85
}

-- Verificar rate limit para IA
SELECT check_ai_rate_limit(auth.uid());
*/
