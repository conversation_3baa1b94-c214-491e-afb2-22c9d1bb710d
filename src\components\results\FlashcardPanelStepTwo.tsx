
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { FlashcardGenerationPreview } from "./FlashcardGenerationPreview";
import { useIsMobile } from "@/hooks/use-mobile";

interface PanelStepTwoProps {
  loading: boolean;
  loadingMessage: string;
  suggested: any[];
  hasImportedAll: boolean;
  handleImportAll: () => void;
  handleImportCard: (cardId: string) => void;
  totalImportedCards: number;
  handleReset: () => void;
  startFlashcardSession: () => void;
  setStep: (n: number) => void;
}

export const FlashcardPanelStepTwo: React.FC<PanelStepTwoProps> = ({
  loading,
  loadingMessage,
  suggested,
  hasImportedAll,
  handleImportAll,
  handleImportCard,
  totalImportedCards,
  handleReset,
  startFlashcardSession,
  setStep
}) => {
  const [importedIndexes, setImportedIndexes] = useState<Set<number>>(new Set());
  const isMobile = useIsMobile();

  // Reset imported indexes when suggested cards change
  useEffect(() => {
    setImportedIndexes(new Set());
  }, [suggested]);

  // Marcar todos como importados quando importar todos
  useEffect(() => {
    if (hasImportedAll && suggested.length > 0) {
      setImportedIndexes(new Set(suggested.map((_, idx) => idx)));
    }
  }, [hasImportedAll, suggested]);

  // Marcar individualmente como importado
  const handleImportOne = (idx: number, cardId: string) => {
    setImportedIndexes(prev => {
      const newSet = new Set(prev);
      newSet.add(idx);
      return newSet;
    });
    handleImportCard(cardId);
  };

  // Mostrar apenas cartões não importados
  const visibleCards = suggested.filter((_, idx) => !importedIndexes.has(idx));



  return (
    <>
      <div className="flex flex-col items-center mb-6">
        <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider text-center">
          Flashcards Gerados
        </h3>

        {suggested.length > 0 && !loading && !hasImportedAll && (
          <Button
            onClick={handleImportAll}
            className="bg-green-600 hover:bg-green-700 text-white mt-3"
            size="sm"
          >
            <svg className="h-4 w-4 mr-1" viewBox="0 0 16 16" fill="none">
              <path d="M6 8h4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
              <path d="M8 6v4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
              <path d="M2.75 8a5.25 5.25 0 1010.5 0 5.25 5.25 0 00-10.5 0z" stroke="currentColor" strokeWidth="1.5"/>
            </svg>
            Importar Todos
          </Button>
        )}
      </div>

      {loading ? (
        <div className="py-8">
          <Card className="p-8 text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
            <p className="text-gray-800 font-medium">{loadingMessage}</p>
            <p className="text-sm text-gray-600">Aguarde enquanto o Dr. Will prepara seus flashcards personalizados</p>
          </Card>
        </div>
      ) : (
        <>
          {suggested.length > 0 ? (
            <div className="space-y-4">
              <p className="text-sm text-gray-600 text-center">
                {suggested.length} flashcards foram gerados com sucesso!
              </p>

              {/* Cards não importados */}
              <div className="grid gap-4">
                {visibleCards.length === 0 && hasImportedAll && (
                  <Card className="p-4 text-center text-green-700 bg-green-50 border-green-200">
                    Todos os flashcards desta geração já foram importados!
                  </Card>
                )}
                <div className={`grid gap-4 ${isMobile ? '' : 'sm:grid-cols-2 md:grid-cols-3'}`}>
                  {visibleCards.map((card, idx) => {
                    // O índice original do card
                    const origIdx = suggested.findIndex(c => c === card);
                    return (
                      <FlashcardGenerationPreview
                        key={origIdx}
                        card={card}
                        isImported={importedIndexes.has(origIdx) || hasImportedAll}
                        onImport={(cardId) => handleImportOne(origIdx, cardId)}
                      />
                    );
                  })}
                </div>
              </div>

              {/* Botão para gerar mais flashcards só aparece no rodapé quando há cards visíveis ou quando todos já foram importados */}
              <div className="flex flex-col items-center gap-3 mt-6">
                {totalImportedCards > 0 && (
                  <div className="text-center text-gray-700">
                    {totalImportedCards} flashcards importados no total
                  </div>
                )}

                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-fit"
                >
                  Gerar Mais Flashcards
                </Button>
              </div>
            </div>
          ) : (
            <div className="py-8 flex flex-col items-center">
              {totalImportedCards > 0 ? (
                <div className="space-y-4 text-center">
                  <p className="text-gray-700 font-medium">
                    {totalImportedCards} flashcards prontos para estudo!
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setStep(1)}
                    className="w-fit"
                  >
                    Gerar Mais
                  </Button>
                </div>
              ) : (
                <>
                  <p className="text-gray-600">Nenhum flashcard foi gerado. Tente novamente.</p>
                  <Button
                    variant="outline"
                    onClick={() => setStep(1)}
                    className="mt-4"
                  >
                    Voltar
                  </Button>
                </>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};
