import { useUserData } from './useUserData';

/**
 * Hook para obter o ID do usuário atual de forma otimizada
 * Usa o cache do useUserData para evitar múltiplas chamadas
 */
export const useUserId = () => {
  const { user, isLoading } = useUserData();
  
  return {
    userId: user?.id || null,
    isLoading,
    ensureUserId: () => {
      if (!user?.id) {
        throw new Error("Usuário não autenticado");
      }
      return user.id;
    }
  };
};
