import { useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { SelectedFilters } from "@/types/flashcard";
import { selectedFiltersToJson } from "@/components/filters/types";

export const useFlashcardFilters = () => {
  const createSession = useCallback(async (
    user_id: string,
    filters: SelectedFilters,
    cards: string[]
  ) => {
    try {
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({ current_state: 'reviewing' })
        .in('id', cards);

      if (updateError) {
        throw updateError;
      }

      const { data: session, error: sessionError } = await supabase
        .from('flashcards_sessions')
        .insert({
          user_id,
          filters: selectedFiltersToJson(filters),
          status: 'in_progress',
          cards
        })
        .select()
        .single();

      if (sessionError) {
        throw sessionError;
      }

      return session;
    } catch (error: any) {
      toast.error("Error creating session");
      throw error;
    }
  }, []);

  const fetchFilteredCards = useCallback(async (
    user_id: string,
    filters: SelectedFilters
  ) => {
    try {
      // ✅ Otimizado: Query sem joins para melhor performance
      let query = supabase
        .from('flashcards_cards')
        .select('*')
        .eq('user_id', user_id)
        .eq('current_state', 'available');

      if (filters.specialties.length > 0) {
        query = query.in('specialty_id', filters.specialties);
      }

      if (filters.themes.length > 0) {
        query = query.in('theme_id', filters.themes);
      }

      if (filters.focuses.length > 0) {
        query = query.in('focus_id', filters.focuses);
      }

      const { data: cards, error } = await query;

      if (error) throw error;

      return cards || [];
    } catch (error) {
      throw error;
    }
  }, []);

  return {
    createSession,
    fetchFilteredCards
  };
};
