# 🔥 Sistema de Sequência de Estudos - Regras Completas

## 📋 O que conta como "Dia de Estudo"?

### ✅ **Atividades que CONTAM para sequência:**

#### 1. **Responder Questões** 📝
- **Mínimo**: 1 questão respondida
- **Quando conta**: Assim que a resposta é enviada
- **Localização**: Qualquer área da plataforma (simulados, questões avulsas, etc.)
- **Exemplo**: Responder uma única questão já garante o dia

#### 2. **Completar Sessões de Estudo** 🎯
- **Mínimo**: 1 sessão completada
- **Quando conta**: Quando a sessão é marcada como "completed"
- **Tipos**: Simulados, revisões, estudos dirigidos
- **Exemplo**: Finalizar um simulado de 10 questões

#### 3. **Marcar Estudos Manuais como Concluídos** ✅
- **Mínimo**: 1 item do cronograma marcado como "completed"
- **Quando conta**: Quando o usuário marca manualmente como estudado
- **Localização**: Cronograma de estudos
- **Exemplo**: Marcar "Cardiologia - Arritmias" como estudado

### ❌ **Atividades que NÃO CONTAM:**

- Apenas navegar pela plataforma
- Visualizar questões sem responder
- Criar cronogramas sem estudar
- Acessar materiais sem interação
- Sessões iniciadas mas não completadas

## 🕐 **Regras de Tempo e Timezone**

### **Definição de "Dia"**
- **Baseado no timezone do usuário** (não UTC)
- **Período**: 00:00:00 às 23:59:59 no fuso local
- **Detecção automática**: Sistema detecta timezone do navegador

### **Exemplos Práticos**
```
Usuário no Brasil (GMT-3):
- Estuda às 23:30 (horário local) = Conta para o dia atual
- Estuda às 00:30 (horário local) = Conta para o novo dia

Usuário em Portugal (GMT+1):
- Mesmo horário UTC, dias diferentes baseados no fuso local
```

## 📊 **Como Funciona a Sequência**

### **Sequência Atual**
- **Conta hoje**: Se estudou hoje OU ontem
- **Quebra**: Se não estudou nem hoje nem ontem
- **Continua**: Dias consecutivos de estudo

### **Sequência Máxima (Recorde)**
- **Histórico**: Maior sequência já alcançada
- **Permanente**: Nunca diminui, apenas aumenta
- **Cálculo**: Analisa todo o histórico do usuário

### **Exemplos de Cálculo**

#### Cenário 1: Sequência Ativa
```
Hoje: Estudou ✅ (1 questão)
Ontem: Estudou ✅ (1 sessão)
Anteontem: Estudou ✅ (cronograma)
→ Sequência atual: 3 dias
```

#### Cenário 2: Sequência Quebrada
```
Hoje: NÃO estudou ❌
Ontem: NÃO estudou ❌
Anteontem: Estudou ✅
→ Sequência atual: 0 dias
```

#### Cenário 3: Pode Recuperar
```
Hoje: NÃO estudou ainda ⏳
Ontem: Estudou ✅
Anteontem: Estudou ✅
→ Sequência atual: 2 dias (ainda pode estudar hoje)
```

## 🎯 **Estratégias para Manter a Sequência**

### **Mínimo Diário Recomendado**
1. **Responder 1 questão** (30 segundos)
2. **OU** completar 1 sessão rápida (5 minutos)
3. **OU** marcar 1 item do cronograma como estudado

### **Dicas para Não Quebrar**
- ⏰ **Defina um horário fixo** para estudar
- 📱 **Use notificações** para lembrar
- 🎯 **Comece pequeno**: 1 questão por dia
- 📈 **Aumente gradualmente** conforme o hábito

## 🏆 **Sistema de Recompensas e Gamificação**

### **Marcos de Sequência**
- 🔥 **7 dias**: Primeira semana
- 🌟 **30 dias**: Primeiro mês
- 💎 **100 dias**: Centurião
- 👑 **365 dias**: Mestre dos estudos

### **Benefícios por Sequência**
- **7+ dias**: Acesso a conteúdo premium
- **30+ dias**: Desconto em cursos
- **100+ dias**: Certificado de dedicação
- **365+ dias**: Status VIP permanente

## 🔧 **Aspectos Técnicos**

### **Fontes de Dados**
1. **`user_answers`**: Respostas de questões
2. **`study_sessions`**: Sessões completadas
3. **`study_schedule_items`**: Cronograma manual

### **Cálculo em Tempo Real**
- **Cache**: 5 minutos para performance
- **Atualização**: Automática após atividades
- **Fallback**: Sistema funciona mesmo com falhas

### **Precisão**
- **Timezone**: Considerado corretamente
- **Duplicatas**: Evitadas (múltiplas atividades no mesmo dia = 1 dia)
- **Histórico**: Análise completa desde o cadastro

## ❓ **FAQ - Perguntas Frequentes**

### **P: Quantas questões preciso responder por dia?**
R: Apenas 1 questão já conta como dia de estudo.

### **P: Se eu estudar às 23:59, ainda conta para hoje?**
R: Sim, conta baseado no seu fuso horário local.

### **P: Posso recuperar uma sequência quebrada?**
R: Não, mas você pode começar uma nova sequência imediatamente.

### **P: O que acontece se eu viajar para outro fuso horário?**
R: O sistema se adapta automaticamente ao seu novo fuso.

### **P: Sessões não completadas contam?**
R: Não, apenas sessões marcadas como "completed" contam.

### **P: Posso ter múltiplas atividades no mesmo dia?**
R: Sim, mas todas contam como apenas 1 dia de sequência.

## 🚨 **Casos Especiais**

### **Mudança de Fuso Horário**
- Sistema se adapta automaticamente
- Não afeta sequências existentes
- Cálculos futuros usam novo fuso

### **Problemas Técnicos**
- Sistema tem fallbacks automáticos
- Dados são preservados mesmo com falhas
- Suporte pode restaurar sequências perdidas por bugs

### **Manutenção da Plataforma**
- Sequências não são afetadas por manutenção
- Dados ficam seguros durante atualizações
- Tempo de inatividade não quebra sequências

## 📈 **Métricas e Analytics**

### **Para o Usuário**
- Sequência atual
- Recorde pessoal
- Dias estudados no mês
- Atividades por tipo

### **Para a Plataforma**
- Taxa de retenção por sequência
- Atividades mais efetivas
- Padrões de uso por horário
- Impacto das sequências no engajamento

---

## 🎯 **Resumo Executivo**

O sistema de sequências do MedEvo é projetado para ser:

- **Simples**: 1 atividade = 1 dia
- **Justo**: Considera fuso horário do usuário
- **Motivador**: Recompensas por consistência
- **Robusto**: Funciona mesmo com problemas técnicos
- **Escalável**: Preparado para milhares de usuários

**Objetivo**: Criar o hábito de estudo diário através de gamificação efetiva e regras claras.
