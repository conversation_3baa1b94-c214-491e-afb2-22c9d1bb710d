
import { supabase } from "@/integrations/supabase/client";
import type { RevisionInfo } from "./types";

/**
 * Calculates the next revision date and the week it falls in
 */
export const calculateRevisionDateAndWeek = async (
  currentDate: Date,
  daysToAdd: number,
  schedules: any[]
): Promise<RevisionInfo | null> => {
  const normalizeDate = (date: Date | string) => {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    return d;
  };

  const nextDate = normalizeDate(currentDate);
  nextDate.setDate(nextDate.getDate() + daysToAdd);

  const sortedSchedules = [...schedules].sort((a, b) => 
    new Date(a.week_start_date).getTime() - new Date(b.week_start_date).getTime()
  );
  
  console.log('📆 Looking for week for date:', {
    nextDate: nextDate.toISOString(),
    availableWeeks: sortedSchedules.map(s => ({
      start: s.week_start_date,
      end: s.week_end_date,
      weekNumber: s.week_number
    }))
  });

  let targetSchedule = sortedSchedules.find(s => {
    const weekStart = normalizeDate(s.week_start_date);
    const weekEnd = normalizeDate(s.week_end_date);
    weekEnd.setHours(23, 59, 59);
    
    return nextDate >= weekStart && nextDate <= weekEnd;
  });

  if (!targetSchedule) {
    console.log('⚠️ Revision date beyond available weeks, creating new weeks...');
    
    const lastWeek = sortedSchedules[sortedSchedules.length - 1];
    const lastWeekEndDate = normalizeDate(lastWeek.week_end_date);
    lastWeekEndDate.setHours(23, 59, 59);
    
    // If revision date is later than our latest available week, return null
    // The calling function will need to handle this case
    if (nextDate > lastWeekEndDate) {
      console.log('❌ Revision date is beyond our available weeks and no new weeks can be created in this context');
      return null;
    }
  }

  if (!targetSchedule) {
    console.error('❌ Could not find a week for the revision date');
    return null;
  }

  const dayOfWeekNames = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const dayOfWeek = dayOfWeekNames[nextDate.getDay()];

  return {
    date: nextDate,
    formattedDate: nextDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: '2-digit' }),
    weekNumber: targetSchedule.week_number,
    scheduleId: targetSchedule.id,
    dayOfWeek: dayOfWeek
  };
};

/**
 * Creates a revision item based on an original study item
 */
export const createRevisionItem = async (
  originalItem: any,
  revisionDate: Date,
  revisionNumber: number,
  parentId: string,
  revisionChain: string[],
  schedules: any[]
) => {
  try {
    console.log('📝 Creating revision:', {
      originalItem,
      revisionDate,
      revisionNumber,
      parentId,
      schedules
    });

    const revisionInfo = await calculateRevisionDateAndWeek(revisionDate, 0, schedules);
    if (!revisionInfo) {
      console.log('❌ Could not create revision - date outside available period');
      return null;
    }

    const newItem = {
      schedule_id: revisionInfo.scheduleId,
      day_of_week: revisionInfo.dayOfWeek,
      topic: originalItem.topic,
      duration: originalItem.duration,
      priority: originalItem.priority,
      type: 'revision',
      specialty_id: originalItem.specialty_id,
      theme_id: originalItem.theme_id,
      focus_id: originalItem.focus_id,
      difficulty: originalItem.difficulty,
      activity_type: originalItem.activity_type,
      start_time: originalItem.start_time,
      specialty_name: originalItem.specialty_name,
      theme_name: originalItem.theme_name,
      focus_name: originalItem.focus_name,
      activity_description: `Revisão ${revisionNumber} - ${originalItem.activity_description}`,
      week_number: revisionInfo.weekNumber,
      study_status: 'pending',
      revision_number: revisionNumber,
      parent_item_id: parentId,
      revision_chain: revisionChain,
      is_manual: originalItem.is_manual
    };

    const { error } = await supabase
      .from('study_schedule_items')
      .insert(newItem);

    if (error) throw error;

    console.log('✅ Revision created:', newItem);
    return newItem;
  } catch (error) {
    console.error('❌ Error creating revision:', error);
    throw error;
  }
};
