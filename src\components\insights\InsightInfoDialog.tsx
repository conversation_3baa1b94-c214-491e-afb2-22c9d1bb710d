import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Brain, Thermometer, TrendingUp, Clock, BookOpen } from 'lucide-react';

interface InsightInfoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const InsightInfoDialog: React.FC<InsightInfoDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const temperatureTypes = [
    {
      type: 'vulcanico',
      icon: '🌋',
      label: 'Vulcânico',
      color: 'text-red-700',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      description: 'Tópicos com alta frequência de aparição e que apareceram recentemente',
      characteristics: ['Muito frequente nas provas', 'Apareceu nos últimos anos', 'Alta prioridade de estudo']
    },
    {
      type: 'quente',
      icon: '🔥',
      label: 'Quente',
      color: 'text-orange-700',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      description: 'Tópicos frequentes que merecem atenção especial',
      characteristics: ['Frequente nas provas', 'Aparição regular', 'Prioridade alta']
    },
    {
      type: 'morno',
      icon: '♨️',
      label: 'Morno',
      color: 'text-yellow-700',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      description: 'Tópicos com frequência moderada, bons para revisão',
      characteristics: ['Frequência moderada', 'Aparição esporádica', 'Prioridade média']
    },
    {
      type: 'frio',
      icon: '🧊',
      label: 'Frio',
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      description: 'Tópicos que não aparecem há muito tempo, podem ser uma surpresa',
      characteristics: ['Baixa frequência recente', 'Não aparece há anos', 'Pode ser uma surpresa']
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Brain className="h-6 w-6 text-blue-600" />
            Insights Inteligentes
          </DialogTitle>
          <DialogDescription className="text-base">
            Entenda como nosso sistema analisa os padrões das provas para sugerir os melhores tópicos de estudo
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Como funciona */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Como funciona
            </h3>
            <p className="text-blue-800 text-sm leading-relaxed">
              Nosso algoritmo analisa milhares de questões das suas instituições preferidas, 
              calculando a frequência de cada tópico e quando foi a última vez que apareceu. 
              Com base nesses dados, sugerimos o tópico mais estratégico para estudar hoje.
            </p>
          </div>

          {/* Tipos de temperatura */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Thermometer className="h-4 w-4" />
              Tipos de Insights
            </h3>
            <div className="grid gap-3">
              {temperatureTypes.map((temp) => (
                <div
                  key={temp.type}
                  className={`${temp.bgColor} ${temp.borderColor} border rounded-lg p-4`}
                >
                  <div className="flex items-start gap-3">
                    <div className="text-2xl">{temp.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`font-semibold ${temp.color}`}>
                          {temp.label}
                        </span>
                      </div>
                      <p className={`text-sm ${temp.color} mb-3`}>
                        {temp.description}
                      </p>
                      <ul className={`text-xs ${temp.color} space-y-1`}>
                        {temp.characteristics.map((char, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-current rounded-full"></div>
                            {char}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dicas de uso */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
            <h3 className="font-semibold text-green-900 mb-2 flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Dicas de uso
            </h3>
            <ul className="text-green-800 text-sm space-y-2">
              <li className="flex items-start gap-2">
                <div className="w-1 h-1 bg-green-600 rounded-full mt-2"></div>
                <span>Priorize tópicos <strong>Vulcânicos</strong> e <strong>Quentes</strong> para maximizar seus resultados</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1 h-1 bg-green-600 rounded-full mt-2"></div>
                <span>Use tópicos <strong>Frios</strong> para se preparar para possíveis surpresas</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1 h-1 bg-green-600 rounded-full mt-2"></div>
                <span>O sistema atualiza diariamente com base nas suas preferências de instituições</span>
              </li>
            </ul>
          </div>

          {/* Personalização */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
            <h3 className="font-semibold text-purple-900 mb-2 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Personalização
            </h3>
            <p className="text-purple-800 text-sm leading-relaxed">
              Os insights são personalizados com base nas suas instituições preferidas e 
              excluem automaticamente tópicos que você já está estudando hoje, 
              garantindo uma experiência única e otimizada.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
