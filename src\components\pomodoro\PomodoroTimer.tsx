import { useEffect } from "react";
import { StudyTimer } from "../StudyTimer";
import { createAudioPlayer, AudioPlayer } from "@/utils/audioUtils";

interface PomodoroTimerProps {
  isRunning: boolean;
  isWorkTime: boolean;
  workTime: number;
  breakTime: number;
  onTimeUpdate: (elapsedSeconds: number) => void;
  audioPlayerRef: React.MutableRefObject<AudioPlayer | null>;
}

export const PomodoroTimer = ({ 
  isRunning, 
  isWorkTime, 
  workTime, 
  breakTime, 
  onTimeUpdate,
  audioPlayerRef 
}: PomodoroTimerProps) => {
  useEffect(() => {
    audioPlayerRef.current = createAudioPlayer("bell.mp3");
    return () => {
      audioPlayerRef.current?.destroy();
    };
  }, []);

  // Convert minutes to seconds for the timer
  const initialSeconds = (isWorkTime ? workTime : breakTime) * 60;

  return (
    <StudyTimer 
      isActive={isRunning} 
      initialSeconds={initialSeconds}
      onTimeUpdate={onTimeUpdate} 
    />
  );
};