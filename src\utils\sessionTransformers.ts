import type { RawStudySession, StudySession, SessionStats } from '@/types/study-session-types';

export const transformRawSession = (rawSession: RawStudySession): StudySession => {
  // Console log for debugging
  console.log('Transforming raw session:', rawSession.id);

  // Ensure stats has the correct shape before casting
  const rawStats = rawSession.stats as any;

  // Create a safe stats object with default values
  const stats: SessionStats = {
    total_correct: rawStats?.total_correct || rawSession.total_correct || 0,
    total_incorrect: rawStats?.total_incorrect || rawSession.total_incorrect || 0,
    avg_response_time: (rawStats?.avg_response_time || rawSession.avg_response_time || "0").toString(),
    by_specialty: rawStats?.by_specialty || {},
    by_theme: rawStats?.by_theme || {},
    by_focus: rawStats?.by_focus || {}
  };

  return {
    ...rawSession,
    stats
  };
};

/**
 * Improved streak calculation that considers multiple study activities
 */
export const calculateImprovedStreakStats = async (userId: string, supabase: any) => {
  try {
    // Get user's timezone (default to UTC if not available)
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';

    // Get all study activities from different sources
    const [sessionsResult, answersResult, scheduleResult] = await Promise.all([
      // Completed study sessions
      supabase
        .from('study_sessions')
        .select('completed_at, started_at')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .not('completed_at', 'is', null),

      // Individual question answers
      supabase
        .from('user_answers')
        .select('created_at')
        .eq('user_id', userId),

      // Manual study schedule items marked as completed (JOIN with study_schedules)
      supabase
        .from('study_schedule_items')
        .select('last_revision_date, study_schedules!inner(user_id)')
        .eq('study_schedules.user_id', userId)
        .eq('study_status', 'completed')
        .not('last_revision_date', 'is', null)
    ]);

    if (sessionsResult.error || answersResult.error || scheduleResult.error) {
      console.error('Error fetching study data:', {
        sessions: sessionsResult.error,
        answers: answersResult.error,
        schedule: scheduleResult.error
      });
      return { currentStreak: 0, maxStreak: 0 };
    }

    // Collect all study dates
    const studyDates = new Set<string>();

    // Add session completion dates
    sessionsResult.data?.forEach(session => {
      if (session.completed_at) {
        const date = new Date(session.completed_at);
        const localDate = formatDateToLocal(date, userTimezone);
        studyDates.add(localDate);
      }
    });

    // Add answer dates
    answersResult.data?.forEach(answer => {
      const date = new Date(answer.created_at);
      const localDate = formatDateToLocal(date, userTimezone);
      studyDates.add(localDate);
    });

    // Add manual study dates
    scheduleResult.data?.forEach(item => {
      if (item.last_revision_date) {
        const date = new Date(item.last_revision_date);
        const localDate = formatDateToLocal(date, userTimezone);
        studyDates.add(localDate);
      }
    });

    // Convert to sorted array (newest first)
    const sortedDates = Array.from(studyDates)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    if (sortedDates.length === 0) {
      return { currentStreak: 0, maxStreak: 0 };
    }

    // Calculate current streak
    const currentStreak = calculateCurrentStreak(sortedDates, userTimezone);

    // Calculate max streak
    const maxStreak = calculateMaxStreak(sortedDates);

    return { currentStreak, maxStreak };

  } catch (error) {
    console.error('Error calculating improved streak stats:', error);
    return { currentStreak: 0, maxStreak: 0 };
  }
};

/**
 * Format date to local timezone date string (YYYY-MM-DD)
 */
const formatDateToLocal = (date: Date, timezone: string): string => {
  try {
    return new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);
  } catch (error) {
    // Fallback to UTC if timezone is invalid
    return date.toISOString().split('T')[0];
  }
};

/**
 * Calculate current streak from sorted dates
 */
const calculateCurrentStreak = (sortedDates: string[], timezone: string): number => {
  const today = formatDateToLocal(new Date(), timezone);
  const yesterday = formatDateToLocal(new Date(Date.now() - 24 * 60 * 60 * 1000), timezone);

  let currentStreak = 0;

  // Check if user studied today or yesterday
  const latestDate = sortedDates[0];
  if (latestDate !== today && latestDate !== yesterday) {
    return 0; // Streak is broken
  }

  // Start counting from the latest date
  let expectedDate = latestDate;

  for (const date of sortedDates) {
    if (date === expectedDate) {
      currentStreak++;
      // Calculate previous day
      const prevDay = new Date(expectedDate);
      prevDay.setDate(prevDay.getDate() - 1);
      expectedDate = formatDateToLocal(prevDay, timezone);
    } else {
      break; // Streak is broken
    }
  }

  return currentStreak;
};

/**
 * Calculate maximum streak from sorted dates
 */
const calculateMaxStreak = (sortedDates: string[]): number => {
  if (sortedDates.length === 0) return 0;

  let maxStreak = 1;
  let currentStreak = 1;

  // Reverse to go chronologically
  const chronologicalDates = [...sortedDates].reverse();

  for (let i = 1; i < chronologicalDates.length; i++) {
    const currentDate = new Date(chronologicalDates[i]);
    const previousDate = new Date(chronologicalDates[i - 1]);

    // Check if dates are consecutive
    const dayDifference = Math.floor(
      (currentDate.getTime() - previousDate.getTime()) / (24 * 60 * 60 * 1000)
    );

    if (dayDifference === 1) {
      currentStreak++;
      maxStreak = Math.max(maxStreak, currentStreak);
    } else {
      currentStreak = 1;
    }
  }

  return maxStreak;
};

/**
 * Verifica se o usuário estudou hoje
 */
export const checkTodayStudyActivity = async (userId: string, supabase: any): Promise<boolean> => {
  try {
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
    const today = formatDateToLocal(new Date(), userTimezone);

    // Verificar atividades de hoje
    const [sessionsResult, answersResult, scheduleResult] = await Promise.all([
      // Sessões completadas hoje
      supabase
        .from('study_sessions')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .gte('completed_at', `${today}T00:00:00`)
        .lt('completed_at', `${today}T23:59:59`)
        .limit(1),

      // Respostas de questões hoje
      supabase
        .from('user_answers')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', `${today}T00:00:00`)
        .lt('created_at', `${today}T23:59:59`)
        .limit(1),

      // Estudos manuais marcados como completados hoje
      supabase
        .from('study_schedule_items')
        .select('id, study_schedules!inner(user_id)')
        .eq('study_schedules.user_id', userId)
        .eq('study_status', 'completed')
        .gte('last_revision_date', `${today}T00:00:00`)
        .lt('last_revision_date', `${today}T23:59:59`)
        .limit(1)
    ]);

    const hasActivity =
      (sessionsResult.data && sessionsResult.data.length > 0) ||
      (answersResult.data && answersResult.data.length > 0) ||
      (scheduleResult.data && scheduleResult.data.length > 0);

    console.log(`📊 [checkTodayStudyActivity] User ${userId} studied today: ${hasActivity}`, {
      sessions: sessionsResult.data?.length || 0,
      answers: answersResult.data?.length || 0,
      schedule: scheduleResult.data?.length || 0
    });

    return hasActivity;
  } catch (error) {
    console.error('❌ [checkTodayStudyActivity] Error checking today activity:', error);
    return false;
  }
};

/**
 * Legacy function - kept for backward compatibility
 * @deprecated Use calculateImprovedStreakStats instead
 */
export const calculateSessionStats = (sessions: any[]) => {
  console.warn('calculateSessionStats is deprecated. Use calculateImprovedStreakStats instead.');

  const today = new Date().setHours(0, 0, 0, 0);
  let currentStreak = 0;
  let maxStreak = 0;

  if (!sessions || sessions.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  // Get unique dates of completed sessions and sort them in descending order
  const uniqueDates = [...new Set(
    sessions
      .filter(session => session.status === 'completed')
      .map(session => {
        // Use completed_at if available, otherwise started_at
        const dateField = session.completed_at || session.started_at;
        const date = new Date(dateField);
        return date.setHours(0, 0, 0, 0);
      })
  )].sort((a, b) => b - a);

  if (uniqueDates.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  // Check if there's activity today or yesterday
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);

  const hasActivityToday = uniqueDates[0] === today;
  const hasActivityYesterday = uniqueDates[0] === yesterday.getTime();

  // Calculate current streak
  if (hasActivityToday || hasActivityYesterday) {
    currentStreak = 1;
    for (let i = 1; i < uniqueDates.length; i++) {
      const expectedPreviousDay = new Date(uniqueDates[i - 1]);
      expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
      expectedPreviousDay.setHours(0, 0, 0, 0);

      if (uniqueDates[i] === expectedPreviousDay.getTime()) {
        currentStreak++;
      } else {
        break;
      }
    }
  } else {
    currentStreak = 0;
  }

  // Calculate max streak historically
  let tempStreak = 1;
  for (let i = 1; i < uniqueDates.length; i++) {
    const currentDate = new Date(uniqueDates[i - 1]);
    const expectedPreviousDay = new Date(currentDate);
    expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
    expectedPreviousDay.setHours(0, 0, 0, 0);

    if (uniqueDates[i] === expectedPreviousDay.getTime()) {
      tempStreak++;
    } else {
      maxStreak = Math.max(maxStreak, tempStreak);
      tempStreak = 1;
    }
  }
  maxStreak = Math.max(maxStreak, tempStreak);

  return { currentStreak, maxStreak };
};

/**
 * Processes unique answers for progress visualization
 */
export const processUniqueAnswers = (answers: any[]) => {
  if (!answers || answers.length === 0) {
    return {
      bySpecialty: {},
      byTheme: {},
      byFocus: {},
      totalQuestions: 0,
      correctAnswers: 0,
      incorrectAnswers: 0
    };
  }

  // Use a Map to store only the latest answer for each question
  const uniqueQuestionsMap = new Map();

  // Sort answers by created_at in descending order (newest first)
  const sortedAnswers = [...answers].sort(
    (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  // Keep only the most recent answer for each question
  for (const answer of sortedAnswers) {
    if (!uniqueQuestionsMap.has(answer.question_id)) {
      uniqueQuestionsMap.set(answer.question_id, answer);
    }
  }

  // Convert to array of unique answers
  const uniqueAnswers = Array.from(uniqueQuestionsMap.values());

  // Calculate statistics
  const totalQuestions = uniqueAnswers.length;
  const correctAnswers = uniqueAnswers.filter(a => a.is_correct).length;
  const incorrectAnswers = totalQuestions - correctAnswers;

  // Group by categories
  const bySpecialty: Record<string, { name: string; correct: number; total: number }> = {};
  const byTheme: Record<string, { name: string; correct: number; total: number }> = {};
  const byFocus: Record<string, { name: string; correct: number; total: number }> = {};

  // Process each unique answer
  for (const answer of uniqueAnswers) {
    // Process specialty
    if (answer.specialty && answer.specialty_id) {
      const id = answer.specialty_id;
      if (!bySpecialty[id]) {
        bySpecialty[id] = {
          name: answer.specialty.name || 'Unknown',
          correct: 0,
          total: 0
        };
      }
      bySpecialty[id].total++;
      if (answer.is_correct) {
        bySpecialty[id].correct++;
      }
    }

    // Process theme
    if (answer.theme && answer.theme_id) {
      const id = answer.theme_id;
      if (!byTheme[id]) {
        byTheme[id] = {
          name: answer.theme.name || 'Unknown',
          correct: 0,
          total: 0
        };
      }
      byTheme[id].total++;
      if (answer.is_correct) {
        byTheme[id].correct++;
      }
    }

    // Process focus
    if (answer.focus && answer.focus_id) {
      const id = answer.focus_id;
      if (!byFocus[id]) {
        byFocus[id] = {
          name: answer.focus.name || 'Unknown',
          correct: 0,
          total: 0
        };
      }
      byFocus[id].total++;
      if (answer.is_correct) {
        byFocus[id].correct++;
      }
    }
  }

  return {
    bySpecialty,
    byTheme,
    byFocus,
    totalQuestions,
    correctAnswers,
    incorrectAnswers
  };
};
