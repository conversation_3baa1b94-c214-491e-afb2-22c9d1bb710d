import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface DangerZoneProps {
  loading: boolean;
  handleResetData: () => Promise<void>;
  handleResetQuestions: () => Promise<void>;
}

export const DangerZone = ({
  loading,
  handleResetData,
  handleResetQuestions
}: DangerZoneProps) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">Zona de Perigo</h3>
      <Card className="p-6 border-destructive">
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium">Resetar Dados</h4>
            <p className="text-sm text-muted-foreground">
              Apaga todos os seus dados de progresso. Esta ação não pode ser desfeita.
            </p>
            <Button
              variant="destructive"
              className="mt-2"
              onClick={handleResetData}
              disabled={loading}
            >
              {loading ? "Resetando..." : "Resetar Dados"}
            </Button>
          </div>
          <Separator />
          <div>
            <h4 className="text-sm font-medium">Resetar Questões</h4>
            <p className="text-sm text-muted-foreground">
              Apaga todas as suas respostas e progresso em questões. Esta ação não pode ser desfeita.
            </p>
            <Button
              variant="destructive"
              className="mt-2"
              onClick={handleResetQuestions}
              disabled={loading}
            >
              {loading ? "Resetando..." : "Resetar Questões"}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};