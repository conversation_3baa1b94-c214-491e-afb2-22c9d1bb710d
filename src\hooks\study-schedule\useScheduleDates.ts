import { supabase } from "@/integrations/supabase/client";

/**
 * Calculates the start date (Sunday) and end date (Saturday) of a week
 * @param date - A date within the week
 * @param useExactDate - If true, uses the exact date as the start date, but still ensures it's a Sunday
 */
export const getWeekDates = (date: Date, useExactDate: boolean = false) => {
  // Criar uma cópia da data para evitar modificar a original
  // Usar UTC para evitar problemas de fuso horário
  const inputDate = new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    12, 0, 0, 0
  ));

  const result = {
    startDate: new Date(inputDate),
    endDate: new Date(inputDate),
  };

  // Sempre verificar se a data de início é um domingo (dia 0)
  const dayOfWeek = result.startDate.getUTCDay();

  // Se não for domingo, ajustar para o domingo
  if (dayOfWeek !== 0) {
    // Sempre ajustar para o domingo (independente de useExactDate)
    result.startDate.setUTCDate(result.startDate.getUTCDate() - dayOfWeek);
  }

  // End date is always Saturday (startDate + 6 days)
  result.endDate = new Date(result.startDate);
  result.endDate.setUTCDate(result.startDate.getUTCDate() + 6);

  return result;
};

/**
 * Gets the dates for the current week (Sunday to Saturday)
 */
export const getCurrentWeekDates = () => {
  const now = new Date();
  return getWeekDates(now);
};

/**
 * Format a date as dd/mm/yyyy
 */
export const formatDate = (date: Date | string) => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const year = d.getFullYear();

  return `${day}/${month}/${year}`;
};

/**
 * Format a date as dd/mm
 */
export const formatShortDate = (date: Date | string) => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');

  return `${day}/${month}`;
};

/**
 * Finds a schedule that contains the provided date
 */
export const findScheduleForDate = async (date: Date) => {
  // Format date to yyyy-mm-dd for comparison with database
  const dateStr = date.toISOString().split('T')[0];

  try {
    const { data: schedule, error } = await supabase
      .from('study_schedules')
      .select('*')
      .lte('week_start_date', dateStr)
      .gte('week_end_date', dateStr)
      .maybeSingle();

    if (error) throw error;

    if (!schedule) {
      throw new Error('Schedule not found for the specified date');
    }

    return schedule;
  } catch (error) {
    throw error;
  }
};

/**
 * Ensures weeks exist up to a target date
 */
export const ensureWeeksExist = async (targetDate: Date, userId: string, addWeeks: (numberOfWeeks: number) => Promise<void>) => {

  try {
    const { data: existingSchedules, error } = await supabase
      .from('study_schedules')
      .select('week_number, week_end_date')
      .eq('user_id', userId)
      .order('week_number', { ascending: true });

    if (error) throw error;

    if (!existingSchedules?.length) {
      return;
    }

    const lastWeek = existingSchedules[existingSchedules.length - 1];

    const lastWeekEndDate = new Date(lastWeek.week_end_date);
    if (targetDate > lastWeekEndDate) {
      // Calculate how many weeks to add (rounded up)
      const weeksNeeded = Math.ceil(
        (targetDate.getTime() - lastWeekEndDate.getTime()) /
        (7 * 24 * 60 * 60 * 1000)
      );

      await addWeeks(weeksNeeded);
    }
  } catch (error) {
    throw error;
  }
};
