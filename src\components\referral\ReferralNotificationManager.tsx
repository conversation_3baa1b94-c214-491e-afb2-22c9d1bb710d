import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useReferralStatus } from '@/hooks/useReferralStatus';
import { ReferralWelcomeDialog } from './ReferralWelcomeDialog';

export const ReferralNotificationManager: React.FC = () => {
  const location = useLocation();
  const {
    isReferred,
    referrerName,
    referralCode,
    showNotification,
    isLoading,
    markNotificationAsShown
  } = useReferralStatus();

  const [dialogOpen, setDialogOpen] = useState(false);

  // Não mostrar o dialog no onboarding - será mostrado no welcome
  const shouldShowDialog = !location.pathname.includes('/onboarding');

  useEffect(() => {
    // Aguardar um pouco após o carregamento para mostrar o dialog
    if (showNotification && isReferred && referralCode && !isLoading && shouldShowDialog) {
      // Pequeno delay para garantir que a UI está pronta
      const timer = setTimeout(() => {
        setDialogOpen(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [showNotification, isReferred, referrerName, referralCode, isLoading, shouldShowDialog]);

  const handleDialogClose = (open: boolean) => {
    setDialogOpen(open);
    if (!open) {
      markNotificationAsShown();
    }
  };

  // Não mostrar se não está na página correta
  if (!shouldShowDialog || !isReferred || !referralCode) {
    return null;
  }

  return (
    <ReferralWelcomeDialog
      open={dialogOpen}
      onOpenChange={handleDialogClose}
      referrerName={referrerName || 'Um amigo'}
      referralCode={referralCode}
    />
  );
};
