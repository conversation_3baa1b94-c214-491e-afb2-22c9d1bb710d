import { Dialog, DialogContent } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Mail, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface NotificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "success" | "error" | "info";
  title: string;
  description: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

const NotificationDialog = ({
  open,
  onOpenChange,
  type,
  title,
  description,
  buttonText = "Continuar",
  onButtonClick
}: NotificationDialogProps) => {
  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-8 w-8 text-green-600" />;
      case "error":
        return <AlertCircle className="h-8 w-8 text-red-600" />;
      case "info":
        return <Mail className="h-8 w-8 text-blue-600" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case "success":
        return {
          bg: "bg-green-50",
          iconBg: "bg-green-100",
          button: "bg-green-600 hover:bg-green-700 text-white"
        };
      case "error":
        return {
          bg: "bg-red-50",
          iconBg: "bg-red-100",
          button: "bg-red-600 hover:bg-red-700 text-white"
        };
      case "info":
        return {
          bg: "bg-blue-50",
          iconBg: "bg-blue-100",
          button: "bg-blue-600 hover:bg-blue-700 text-white"
        };
    }
  };

  const colors = getColors();

  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[calc(100dvw-2rem)] max-w-lg border-2 border-black bg-white max-h-[80dvh] rounded-xl overflow-hidden p-0">
        {/* Header with colored background */}
        <div className={cn("w-full py-3", colors.bg)}>
          <div className="space-y-1.5 flex flex-col items-center text-center gap-2 px-6">
            <div className={cn("w-16 h-16 rounded-full flex items-center justify-center", colors.iconBg)}>
              {getIcon()}
            </div>
            <h2 className="tracking-tight text-xl font-bold mt-2 text-gray-900">
              {title}
            </h2>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 text-center text-base mb-6">
            {description}
          </p>
          
          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <Button
              onClick={handleButtonClick}
              className={cn(
                "w-full py-6 text-base font-medium rounded-lg border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all",
                colors.button
              )}
            >
              {buttonText}
            </Button>
          </div>
        </div>

        {/* Close button */}
        <button
          type="button"
          onClick={() => onOpenChange(false)}
          className="absolute right-3 top-3 p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 shadow-sm hover:shadow-md"
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Fechar</span>
        </button>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationDialog;
