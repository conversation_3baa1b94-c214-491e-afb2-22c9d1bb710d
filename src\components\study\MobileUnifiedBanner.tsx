import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Sparkles,
  Calendar,
  Clock,
  CheckCircle,
  Flame,
  Star,
  Trophy
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { useTodayStats } from '@/hooks/useTodayStats';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const MobileUnifiedBanner: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  
  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading: streakLoading
  } = useStreakSystem();

  const {
    questionsAnswered,
    timeStudied,
    isLoading: statsLoading
  } = useTodayStats();

  // Atualizar horário a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  // Nome do usuário
  const userName = user?.user_metadata?.name || 
    user?.user_metadata?.full_name || 
    user?.email?.split('@')[0] || 
    'Estudante';

  // Função para formatar tempo
  const formatTime = (minutes: number) => {
    if (minutes < 1) return '0min';
    return `${minutes}min`;
  };

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-blue-50 via-white to-orange-50 rounded-xl p-4 border-2 border-gray-200 shadow-lg"
    >
      {/* Header com Saudação */}
      <div className="flex items-center gap-3 mb-4">
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-2 rounded-full border-2 border-blue-300">
          <Sparkles className="h-4 w-4 text-white" />
        </div>
        <div className="flex-1">
          <h3 className="text-base font-bold text-gray-800">
            {getGreeting()}, {userName}! 👋
          </h3>
          <div className="flex items-center gap-1 text-xs text-gray-600">
            <Calendar className="h-3 w-3" />
            <span>{format(currentTime, 'EEEE, dd/MM', { locale: ptBR })}</span>
          </div>
        </div>
      </div>

      {/* Stats do Dia - Grid 1x2 */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className="bg-green-50 rounded-lg p-3 border border-green-200">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div>
              <div className="text-sm font-bold text-green-700">
                {statsLoading ? '...' : questionsAnswered}
              </div>
              <div className="text-xs text-green-600">questões hoje</div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-600" />
            <div>
              <div className="text-sm font-bold text-blue-700">
                {statsLoading ? '...' : formatTime(timeStudied)}
              </div>
              <div className="text-xs text-blue-600">estudado hoje</div>
            </div>
          </div>
        </div>
      </div>

      {/* Ofensiva Compacta */}
      <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-3 border border-orange-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-xs font-semibold text-orange-700">Ofensiva Semanal</span>
            {maxStreak > 0 && (
              <div className="flex items-center gap-1 bg-amber-50 px-1.5 py-0.5 rounded-full border border-amber-200">
                <Trophy className="h-2.5 w-2.5 text-amber-600" />
                <span className="text-xs font-bold text-amber-700">{maxStreak}</span>
              </div>
            )}
          </div>
        </div>

        {/* Mini Calendário */}
        <div className="flex items-center justify-between gap-1">
          {weekDays.map((day, index) => (
            <motion.div
              key={day.key}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.03 * index }}
              className="flex flex-col items-center"
            >
              <span className={`text-xs font-bold mb-0.5 ${
                day.isCurrentDay 
                  ? 'text-blue-600' 
                  : day.isActive 
                    ? 'text-orange-500' 
                    : 'text-gray-500'
              }`}>
                {day.shortName}
              </span>
              
              <div className={`
                w-4 h-4 rounded-full flex items-center justify-center transition-all duration-300
                ${day.isActive
                  ? 'bg-orange-500 text-white'
                  : day.isCurrentDay
                    ? 'bg-blue-200 text-blue-600'
                    : 'bg-gray-200 text-gray-400'
                }
              `}>
                {day.isActive ? (
                  <CheckCircle className="h-2 w-2" />
                ) : day.isCurrentDay ? (
                  <Star className="h-1.5 w-1.5" />
                ) : null}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default MobileUnifiedBanner;
