
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface FilterSearchProps {
  value: string;
  onChange: (value: string) => void;
}

export const FilterSearch = ({ value, onChange }: FilterSearchProps) => {
  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
      <Input
        placeholder="Buscar nas especialidades e temas..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-9 border-2 border-gray-200 focus:border-[#FF6B00] focus:ring-[#FF6B00] rounded-lg transition-colors"
      />
    </div>
  );
};
