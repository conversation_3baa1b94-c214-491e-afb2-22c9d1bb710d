
import { supabase } from "@/integrations/supabase/client";
import type { SelectedFilters } from "@/types/question";

export const useFilterQuery = () => {
  const buildQuery = (filters: SelectedFilters, domain?: string) => {
    // ✅ OTIMIZAÇÃO AGRESSIVA: Buscar apenas IDs para evitar dados desnecessários
    let query = supabase
      .from('questions')
      .select('id')
      .limit(1000);

    // Add domain filter if provided
    if (domain) {
      // Exact match on knowledge_domain column
      query = query.eq('knowledge_domain', domain);
    }

    const categoryConditions = [];

    if (filters.specialties?.length > 0) {
      const specialtyFilter = `specialty_id.in.(${filters.specialties.join(',')})`;
      categoryConditions.push(specialtyFilter);
    }

    if (filters.themes?.length > 0) {
      const themeFilter = `theme_id.in.(${filters.themes.join(',')})`;
      categoryConditions.push(themeFilter);
    }

    if (filters.focuses?.length > 0) {
      const focusFilter = `focus_id.in.(${filters.focuses.join(',')})`;
      categoryConditions.push(focusFilter);
    }

    if (categoryConditions.length > 0) {
      const orCondition = categoryConditions.join(',');
      query = query.or(orCondition);
    }

    if (filters.locations?.length > 0) {
      query = query.in('exam_location', filters.locations);
    }

    if (filters.years?.length > 0) {
      query = query.in('exam_year', filters.years.map(Number));
    }

    if (filters.question_types?.length > 0) {
      query = query.in('assessment_type', filters.question_types);
    }

    if (filters.question_formats?.length > 0) {
      query = query.in('question_format', filters.question_formats);
    }

    return query;
  };

  return { buildQuery };
};
