
import { Card, CardContent } from "@/components/ui/card";
import { Database, Shield, Users } from "lucide-react";
import { useAdminStats } from "@/hooks/useAdminStats";
import StatCard from "@/components/StatCard";

export const AdminStats = () => {
  const { userCount, questionCount, permissionCount, loading } = useAdminStats();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <StatCard 
        title="Usuários"
        value={loading ? "..." : userCount.toLocaleString()}
        icon={<Users className="h-8 w-8 text-primary" />}
      />
      
      <StatCard 
        title="Questões"
        value={loading ? "..." : questionCount.toLocaleString()}
        icon={<Database className="h-8 w-8 text-primary" />}
      />
      
      <StatCard 
        title="Permissões"
        value={loading ? "..." : permissionCount.toLocaleString()}
        icon={<Shield className="h-8 w-8 text-primary" />}
      />
    </div>
  );
};
