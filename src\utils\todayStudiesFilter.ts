import type { WeeklySchedule, StudyTopic } from '@/types/study-schedule';

/**
 * Utilitários para filtrar focos dos estudos do dia
 */

/**
 * Extrai os focus_ids dos tópicos dos estudos do dia
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Array de focus_ids dos estudos de hoje
 */
export const extractTodayFocusIds = (weeklySchedule: WeeklySchedule | null): string[] => {
  if (!weeklySchedule?.recommendations) {
    return [];
  }

  const today = new Date();
  const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const currentDay = weekDays[today.getDay()];

  console.log('🗓️ [extractTodayFocusIds] Dia atual:', currentDay);

  // Encontrar os estudos do dia atual
  const todayStudies = weeklySchedule.recommendations.find(day => 
    day.day.toLowerCase() === currentDay
  );

  if (!todayStudies?.topics) {
    console.log('📚 [extractTodayFocusIds] Nenhum estudo encontrado para hoje');
    return [];
  }

  // Extrair focus_ids dos tópicos
  const focusIds = todayStudies.topics
    .map(topic => topic.focusId)
    .filter((id): id is string => Boolean(id));

  console.log('🎯 [extractTodayFocusIds] Focus IDs dos estudos de hoje:', focusIds);

  return focusIds;
};

/**
 * Extrai informações detalhadas dos tópicos dos estudos do dia
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Array de tópicos dos estudos de hoje
 */
export const extractTodayTopics = (weeklySchedule: WeeklySchedule | null): StudyTopic[] => {
  if (!weeklySchedule?.recommendations) {
    return [];
  }

  const today = new Date();
  const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const currentDay = weekDays[today.getDay()];

  // Encontrar os estudos do dia atual
  const todayStudies = weeklySchedule.recommendations.find(day => 
    day.day.toLowerCase() === currentDay
  );

  return todayStudies?.topics || [];
};

/**
 * Verifica se um focus_id está nos estudos do dia
 * 
 * @param focusId - ID do foco a verificar
 * @param weeklySchedule - Cronograma semanal atual
 * @returns true se o foco está nos estudos de hoje
 */
export const isFocusInTodayStudies = (focusId: string, weeklySchedule: WeeklySchedule | null): boolean => {
  const todayFocusIds = extractTodayFocusIds(weeklySchedule);
  return todayFocusIds.includes(focusId);
};

/**
 * Conta quantos focos únicos estão nos estudos do dia
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Número de focos únicos nos estudos de hoje
 */
export const countTodayUniqueFoci = (weeklySchedule: WeeklySchedule | null): number => {
  const focusIds = extractTodayFocusIds(weeklySchedule);
  const uniqueFocusIds = [...new Set(focusIds)];
  return uniqueFocusIds.length;
};

/**
 * Obtém estatísticas dos estudos do dia para debug
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Objeto com estatísticas dos estudos de hoje
 */
export const getTodayStudiesStats = (weeklySchedule: WeeklySchedule | null) => {
  const topics = extractTodayTopics(weeklySchedule);
  const focusIds = extractTodayFocusIds(weeklySchedule);
  const uniqueFocusIds = [...new Set(focusIds)];

  return {
    totalTopics: topics.length,
    totalFocusIds: focusIds.length,
    uniqueFocusIds: uniqueFocusIds.length,
    focusIds: uniqueFocusIds,
    topics: topics.map(topic => ({
      id: topic.id,
      focus: topic.focus,
      focusId: topic.focusId,
      specialty: topic.specialty,
      theme: topic.theme
    }))
  };
};
