import { motion } from "framer-motion";
import { LogIn, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface FloatingActionButtonProps {
  isAuthenticated?: boolean;
  className?: string;
  onLoginClick?: () => void;
}

const FloatingActionButton = ({ isAuthenticated, className = "", onLoginClick }: FloatingActionButtonProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (isAuthenticated) {
      navigate("/plataformadeestudos");
    } else {
      onLoginClick?.();
    }
  };

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 0.5 
      }}
      className={`fixed bottom-6 right-6 z-40 md:hidden ${className}`}
    >
      <Button
        onClick={handleClick}
        className="w-14 h-14 rounded-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black shadow-card hover:shadow-card-light transition-all duration-200 hover:scale-105"
      >
        {isAuthenticated ? (
          <User className="h-6 w-6 text-black" />
        ) : (
          <LogIn className="h-6 w-6 text-black" />
        )}
      </Button>
      
      {/* Pulse animation for non-authenticated users */}
      {!isAuthenticated && (
        <motion.div
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ 
            duration: 2, 
            repeat: Infinity, 
            repeatType: "loop" 
          }}
          className="absolute inset-0 rounded-full bg-hackathon-yellow/30 border-2 border-hackathon-yellow -z-10"
        />
      )}
    </motion.div>
  );
};

export default FloatingActionButton;
