import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Timer, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { FlashcardSession } from "../types/FlashcardSession";

interface SessionCardProps {
  session: FlashcardSession;
  onDelete: (sessionId: string) => void;
  onClick: (sessionId: string) => void;
}

export const SessionCard = ({ session, onDelete, onClick }: SessionCardProps) => {
  const totalCards = session.cards?.length || 0;
  const reviewedCards = session.flashcards_session_cards?.filter(
    card => card.review_status === 'reviewed'
  ).length || 0;

  return (
    <Card 
      key={session.id}
      className="p-4 hover:shadow-lg transition-shadow cursor-pointer relative group"
      onClick={() => onClick(session.id)}
    >
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={(e) => {
          e.stopPropagation();
          onDelete(session.id);
        }}
      >
        <Trash2 className="h-4 w-4 text-destructive" />
      </Button>

      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Timer className="h-4 w-4" />
          <span>
            {formatDistanceToNow(new Date(session.start_time), {
              addSuffix: true,
              locale: ptBR,
            })}
          </span>
        </div>

        {session.filters?.specialty_name && (
          <p className="text-sm text-muted-foreground truncate">
            {session.filters.specialty_name}
          </p>
        )}

        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">
            {reviewedCards} de {totalCards}
          </span>
          <span className="font-medium">
            {session.status === 'completed' ? 'Concluído' : 'Em andamento'}
          </span>
        </div>

        <div className="w-full bg-secondary h-1 rounded-full overflow-hidden">
          <div 
            className="bg-primary h-full transition-all"
            style={{ 
              width: `${(reviewedCards / totalCards) * 100}%`
            }}
          />
        </div>
      </div>
    </Card>
  );
};