
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ircle, ArrowR<PERSON>, Eye } from "lucide-react";
import React from "react";

export function StatisticsSection() {
  const stats = [
    { 
      value: "5.000+", 
      label: "Questõ<PERSON>", 
      icon: BookOpen, 
      description: "Questões comentadas e atualizadas" 
    },
    { 
      value: "100%", 
      label: "Especializado", 
      icon: Eye, 
      description: "Focado exclusivamente em Oftalmologia" 
    },
    { 
      value: "100%", 
      label: "Gratuito", 
      icon: CheckCircle, 
      description: "Acesso a todo o conteúdo sem custos" 
    }
  ];

  const features = [
    "Questões Comentadas", 
    "Conteúdo Especializado", 
    "Estudo Organizado", 
    "Aprendizado Eficiente"
  ];

  return (
    <section className="py-16 md:py-24 bg-gradient-to-b from-blue-50 via-blue-100 to-blue-50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10" />
      <div className="absolute top-0 left-0 w-full h-16 bg-primary/5"></div>
      
      <div className="container mx-auto px-4 relative">
        <motion.div 
          className="flex justify-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium">
            Plataforma Especializada em Oftalmologia
          </div>
        </motion.div>
        
        <motion.h2 
          className="text-2xl md:text-3xl font-bold text-center mb-6 text-primary"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          A plataforma que vai transformar seus estudos em oftalmologia
        </motion.h2>
        
        <div className="max-w-3xl mx-auto">
          <motion.div 
            className="bg-gradient-to-r from-primary/10 to-primary/5 p-4 rounded-lg mb-10 border-l-4 border-primary"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <p className="text-center text-gray-700">
              <span className="font-semibold text-primary">Exclusivo para Oftalmologia</span> - 
              Conteúdo especializado para sua formação e atualização
            </p>
          </motion.div>
        </div>

        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Experimente nossa plataforma de questões comentadas no topo da página. 
            Veja como é fácil e intuitivo estudar com nosso sistema!
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl transition-shadow relative overflow-hidden group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
            >
              {/* Colorful corner accent */}
              <div className="absolute -top-6 -right-6 w-12 h-12 bg-primary/10 rotate-12 transform group-hover:scale-150 transition-transform duration-300"></div>
              
              <div className="inline-flex p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 mb-4 group-hover:scale-110 transform transition-transform">
                <stat.icon className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-3xl md:text-4xl font-bold text-primary mb-2">{stat.value}</h3>
              <p className="text-lg font-medium mb-1 text-gray-800">{stat.label}</p>
              <p className="text-sm text-gray-600">{stat.description}</p>
              
              {/* Interactive element */}
              <div className="mt-4 w-full h-1 bg-gray-100 rounded-full overflow-hidden">
                <motion.div 
                  className="h-full bg-primary/30"
                  initial={{ width: 0 }}
                  whileInView={{ width: '75%' }}
                  viewport={{ once: true }}
                  transition={{ duration: 1, delay: 0.5 + (index * 0.1) }}
                ></motion.div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-16 flex justify-center">
          <div className="max-w-3xl text-center">
            <motion.h3 
              className="text-xl font-bold mb-6 text-gray-800"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Comece hoje mesmo e prepare-se para sua carreira em oftalmologia
            </motion.h3>
            <motion.div 
              className="flex flex-wrap items-center justify-center gap-6 md:gap-5"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              {features.map((feature, i) => (
                <motion.div 
                  key={i} 
                  className="bg-white shadow-sm border border-gray-200 px-4 py-2 rounded-lg hover:shadow-md transition-shadow group cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center gap-2">
                    <ArrowRight className="w-4 h-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
                    <div className="text-base font-bold text-gray-700">{feature}</div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
