import { useEffect, useRef, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface NetworkRequest {
  url: string;
  method: string;
  timestamp: number;
  size?: number;
  duration?: number;
  queryKey?: string;
  status?: number;
}

interface NetworkMetrics {
  totalRequests: number;
  totalDataTransferred: number;
  averageRequestTime: number;
  duplicateRequests: number;
  recentRequests: NetworkRequest[];
}

/**
 * Hook para monitorar requisições de rede e identificar problemas de performance
 */
export const useNetworkMonitor = (componentName: string) => {
  const [metrics, setMetrics] = useState<NetworkMetrics>({
    totalRequests: 0,
    totalDataTransferred: 0,
    averageRequestTime: 0,
    duplicateRequests: 0,
    recentRequests: []
  });

  const requestsRef = useRef<NetworkRequest[]>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    // Interceptar fetch requests
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0] as string;
      const options = args[1] || {};
      


      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Estimar tamanho da resposta
        const contentLength = response.headers.get('content-length');
        const size = contentLength ? parseInt(contentLength) : 0;
        
        const request: NetworkRequest = {
          url: url.includes('supabase') ? url.split('/').pop() || url : url,
          method: options.method || 'GET',
          timestamp: startTime,
          duration,
          size,
          status: response.status
        };

        requestsRef.current.push(request);
        


        // Detectar requisições duplicadas
        const recentSimilar = requestsRef.current.filter(r => 
          r.url === request.url && 
          Math.abs(r.timestamp - request.timestamp) < 5000 // 5 segundos
        );

        if (recentSimilar.length > 1) {
          // Duplicate request detection disabled for production
        }

        // Detectar requisições lentas
        if (duration > 2000) {
          // Slow request detection disabled for production
        }

        // Detectar respostas grandes
        if (size > 100 * 1024) { // 100KB
          // Large response detection disabled for production
        }

        updateMetrics();
        return response;
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Network error logging disabled for production
        
        throw error;
      }
    };

    // Monitorar React Query cache
    const monitorReactQuery = () => {
      const queryCache = queryClient.getQueryCache();
      const queries = queryCache.getAll();
      


      // Detectar queries que podem estar sendo executadas desnecessariamente
      const recentlyFetched = queries.filter(q => {
        const lastFetch = q.state.dataUpdatedAt;
        return lastFetch && Date.now() - lastFetch < 10000; // 10 segundos
      });


    };

    // Monitorar a cada 30 segundos
    const interval = setInterval(monitorReactQuery, 30000);

    // Cleanup
    return () => {
      window.fetch = originalFetch;
      clearInterval(interval);
    };
  }, [componentName, queryClient]);

  const updateMetrics = () => {
    const requests = requestsRef.current;
    const recentRequests = requests.slice(-10); // Últimas 10 requisições
    
    const totalRequests = requests.length;
    const totalDataTransferred = requests.reduce((sum, r) => sum + (r.size || 0), 0);
    const averageRequestTime = requests.length > 0 
      ? requests.reduce((sum, r) => sum + (r.duration || 0), 0) / requests.length 
      : 0;

    // Detectar duplicatas
    const urlCounts = {};
    requests.forEach(r => {
      urlCounts[r.url] = (urlCounts[r.url] || 0) + 1;
    });
    const duplicateRequests = Object.values(urlCounts).filter(count => count > 1).length;

    setMetrics({
      totalRequests,
      totalDataTransferred,
      averageRequestTime,
      duplicateRequests,
      recentRequests
    });
  };

  const logNetworkSummary = () => {
    // Network summary logging disabled for production
  };

  const clearMetrics = () => {
    requestsRef.current = [];
    setMetrics({
      totalRequests: 0,
      totalDataTransferred: 0,
      averageRequestTime: 0,
      duplicateRequests: 0,
      recentRequests: []
    });
  };

  return {
    metrics,
    logNetworkSummary,
    clearMetrics
  };
};
