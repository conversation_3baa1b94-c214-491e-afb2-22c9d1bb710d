import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useStaticStudyCategories } from '@/hooks/useStaticDataCache';

export interface TemporalInsight {
  category: string;
  categoryId: string;
  categoryType: 'specialty' | 'theme' | 'focus';
  trend: 'improving' | 'declining' | 'stable';
  weeklyChange: number;
  monthlyChange: number;
  bestPeriod: string;
  currentAccuracy: number;
  previousAccuracy: number;
  totalQuestions: number;
}

export interface HourlyPerformance {
  hour: number;
  accuracy: number;
  totalQuestions: number;
  avgTime: number;
}

export interface DailyEvolution {
  date: string;
  accuracy: number;
  totalQuestions: number;
  correctAnswers: number;
}

export interface TemporalData {
  insights: TemporalInsight[];
  hourlyPerformance: HourlyPerformance[];
  dailyEvolution: DailyEvolution[];
  bestStudyTime: string;
  worstStudyTime: string;
  improvingCategories: TemporalInsight[];
  decliningCategories: TemporalInsight[];
}

export const useTemporalInsights = () => {
  const { user } = useAuth();
  const { data: staticCategories } = useStaticStudyCategories();

  return useQuery({
    queryKey: ['temporal-insights', user?.id],
    queryFn: async (): Promise<TemporalData> => {
      if (!user?.id) throw new Error('User not authenticated');
      if (!staticCategories) throw new Error("Static categories not loaded");



      // Criar mapeamento de categorias
      const allCategories = [
        ...staticCategories.specialties.map(s => ({ ...s, type: 'specialty' as const })),
        ...staticCategories.themes.map(t => ({ ...t, type: 'theme' as const })),
        ...staticCategories.focuses.map(f => ({ ...f, type: 'focus' as const }))
      ];

      const categoryMap = allCategories.reduce((acc, cat) => {
        acc[cat.id] = cat;
        return acc;
      }, {} as Record<string, any>);

      // Buscar respostas dos últimos 60 dias para análise temporal
      const sixtyDaysAgo = new Date();
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

      const { data: userAnswers, error } = await supabase
        .from('user_answers')
        .select('*')
        .eq('user_id', user.id)
        .gte('created_at', sixtyDaysAgo.toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;



      // Análise por horário
      const hourlyStats: Record<number, { correct: number; total: number; totalTime: number }> = {};

      // Análise diária
      const dailyStats: Record<string, { correct: number; total: number }> = {};

      // Análise por categoria e período
      const categoryWeeklyStats: Record<string, {
        thisWeek: { correct: number; total: number };
        lastWeek: { correct: number; total: number };
        thisMonth: { correct: number; total: number };
        lastMonth: { correct: number; total: number };
      }> = {};

      // Processar cada resposta
      userAnswers?.forEach(answer => {
        const date = new Date(answer.created_at);
        const hour = date.getHours();
        const dayKey = date.toISOString().split('T')[0];

        // Estatísticas por horário
        if (!hourlyStats[hour]) {
          hourlyStats[hour] = { correct: 0, total: 0, totalTime: 0 };
        }
        hourlyStats[hour].total++;
        hourlyStats[hour].totalTime += answer.time_spent || 0;
        if (answer.is_correct) hourlyStats[hour].correct++;

        // Estatísticas diárias
        if (!dailyStats[dayKey]) {
          dailyStats[dayKey] = { correct: 0, total: 0 };
        }
        dailyStats[dayKey].total++;
        if (answer.is_correct) dailyStats[dayKey].correct++;

        // Estatísticas por categoria e período
        const categories = [
          { id: answer.specialty_id, type: 'specialty' },
          { id: answer.theme_id, type: 'theme' },
          { id: answer.focus_id, type: 'focus' }
        ].filter(cat => cat.id);

        categories.forEach(({ id, type }) => {
          if (!categoryWeeklyStats[id]) {
            categoryWeeklyStats[id] = {
              thisWeek: { correct: 0, total: 0 },
              lastWeek: { correct: 0, total: 0 },
              thisMonth: { correct: 0, total: 0 },
              lastMonth: { correct: 0, total: 0 }
            };
          }

          const now = new Date();
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

          if (date >= weekAgo) {
            categoryWeeklyStats[id].thisWeek.total++;
            if (answer.is_correct) categoryWeeklyStats[id].thisWeek.correct++;
          } else if (date >= twoWeeksAgo) {
            categoryWeeklyStats[id].lastWeek.total++;
            if (answer.is_correct) categoryWeeklyStats[id].lastWeek.correct++;
          }

          if (date >= monthAgo) {
            categoryWeeklyStats[id].thisMonth.total++;
            if (answer.is_correct) categoryWeeklyStats[id].thisMonth.correct++;
          } else if (date >= twoMonthsAgo) {
            categoryWeeklyStats[id].lastMonth.total++;
            if (answer.is_correct) categoryWeeklyStats[id].lastMonth.correct++;
          }
        });
      });

      // Processar insights temporais por categoria
      const insights: TemporalInsight[] = Object.entries(categoryWeeklyStats)
        .map(([categoryId, stats]) => {
          const category = categoryMap[categoryId];
          if (!category) return null;

          const thisWeekAcc = stats.thisWeek.total > 0
            ? (stats.thisWeek.correct / stats.thisWeek.total) * 100
            : 0;
          const lastWeekAcc = stats.lastWeek.total > 0
            ? (stats.lastWeek.correct / stats.lastWeek.total) * 100
            : 0;
          const thisMonthAcc = stats.thisMonth.total > 0
            ? (stats.thisMonth.correct / stats.thisMonth.total) * 100
            : 0;
          const lastMonthAcc = stats.lastMonth.total > 0
            ? (stats.lastMonth.correct / stats.lastMonth.total) * 100
            : 0;

          const weeklyChange = thisWeekAcc - lastWeekAcc;
          const monthlyChange = thisMonthAcc - lastMonthAcc;

          let trend: 'improving' | 'declining' | 'stable' = 'stable';
          if (weeklyChange > 5) trend = 'improving';
          else if (weeklyChange < -5) trend = 'declining';

          return {
            category: category.name,
            categoryId,
            categoryType: category.type,
            trend,
            weeklyChange,
            monthlyChange,
            bestPeriod: 'manhã', // Será calculado depois
            currentAccuracy: thisWeekAcc,
            previousAccuracy: lastWeekAcc,
            totalQuestions: stats.thisWeek.total + stats.lastWeek.total
          };
        })
        .filter(Boolean) as TemporalInsight[];

      // Processar performance por horário
      const hourlyPerformance: HourlyPerformance[] = Object.entries(hourlyStats)
        .map(([hour, stats]) => ({
          hour: parseInt(hour),
          accuracy: stats.total > 0 ? (stats.correct / stats.total) * 100 : 0,
          totalQuestions: stats.total,
          avgTime: stats.total > 0 ? stats.totalTime / stats.total : 0
        }))
        .sort((a, b) => a.hour - b.hour);

      // Processar evolução diária
      const dailyEvolution: DailyEvolution[] = Object.entries(dailyStats)
        .map(([date, stats]) => ({
          date,
          accuracy: stats.total > 0 ? (stats.correct / stats.total) * 100 : 0,
          totalQuestions: stats.total,
          correctAnswers: stats.correct
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      // Encontrar melhor e pior horário
      const bestHour = hourlyPerformance.reduce((best, current) =>
        current.accuracy > best.accuracy ? current : best, hourlyPerformance[0]);
      const worstHour = hourlyPerformance.reduce((worst, current) =>
        current.accuracy < worst.accuracy ? current : worst, hourlyPerformance[0]);

      const bestStudyTime = bestHour ? `${bestHour.hour}h-${bestHour.hour + 1}h` : 'N/A';
      const worstStudyTime = worstHour ? `${worstHour.hour}h-${worstHour.hour + 1}h` : 'N/A';

      // Categorizar insights
      const improvingCategories = insights.filter(i => i.trend === 'improving');
      const decliningCategories = insights.filter(i => i.trend === 'declining');

      const result: TemporalData = {
        insights,
        hourlyPerformance,
        dailyEvolution,
        bestStudyTime,
        worstStudyTime,
        improvingCategories,
        decliningCategories
      };



      return result;
    },
    enabled: !!user?.id && !!staticCategories,
    retry: 1,
    staleTime: 0,
    cacheTime: 10 * 60 * 1000,
  });
};
