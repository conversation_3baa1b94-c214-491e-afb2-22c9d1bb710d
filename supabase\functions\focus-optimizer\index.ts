import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const openAiKey = Deno.env.get("OPENAI_API_KEY");

interface Focus {
  id: string;
  name: string;
  questionCount: number;
  createdAt: string;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { specialty, theme, allFocuses } = await req.json();

    console.log(`🎯 Analyzing all focuses in ${specialty} > ${theme}`);
    console.log(`📊 Total focuses to analyze: ${allFocuses?.length || 0}`);

    if (!specialty || !theme || !allFocuses || allFocuses.length === 0) {
      console.error("❌ Missing required parameters");
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!openAiKey) {
      return new Response(
        JSON.stringify({ error: "OpenAI API key not configured" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Prepare focuses list for AI analysis
    const focusesFormatted = allFocuses.map((f: any) =>
      `- ID: ${f.id}, Nome: "${f.name}", Questões: ${f.questionCount}`
    ).join('\n');

    console.log(`📏 Analyzing ${allFocuses.length} focuses`);

    // Make request to OpenAI API
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${openAiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `Você é um especialista em organização de conteúdo médico. Sua tarefa é analisar TODOS os focos de um tema e identificar:

1. **DUPLICATAS**: Focos com nomes idênticos ou muito similares que devem ser mesclados
2. **RENOMEAÇÕES**: Focos com nomes fora do padrão que precisam ser padronizados

Critérios de análise:
- Similaridade semântica exata entre focos
- Padrões de nomenclatura médica consistentes
- Clareza e precisão dos nomes
- Consistência terminológica no tema

Responda APENAS com um objeto JSON no formato especificado.`
          },
          {
            role: "user",
            content: `Analise todos os focos deste tema e identifique problemas:

ESPECIALIDADE: ${specialty}
TEMA: ${theme}

TODOS OS FOCOS DO TEMA:
${focusesFormatted}

Responda APENAS com um objeto JSON neste formato:
{
  "suggestions": [
    {
      "type": "duplicate|rename",
      "currentFocus": {
        "id": "id-do-foco",
        "name": "nome-do-foco",
        "questionCount": 10
      },
      "suggestion": "Descrição clara da sugestão",
      "reasoning": "Explicação do motivo (máximo 150 caracteres)",
      "confidence": 85,
      "duplicateOf": {
        "id": "id-do-foco-duplicado",
        "name": "nome-do-foco-duplicado",
        "questionCount": 20
      },
      "suggestedName": "novo-nome-sugerido-se-for-rename"
    }
  ]
}

IMPORTANTE:
- Para duplicatas: confidence > 90, inclua "duplicateOf" com o foco que tem MAIS questões
- Para renomeações: confidence > 75, inclua "suggestedName"
- Máximo 20 sugestões
- Priorize duplicatas óbvias e nomes claramente fora do padrão
- Seja conservador e preciso`
          }
        ],
        temperature: 0.3,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ OpenAI API error:", response.status, errorText);
      return new Response(
        JSON.stringify({ 
          error: "Failed to analyze focus optimization",
          details: `OpenAI API error: ${response.status}`
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return new Response(
        JSON.stringify({ error: "Invalid response from AI" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    let result: any;
    try {
      result = JSON.parse(data.choices[0].message.content);
    } catch (parseError) {
      console.error("Failed to parse AI response:", parseError);
      return new Response(
        JSON.stringify({ error: "Failed to parse AI response" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate the result structure
    if (!result.suggestions || !Array.isArray(result.suggestions)) {
      return new Response(
        JSON.stringify({ error: "Invalid AI response format - missing suggestions array" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate and sanitize suggestions
    const validSuggestions = result.suggestions.filter((suggestion: any) => {
      return suggestion.type &&
             suggestion.currentFocus &&
             suggestion.suggestion &&
             suggestion.reasoning &&
             typeof suggestion.confidence === 'number' &&
             suggestion.confidence >= 0 &&
             suggestion.confidence <= 100;
    });

    console.log(`✅ Analysis completed: ${validSuggestions.length} valid suggestions found`);
    console.log(`📊 Duplicates: ${validSuggestions.filter((s: any) => s.type === 'duplicate').length}`);
    console.log(`📊 Renames: ${validSuggestions.filter((s: any) => s.type === 'rename').length}`);

    return new Response(
      JSON.stringify({ suggestions: validSuggestions }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error) {
    console.error("Error in focus-optimizer:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
