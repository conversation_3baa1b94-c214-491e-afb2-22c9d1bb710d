
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, X, BookOpen, MessageSquare, ThumbsUp, ArrowRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';

const sampleQuestion = {
  question_content: "Um paciente de 5 anos apresenta estrabismo convergente intermitente no olho esquerdo durante consulta oftalmológica. Qual dos seguintes é o tratamento inicial mais adequado?",
  statement: "Um paciente de 5 anos apresenta estrabismo convergente intermitente no olho esquerdo durante consulta oftalmológica. Qual dos seguintes é o tratamento inicial mais adequado?",
  response_choices: [
    "Cirurgia imediata para correção do estrabismo",
    "Prescrição de óculos para correção de erro refrativo",
    "Terapia com atropina no olho direito",
    "Apenas observação por 6 meses",
    "Uso de prismas para correção visual"
  ],
  alternatives: [
    "Cirurgia imediata para correção do estrabismo",
    "Prescrição de óculos para correção de erro refrativo",
    "Terapia com atropina no olho direito",
    "Apenas observação por 6 meses",
    "Uso de prismas para correção visual"
  ],
  correctAnswer: 1,
  explanation: "O tratamento inicial do estrabismo convergente intermitente em crianças frequentemente envolve a correção de erros refrativos com óculos. A hipermetropia não corrigida é uma causa comum de estrabismo convergente, e a prescrição de óculos adequados pode resolver o problema em muitos casos, especialmente quando diagnosticado precocemente."
};

export const QuestionPreview = () => {
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [step, setStep] = useState(1);

  const handleSelectAnswer = (index: number) => {
    if (!hasAnswered) {
      setSelectedAnswer(index);
    }
  };

  const handleConfirm = () => {
    setHasAnswered(true);
    setStep(2);
  };

  const handleShowExplanation = () => {
    setShowExplanation(true);
    setStep(3);
  };

  return (
    <div className="max-w-3xl mx-auto mt-12 mb-8">
      <motion.h2
        className="text-2xl md:text-3xl font-bold text-center text-white mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        Veja como é simples responder questões na plataforma
      </motion.h2>

      <motion.div
        className="bg-white rounded-xl shadow-xl overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {/* Question header */}
        <div className="bg-primary/10 p-4 border-b border-primary/20">
          <div className="flex items-center gap-2 text-primary font-medium">
            <Eye className="h-5 w-5" />
            <span>Oftalmologia</span>
            <span className="mx-2">•</span>
            <span>Estrabismo</span>
          </div>
        </div>

        {/* Question content */}
        <div className="p-6">
          <p className="text-lg font-medium text-gray-800 mb-6">
            {sampleQuestion.statement}
          </p>

          <div className="space-y-3 mb-6">
            {sampleQuestion.alternatives.map((alternative, index) => (
              <div
                key={index}
                onClick={() => handleSelectAnswer(index)}
                className={`p-4 rounded-lg flex items-start gap-3 cursor-pointer transition-colors
                  ${selectedAnswer === index ? 'bg-primary/10 border-primary/30' : 'bg-gray-50 hover:bg-gray-100 border-transparent'}
                  ${hasAnswered && index === sampleQuestion.correctAnswer ? 'bg-green-50 border-green-300' : ''}
                  ${hasAnswered && selectedAnswer === index && index !== sampleQuestion.correctAnswer ? 'bg-red-50 border-red-300' : ''}
                  border
                `}
              >
                <div className={`w-6 h-6 rounded-full flex-shrink-0 flex items-center justify-center text-sm mt-0.5
                  ${hasAnswered && index === sampleQuestion.correctAnswer ? 'bg-green-500 text-white' : ''}
                  ${hasAnswered && selectedAnswer === index && index !== sampleQuestion.correctAnswer ? 'bg-red-500 text-white' : ''}
                  ${selectedAnswer === index && !hasAnswered ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700'}
                `}>
                  {hasAnswered && index === sampleQuestion.correctAnswer ? (
                    <Check className="h-4 w-4" />
                  ) : hasAnswered && selectedAnswer === index && index !== sampleQuestion.correctAnswer ? (
                    <X className="h-4 w-4" />
                  ) : (
                    String.fromCharCode(65 + index)
                  )}
                </div>
                <span className={`${hasAnswered && index === sampleQuestion.correctAnswer ? 'text-green-800 font-medium' : ''}`}>
                  {alternative}
                </span>
              </div>
            ))}
          </div>

          {!hasAnswered && (
            <div className="flex justify-end">
              <Button
                onClick={handleConfirm}
                disabled={selectedAnswer === null}
                className="bg-primary hover:bg-primary/90"
              >
                Confirmar Resposta
              </Button>
            </div>
          )}

          {hasAnswered && !showExplanation && (
            <div className="flex justify-end">
              <Button
                onClick={handleShowExplanation}
                className="bg-primary hover:bg-primary/90"
              >
                Ver Explicação
              </Button>
            </div>
          )}

          {showExplanation && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200"
            >
              <h3 className="font-bold text-blue-800 mb-2 flex items-center">
                <MessageSquare className="mr-2 h-5 w-5" />
                Comentário do Especialista
              </h3>
              <p className="text-blue-800">
                {sampleQuestion.explanation}
              </p>

              <div className="mt-4 flex items-center justify-between border-t border-blue-200 pt-3">
                <div className="flex items-center gap-1 text-blue-600 text-sm">
                  <ThumbsUp className="h-4 w-4" />
                  <span>23 pessoas acharam útil</span>
                </div>
                <Button variant="outline" size="sm" className="text-primary border-primary hover:bg-primary/5">
                  Próxima Questão
                </Button>
              </div>
            </motion.div>
          )}
        </div>

        {/* Step indicator */}
        <div className="bg-gray-50 p-4 border-t border-gray-100 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-1.5 ${step >= 1 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm ${step >= 1 ? 'bg-primary text-white' : 'bg-gray-200'}`}>1</div>
              <span className="font-medium">Responder</span>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-300" />
            <div className={`flex items-center gap-1.5 ${step >= 2 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm ${step >= 2 ? 'bg-primary text-white' : 'bg-gray-200'}`}>2</div>
              <span className="font-medium">Feedback</span>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-300" />
            <div className={`flex items-center gap-1.5 ${step >= 3 ? 'text-primary' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm ${step >= 3 ? 'bg-primary text-white' : 'bg-gray-200'}`}>3</div>
              <span className="font-medium">Aprender</span>
            </div>
          </div>

          <div className="text-sm text-gray-500">
            +5.000 questões de oftalmologia comentadas
          </div>
        </div>
      </motion.div>
    </div>
  );
};
