import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, Target, CheckCircle, XCircle, SkipForward, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Category {
  id: string;
  name: string;
}

interface OrphanQuestion {
  id: string;
  question_content: string;
  response_choices: string[];
  correct_choice: string;
  exam_year: number;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
}

interface CategorizationSuggestion {
  questionId: string;
  questionText: string;
  suggestedTheme?: {
    id: string;
    name: string;
    confidence: number;
  };
  suggestedFocus?: {
    id: string;
    name: string;
    confidence: number;
  };
  reasoning: string;
}

interface ProcessedQuestion {
  question: OrphanQuestion;
  suggestion: CategorizationSuggestion;
  status: 'pending' | 'approved' | 'applied' | 'skipped';
}

// Componente para exibir resultados das questões
const QuestionResultsCard = ({
  processedQuestions,
  analysisType,
  onApprove,
  onSkip,
  onApplyAll,
  onApproveAll,
  onUnapproveAll
}: {
  processedQuestions: ProcessedQuestion[];
  analysisType: 'theme' | 'focus';
  onApprove: (questionId: string) => void;
  onSkip: (questionId: string) => void;
  onApplyAll: () => void;
  onApproveAll: () => void;
  onUnapproveAll: () => void;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Sugestões de Categorização</CardTitle>
        <CardDescription>
          Sugestões para categorizar questões órfãs por {analysisType === 'theme' ? 'tema' : 'foco'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Controles de Lote */}
        <div className="flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg border">
          <Button
            onClick={onApproveAll}
            disabled={processedQuestions.filter(p => p.status === 'pending' && (analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus)).length === 0}
            variant="secondary"
            className="flex-1 md:flex-none"
          >
            <Target className="h-4 w-4 mr-2" />
            Aprovar Todos ({processedQuestions.filter(p => p.status === 'pending' && (analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus)).length})
          </Button>

          <Button
            onClick={onApplyAll}
            disabled={processedQuestions.filter(p => p.status === 'approved').length === 0}
            className="flex-1 md:flex-none"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Aplicar Aprovadas ({processedQuestions.filter(p => p.status === 'approved').length})
          </Button>

          <Button
            onClick={onUnapproveAll}
            variant="outline"
            disabled={processedQuestions.filter(p => p.status === 'approved').length === 0}
            className="flex-1 md:flex-none"
          >
            <XCircle className="h-4 w-4 mr-2" />
            Cancelar Todas Aprovações
          </Button>

          <div className="text-sm text-muted-foreground flex items-center">
            💡 Aprove individualmente ou em lote, depois clique "Aplicar Aprovadas"
          </div>
        </div>

        {/* Lista de Questões */}
        <div className="space-y-4">
          {processedQuestions.filter(pq => pq && pq.question && pq.question.id).map((processedQuestion) => (
            <QuestionCard
              key={processedQuestion.question.id}
              processedQuestion={processedQuestion}
              analysisType={analysisType}
              onApprove={onApprove}
              onSkip={onSkip}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Componente para cada questão individual
const QuestionCard = ({
  processedQuestion,
  analysisType,
  onApprove,
  onSkip
}: {
  processedQuestion: ProcessedQuestion;
  analysisType: 'theme' | 'focus';
  onApprove: (questionId: string) => void;
  onSkip: (questionId: string) => void;
}) => {
  const { question, suggestion, status } = processedQuestion;

  if (status === 'applied') {
    return (
      <div className="p-4 border border-green-500 bg-green-50 rounded-lg">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <span className="font-medium text-green-800">
            Questão {question.exam_year} - ✅ Aplicada
          </span>
        </div>
      </div>
    );
  }

  if (status === 'skipped') {
    return (
      <div className="p-4 border border-gray-500 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <SkipForward className="h-5 w-5 text-gray-600" />
          <span className="font-medium text-gray-800">
            Questão {question.exam_year} - ⏭️ Pulada
          </span>
        </div>
      </div>
    );
  }

  if (status === 'approved') {
    const suggestedCategory = analysisType === 'theme' ? suggestion.suggestedTheme : suggestion.suggestedFocus;

    return (
      <div className="p-4 border border-blue-500 bg-blue-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Questão {question.exam_year} - 👍 Aprovada
            </span>
            {suggestedCategory && (
              <Badge variant="outline" className="text-xs">
                {analysisType === 'theme' ? 'Tema' : 'Foco'}: {suggestedCategory.name}
              </Badge>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onApprove(question.id)}
          >
            Cancelar
          </Button>
        </div>
      </div>
    );
  }

  // Status pending
  const suggestedCategory = analysisType === 'theme' ? suggestion.suggestedTheme : suggestion.suggestedFocus;

  return (
    <div className="p-4 border rounded-lg bg-white">
      <div className="space-y-3">
        {/* Cabeçalho da questão */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-500" />
            <span className="font-medium">Questão {question.exam_year}</span>
            {suggestedCategory && (
              <Badge variant="outline" className="text-xs">
                {suggestedCategory.confidence}% confiança
              </Badge>
            )}
          </div>
        </div>

        {/* Texto da questão (truncado) */}
        <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded">
          {question.question_content.length > 200
            ? `${question.question_content.substring(0, 200)}...`
            : question.question_content
          }
        </div>

        {/* Sugestão */}
        {suggestedCategory ? (
          <div className="space-y-2">
            <div className="text-sm">
              <strong>Sugestão de {analysisType === 'theme' ? 'Tema' : 'Foco'}:</strong> {suggestedCategory.name}
            </div>
            <div className="text-xs text-muted-foreground">
              <strong>Motivo:</strong> {suggestion.reasoning}
            </div>
          </div>
        ) : (
          <div className="text-sm text-orange-600">
            <AlertTriangle className="h-4 w-4 inline mr-1" />
            Nenhuma categoria sugerida pela IA
          </div>
        )}

        {/* Ações */}
        <div className="flex gap-2">
          <Button
            onClick={() => onApprove(question.id)}
            disabled={!suggestedCategory}
            className="flex-1"
            size="sm"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Aprovar Sugestão
          </Button>
          <Button
            variant="outline"
            onClick={() => onSkip(question.id)}
            size="sm"
          >
            <SkipForward className="h-4 w-4 mr-2" />
            Pular
          </Button>
        </div>
      </div>
    </div>
  );
};

// Função para analisar o conteúdo da questão e sugerir tema
const analyzeQuestionContent = (content: string): string => {
  const lowerContent = content.toLowerCase();

  if (lowerContent.includes('depressão') || lowerContent.includes('transtorno') || lowerContent.includes('psiquiátrico') || lowerContent.includes('mental')) {
    return 'Psiquiatria/Saúde Mental Pediátrica (não disponível na lista)';
  }

  if (lowerContent.includes('hormônio') || lowerContent.includes('crescimento') || lowerContent.includes('endócrino')) {
    return 'Endocrinologia Pediátrica (disponível na lista)';
  }

  if (lowerContent.includes('adolescente') || lowerContent.includes('puberdade')) {
    return 'Medicina do Adolescente/Puericultura';
  }

  if (lowerContent.includes('infecção') || lowerContent.includes('febre') || lowerContent.includes('vírus')) {
    return 'Infectologia Pediátrica';
  }

  return 'Tema não identificado automaticamente';
};

// Função para rotacionar chaves API automaticamente
const getNextApiKey = (currentKey: string, availableKeys: string[]): string => {
  const currentIndex = availableKeys.indexOf(currentKey);
  const nextIndex = (currentIndex + 1) % availableKeys.length;
  return availableKeys[nextIndex];
};

const QuestionCategorization = () => {
  const { toast: toastHook } = useToast();

  // States
  const [specialties, setSpecialties] = useState<Category[]>([]);
  const [themes, setThemes] = useState<Category[]>([]);
  const [focuses, setFocuses] = useState<Category[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('');
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [loading, setLoading] = useState(false);
  
  // Question states
  const [orphanQuestions, setOrphanQuestions] = useState<OrphanQuestion[]>([]);
  const [questionsWithoutTheme, setQuestionsWithoutTheme] = useState<number>(0);
  const [questionsWithoutFocus, setQuestionsWithoutFocus] = useState<number>(0);
  
  // Processing states
  const [batchSize, setBatchSize] = useState<number>(10);
  const [numberOfBatches, setNumberOfBatches] = useState<number>(1);
  const [processedQuestions, setProcessedQuestions] = useState<ProcessedQuestion[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisType, setAnalysisType] = useState<'theme' | 'focus'>('theme');

  // Batch processing states
  const [currentBatch, setCurrentBatch] = useState<number>(0);
  const [totalBatches, setTotalBatches] = useState<number>(0);
  const [batchResults, setBatchResults] = useState<ProcessedQuestion[][]>([]);
  const [overallStats, setOverallStats] = useState({
    totalQuestionsProcessed: 0,
    totalSuggestions: 0,
    totalBatchesCompleted: 0
  });

  // Auto processing states
  const [isAutoProcessing, setIsAutoProcessing] = useState(false);
  const [autoProcessInterval, setAutoProcessInterval] = useState<NodeJS.Timeout | null>(null);
  const [autoProcessStats, setAutoProcessStats] = useState({
    cyclesCompleted: 0,
    totalQuestionsProcessed: 0,
    totalSuggestions: 0,
    totalApplied: 0,
    startTime: null as Date | null
  });
  const [nextProcessTime, setNextProcessTime] = useState<Date | null>(null);

  // API Key selection
  const [selectedApiKey, setSelectedApiKey] = useState<string>('gemini_key1');
  const [apiKeyRotation, setApiKeyRotation] = useState(false);
  const [rotationKeys, setRotationKeys] = useState<string[]>(['gemini_key1', 'gemini_key2', 'gemini_key3', 'gemini_key4', 'gemini_key5', 'gemini_key6', 'openai_key1']);

  // Processing mode
  const [processingMode, setProcessingMode] = useState<'parallel' | 'sequential'>('sequential');

  // All focuses processing states
  const [isProcessingAllFocuses, setIsProcessingAllFocuses] = useState(false);
  const [allFocusesStats, setAllFocusesStats] = useState({
    currentFocusIndex: 0,
    totalFocuses: 0,
    currentFocusName: '',
    totalQuestionsProcessed: 0,
    totalSuggestions: 0,
    totalApplied: 0,
    startTime: null as Date | null
  });

  // Sequential focus processing states
  const [isProcessingSequentialFocuses, setIsProcessingSequentialFocuses] = useState(false);
  const [selectedFocus, setSelectedFocus] = useState<string>('');
  const [sequentialFocusStats, setSequentialFocusStats] = useState({
    startingFocusIndex: 0,
    currentFocusIndex: 0,
    totalFocuses: 0,
    currentFocusName: '',
    totalQuestionsProcessed: 0,
    totalSuggestions: 0,
    totalApplied: 0,
    startTime: null as Date | null
  });

  // Sequential theme processing states
  const [isProcessingSequentialThemes, setIsProcessingSequentialThemes] = useState(false);
  const [sequentialThemeStats, setSequentialThemeStats] = useState({
    startingThemeIndex: 0,
    currentThemeIndex: 0,
    totalThemes: 0,
    currentThemeName: '',
    totalQuestionsProcessed: 0,
    totalSuggestions: 0,
    totalApplied: 0,
    startTime: null as Date | null
  });

  // Função para alternar seleção de chaves na rotação
  const toggleRotationKey = (keyId: string) => {
    setRotationKeys(prev => {
      if (prev.includes(keyId)) {
        // Remove a chave se já estiver selecionada (mínimo 1 chave)
        if (prev.length > 1) {
          return prev.filter(k => k !== keyId);
        }
        return prev; // Não remove se for a última chave
      } else {
        // Adiciona a chave se não estiver selecionada
        return [...prev, keyId];
      }
    });
  };

  // Load specialties on mount
  useEffect(() => {
    loadSpecialties();
  }, []);

  // Load themes when specialty is selected
  useEffect(() => {
    if (selectedSpecialty) {
      loadThemes();
      loadOrphanQuestions();
    }
  }, [selectedSpecialty]);

  // Load focuses when theme is selected
  useEffect(() => {
    if (selectedSpecialty && selectedTheme) {
      loadFocuses();
      loadOrphanQuestions();
    }
  }, [selectedSpecialty, selectedTheme]);

  // Cleanup auto processing on unmount
  useEffect(() => {
    return () => {
      if (autoProcessInterval) {
        clearInterval(autoProcessInterval);
      }
    };
  }, [autoProcessInterval]);

  // Clear theme selection when changing analysis type
  useEffect(() => {
    if (analysisType === 'theme') {
      setSelectedTheme('');
      setFocuses([]);
    }
  }, [analysisType]);

  const loadSpecialties = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'specialty')
        .order('name');

      if (error) throw error;
      setSpecialties(data || []);
    } catch (error) {
      console.error('Error loading specialties:', error);
      toast.error('Erro ao carregar especialidades');
    }
  };

  const loadThemes = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'theme')
        .eq('parent_id', selectedSpecialty)
        .order('name');

      if (error) throw error;
      setThemes(data || []);
      setSelectedTheme(''); // Reset theme selection
      setFocuses([]); // Reset focuses
      setProcessedQuestions([]); // Reset results
    } catch (error) {
      console.error('Error loading themes:', error);
      toast.error('Erro ao carregar temas');
    }
  };

  const loadFocuses = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'focus')
        .eq('parent_id', selectedTheme)
        .order('name');

      if (error) throw error;
      setFocuses(data || []);
    } catch (error) {
      console.error('Error loading focuses:', error);
      toast.error('Erro ao carregar focos');
    }
  };

  const loadOrphanQuestions = async () => {
    setLoading(true);
    try {
      console.log('🔍 Carregando questões órfãs...');
      
      // Count questions without theme
      const { count: withoutThemeCount, error: themeError } = await supabase
        .from('questions')
        .select('*', { count: 'exact', head: true })
        .eq('specialty_id', selectedSpecialty)
        .is('theme_id', null);

      if (themeError) throw themeError;

      // Count questions without focus (but with theme if selected)
      let withoutFocusQuery = supabase
        .from('questions')
        .select('*', { count: 'exact', head: true })
        .eq('specialty_id', selectedSpecialty)
        .is('focus_id', null);

      if (selectedTheme) {
        withoutFocusQuery = withoutFocusQuery.eq('theme_id', selectedTheme);
      }

      const { count: withoutFocusCount, error: focusError } = await withoutFocusQuery;

      if (focusError) throw focusError;

      setQuestionsWithoutTheme(withoutThemeCount || 0);
      setQuestionsWithoutFocus(withoutFocusCount || 0);

      console.log(`📊 Questões sem tema: ${withoutThemeCount}`);
      console.log(`📊 Questões sem foco: ${withoutFocusCount}`);

    } catch (error) {
      console.error('Error loading orphan questions:', error);
      toast.error('Erro ao carregar questões órfãs');
    } finally {
      setLoading(false);
    }
  };

  // Função para fazer chamada com retry inteligente para rate limiting
  const callEdgeFunctionWithRetry = async (body: any, maxRetries = 5): Promise<any> => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Tentativa ${attempt}/${maxRetries} da chamada para Edge Function...`);

        const { data, error } = await supabase.functions.invoke('categorize-questions', { body });

        if (error) {
          // Se for erro 429 (rate limit), aguardar mais tempo
          if (error.message?.includes('429') || error.message?.includes('Too Many Requests')) {
            const waitTime = Math.min(30000, 5000 * Math.pow(2, attempt - 1)); // Backoff exponencial até 30s
            console.log(`⏳ Rate limit detectado. Aguardando ${waitTime/1000}s antes da próxima tentativa...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          }

          // Para outros erros, tentar novamente com delay menor
          if (attempt < maxRetries) {
            const waitTime = 2000 * attempt;
            console.log(`⚠️ Erro na tentativa ${attempt}. Aguardando ${waitTime/1000}s...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          }

          throw error;
        }

        console.log(`✅ Chamada bem-sucedida na tentativa ${attempt}`);
        return data;

      } catch (error: any) {
        console.log(`❌ Erro na tentativa ${attempt}:`, error);

        // Se for erro 429 ou rate limit, aguardar mais tempo
        if (error.message?.includes('429') || error.message?.includes('Too Many Requests') ||
            error.message?.includes('status code') && attempt < maxRetries) {
          const waitTime = Math.min(60000, 10000 * Math.pow(2, attempt - 1)); // Backoff exponencial até 60s
          console.log(`⏳ Rate limit/erro de rede detectado. Aguardando ${waitTime/1000}s antes da próxima tentativa...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        if (attempt === maxRetries) {
          console.log(`💥 Falha após ${maxRetries} tentativas`);
          throw error;
        }

        // Delay progressivo para outros erros
        const waitTime = 3000 * attempt;
        console.log(`⚠️ Tentativa ${attempt} falhou. Aguardando ${waitTime/1000}s...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  };

  // Função para processar temas sequencialmente a partir de um tema selecionado
  const processSequentialThemesFromSelected = async () => {
    if (!selectedSpecialty || !selectedTheme || themes.length === 0) {
      toast.error('Selecione uma especialidade e tema para iniciar o processamento sequencial de temas');
      return;
    }

    if (isProcessingSequentialThemes || isAnalyzing || isProcessingAllFocuses || isProcessingSequentialFocuses) {
      return;
    }

    // Encontrar o índice do tema selecionado
    const startingThemeIndex = themes.findIndex(t => t.id === selectedTheme);
    if (startingThemeIndex === -1) {
      toast.error('Tema selecionado não encontrado');
      return;
    }

    setIsProcessingSequentialThemes(true);
    setSequentialThemeStats({
      startingThemeIndex: startingThemeIndex,
      currentThemeIndex: startingThemeIndex,
      totalThemes: themes.length,
      currentThemeName: themes[startingThemeIndex].name,
      totalQuestionsProcessed: 0,
      totalSuggestions: 0,
      totalApplied: 0,
      startTime: new Date()
    });

    const selectedThemeName = themes[startingThemeIndex].name;
    const specialtyName = specialties.find(s => s.id === selectedSpecialty)?.name;

    // Delay inicial para evitar rate limiting
    await new Promise(resolve => setTimeout(resolve, 5000));

    let totalProcessed = 0;
    let totalSuggestions = 0;
    let totalApplied = 0;

    try {
      // Processar a partir do tema selecionado até o final
      for (let themeIndex = startingThemeIndex; themeIndex < themes.length; themeIndex++) {
        const currentTheme = themes[themeIndex];



        // Atualizar stats
        setSequentialThemeStats(prev => ({
          ...prev,
          currentThemeIndex: themeIndex,
          currentThemeName: currentTheme.name
        }));

        // Atualizar o tema selecionado para carregar os focos
        setSelectedTheme(currentTheme.id);

        // Aguardar um pouco para garantir que os focos sejam carregados
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Carregar focos para este tema
        const { data: themeFocuses, error: focusError } = await supabase
          .from('study_categories')
          .select('id, name')
          .eq('type', 'focus')
          .eq('parent_id', currentTheme.id)
          .order('name');

        if (focusError) {
          console.error(`❌ Erro ao carregar focos para tema ${currentTheme.name}:`, focusError);
          continue;
        }

        if (!themeFocuses || themeFocuses.length === 0) {
          console.log(`⚠️ Tema ${currentTheme.name}: Nenhum foco encontrado`);
          continue;
        }

        console.log(`📊 Tema ${currentTheme.name}: ${themeFocuses.length} focos encontrados`);

        // Processar todos os focos deste tema até esgotar
        let themeQuestionsProcessed = 0;
        let themeQuestionsApplied = 0;

        for (const focus of themeFocuses) {
          console.log(`\n  🎯 Processando foco: ${focus.name}`);

          // Processar este foco até esgotar todas as questões órfãs
          let focusCompleted = false;
          let focusQuestionsProcessed = 0;
          let focusQuestionsApplied = 0;

          while (!focusCompleted) {
            const focusResults = await processSingleFocusBatchForTheme(focus, currentTheme.id);

            if (focusResults.length === 0) {
              console.log(`    ✅ Foco ${focus.name}: Todas as questões processadas`);
              focusCompleted = true;
              break;
            }

            focusQuestionsProcessed += focusResults.length;
            themeQuestionsProcessed += focusResults.length;
            totalProcessed += focusResults.length;
            totalSuggestions += focusResults.length;

            console.log(`    📊 Foco ${focus.name}: ${focusResults.length} questões categorizadas neste lote`);

            // Aplicar automaticamente as sugestões
            let appliedCount = 0;
            for (const result of focusResults) {
              try {
                const { error } = await supabase
                  .from('questions')
                  .update({ focus_id: result.suggestion.suggestedFocus?.id })
                  .eq('id', result.question.id);

                if (!error) {
                  appliedCount++;
                  totalApplied++;
                  themeQuestionsApplied++;
                  focusQuestionsApplied++;
                } else {
                  console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
                }
              } catch (error) {
                console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
              }
            }

            console.log(`    ✅ Foco ${focus.name}: ${appliedCount} questões aplicadas neste lote`);

            // Atualizar estatísticas
            setSequentialThemeStats(prev => ({
              ...prev,
              totalQuestionsProcessed: totalProcessed,
              totalSuggestions: totalSuggestions,
              totalApplied: totalApplied
            }));

            // Delay maior entre lotes do mesmo foco para evitar rate limiting
            await new Promise(resolve => setTimeout(resolve, 5000));
          }

          console.log(`  🎉 Foco ${focus.name} concluído: ${focusQuestionsProcessed} questões processadas, ${focusQuestionsApplied} aplicadas`);

          // Delay maior entre focos para não sobrecarregar a API
          await new Promise(resolve => setTimeout(resolve, 4000));
        }

        console.log(`🎉 Tema ${currentTheme.name} concluído: ${themeQuestionsProcessed} questões processadas, ${themeQuestionsApplied} aplicadas`);

        // Delay maior entre temas para não sobrecarregar a API
        if (themeIndex < themes.length - 1) {
          console.log(`⏳ Aguardando 10 segundos antes do próximo tema...`);
          await new Promise(resolve => setTimeout(resolve, 10000));
        }
      }

      console.log(`\n🎉 Processamento sequencial de temas concluído!`);
      console.log(`📊 Resumo final:`);
      console.log(`   - Temas processados: ${themes.length - startingThemeIndex}`);
      console.log(`   - Questões processadas: ${totalProcessed}`);
      console.log(`   - Sugestões geradas: ${totalSuggestions}`);
      console.log(`   - Questões aplicadas: ${totalApplied}`);

      toast.success(`Processamento sequencial completo! ${totalApplied} questões categorizadas em ${themes.length - startingThemeIndex} temas.`);

      // Recarregar contadores
      await loadOrphanQuestions();

    } catch (error) {
      console.error('❌ Erro no processamento sequencial de temas:', error);
      toast.error('Erro no processamento sequencial de temas');
    } finally {
      setIsProcessingSequentialThemes(false);
      setSequentialThemeStats(prev => ({
        ...prev,
        currentThemeIndex: 0,
        currentThemeName: ''
      }));
    }
  };

  // Função para processar focos sequencialmente a partir de um foco selecionado
  const processSequentialFocusesFromSelected = async () => {
    if (!selectedSpecialty || !selectedTheme || !selectedFocus || focuses.length === 0) {
      toast.error('Selecione uma especialidade, tema e foco para iniciar o processamento sequencial');
      return;
    }

    if (isProcessingSequentialFocuses || isAnalyzing || isProcessingAllFocuses || isProcessingSequentialThemes) {
      return;
    }

    // Encontrar o índice do foco selecionado
    const startingFocusIndex = focuses.findIndex(f => f.id === selectedFocus);
    if (startingFocusIndex === -1) {
      toast.error('Foco selecionado não encontrado');
      return;
    }

    setIsProcessingSequentialFocuses(true);
    setSequentialFocusStats({
      startingFocusIndex: startingFocusIndex,
      currentFocusIndex: startingFocusIndex,
      totalFocuses: focuses.length,
      currentFocusName: focuses[startingFocusIndex].name,
      totalQuestionsProcessed: 0,
      totalSuggestions: 0,
      totalApplied: 0,
      startTime: new Date()
    });

    const selectedFocusName = focuses[startingFocusIndex].name;
    const themeName = themes.find(t => t.id === selectedTheme)?.name;

    console.log(`🎯 Iniciando processamento sequencial a partir do foco: ${selectedFocusName}`);
    console.log(`📊 Tema: ${themeName}`);
    console.log(`📊 Foco inicial: ${startingFocusIndex + 1}/${focuses.length}`);
    console.log(`📋 Focos restantes: ${focuses.slice(startingFocusIndex).map(f => f.name).join(', ')}`);

    let totalProcessed = 0;
    let totalSuggestions = 0;
    let totalApplied = 0;

    try {
      // Processar a partir do foco selecionado até o final
      for (let focusIndex = startingFocusIndex; focusIndex < focuses.length; focusIndex++) {
        const currentFocus = focuses[focusIndex];

        console.log(`\n🎯 Processando foco ${focusIndex + 1}/${focuses.length}: ${currentFocus.name}`);

        // Atualizar stats
        setSequentialFocusStats(prev => ({
          ...prev,
          currentFocusIndex: focusIndex,
          currentFocusName: currentFocus.name
        }));

        // Processar este foco até esgotar todas as questões órfãs
        let focusCompleted = false;
        let focusQuestionsProcessed = 0;
        let focusQuestionsApplied = 0;

        while (!focusCompleted) {
          const focusResults = await processSingleFocusBatch(currentFocus);

          if (focusResults.length === 0) {
            console.log(`✅ Foco ${currentFocus.name}: Todas as questões processadas`);
            focusCompleted = true;
            break;
          }

          focusQuestionsProcessed += focusResults.length;
          totalProcessed += focusResults.length;
          totalSuggestions += focusResults.length;

          console.log(`📊 Foco ${currentFocus.name}: ${focusResults.length} questões categorizadas neste lote`);

          // Aplicar automaticamente as sugestões
          let appliedCount = 0;
          for (const result of focusResults) {
            try {
              const { error } = await supabase
                .from('questions')
                .update({ focus_id: result.suggestion.suggestedFocus?.id })
                .eq('id', result.question.id);

              if (!error) {
                appliedCount++;
                totalApplied++;
                focusQuestionsApplied++;
              } else {
                console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
              }
            } catch (error) {
              console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
            }
          }

          console.log(`✅ Foco ${currentFocus.name}: ${appliedCount} questões aplicadas neste lote`);

          // Atualizar estatísticas
          setSequentialFocusStats(prev => ({
            ...prev,
            totalQuestionsProcessed: totalProcessed,
            totalSuggestions: totalSuggestions,
            totalApplied: totalApplied
          }));

          // Delay maior entre lotes do mesmo foco para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        console.log(`🎉 Foco ${currentFocus.name} concluído: ${focusQuestionsProcessed} questões processadas, ${focusQuestionsApplied} aplicadas`);

        // Delay maior entre focos para não sobrecarregar a API
        if (focusIndex < focuses.length - 1) {
          console.log(`⏳ Aguardando 5 segundos antes do próximo foco...`);
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }

      console.log(`\n🎉 Processamento sequencial concluído!`);
      console.log(`📊 Resumo final:`);
      console.log(`   - Focos processados: ${focuses.length - startingFocusIndex}`);
      console.log(`   - Questões processadas: ${totalProcessed}`);
      console.log(`   - Sugestões geradas: ${totalSuggestions}`);
      console.log(`   - Questões aplicadas: ${totalApplied}`);

      toast.success(`Processamento sequencial completo! ${totalApplied} questões categorizadas em ${focuses.length - startingFocusIndex} focos.`);

      // Recarregar contadores
      await loadOrphanQuestions();

    } catch (error) {
      console.error('❌ Erro no processamento sequencial de focos:', error);
      toast.error('Erro no processamento sequencial de focos');
    } finally {
      setIsProcessingSequentialFocuses(false);
      setSequentialFocusStats(prev => ({
        ...prev,
        currentFocusIndex: 0,
        currentFocusName: ''
      }));
    }
  };

  // Função para processar todos os focos de um tema automaticamente
  const processAllFocusesInTheme = async () => {
    if (!selectedSpecialty || !selectedTheme || focuses.length === 0) {
      toast.error('Selecione uma especialidade e tema com focos disponíveis');
      return;
    }

    if (isProcessingAllFocuses || isAnalyzing || isProcessingSequentialFocuses) {
      return;
    }

    setIsProcessingAllFocuses(true);
    setAllFocusesStats({
      currentFocusIndex: 0,
      totalFocuses: focuses.length,
      currentFocusName: '',
      totalQuestionsProcessed: 0,
      totalSuggestions: 0,
      totalApplied: 0,
      startTime: new Date()
    });

    console.log(`🎯 Iniciando processamento automático de TODOS os focos do tema: ${themes.find(t => t.id === selectedTheme)?.name}`);
    console.log(`📊 Total de focos a processar: ${focuses.length}`);
    console.log(`📋 Focos: ${focuses.map(f => f.name).join(', ')}`);

    let totalProcessed = 0;
    let totalSuggestions = 0;
    let totalApplied = 0;

    try {
      for (let focusIndex = 0; focusIndex < focuses.length; focusIndex++) {
        const currentFocus = focuses[focusIndex];

        console.log(`\n🎯 Processando foco ${focusIndex + 1}/${focuses.length}: ${currentFocus.name}`);

        // Atualizar stats
        setAllFocusesStats(prev => ({
          ...prev,
          currentFocusIndex: focusIndex + 1,
          currentFocusName: currentFocus.name
        }));

        // Processar este foco específico
        const focusResults = await processSingleFocusBatch(currentFocus);

        if (focusResults.length > 0) {
          totalProcessed += focusResults.length;
          totalSuggestions += focusResults.length;

          console.log(`✅ Foco ${currentFocus.name}: ${focusResults.length} questões categorizadas`);

          // Aplicar automaticamente as sugestões
          let appliedCount = 0;
          for (const result of focusResults) {
            try {
              const { error } = await supabase
                .from('questions')
                .update({ focus_id: result.suggestion.suggestedFocus?.id })
                .eq('id', result.question.id);

              if (!error) {
                appliedCount++;
                totalApplied++;
              } else {
                console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
              }
            } catch (error) {
              console.error(`❌ Erro ao aplicar foco para questão ${result.question.id}:`, error);
            }
          }

          console.log(`✅ Foco ${currentFocus.name}: ${appliedCount} questões aplicadas automaticamente`);
        } else {
          console.log(`⚠️ Foco ${currentFocus.name}: Nenhuma questão encontrada para categorizar`);
        }

        // Atualizar estatísticas
        setAllFocusesStats(prev => ({
          ...prev,
          totalQuestionsProcessed: totalProcessed,
          totalSuggestions: totalSuggestions,
          totalApplied: totalApplied
        }));

        // Delay entre focos para não sobrecarregar a API
        if (focusIndex < focuses.length - 1) {
          console.log(`⏳ Aguardando 2 segundos antes do próximo foco...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      console.log(`\n🎉 Processamento de todos os focos concluído!`);
      console.log(`📊 Resumo final:`);
      console.log(`   - Focos processados: ${focuses.length}`);
      console.log(`   - Questões processadas: ${totalProcessed}`);
      console.log(`   - Sugestões geradas: ${totalSuggestions}`);
      console.log(`   - Questões aplicadas: ${totalApplied}`);

      toast.success(`Processamento completo! ${totalApplied} questões categorizadas em ${focuses.length} focos.`);

      // Recarregar contadores
      await loadOrphanQuestions();

    } catch (error) {
      console.error('❌ Erro no processamento de todos os focos:', error);
      toast.error('Erro no processamento automático de focos');
    } finally {
      setIsProcessingAllFocuses(false);
      setAllFocusesStats(prev => ({
        ...prev,
        currentFocusIndex: 0,
        currentFocusName: ''
      }));
    }
  };

  // Função para processar um único foco para um tema específico
  const processSingleFocusBatchForTheme = async (focus: Category, themeId: string): Promise<ProcessedQuestion[]> => {
    try {
      console.log(`📦 Processando foco: ${focus.name} (ID: ${focus.id}) para tema: ${themeId}`);

      // Buscar questões órfãs para este tema específico (sem foco)
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id, question_content, response_choices, correct_choice, exam_year, specialty_id, theme_id, focus_id')
        .eq('specialty_id', selectedSpecialty)
        .eq('theme_id', themeId)
        .is('focus_id', null)
        .limit(batchSize); // Usar o tamanho de lote configurado

      if (questionsError) throw questionsError;

      if (!questions || questions.length === 0) {
        console.log(`⚠️ Nenhuma questão órfã encontrada para o foco: ${focus.name}`);
        return [];
      }

      console.log(`📊 Encontradas ${questions.length} questões órfãs para o foco: ${focus.name}`);

      // Buscar todos os focos deste tema para enviar para a IA
      const { data: themeFocuses, error: focusError } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'focus')
        .eq('parent_id', themeId)
        .order('name');

      if (focusError) throw focusError;

      const focusesForTheme = themeFocuses || [];

      // Determinar chave API a usar
      let apiKeyToUse = selectedApiKey;
      if (apiKeyRotation && rotationKeys.length > 0) {
        const randomIndex = Math.floor(Math.random() * rotationKeys.length);
        apiKeyToUse = rotationKeys[randomIndex];
      }

      console.log(`🔑 Usando chave API: ${apiKeyToUse}`);

      // Chamar edge function com retry inteligente
      const data = await callEdgeFunctionWithRetry({
        specialty: specialties.find(s => s.id === selectedSpecialty)?.name,
        theme: themes.find(t => t.id === themeId)?.name,
        analysisType: 'focus',
        questions: questions.map(q => ({
          id: q.id,
          text: q.question_content,
          alternatives: q.response_choices,
          correctAnswer: q.correct_choice,
          examYear: q.exam_year
        })),
        availableCategories: focusesForTheme.map(f => ({
          id: f.id,
          name: f.name
        })),
        selectedApiKey: apiKeyToUse
      });

      if (!data?.suggestions || !Array.isArray(data.suggestions)) {
        console.log(`⚠️ Nenhuma sugestão válida retornada para o foco: ${focus.name}`);
        return [];
      }

      // Converter para ProcessedQuestion
      const processedResults: ProcessedQuestion[] = data.suggestions.map((suggestion: any) => {
        const question = questions.find(q => q.id === suggestion.questionId);
        if (!question) return null;

        return {
          question,
          suggestion,
          status: 'pending' as const
        };
      }).filter(Boolean);

      console.log(`✅ Foco ${focus.name}: ${processedResults.length} sugestões válidas geradas`);
      return processedResults;

    } catch (error) {
      console.error(`❌ Erro ao processar foco ${focus.name}:`, error);
      return [];
    }
  };

  // Função para processar um único foco
  const processSingleFocusBatch = async (focus: Category): Promise<ProcessedQuestion[]> => {
    try {
      console.log(`📦 Processando foco: ${focus.name} (ID: ${focus.id})`);

      // Buscar questões órfãs para este tema específico (sem foco)
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id, question_content, response_choices, correct_choice, exam_year, specialty_id, theme_id, focus_id')
        .eq('specialty_id', selectedSpecialty)
        .eq('theme_id', selectedTheme)
        .is('focus_id', null)
        .limit(batchSize); // Usar o tamanho de lote configurado

      if (questionsError) throw questionsError;

      if (!questions || questions.length === 0) {
        console.log(`⚠️ Nenhuma questão órfã encontrada para o foco: ${focus.name}`);
        return [];
      }

      console.log(`📊 Encontradas ${questions.length} questões órfãs para o foco: ${focus.name}`);

      // Preparar categorias (apenas os focos deste tema)
      const focusesForTheme = focuses; // Já filtrados pelo tema selecionado

      // Determinar chave API a usar
      let apiKeyToUse = selectedApiKey;
      if (apiKeyRotation && rotationKeys.length > 0) {
        const randomIndex = Math.floor(Math.random() * rotationKeys.length);
        apiKeyToUse = rotationKeys[randomIndex];
      }

      console.log(`🔑 Usando chave API: ${apiKeyToUse}`);

      // Chamar edge function com retry inteligente
      const data = await callEdgeFunctionWithRetry({
        specialty: specialties.find(s => s.id === selectedSpecialty)?.name,
        theme: themes.find(t => t.id === selectedTheme)?.name,
        analysisType: 'focus',
        questions: questions.map(q => ({
          id: q.id,
          text: q.question_content,
          alternatives: q.response_choices,
          correctAnswer: q.correct_choice,
          examYear: q.exam_year
        })),
        availableCategories: focusesForTheme.map(f => ({
          id: f.id,
          name: f.name
        })),
        selectedApiKey: apiKeyToUse
      });

      if (!data?.suggestions || !Array.isArray(data.suggestions)) {
        console.log(`⚠️ Nenhuma sugestão válida retornada para o foco: ${focus.name}`);
        return [];
      }

      // Converter para ProcessedQuestion
      const processedResults: ProcessedQuestion[] = data.suggestions.map((suggestion: any) => {
        const question = questions.find(q => q.id === suggestion.questionId);
        if (!question) return null;

        return {
          question,
          suggestion,
          status: 'pending' as const
        };
      }).filter(Boolean);

      console.log(`✅ Foco ${focus.name}: ${processedResults.length} sugestões válidas geradas`);
      return processedResults;

    } catch (error) {
      console.error(`❌ Erro ao processar foco ${focus.name}:`, error);
      return [];
    }
  };

  const analyzeMultipleBatchesSequential = async (autoApproveAndApply = false) => {
    if (isAnalyzing) return;

    console.log(`🔧 DEBUG: analyzeMultipleBatchesSequential chamada com autoApproveAndApply = ${autoApproveAndApply}`);

    setIsAnalyzing(true);
    setCurrentBatch(0);
    setTotalBatches(numberOfBatches);
    setProcessedQuestions([]);

    console.log(`🚀 Iniciando processamento SEQUENCIAL de ${numberOfBatches} lotes...`);
    console.log(`⏳ Processamento um por vez com delay de 3 segundos entre lotes`);
    console.log(`🤖 Modo automático: ${autoApproveAndApply ? 'ATIVADO' : 'DESATIVADO'}`);

    try {
      const allResults: ProcessedQuestion[] = [];

      for (let batchIndex = 0; batchIndex < numberOfBatches; batchIndex++) {
        console.log(`\n📦 Processando lote ${batchIndex + 1}/${numberOfBatches} sequencialmente...`);
        setCurrentBatch(batchIndex + 1);

        const batchResults = await processSingleBatch(batchIndex);

        if (batchResults.length > 0) {
          allResults.push(...batchResults);
          console.log(`✅ Lote ${batchIndex + 1} concluído: ${batchResults.length} resultados`);
        } else {
          console.log(`⚠️ Lote ${batchIndex + 1} não retornou resultados`);
        }

        // Delay entre lotes (exceto no último)
        if (batchIndex < numberOfBatches - 1) {
          console.log(`⏳ Aguardando 3 segundos antes do próximo lote...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }

      setProcessedQuestions(allResults);

      console.log(`\n🎉 Processamento sequencial completo!`);
      console.log(`📊 Resumo geral:`);
      console.log(`   - Lotes processados: ${numberOfBatches}`);
      console.log(`   - Questões tentadas: ${numberOfBatches * batchSize}`);
      console.log(`   - Sugestões válidas: ${allResults.length}`);
      console.log(`   - Taxa de sucesso geral: ${Math.round((allResults.length / (numberOfBatches * batchSize)) * 100)}%`);

      // Se for processamento automático, aprovar e aplicar imediatamente
      console.log(`🔧 DEBUG SEQUENCIAL: autoApproveAndApply = ${autoApproveAndApply}, tipo: ${typeof autoApproveAndApply}`);
      if (autoApproveAndApply === true && allResults.length > 0) {
        console.log(`🤖 Modo automático: Aprovando e aplicando ${allResults.length} sugestões...`);

        // Aguardar um pouco para garantir que o estado foi atualizado
        await new Promise(resolve => setTimeout(resolve, 500));

        // Aprovar todas as questões válidas
        setProcessedQuestions(prev => {
          const updated = prev.map(p => {
            const hasValidSuggestion = analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus;
            if (p.status === 'pending' && hasValidSuggestion) {
              console.log(`✅ Aprovando questão: ${p.question.id}`);
              return { ...p, status: 'approved' };
            }
            return p;
          });
          console.log(`📊 Questões aprovadas: ${updated.filter(p => p.status === 'approved').length}`);
          return updated;
        });

        // Aguardar atualização do estado
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Aplicar todas as aprovações manualmente
        console.log(`📝 Iniciando aplicação das questões aprovadas...`);

        let appliedCount = 0;

        for (const item of allResults) {
          try {
            const updateData: any = {};

            if (analysisType === 'theme' && item.suggestion.suggestedTheme) {
              updateData.theme_id = item.suggestion.suggestedTheme.id;
              console.log(`📋 Categorizando questão ${item.question.id} no tema: ${item.suggestion.suggestedTheme.name}`);
            } else if (analysisType === 'focus' && item.suggestion.suggestedFocus) {
              updateData.focus_id = item.suggestion.suggestedFocus.id;
              console.log(`📋 Categorizando questão ${item.question.id} no foco: ${item.suggestion.suggestedFocus.name}`);
            }

            const { error } = await supabase
              .from('questions')
              .update(updateData)
              .eq('id', item.question.id);

            if (error) {
              console.error(`❌ Erro ao categorizar questão ${item.question.id}:`, error);
            } else {
              appliedCount++;
              console.log(`✅ Questão ${item.question.id} categorizada com sucesso!`);
            }

          } catch (error) {
            console.error(`❌ Erro ao categorizar questão ${item.question.id}:`, error);
          }
        }

        // Marcar questões como aplicadas
        setProcessedQuestions(prev => prev.map(p =>
          p.status === 'approved' ? { ...p, status: 'applied' } : p
        ));

        console.log(`✅ Processamento automático: ${appliedCount} questões aplicadas automaticamente`);
        toast.success(`Automático: ${appliedCount} questões categorizadas e aplicadas!`);

        return appliedCount; // Retornar número real de questões aplicadas
      } else {
        console.log(`📋 Processamento manual concluído: ${allResults.length} sugestões aguardando aprovação`);
        toast.success(`Processamento sequencial concluído! ${allResults.length} questões categorizadas. Revise e aprove as sugestões.`);
        return 0; // Modo manual, não aplicou automaticamente
      }

    } catch (error) {
      console.error('❌ Erro no processamento sequencial:', error);
      toast.error('Erro no processamento sequencial');
      return 0;
    } finally {
      setIsAnalyzing(false);
      setCurrentBatch(0);
    }
  };

  const analyzeMultipleBatches = async (autoApproveAndApply = false) => {
    console.log(`🔧 DEBUG: analyzeMultipleBatches chamada com autoApproveAndApply = ${autoApproveAndApply}, tipo: ${typeof autoApproveAndApply}`);
    console.log(`🔧 DEBUG: processingMode = ${processingMode}`);

    if (processingMode === 'sequential') {
      console.log(`🔧 DEBUG: Redirecionando para processamento sequencial com autoApproveAndApply = ${autoApproveAndApply}`);
      return analyzeMultipleBatchesSequential(autoApproveAndApply);
    }

    console.log(`🔧 DEBUG: Continuando com processamento paralelo com autoApproveAndApply = ${autoApproveAndApply}`);

    // Processamento paralelo (código existente)
    if (!selectedSpecialty) {
      toast.error('Selecione uma especialidade');
      return;
    }

    if (analysisType === 'focus' && !selectedTheme) {
      toast.error('Selecione um tema para análise de focos');
      return;
    }

    setIsAnalyzing(true);
    setProcessedQuestions([]);
    setBatchResults([]);
    setCurrentBatch(0);
    setTotalBatches(numberOfBatches);
    setOverallStats({
      totalQuestionsProcessed: 0,
      totalSuggestions: 0,
      totalBatchesCompleted: 0
    });

    console.log(`🚀 Iniciando processamento em múltiplos lotes:`);
    console.log(`📊 Configuração: ${numberOfBatches} lotes de ${batchSize} questões cada`);
    console.log(`📊 Total máximo de questões: ${numberOfBatches * batchSize}`);

    try {
      console.log(`🚀 Iniciando processamento paralelo de ${numberOfBatches} lotes...`);

      // Criar array de promessas com delay escalonado para evitar sobrecarga
      const batchPromises = Array.from({ length: numberOfBatches }, (_, batchIndex) =>
        new Promise(resolve => {
          // Delay escalonado: 0ms, 500ms, 1000ms, 1500ms...
          const delay = batchIndex * 500;
          setTimeout(() => {
            resolve(processSingleBatch(batchIndex));
          }, delay);
        })
      );

      console.log(`⏳ Lotes iniciados com delay escalonado (0ms, 500ms, 1000ms...)`);

      // Processar todos os lotes em paralelo (mas com início escalonado)
      const batchResultsArray = await Promise.all(batchPromises);

      // Filtrar resultados válidos e consolidar
      const validBatchResults = batchResultsArray.filter(results => results && results.length > 0);
      const allResults: ProcessedQuestion[] = validBatchResults.flat();

      // Atualizar estados
      setBatchResults(validBatchResults);

      // Calcular estatísticas finais
      const totalQuestionsAttempted = numberOfBatches * batchSize;
      const totalSuggestions = allResults.length;
      const batchesWithResults = validBatchResults.length;

      setOverallStats({
        totalQuestionsProcessed: totalQuestionsAttempted,
        totalSuggestions: totalSuggestions,
        totalBatchesCompleted: batchesWithResults
      });

      console.log(`✅ Processamento paralelo concluído!`);
      console.log(`📊 Lotes processados simultaneamente: ${numberOfBatches}`);
      console.log(`📊 Lotes com resultados: ${batchesWithResults}`);
      console.log(`📊 Total de sugestões: ${totalSuggestions}`);

      // Log detalhado por lote
      validBatchResults.forEach((batchResults, index) => {
        console.log(`   Lote ${index + 1}: ${batchResults.length} sugestões`);
      });

      // Filtrar e validar resultados antes de definir no estado
      const validResults = allResults.filter(result =>
        result &&
        result.question &&
        result.question.id &&
        result.suggestion
      );

      setProcessedQuestions(validResults);

      console.log(`\n🎉 Processamento paralelo completo!`);
      console.log(`📊 Resumo geral:`);
      console.log(`   - Lotes processados: ${batchesWithResults}`);
      console.log(`   - Questões tentadas: ${totalQuestionsAttempted}`);
      console.log(`   - Sugestões válidas: ${validResults.length}`);
      console.log(`   - Taxa de sucesso geral: ${Math.round((validResults.length / totalQuestionsAttempted) * 100)}%`);

      // Se for processamento automático, aprovar e aplicar imediatamente
      console.log(`🔧 DEBUG PARALELO: autoApproveAndApply = ${autoApproveAndApply}, tipo: ${typeof autoApproveAndApply}`);
      if (autoApproveAndApply === true && validResults.length > 0) {
        console.log(`🤖 Modo automático: Aprovando e aplicando ${validResults.length} sugestões...`);

        // Aguardar um pouco para garantir que o estado foi atualizado
        await new Promise(resolve => setTimeout(resolve, 500));

        // Aprovar todas as questões válidas
        setProcessedQuestions(prev => {
          const updated = prev.map(p => {
            const hasValidSuggestion = analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus;
            if (p.status === 'pending' && hasValidSuggestion) {
              console.log(`✅ Aprovando questão: ${p.question.id}`);
              return { ...p, status: 'approved' };
            }
            return p;
          });
          console.log(`📊 Questões aprovadas: ${updated.filter(p => p.status === 'approved').length}`);
          return updated;
        });

        // Aguardar atualização do estado
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Aplicar todas as aprovações manualmente (sem usar applyAllApproved)
        console.log(`📝 Iniciando aplicação das questões aprovadas...`);

        let appliedCount = 0;

        for (const item of validResults) {
          try {
            const updateData: any = {};

            if (analysisType === 'theme' && item.suggestion.suggestedTheme) {
              updateData.theme_id = item.suggestion.suggestedTheme.id;
              console.log(`📋 Categorizando questão ${item.question.id} no tema: ${item.suggestion.suggestedTheme.name}`);
            } else if (analysisType === 'focus' && item.suggestion.suggestedFocus) {
              updateData.focus_id = item.suggestion.suggestedFocus.id;
              console.log(`📋 Categorizando questão ${item.question.id} no foco: ${item.suggestion.suggestedFocus.name}`);
            }

            const { error } = await supabase
              .from('questions')
              .update(updateData)
              .eq('id', item.question.id);

            if (error) {
              console.error(`❌ Erro ao categorizar questão ${item.question.id}:`, error);
            } else {
              appliedCount++;
              console.log(`✅ Questão ${item.question.id} categorizada com sucesso!`);
            }

          } catch (error) {
            console.error(`❌ Erro ao categorizar questão ${item.question.id}:`, error);
          }
        }

        // Marcar questões como aplicadas
        setProcessedQuestions(prev => prev.map(p =>
          p.status === 'approved' ? { ...p, status: 'applied' } : p
        ));

        console.log(`✅ Processamento automático: ${appliedCount} questões aplicadas automaticamente`);
        toast.success(`Automático: ${appliedCount} questões categorizadas e aplicadas!`);

        return appliedCount; // Retornar número real de questões aplicadas
      } else {
        console.log(`📋 Processamento manual concluído: ${validResults.length} sugestões aguardando aprovação`);
        toast.success(`Processamento paralelo concluído! ${validResults.length} questões categorizadas. Revise e aprove as sugestões.`);
        return 0; // Modo manual, não aplicou automaticamente
      }

    } catch (error) {
      console.error('❌ Erro no processamento em lotes:', error);
      toast.error('Erro no processamento em lotes');
    } finally {
      setIsAnalyzing(false);
      setCurrentBatch(0);
    }
  };

  const processSingleBatch = async (batchIndex: number, retryCount = 0): Promise<ProcessedQuestion[]> => {
    const maxRetries = 2;

    try {


      // Load questions to analyze for this specific batch
    const offset = batchIndex * batchSize;

    let questionsQuery = supabase
      .from('questions')
      .select('id, question_content, response_choices, correct_choice, exam_year, specialty_id, theme_id, focus_id')
      .eq('specialty_id', selectedSpecialty)
      .range(offset, offset + batchSize - 1);

    if (analysisType === 'theme') {
      questionsQuery = questionsQuery.is('theme_id', null);
    } else {
      questionsQuery = questionsQuery
        .eq('theme_id', selectedTheme)
        .is('focus_id', null);
    }

    const { data: questions, error: questionsError } = await questionsQuery;

    if (questionsError) throw questionsError;

    if (!questions || questions.length === 0) {
      console.log(`⚠️ Lote ${batchIndex + 1}: Nenhuma questão encontrada`);
      return [];
    }

    console.log(`📊 Lote ${batchIndex + 1}: Analisando ${questions.length} questões (offset: ${offset})`);
      // Prepare categories for AI
      const categories = analysisType === 'theme' ? themes : focuses;

      // Determinar qual chave usar (rotação automática ou selecionada)
      let apiKeyToUse = selectedApiKey;
      if (apiKeyRotation) {
        // Rotacionar baseado no índice do lote usando apenas as chaves selecionadas
        apiKeyToUse = rotationKeys[batchIndex % rotationKeys.length];
      }

      const { data, error } = await supabase.functions.invoke('categorize-questions', {
        body: {
          specialty: specialties.find(s => s.id === selectedSpecialty)?.name,
          theme: selectedTheme ? themes.find(t => t.id === selectedTheme)?.name : null,
          analysisType,
          questions: questions.map(q => ({
            id: q.id,
            text: q.question_content,
            alternatives: q.response_choices,
            correctAnswer: q.correct_choice,
            examYear: q.exam_year
          })),
          availableCategories: categories.map(c => ({
            id: c.id,
            name: c.name
          })),
          selectedApiKey: apiKeyToUse
        }
      });

      if (error) {
        throw error;
      }

      // Process suggestions (detailed logging removed for production)

      // Verificar quais questões enviadas não retornaram
      const questionsSent = questions.map(q => q.id);
      const questionsReturned = data.suggestions?.map((s: CategorizationSuggestion) => s.questionId) || [];
      const missingQuestions = questionsSent.filter(id => !questionsReturned.includes(id));

      // Process results with validation
      const processedResults: ProcessedQuestion[] = data.suggestions
        .map((suggestion: CategorizationSuggestion) => {
          const question = questions.find(q => q.id === suggestion.questionId);
          if (!question) {
            return null;
          }
          return {
            question,
            suggestion,
            status: 'pending' as const
          };
        })
        .filter((result): result is ProcessedQuestion => result !== null);

      return processedResults;
    } catch (error) {
      // Tentar novamente se ainda há tentativas disponíveis
      if (retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return processSingleBatch(batchIndex, retryCount + 1);
      }

      return []; // Retorna array vazio após esgotar tentativas
    }
  };

  const approveSuggestion = (questionId: string) => {
    setProcessedQuestions(prev => prev.map(p => {
      if (p.question.id === questionId) {
        return { ...p, status: p.status === 'approved' ? 'pending' : 'approved' };
      }
      return p;
    }));
    
    const currentStatus = processedQuestions.find(p => p.question.id === questionId)?.status;
    
    if (currentStatus === 'approved') {
      toast.info('Aprovação cancelada');
    } else {
      toast.success('Sugestão aprovada');
    }
  };

  const skipQuestion = (questionId: string) => {
    setProcessedQuestions(prev => prev.map(p =>
      p.question.id === questionId ? { ...p, status: 'skipped' } : p
    ));
    toast.info('Questão pulada');
  };

  const applyAllApproved = async () => {
    const approvedItems = processedQuestions.filter(p => p.status === 'approved');
    
    if (approvedItems.length === 0) {
      toast.error('Nenhuma sugestão aprovada para aplicar');
      return;
    }


    
    for (const item of approvedItems) {
      try {
        const updateData: any = {};
        
        if (analysisType === 'theme' && item.suggestion.suggestedTheme) {
          updateData.theme_id = item.suggestion.suggestedTheme.id;
        } else if (analysisType === 'focus' && item.suggestion.suggestedFocus) {
          updateData.focus_id = item.suggestion.suggestedFocus.id;
        }

        const { error } = await supabase
          .from('questions')
          .update(updateData)
          .eq('id', item.question.id);

        if (error) throw error;

        // Marcar como aplicado
        setProcessedQuestions(prev => prev.map(p =>
          p.question.id === item.question.id ? { ...p, status: 'applied' } : p
        ));


      } catch (error) {
        toast.error(`Erro ao categorizar questão`);
      }
    }
    
    toast.success(`${approvedItems.length} questões categorizadas com sucesso!`);
    console.log(`✅ Todas as ${approvedItems.length} questões foram categorizadas`);
    
    // Recarregar contadores
    loadOrphanQuestions();
  };

  const approveAll = () => {
    const pendingQuestions = processedQuestions.filter(p => p.status === 'pending' &&
      (analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus)
    );

    setProcessedQuestions(prev => prev.map(p => {
      const hasValidSuggestion = analysisType === 'theme' ? p.suggestion.suggestedTheme : p.suggestion.suggestedFocus;
      return p.status === 'pending' && hasValidSuggestion ? { ...p, status: 'approved' } : p;
    }));

    toast.success(`${pendingQuestions.length} sugestões aprovadas em lote!`);
    console.log(`✅ Aprovação em lote: ${pendingQuestions.length} questões aprovadas`);
  };

  const unapproveAll = () => {
    setProcessedQuestions(prev => prev.map(p => ({ ...p, status: 'pending' })));
    toast.info('Todas as aprovações foram removidas');
  };

  const startAutoProcessing = () => {
    if (isAutoProcessing) return;



    setIsAutoProcessing(true);
    setAutoProcessStats({
      cyclesCompleted: 0,
      totalQuestionsProcessed: 0,
      totalSuggestions: 0,
      totalApplied: 0,
      startTime: new Date()
    });

    // Executar primeiro ciclo imediatamente
    executeAutoProcessCycle();

    // Configurar intervalo de 1.5 minutos (90 segundos)
    const interval = setInterval(() => {
      executeAutoProcessCycle();
    }, 90000);

    setAutoProcessInterval(interval);

    // Calcular próximo processamento
    const nextTime = new Date();
    nextTime.setSeconds(nextTime.getSeconds() + 90);
    setNextProcessTime(nextTime);

    toast.success('Processamento automático iniciado! Executando a cada 1.5 minutos.');
  };

  const stopAutoProcessing = () => {
    if (!isAutoProcessing) return;

    console.log(`🛑 Parando processamento automático...`);
    console.log(`📊 Estatísticas finais:`, autoProcessStats);

    setIsAutoProcessing(false);
    setNextProcessTime(null);

    if (autoProcessInterval) {
      clearInterval(autoProcessInterval);
      setAutoProcessInterval(null);
    }

    toast.info('Processamento automático interrompido.');
  };

  const executeAutoProcessCycle = async () => {
    try {
      console.log(`\n🔄 Executando ciclo automático ${autoProcessStats.cyclesCompleted + 1}...`);

      // Verificar se ainda há questões órfãs
      await loadOrphanQuestions();

      const questionsAvailable = analysisType === 'theme' ? questionsWithoutTheme : questionsWithoutFocus;

      if (questionsAvailable === 0) {
        console.log(`✅ Não há mais questões órfãs! Parando processamento automático.`);
        stopAutoProcessing();
        toast.success('Processamento automático concluído! Todas as questões foram categorizadas.');
        return;
      }

      console.log(`📊 Questões órfãs disponíveis: ${questionsAvailable}`);

      // Limpar questões processadas anteriores
      setProcessedQuestions([]);

      // Executar processamento com auto-aprovação e aplicação
      const appliedCount = await analyzeMultipleBatches(true);

      // Atualizar estatísticas
      setAutoProcessStats(prev => ({
        ...prev,
        cyclesCompleted: prev.cyclesCompleted + 1,
        totalQuestionsProcessed: prev.totalQuestionsProcessed + (numberOfBatches * batchSize),
        totalSuggestions: prev.totalSuggestions + appliedCount,
        totalApplied: prev.totalApplied + appliedCount
      }));

      console.log(`✅ Ciclo ${autoProcessStats.cyclesCompleted + 1} concluído: ${appliedCount} questões aplicadas automaticamente`);

      // Calcular próximo processamento
      const nextTime = new Date();
      nextTime.setSeconds(nextTime.getSeconds() + 90);
      setNextProcessTime(nextTime);

    } catch (error) {
      console.error('❌ Erro no ciclo automático:', error);
      toast.error('Erro no processamento automático. Continuando...');

      // Ainda assim atualizar o contador de ciclos
      setAutoProcessStats(prev => ({
        ...prev,
        cyclesCompleted: prev.cyclesCompleted + 1
      }));

      // Calcular próximo processamento mesmo com erro
      const nextTime = new Date();
      nextTime.setSeconds(nextTime.getSeconds() + 90);
      setNextProcessTime(nextTime);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Categorização de Questões</h1>
          <p className="text-muted-foreground">
            Categorize questões órfãs (sem tema ou foco) usando IA
          </p>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Seleção e Configuração</CardTitle>
            <CardDescription>
              Escolha a especialidade, tipo de análise e configure o lote
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Especialidade</label>
                <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma especialidade" />
                  </SelectTrigger>
                  <SelectContent>
                    {specialties.map((specialty) => (
                      <SelectItem key={specialty.id} value={specialty.id}>
                        {specialty.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  {analysisType === 'focus' ? (
                    <span className="text-red-600">Tema (obrigatório para análise de focos) *</span>
                  ) : (
                    <span className="text-gray-500">Tema (não usado para análise de temas)</span>
                  )}
                </label>
                <Select
                  value={selectedTheme || ''}
                  onValueChange={setSelectedTheme}
                  disabled={!selectedSpecialty || analysisType === 'theme'}
                >
                  <SelectTrigger className={analysisType === 'focus' && !selectedTheme ? 'border-red-300' : ''}>
                    <SelectValue placeholder={
                      analysisType === 'focus'
                        ? "Selecione o tema para buscar focos"
                        : "Não necessário para análise de temas"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map((theme) => (
                      <SelectItem key={theme.id} value={theme.id}>
                        {theme.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {analysisType === 'focus' && !selectedTheme && (
                  <p className="text-xs text-red-600 mt-1">
                    ⚠️ Selecione um tema para carregar os focos disponíveis para categorização
                  </p>
                )}
                {analysisType === 'focus' && selectedTheme && (
                  <p className="text-xs text-green-600 mt-1">
                    ✅ Tema selecionado: {themes.find(t => t.id === selectedTheme)?.name} - Focos carregados: {focuses.length}
                  </p>
                )}
              </div>

              {/* Seleção de Foco para Processamento Sequencial */}
              {analysisType === 'focus' && selectedTheme && focuses.length > 0 && (
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    <span className="text-blue-600">Foco Inicial (para processamento sequencial)</span>
                  </label>
                  <Select
                    value={selectedFocus || ''}
                    onValueChange={setSelectedFocus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o foco para iniciar" />
                    </SelectTrigger>
                    <SelectContent>
                      {focuses.map((focus) => (
                        <SelectItem key={focus.id} value={focus.id}>
                          {focus.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedFocus && (
                    <p className="text-xs text-blue-600 mt-1">
                      🎯 Iniciará em: {focuses.find(f => f.id === selectedFocus)?.name} e continuará até o final
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Estatísticas */}
            {selectedSpecialty && (
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{questionsWithoutTheme}</div>
                  <div className="text-xs text-muted-foreground">Sem Tema</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{questionsWithoutFocus}</div>
                  <div className="text-xs text-muted-foreground">Sem Foco</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{numberOfBatches * batchSize}</div>
                  <div className="text-xs text-muted-foreground">Máx. Processadas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {processedQuestions.filter(p => p.status === 'approved').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Aprovadas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {processedQuestions.filter(p => p.status === 'applied').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Aplicadas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{processedQuestions.length}</div>
                  <div className="text-xs text-muted-foreground">Categorizadas</div>
                </div>
              </div>
            )}

            {/* Configuração de API Keys */}
            <div className="pt-4 border-t space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Chave API (Modo Manual)</label>
                  <Select value={selectedApiKey} onValueChange={setSelectedApiKey} disabled={apiKeyRotation}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gemini_key1">Gemini 1 (GEMINI_API_KEY)</SelectItem>
                      <SelectItem value="gemini_key2">Gemini 2 (GEMINI_API_KEY2)</SelectItem>
                      <SelectItem value="gemini_key3">Gemini 3 (GEMINI_API_KEY3)</SelectItem>
                      <SelectItem value="gemini_key4">Gemini 4 (GEMINI_API_KEY4)</SelectItem>
                      <SelectItem value="gemini_key5">Gemini 5 (GEMINI_API_KEY5)</SelectItem>
                      <SelectItem value="gemini_key6">Gemini 6 (GEMINI_API_KEY6)</SelectItem>
                      <SelectItem value="openai_key1">OpenAI GPT-4o Mini (OPENAI_API_KEY)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-6">
                  <input
                    type="checkbox"
                    id="apiKeyRotation"
                    checked={apiKeyRotation}
                    onChange={(e) => setApiKeyRotation(e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="apiKeyRotation" className="text-sm font-medium">
                    Rotação Automática
                  </label>
                </div>
              </div>

              {/* Seleção de Chaves para Rotação */}
              {apiKeyRotation && (
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <label className="text-sm font-medium mb-3 block">Chaves para Rotação Automática</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {[
                      { id: 'gemini_key1', name: 'Gemini 1', env: 'GEMINI_API_KEY', type: 'gemini' },
                      { id: 'gemini_key2', name: 'Gemini 2', env: 'GEMINI_API_KEY2', type: 'gemini' },
                      { id: 'gemini_key3', name: 'Gemini 3', env: 'GEMINI_API_KEY3', type: 'gemini' },
                      { id: 'gemini_key4', name: 'Gemini 4', env: 'GEMINI_API_KEY4', type: 'gemini' },
                      { id: 'gemini_key5', name: 'Gemini 5', env: 'GEMINI_API_KEY5', type: 'gemini' },
                      { id: 'gemini_key6', name: 'Gemini 6', env: 'GEMINI_API_KEY6', type: 'gemini' },
                      { id: 'openai_key1', name: 'OpenAI GPT-4o Mini', env: 'OPENAI_API_KEY', type: 'openai' }
                    ].map((key) => (
                      <div key={key.id} className="flex items-center space-x-2 p-2 bg-white rounded border">
                        <input
                          type="checkbox"
                          id={`rotation_${key.id}`}
                          checked={rotationKeys.includes(key.id)}
                          onChange={() => toggleRotationKey(key.id)}
                          className="rounded"
                        />
                        <label htmlFor={`rotation_${key.id}`} className="text-sm flex-1">
                          <span className={`font-medium ${key.type === 'openai' ? 'text-green-600' : 'text-blue-600'}`}>
                            {key.name}
                          </span>
                          <div className="text-xs text-muted-foreground">{key.env}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3 text-xs text-muted-foreground">
                    <span className="text-blue-600">
                      🔄 Rotação ativa: {rotationKeys.length} chave{rotationKeys.length !== 1 ? 's' : ''} selecionada{rotationKeys.length !== 1 ? 's' : ''}
                    </span>
                    <div className="mt-1">
                      Ordem: {rotationKeys.map((key, index) => {
                        const keyInfo = {
                          gemini_key1: 'G1', gemini_key2: 'G2', gemini_key3: 'G3', gemini_key4: 'G4',
                          gemini_key5: 'G5', gemini_key6: 'G6', openai_key1: 'AI'
                        }[key];
                        return keyInfo;
                      }).join(' → ')}
                    </div>
                  </div>
                </div>
              )}

              {/* Status da Configuração */}
              <div className="text-xs text-muted-foreground">
                {!apiKeyRotation ? (
                  <div>
                    <span>🔑 Chave fixa: {selectedApiKey.includes('openai') ? 'OpenAI GPT-4o Mini' : 'Gemini 2.5 Flash'}</span>
                    {selectedApiKey.includes('gemini') && (
                      <div className="mt-1 text-orange-600">
                        ⚠️ Gemini: Use máximo 5-8 questões por lote para evitar truncamento
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <span className="text-blue-600">
                      🔄 Rotação entre {rotationKeys.length} chave{rotationKeys.length !== 1 ? 's' : ''}
                    </span>
                    {rotationKeys.some(key => key.includes('gemini')) && (
                      <div className="mt-1 text-orange-600">
                        ⚠️ Lotes com Gemini: Use máximo 5-8 questões para evitar truncamento
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Modo de Processamento */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Modo de Processamento</label>
                  <Select value={processingMode} onValueChange={(value: 'parallel' | 'sequential') => setProcessingMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sequential">Sequencial (Mais Estável)</SelectItem>
                      <SelectItem value="parallel">Paralelo (Mais Rápido)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-xs text-muted-foreground pt-6">
                  {processingMode === 'sequential' ? (
                    <span className="text-green-600">
                      🐌 Sequencial: Um lote por vez, delay de 3s, mais estável
                    </span>
                  ) : (
                    <span className="text-orange-600">
                      ⚡ Paralelo: Todos simultâneos, mais rápido, pode dar timeout
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Controles */}
            {selectedSpecialty && (
              <div className="pt-4 border-t space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Tipo de Análise</label>
                    <Select value={analysisType} onValueChange={(value: 'theme' | 'focus') => setAnalysisType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="theme">Categorizar por Tema</SelectItem>
                        <SelectItem value="focus">Categorizar por Foco</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Questões por Lote</label>
                    <Select value={batchSize.toString()} onValueChange={(value) => setBatchSize(parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 questões</SelectItem>
                        <SelectItem value="10">10 questões</SelectItem>
                        <SelectItem value="15">15 questões</SelectItem>
                        <SelectItem value="20">20 questões</SelectItem>
                        <SelectItem value="25">25 questões</SelectItem>
                        <SelectItem value="50">50 questões</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Número de Lotes</label>
                    <Select value={numberOfBatches.toString()} onValueChange={(value) => setNumberOfBatches(parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 lote</SelectItem>
                        <SelectItem value="2">2 lotes</SelectItem>
                        <SelectItem value="3">3 lotes</SelectItem>
                        <SelectItem value="5">5 lotes</SelectItem>
                        <SelectItem value="10">10 lotes</SelectItem>
                        <SelectItem value="20">20 lotes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-2">
                    <Button
                      onClick={() => analyzeMultipleBatches()}
                      disabled={isAnalyzing || isAutoProcessing || isProcessingAllFocuses || loading || !selectedSpecialty || (analysisType === 'focus' && !selectedTheme)}
                      className="w-full"
                    >
                      {isAnalyzing ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          {processingMode === 'sequential' ? 'Sequencial' : 'Paralelo'} {numberOfBatches} lotes...
                        </>
                      ) : (
                        <>
                          <Target className="h-4 w-4 mr-2" />
                          {processingMode === 'sequential' ? 'Sequencial' : 'Paralelo'} {numberOfBatches}x{batchSize}
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={isAutoProcessing ? stopAutoProcessing : startAutoProcessing}
                      disabled={isAnalyzing || isProcessingAllFocuses || loading || !selectedSpecialty || (analysisType === 'focus' && !selectedTheme)}
                      variant={isAutoProcessing ? "destructive" : "secondary"}
                      className="w-full"
                    >
                      {isAutoProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Parar Automático
                        </>
                      ) : (
                        <>
                          <Target className="h-4 w-4 mr-2" />
                          Iniciar Automático (1.5min)
                        </>
                      )}
                    </Button>

                    {/* Botão para processamento sequencial de temas */}
                    {analysisType === 'focus' && selectedTheme && themes.length > 0 && (
                      <Button
                        onClick={processSequentialThemesFromSelected}
                        disabled={isAnalyzing || isAutoProcessing || isProcessingAllFocuses || isProcessingSequentialFocuses || isProcessingSequentialThemes || loading}
                        variant="default"
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                      >
                        {isProcessingSequentialThemes ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Tema {sequentialThemeStats.currentThemeIndex + 1}/{sequentialThemeStats.totalThemes}...
                          </>
                        ) : (
                          <>
                            <Target className="h-4 w-4 mr-2" />
                            Iniciar de "{themes.find(t => t.id === selectedTheme)?.name}" até o Final
                          </>
                        )}
                      </Button>
                    )}

                    {/* Botão para processamento sequencial a partir de um foco */}
                    {analysisType === 'focus' && selectedTheme && selectedFocus && focuses.length > 0 && (
                      <Button
                        onClick={processSequentialFocusesFromSelected}
                        disabled={isAnalyzing || isAutoProcessing || isProcessingAllFocuses || isProcessingSequentialFocuses || isProcessingSequentialThemes || loading}
                        variant="default"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {isProcessingSequentialFocuses ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Sequencial {sequentialFocusStats.currentFocusIndex + 1}/{sequentialFocusStats.totalFocuses}...
                          </>
                        ) : (
                          <>
                            <Target className="h-4 w-4 mr-2" />
                            Iniciar de "{focuses.find(f => f.id === selectedFocus)?.name}" até o Final
                          </>
                        )}
                      </Button>
                    )}

                    {/* Botão para processar todos os focos */}
                    {analysisType === 'focus' && selectedTheme && focuses.length > 0 && (
                      <Button
                        onClick={processAllFocusesInTheme}
                        disabled={isAnalyzing || isAutoProcessing || isProcessingAllFocuses || isProcessingSequentialFocuses || isProcessingSequentialThemes || loading}
                        variant="outline"
                        className="w-full border-purple-300 text-purple-700 hover:bg-purple-50"
                      >
                        {isProcessingAllFocuses ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Processando {allFocusesStats.currentFocusIndex}/{allFocusesStats.totalFocuses} focos...
                          </>
                        ) : (
                          <>
                            <Target className="h-4 w-4 mr-2" />
                            Processar TODOS os {focuses.length} Focos
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>

                {/* Status do Processamento Sequencial de Temas */}
                {isProcessingSequentialThemes && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-green-800">Processamento Sequencial de Temas</span>
                      </div>
                      <div className="text-sm text-green-600">
                        {sequentialThemeStats.currentThemeName}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-green-600">
                          {sequentialThemeStats.currentThemeIndex + 1}/{sequentialThemeStats.totalThemes}
                        </div>
                        <div className="text-xs text-muted-foreground">Temas</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-purple-600">{sequentialThemeStats.totalSuggestions}</div>
                        <div className="text-xs text-muted-foreground">Sugestões</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-blue-600">{sequentialThemeStats.totalApplied}</div>
                        <div className="text-xs text-muted-foreground">Aplicadas</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-orange-600">
                          {sequentialThemeStats.startTime ? Math.round((new Date().getTime() - sequentialThemeStats.startTime.getTime()) / 60000) : 0}min
                        </div>
                        <div className="text-xs text-muted-foreground">Tempo</div>
                      </div>
                    </div>

                    {/* Barra de progresso */}
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progresso dos Temas</span>
                        <span>{sequentialThemeStats.currentThemeIndex + 1} / {sequentialThemeStats.totalThemes}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${((sequentialThemeStats.currentThemeIndex + 1) / sequentialThemeStats.totalThemes) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="text-sm text-muted-foreground text-center">
                      🎯 Iniciado em: {themes.find(t => t.id === selectedTheme)?.name} |
                      Processando até o final da especialidade: {specialties.find(s => s.id === selectedSpecialty)?.name}
                    </div>
                  </div>
                )}

                {/* Status do Processamento Sequencial de Focos */}
                {isProcessingSequentialFocuses && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-blue-800">Processamento Sequencial de Focos</span>
                      </div>
                      <div className="text-sm text-blue-600">
                        {sequentialFocusStats.currentFocusName}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-blue-600">
                          {sequentialFocusStats.currentFocusIndex + 1}/{sequentialFocusStats.totalFocuses}
                        </div>
                        <div className="text-xs text-muted-foreground">Focos</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-purple-600">{sequentialFocusStats.totalSuggestions}</div>
                        <div className="text-xs text-muted-foreground">Sugestões</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-green-600">{sequentialFocusStats.totalApplied}</div>
                        <div className="text-xs text-muted-foreground">Aplicadas</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-orange-600">
                          {sequentialFocusStats.startTime ? Math.round((new Date().getTime() - sequentialFocusStats.startTime.getTime()) / 60000) : 0}min
                        </div>
                        <div className="text-xs text-muted-foreground">Tempo</div>
                      </div>
                    </div>

                    {/* Barra de progresso */}
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progresso dos Focos</span>
                        <span>{sequentialFocusStats.currentFocusIndex + 1} / {sequentialFocusStats.totalFocuses}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${((sequentialFocusStats.currentFocusIndex + 1) / sequentialFocusStats.totalFocuses) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="text-sm text-muted-foreground text-center">
                      🎯 Iniciado em: {focuses.find(f => f.id === selectedFocus)?.name} |
                      Processando até o final do tema: {themes.find(t => t.id === selectedTheme)?.name}
                    </div>
                  </div>
                )}

                {/* Status do Processamento de Todos os Focos */}
                {isProcessingAllFocuses && (
                  <div className="mt-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-purple-800">Processando Todos os Focos</span>
                      </div>
                      <div className="text-sm text-purple-600">
                        {allFocusesStats.currentFocusName}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-purple-600">
                          {allFocusesStats.currentFocusIndex}/{allFocusesStats.totalFocuses}
                        </div>
                        <div className="text-xs text-muted-foreground">Focos</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-blue-600">{allFocusesStats.totalSuggestions}</div>
                        <div className="text-xs text-muted-foreground">Sugestões</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-green-600">{allFocusesStats.totalApplied}</div>
                        <div className="text-xs text-muted-foreground">Aplicadas</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-orange-600">
                          {allFocusesStats.startTime ? Math.round((new Date().getTime() - allFocusesStats.startTime.getTime()) / 60000) : 0}min
                        </div>
                        <div className="text-xs text-muted-foreground">Tempo</div>
                      </div>
                    </div>

                    {/* Barra de progresso */}
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progresso dos Focos</span>
                        <span>{allFocusesStats.currentFocusIndex} / {allFocusesStats.totalFocuses}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(allFocusesStats.currentFocusIndex / allFocusesStats.totalFocuses) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="text-sm text-muted-foreground text-center">
                      🎯 Processando automaticamente todos os focos do tema: {themes.find(t => t.id === selectedTheme)?.name}
                    </div>
                  </div>
                )}

                {/* Status do Processamento Automático */}
                {isAutoProcessing && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-green-800">Processamento Automático Ativo</span>
                      </div>
                      <Button
                        onClick={stopAutoProcessing}
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Parar
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-green-600">{autoProcessStats.cyclesCompleted}</div>
                        <div className="text-xs text-muted-foreground">Ciclos</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-blue-600">{autoProcessStats.totalSuggestions}</div>
                        <div className="text-xs text-muted-foreground">Sugestões</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-purple-600">{autoProcessStats.totalApplied}</div>
                        <div className="text-xs text-muted-foreground">Aplicadas</div>
                      </div>
                      <div className="text-center p-2 bg-white rounded border">
                        <div className="text-lg font-bold text-orange-600">
                          {autoProcessStats.startTime ? Math.round((new Date().getTime() - autoProcessStats.startTime.getTime()) / 60000) : 0}min
                        </div>
                        <div className="text-xs text-muted-foreground">Tempo</div>
                      </div>
                    </div>

                    {nextProcessTime && (
                      <div className="text-sm text-muted-foreground text-center">
                        🕐 Próximo processamento: {nextProcessTime.toLocaleTimeString()}
                        <span className="ml-2">
                          (em {Math.max(0, Math.round((nextProcessTime.getTime() - new Date().getTime()) / 1000))}s)
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Progresso dos Lotes */}
                {isAnalyzing && totalBatches > 1 && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg border">
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progresso dos Lotes</span>
                      <span>{currentBatch} / {totalBatches}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(currentBatch / totalBatches) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-muted-foreground mt-2">
                      📊 Processadas: {overallStats.totalQuestionsProcessed} questões |
                      Sugestões: {overallStats.totalSuggestions} |
                      Lotes: {overallStats.totalBatchesCompleted}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Resultados */}
        {processedQuestions.length > 0 && (
          <QuestionResultsCard
            processedQuestions={processedQuestions}
            analysisType={analysisType}
            onApprove={approveSuggestion}
            onSkip={skipQuestion}
            onApplyAll={applyAllApproved}
            onApproveAll={approveAll}
            onUnapproveAll={unapproveAll}
          />
        )}

        {/* Estado vazio */}
        {!loading && selectedSpecialty && questionsWithoutTheme === 0 && questionsWithoutFocus === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-8 text-center space-y-3">
                <CheckCircle className="h-12 w-12 text-green-500" />
                <p className="text-lg font-medium">Todas as questões estão categorizadas!</p>
                <p className="text-muted-foreground">
                  Não há questões órfãs nesta especialidade.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default QuestionCategorization;
