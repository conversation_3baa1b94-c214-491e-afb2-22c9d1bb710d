
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { UserRound, KeyRound } from "lucide-react";
import SignInForm from "./SignInForm";
import SignUpForm from "./SignUpForm";
import ResetPasswordForm from "./ResetPasswordForm";
import "./AuthDialog.css";

interface AuthDialogProps {
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  hidden?: boolean;
  open?: boolean;
  onSuccess?: () => void;
  defaultMode?: "signin" | "signup" | "reset";
}

const AuthDialog = ({ defaultOpen, onOpenChange, hidden, open, onSuccess, defaultMode = "signin" }: AuthDialogProps = {}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || false);
  const [mode, setMode] = useState<"signin" | "signup" | "reset">(defaultMode);

  // Reset mode when dialog opens
  useEffect(() => {
    if (open) {
      setMode(defaultMode);
    }
  }, [open, defaultMode]);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    onOpenChange?.(open);
  };

  const handleSuccess = () => {
    setIsOpen(false);
    onOpenChange?.(false);
    onSuccess?.();
  };

  // Use controlled open state if provided, otherwise use internal state
  const dialogOpen = open !== undefined ? open : isOpen;

  const getTitle = () => {
    switch (mode) {
      case "signin":
        return "Acesse sua conta";
      case "signup":
        return "Crie sua conta";
      case "reset":
        return "Recupere sua senha";
    }
  };

  const getDescription = () => {
    switch (mode) {
      case "signin":
        return "Entre para acessar questões exclusivas e seu progresso de estudos";
      case "signup":
        return "Registre-se para começar sua jornada de estudos médicos";
      case "reset":
        return "Digite seu email para receber um link de recuperação";
    }
  };

  const getPedBookNotice = () => {
    switch (mode) {
      case "signin":
        return "💡 Já tem conta no PedBook? Use os mesmos dados para entrar!";
      case "signup":
        return "💡 Esta plataforma usa os mesmos dados do PedBook. Se já tem conta lá, faça login!";
      case "reset":
        return "💡 Use o mesmo email da sua conta PedBook";
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {!hidden && (
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <UserRound className="h-4 w-4" />
            Entrar
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="w-[85dvw] max-w-md border-2 border-black bg-[#FEF7CD] max-h-[90dvh] overflow-y-auto p-4 sm:p-6 rounded-xl shadow-card">
        <DialogHeader className="space-y-4 text-center pr-0">
          <DialogTitle className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900">
            {getTitle()}
          </DialogTitle>
          <DialogDescription className="text-gray-600 text-sm sm:text-base px-1">
            {getDescription()}
          </DialogDescription>

          {/* PedBook Integration Notice */}
          <div className="bg-hackathon-yellow/20 border-2 border-hackathon-yellow rounded-lg p-3 mx-1">
            <p className="text-xs sm:text-sm text-gray-800 font-medium">
              {getPedBookNotice()}
            </p>
          </div>
        </DialogHeader>
        {mode === "signin" ? (
          <SignInForm
            onModeChange={() => setMode("signup")}
            onResetPassword={() => setMode("reset")}
            onSuccess={handleSuccess}
          />
        ) : mode === "signup" ? (
          <SignUpForm
            onModeChange={() => setMode("signin")}
            onSuccess={handleSuccess}
          />
        ) : (
          <ResetPasswordForm onBackToLogin={() => setMode("signin")} />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
