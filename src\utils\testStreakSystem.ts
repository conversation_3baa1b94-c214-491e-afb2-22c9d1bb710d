/**
 * Utilitários para testar e validar o sistema de sequência de estudos
 */

import { supabase } from '@/integrations/supabase/client';
import { calculateImprovedStreakStats } from './sessionTransformers';

export interface StreakTestResult {
  userId: string;
  oldMethod: {
    currentStreak: number;
    maxStreak: number;
    calculationTime: number;
  };
  newMethod: {
    currentStreak: number;
    maxStreak: number;
    calculationTime: number;
  };
  sqlMethod: {
    currentStreak: number;
    maxStreak: number;
    calculationTime: number;
  };
  differences: {
    currentStreakDiff: number;
    maxStreakDiff: number;
    performanceImprovement: number;
  };
  activities: {
    totalSessions: number;
    totalAnswers: number;
    totalScheduleItems: number;
    uniqueDates: number;
  };
}

/**
 * Testa o sistema de sequência para um usuário específico
 */
export const testStreakSystemForUser = async (userId: string): Promise<StreakTestResult> => {
  console.log(`🧪 [testStreakSystem] Testing streak system for user: ${userId}`);

  // Obter dados brutos para análise
  const [sessionsResult, answersResult, scheduleResult] = await Promise.all([
    supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'completed'),
    
    supabase
      .from('user_answers')
      .select('created_at')
      .eq('user_id', userId),
    
    supabase
      .from('study_schedule_items')
      .select('last_revision_date')
      .eq('user_id', userId)
      .eq('study_status', 'completed')
      .not('last_revision_date', 'is', null)
  ]);

  const sessions = sessionsResult.data || [];
  const answers = answersResult.data || [];
  const scheduleItems = scheduleResult.data || [];

  // Calcular estatísticas de atividades
  const allDates = new Set<string>();
  
  sessions.forEach(session => {
    if (session.completed_at) {
      allDates.add(new Date(session.completed_at).toISOString().split('T')[0]);
    }
  });
  
  answers.forEach(answer => {
    allDates.add(new Date(answer.created_at).toISOString().split('T')[0]);
  });
  
  scheduleItems.forEach(item => {
    if (item.last_revision_date) {
      allDates.add(new Date(item.last_revision_date).toISOString().split('T')[0]);
    }
  });

  // Teste método antigo (apenas sessões)
  const oldMethodStart = performance.now();
  const oldResult = calculateOldMethod(sessions);
  const oldMethodTime = performance.now() - oldMethodStart;

  // Teste método novo (JavaScript)
  const newMethodStart = performance.now();
  const newResult = await calculateImprovedStreakStats(userId, supabase);
  const newMethodTime = performance.now() - newMethodStart;

  // Teste método SQL
  const sqlMethodStart = performance.now();
  const { data: sqlResult } = await supabase
    .rpc('calculate_user_study_streak', {
      p_user_id: userId,
      p_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
    });
  const sqlMethodTime = performance.now() - sqlMethodStart;

  const sqlStats = sqlResult?.[0] || { current_streak: 0, max_streak: 0 };

  // Calcular diferenças
  const currentStreakDiff = newResult.currentStreak - oldResult.currentStreak;
  const maxStreakDiff = newResult.maxStreak - oldResult.maxStreak;
  const performanceImprovement = ((oldMethodTime - sqlMethodTime) / oldMethodTime) * 100;

  const result: StreakTestResult = {
    userId,
    oldMethod: {
      currentStreak: oldResult.currentStreak,
      maxStreak: oldResult.maxStreak,
      calculationTime: oldMethodTime
    },
    newMethod: {
      currentStreak: newResult.currentStreak,
      maxStreak: newResult.maxStreak,
      calculationTime: newMethodTime
    },
    sqlMethod: {
      currentStreak: sqlStats.current_streak,
      maxStreak: sqlStats.max_streak,
      calculationTime: sqlMethodTime
    },
    differences: {
      currentStreakDiff,
      maxStreakDiff,
      performanceImprovement
    },
    activities: {
      totalSessions: sessions.length,
      totalAnswers: answers.length,
      totalScheduleItems: scheduleItems.length,
      uniqueDates: allDates.size
    }
  };

  console.log(`✅ [testStreakSystem] Test completed for user ${userId}:`, result);
  return result;
};

/**
 * Implementação do método antigo para comparação
 */
const calculateOldMethod = (sessions: any[]) => {
  const today = new Date().setHours(0, 0, 0, 0);
  let currentStreak = 0;
  let maxStreak = 0;

  if (!sessions || sessions.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  const uniqueDates = [...new Set(
    sessions
      .filter(session => session.status === 'completed')
      .map(session => {
        const date = new Date(session.started_at);
        return date.setHours(0, 0, 0, 0);
      })
  )].sort((a, b) => b - a);

  if (uniqueDates.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  // Lógica original simplificada
  const hasActivityToday = uniqueDates[0] === today;
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);

  if (hasActivityToday || uniqueDates[0] === yesterday.getTime()) {
    currentStreak = 1;
    for (let i = 1; i < uniqueDates.length; i++) {
      const expectedPreviousDay = new Date(uniqueDates[i - 1]);
      expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
      expectedPreviousDay.setHours(0, 0, 0, 0);

      if (uniqueDates[i] === expectedPreviousDay.getTime()) {
        currentStreak++;
      } else {
        break;
      }
    }
  }

  // Calcular max streak
  let tempStreak = 1;
  for (let i = 1; i < uniqueDates.length; i++) {
    const currentDate = new Date(uniqueDates[i - 1]);
    const expectedPreviousDay = new Date(currentDate);
    expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
    expectedPreviousDay.setHours(0, 0, 0, 0);
    
    if (uniqueDates[i] === expectedPreviousDay.getTime()) {
      tempStreak++;
    } else {
      maxStreak = Math.max(maxStreak, tempStreak);
      tempStreak = 1;
    }
  }
  maxStreak = Math.max(maxStreak, tempStreak);

  return { currentStreak, maxStreak };
};

/**
 * Executa testes em lote para múltiplos usuários
 */
export const runBatchStreakTests = async (userIds: string[]): Promise<StreakTestResult[]> => {
  console.log(`🧪 [runBatchStreakTests] Testing ${userIds.length} users...`);
  
  const results: StreakTestResult[] = [];
  
  for (const userId of userIds) {
    try {
      const result = await testStreakSystemForUser(userId);
      results.push(result);
    } catch (error) {
      console.error(`❌ [runBatchStreakTests] Error testing user ${userId}:`, error);
    }
  }
  
  return results;
};

/**
 * Gera relatório de comparação dos métodos
 */
export const generateComparisonReport = (results: StreakTestResult[]) => {
  const totalTests = results.length;
  const improvements = results.filter(r => r.differences.currentStreakDiff > 0).length;
  const avgPerformanceGain = results.reduce((sum, r) => sum + r.differences.performanceImprovement, 0) / totalTests;
  
  const report = {
    summary: {
      totalTests,
      improvements,
      improvementRate: (improvements / totalTests) * 100,
      avgPerformanceGain
    },
    details: results.map(r => ({
      userId: r.userId,
      oldStreak: r.oldMethod.currentStreak,
      newStreak: r.newMethod.currentStreak,
      sqlStreak: r.sqlMethod.currentStreak,
      improvement: r.differences.currentStreakDiff,
      activities: r.activities.uniqueDates,
      performanceGain: r.differences.performanceImprovement
    }))
  };

  console.log('📊 [generateComparisonReport] Streak System Comparison Report:', report);
  return report;
};

/**
 * Valida consistência entre métodos
 */
export const validateConsistency = (results: StreakTestResult[]) => {
  const inconsistencies = results.filter(r => 
    r.newMethod.currentStreak !== r.sqlMethod.currentStreak ||
    r.newMethod.maxStreak !== r.sqlMethod.maxStreak
  );

  if (inconsistencies.length > 0) {
    console.warn('⚠️ [validateConsistency] Found inconsistencies between JS and SQL methods:', inconsistencies);
    return false;
  }

  console.log('✅ [validateConsistency] All methods are consistent');
  return true;
};
