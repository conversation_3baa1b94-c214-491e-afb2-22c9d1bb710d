
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { Check, X, ChevronRight, Brain, Copy } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface SimilarFocus {
  id: string;
  name: string;
  question_count: number;
  similarity: number;
}

interface ConsolidationGroup {
  group_id: string;
  main_focus_name: string;
  similar_focuses: SimilarFocus[];
  similarity_score: number;
  total_questions: number;
  status: "pending" | "approved" | "rejected";
}

interface Theme {
  id: string;
  name: string;
}

export const FocusConsolidation = () => {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [suggestions, setSuggestions] = useState<ConsolidationGroup[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [processingGroup, setProcessingGroup] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const loadThemes = async () => {
      console.log("🔄 [loadThemes] Iniciando carregamento de temas...");
      const { data, error } = await supabase
        .from("study_categories")
        .select("id, name")
        .eq("type", "theme");

      if (error) {
        console.error("❌ [loadThemes] Erro ao carregar temas:", error);
        toast({
          title: "Erro ao carregar temas",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      console.log("✅ [loadThemes] Temas carregados com sucesso:", data);
      setThemes(data || []);
    };

    loadThemes();
  }, [toast]);

  const generateSuggestions = async (themeId: string) => {
    setIsLoading(true);
    console.log("🔄 [generateSuggestions] Iniciando para tema:", themeId);

    try {
      // Primeiro, buscar grupos pendentes existentes
      console.log("🔍 [generateSuggestions] Buscando grupos pendentes...");
      const { data: existingGroups, error: existingGroupsError } = await supabase
        .from("focus_consolidation_groups")
        .select("id")
        .eq("theme_id", themeId)
        .eq("status", "pending");

      if (existingGroupsError) {
        console.error("❌ [generateSuggestions] Erro ao buscar grupos existentes:", existingGroupsError);
        throw existingGroupsError;
      }

      console.log("📊 [generateSuggestions] Grupos pendentes encontrados:", existingGroups);

      // Primeiro, limpar os mapeamentos antigos
      if (existingGroups && existingGroups.length > 0) {
        const groupIds = existingGroups.map(g => g.id);
        console.log("🧹 [generateSuggestions] Limpando mapeamentos para grupos:", groupIds);

        const { error: cleanMappingsError } = await supabase
          .from("focus_consolidation_mappings")
          .delete()
          .in("group_id", groupIds);

        if (cleanMappingsError) {
          console.error("❌ [generateSuggestions] Erro ao limpar mapeamentos:", cleanMappingsError);
          throw cleanMappingsError;
        }
      }

      // Depois, limpar os grupos antigos

      const { error: cleanGroupsError } = await supabase
        .from("focus_consolidation_groups")
        .delete()
        .eq("theme_id", themeId)
        .eq("status", "pending");

      if (cleanGroupsError) {
        console.error("❌ [generateSuggestions] Erro ao limpar grupos:", cleanGroupsError);
        throw cleanGroupsError;
      }

      // Gerar novas sugestões

      const { error: genError } = await supabase.rpc("generate_consolidation_groups", {
        p_theme_id: themeId,
      });

      if (genError) {
        console.error("❌ [generateSuggestions] Erro ao gerar sugestões:", genError);
        throw genError;
      }

      // Carregar apenas as sugestões pendentes
      console.log("📥 [generateSuggestions] Carregando sugestões geradas...");
      const { data: suggestionsData, error: listError } = await supabase.rpc("list_consolidation_suggestions", {
        p_theme_id: themeId,
      });

      if (listError) {
        console.error("❌ [generateSuggestions] Erro ao listar sugestões:", listError);
        throw listError;
      }

      console.log("📦 [generateSuggestions] Dados brutos recebidos:", suggestionsData);

      if (!suggestionsData) {
        console.log("ℹ️ [generateSuggestions] Nenhuma sugestão encontrada");
        setSuggestions([]);
        return;
      }

      const parsedSuggestions = suggestionsData
        .filter((item: any) => item?.status === "pending")
        .map((item: any) => ({
          group_id: item?.group_id || "",
          main_focus_name: item?.main_focus_name || "",
          similar_focuses: Array.isArray(item?.similar_focuses)
            ? item.similar_focuses.map((focus: any) => ({
                id: focus?.id || "",
                name: focus?.name || "",
                question_count: focus?.question_count || 0,
                similarity: focus?.similarity || 0
              }))
            : [],
          similarity_score: item?.similarity_score || 0,
          total_questions: item?.total_questions || 0,
          status: item?.status || "pending"
        }));


      setSuggestions(parsedSuggestions);

      toast({
        title: "Sugestões geradas com sucesso",
        description: `${parsedSuggestions.length} grupos de consolidação encontrados`,
      });
    } catch (error: any) {
      console.error("❌ [generateSuggestions] Erro não tratado:", error);
      toast({
        title: "Erro ao gerar sugestões",
        description: error.message,
        variant: "destructive",
      });
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const executeConsolidation = async (groupId: string) => {
    try {
      setProcessingGroup(groupId);
      console.log("🔄 [executeConsolidation] Iniciando para grupo:", groupId);

      // Obter detalhes do grupo antes da consolidação
      const { data: groupDetails, error: detailsError } = await supabase
        .from("focus_consolidation_groups")
        .select("*, focus_consolidation_mappings(focus_id)")
        .eq("id", groupId)
        .single();

      if (detailsError) {
        console.error("❌ [executeConsolidation] Erro ao buscar detalhes do grupo:", detailsError);
      } else {
        console.log("ℹ️ [executeConsolidation] Detalhes do grupo:", groupDetails);

        // Verificar o nome no grupo
        if (groupDetails?.name) {
          console.log("🏷️ [executeConsolidation] Nome definido no grupo:", groupDetails.name);
        }
      }

      // Executar a consolidação
      const { error: consolidationError } = await supabase.rpc("execute_consolidation", {
        p_group_id: groupId,
      });

      if (consolidationError) {
        console.error("❌ [executeConsolidation] Erro na execução:", consolidationError);
        throw consolidationError;
      }



      // Remove the consolidated group from the list
      setSuggestions(prev => prev.filter(group => group.group_id !== groupId));

      toast({
        title: "Consolidação executada",
        description: "Focos foram consolidados com sucesso",
      });

      // If there are no more suggestions, update the list
      if (selectedTheme) {
        generateSuggestions(selectedTheme);
      }

    } catch (error: any) {
      console.error("❌ [executeConsolidation] Erro detalhado:", error);
      toast({
        title: "Erro ao consolidar",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setProcessingGroup(null);
    }
  };

  const generateConsolidationSQL = (groupId: string) => {
    return `
-- Executar consolidação do grupo
SELECT execute_consolidation('${groupId}');
    `.trim();
  };

  const copyConsolidationSQL = async (groupId: string) => {
    const sql = generateConsolidationSQL(groupId);
    try {
      await navigator.clipboard.writeText(sql);
      toast({
        title: "SQL Copiado",
        description: "O SQL de consolidação foi copiado para sua área de transferência",
      });
    } catch (err) {
      console.error("Erro ao copiar SQL:", err);
      toast({
        title: "Erro ao copiar SQL",
        description: "Não foi possível copiar o SQL para a área de transferência",
        variant: "destructive",
      });
    }
  };

  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTheme = e.target.value;
    //console.log("🎯 [handleThemeChange] Novo tema selecionado:", newTheme);
    setSelectedTheme(newTheme);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-6 w-6" />
          Consolidação de Focos
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex gap-4">
            <select
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={selectedTheme}
              onChange={handleThemeChange}
            >
              <option value="">Selecione um tema</option>
              {themes.map((theme) => (
                <option key={theme.id} value={theme.id}>
                  {theme.name}
                </option>
              ))}
            </select>
            <Button
              onClick={() => {
                if (selectedTheme) {
                  generateSuggestions(selectedTheme);
                }
              }}
              disabled={!selectedTheme || isLoading}
            >
              Gerar Sugestões
            </Button>
          </div>

          {suggestions.length > 0 && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Foco Principal</TableHead>
                  <TableHead>Focos Similares</TableHead>
                  <TableHead className="w-[100px] text-right">Score</TableHead>
                  <TableHead className="w-[100px] text-right">Questões</TableHead>
                  <TableHead className="w-[150px]">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {suggestions.map((group) => (
                  <TableRow key={group.group_id}>
                    <TableCell className="font-medium">
                      {group.main_focus_name}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {group.similar_focuses.map((focus) => (
                          <div
                            key={focus.id}
                            className="flex items-center text-sm text-gray-600"
                          >
                            <ChevronRight className="h-4 w-4 mr-1" />
                            {focus.name}{" "}
                            <span className="text-xs text-gray-400 ml-2">
                              ({focus.question_count} questões)
                            </span>
                          </div>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {(group.similarity_score * 100).toFixed(0)}%
                    </TableCell>
                    <TableCell className="text-right">
                      {group.total_questions}
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-blue-600"
                          onClick={() => copyConsolidationSQL(group.group_id)}
                          title="Copiar SQL"
                          disabled={processingGroup === group.group_id}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-green-600"
                          onClick={() => executeConsolidation(group.group_id)}
                          disabled={processingGroup === group.group_id}
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-red-600"
                          disabled={processingGroup === group.group_id}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FocusConsolidation;
