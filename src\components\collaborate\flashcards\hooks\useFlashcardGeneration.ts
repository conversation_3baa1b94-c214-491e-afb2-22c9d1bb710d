
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { shuffle } from "@/lib/utils";
import { buildPrompt } from "../utils/flashcardPrompts";
import { processCard, parseGeneratedContent } from "../utils/flashcardProcessing";

interface FlashcardWithMetadata {
  front: string;
  back: string;
  specialty_id?: string;
  theme_id?: string;
  focus_id?: string;
  hierarchy?: {
    specialty: { name?: string };
    theme?: { name?: string };
    focus?: { name?: string };
  };
  source_question_id?: string;
}

export const useFlashcardGeneration = () => {
  const [suggested, setSuggested] = useState<FlashcardWithMetadata[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string>("Gerando flashcards...");

  const generateFlashcardsFromQuestions = async (
    questionIds: string[],
    quantity: number,
    fcType: "cloze"|"vf"|"option"|"qa"
  ) => {
    if (!questionIds || questionIds.length === 0) {
      toast.error("Nenhuma questão selecionada para gerar flashcards!");
      return;
    }

    setLoading(true);
    setSuggested([]);
    setLoadingMessage("Analisando suas questões...");

    try {
      const uniqueQuestionIds = [...new Set(questionIds)];

      // Buscar dados das questões sem joins para evitar múltiplas requisições
      setLoadingMessage("Buscando detalhes das questões...");
      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .select('id, question_content, response_choices, correct_choice, specialty_id, theme_id, focus_id')
        .in('id', uniqueQuestionIds);

      if (questionsError) {
        console.error("❌ [useFlashcardGeneration] Erro ao buscar detalhes das questões:", questionsError);
        throw new Error(`Erro ao buscar detalhes das questões: ${questionsError.message}`);
      }

      if (!questionsData || questionsData.length === 0) {
        console.error("❌ [useFlashcardGeneration] Nenhuma questão encontrada com os IDs fornecidos");
        throw new Error("Nenhuma questão encontrada com os IDs fornecidos");
      }

      // First shuffle the questions to get a good mix
      const shuffledQuestions = shuffle(questionsData);

      // Create a question map for fast lookup by ID
      const questionMap = shuffledQuestions.reduce((map, q) => {
        map[q.id] = q;
        return map;
      }, {} as Record<string, any>);

      const prompt = buildPrompt(fcType, quantity);

      try {
        setLoadingMessage("Gerando flashcards personalizados...");

        const { data, error: fnError } = await supabase.functions.invoke(
          "flashcards-ai-generate",
          {
            body: {
              prompt,
              questionIds: uniqueQuestionIds,
              directQuestions: shuffledQuestions,
              quantity,
              fcType,
            },
          }
        );

        if (fnError) {
          console.error("❌ [useFlashcardGeneration] Erro na função Edge:", fnError);
          throw new Error(`Erro na API: ${fnError.message}`);
        }

        if (!data) {
          throw new Error("A API não retornou dados");
        }

        if (data.error) {
          throw new Error(data.error);
        }



        // Process the generated flashcards
        setLoadingMessage("Processando flashcards...");
        const iaFlashcards = await parseGeneratedContent(data, questionMap, shuffledQuestions);

        if (!iaFlashcards || !Array.isArray(iaFlashcards) || iaFlashcards.length === 0) {
          console.error("❌ [useFlashcardGeneration] A IA não retornou resultados válidos ou retornou um array vazio");
          throw new Error("A IA não retornou resultados válidos.");
        }

        // Double check all cards have hierarchy data
        const finalFlashcards = iaFlashcards.map((card, idx) => {
          if (!card.hierarchy || !card.specialty_id) {
            const questionIndex = idx % shuffledQuestions.length;
            const sourceQuestion = shuffledQuestions[questionIndex];
            return processCard(card, sourceQuestion);
          }
          return card;
        });

        setSuggested(finalFlashcards);
        toast.success(`${finalFlashcards.length} flashcards sugeridos gerados com sucesso!`);
      } catch (apiError: any) {
        console.error("❌ [useFlashcardGeneration] Erro na API:", apiError);
        throw apiError;
      }
    } catch (e: any) {
      toast.error("Erro ao gerar flashcards com IA: " + (e?.message || "Desconhecido"));
    } finally {
      setLoading(false);
    }
  };

  const generateFlashcards = async (
    specialty: string,
    theme: string,
    focus: string,
    extrafocus: string,
    quantity: number,
    fcType: "cloze"|"vf"|"option"|"qa"
  ) => {
    if (!specialty) {
      toast.error("Selecione pelo menos a especialidade!");
      return;
    }

    setLoading(true);
    setSuggested([]);
    setLoadingMessage("Gerando flashcards personalizados...");

    try {
      const prompt = buildPrompt(fcType, quantity);

      const { data, error: fnError } = await supabase.functions.invoke(
        "flashcards-ai-generate",
        {
          body: {
            prompt,
            specialty,
            theme: theme || null,
            focus: focus || null,
            extrafocus: extrafocus || null,
            quantity,
            fcType,
          },
        }
      );

      if (fnError) {
        console.error("❌ [FlashcardAIWizard] Erro na função Edge:", fnError);
        throw new Error(`Erro na API: ${fnError.message}`);
      }

      if (!data) {
        throw new Error("A API não retornou dados");
      }

      if (data.error) {
        throw new Error(data.error);
      }

      const processFlashcard = (card: any) => {
        if (card.statement && (card.answer === "V" || card.answer === "F")) {
          return {
            front: card.statement,
            back: card.answer === "V" ? "Verdadeiro" : "Falso",
            specialty_id: specialty,
            theme_id: theme || null,
            focus_id: focus || null,
          };
        } else if (card.question && card.options && card.answer) {
          const optionsFormatted = card.options
            .map((opt: string, idx: number) => `${String.fromCharCode(65 + idx)}) ${opt}`)
            .join("\n");
          return {
            front: card.question,
            back: card.answer,
            specialty_id: specialty,
            theme_id: theme || null,
            focus_id: focus || null,
          };
        } else if (card.question && card.answer) {
          return {
            front: card.question,
            back: card.answer,
            specialty_id: specialty,
            theme_id: theme || null,
            focus_id: focus || null,
          };
        } else if (card.front && card.back) {
          // Remove categoria if present
          let frontContent = card.front;
          frontContent = frontContent.replace(/\n\nCategoria:.*$/m, '').trim();
          return {
            front: frontContent,
            back: card.back,
            specialty_id: specialty,
            theme_id: theme || null,
            focus_id: focus || null,
          };
        }
        return {
          front: JSON.stringify(card),
          back: "Formato não suportado",
          specialty_id: specialty,
          theme_id: theme || null,
          focus_id: focus || null,
        };
      };

      const iaFlashcards = await parseGeneratedContent(data, null, null, processFlashcard);

      if (!Array.isArray(iaFlashcards) || iaFlashcards.length === 0) {
        throw new Error("A IA não retornou resultados válidos.");
      }

      setSuggested(iaFlashcards);
      toast.success("Flashcards sugeridos gerados com sucesso!");
    } catch (e: any) {
      toast.error("Erro ao gerar flashcards com IA: " + (e?.message || "Desconhecido"));
    } finally {
      setLoading(false);
    }
  };

  return {
    suggested,
    setSuggested,
    loading,
    loadingMessage,
    generateFlashcards,
    generateFlashcardsFromQuestions
  };
};
