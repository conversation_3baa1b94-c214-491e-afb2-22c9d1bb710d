import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  MessageSquare,
  Plus,
  Trash2,
  Clock,
  X,
  History
} from 'lucide-react';
import { DrWillThread } from '@/hooks/useDrWillHistory';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DrWillHistoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  threads: DrWillThread[];
  currentThreadId: string | null;
  onSelectThread: (threadId: string) => void;
  onNewThread: () => void;
  onDeleteThread: (threadId: string) => void;
  isLoading: boolean;
}

export const DrWillHistoryPanel: React.FC<DrWillHistoryPanelProps> = ({
  isOpen,
  onClose,
  threads,
  currentThreadId,
  onSelectThread,
  onNewThread,
  onDeleteThread,
  isLoading
}) => {
  const formatRelativeTime = (date: Date) => {
    try {
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'há alguns momentos';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* History Panel - Expandido na frente do chat */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-2xl h-[80vh] bg-white border-2 border-black shadow-2xl z-50 rounded-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b-2 border-black bg-gradient-to-r from-purple-50 to-indigo-50">
              <div className="flex items-center gap-3">
                <History className="h-6 w-6 text-purple-600" />
                <h2 className="text-xl font-bold text-gray-800">Histórico de Conversas</h2>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={onNewThread}
                  size="sm"
                  className="bg-purple-500 hover:bg-purple-600 text-white border-2 border-black rounded-lg px-4 py-2 font-semibold"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Nova Conversa
                </Button>
                <Button
                  onClick={onClose}
                  variant="ghost"
                  size="sm"
                  className="hover:bg-gray-100 p-2"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Threads List */}
            <ScrollArea className="flex-1 h-[calc(80vh-100px)]">
              <div className="p-6 space-y-3">
                {isLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-20 bg-gray-200 rounded-xl"></div>
                      </div>
                    ))}
                  </div>
                ) : threads.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">
                      Nenhuma conversa ainda
                    </h3>
                    <p className="text-gray-500 text-sm mb-4">
                      Suas conversas com Dr. Will aparecerão aqui
                    </p>
                    <Button
                      onClick={onNewThread}
                      className="bg-purple-500 hover:bg-purple-600 text-white border-2 border-black rounded-lg px-6 py-2 font-semibold"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Iniciar Primeira Conversa
                    </Button>
                  </div>
                ) : (
                  <AnimatePresence>
                    {threads.map((thread) => (
                      <motion.div
                        key={thread.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          currentThreadId === thread.id
                            ? 'border-purple-500 bg-purple-50 shadow-lg'
                            : 'border-gray-200 hover:border-purple-300 hover:bg-purple-25 hover:shadow-md'
                        }`}
                        onClick={() => onSelectThread(thread.id)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${
                            currentThreadId === thread.id
                              ? 'bg-purple-500'
                              : 'bg-gray-300 group-hover:bg-gray-400'
                          }`}>
                            <MessageSquare className={`h-4 w-4 ${
                              currentThreadId === thread.id ? 'text-white' : 'text-gray-600'
                            }`} />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h3 className={`font-semibold text-base mb-2 line-clamp-2 ${
                              currentThreadId === thread.id ? 'text-purple-800' : 'text-gray-800'
                            }`}>
                              {thread.title}
                            </h3>

                            <div className="flex items-center gap-3 text-sm">
                              <div className="flex items-center gap-1 text-gray-500">
                                <Clock className="h-4 w-4" />
                                <span>{formatRelativeTime(thread.lastMessageAt)}</span>
                              </div>
                              <div className="flex items-center gap-1 text-gray-500">
                                <MessageSquare className="h-4 w-4" />
                                <span>{thread.messageCount} mensagens</span>
                              </div>
                            </div>
                          </div>
                          
                          <Button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteThread(thread.id);
                            }}
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto hover:bg-red-100 hover:text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
              </div>
            </ScrollArea>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
