import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { useUserAccess } from "@/hooks/useUserAccess";
import { useUserData } from "@/hooks/useUserData";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAccess?: boolean;
}

const ProtectedRoute = ({ children, requireAccess = false }: ProtectedRouteProps) => {
  const { user, loading: authLoading } = useAuth();
  const { hasAccess, isLoading: accessLoading } = useUserAccess();
  const { hasCompletedOnboarding, isLoading: userDataLoading, isReady } = useUserData();
  const location = useLocation();

  const isLoading = authLoading || accessLoading || userDataLoading;

  // Se não há usuário e não está carregando, redirecionar imediatamente
  if (!user && !authLoading) {
    console.log('🔄 [ProtectedRoute] No user found, redirecting to home');
    return <Navigate to="/" replace />;
  }

  // Show loading state while checking access or user data
  if (isLoading || !isReady) {
    return <div className="flex items-center justify-center min-h-screen">Carregando...</div>;
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  // Não redirecionar para onboarding se já estiver lá ou se for uma rota específica
  if (!hasCompletedOnboarding && location.pathname !== '/onboarding' && !location.pathname.startsWith('/admin')) {
    return <Navigate to="/onboarding" replace />;
  }

  if (requireAccess && !hasAccess) {
    return <Navigate to="/acesso-restrito" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
