
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface AIDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AIDialog = ({ open, onOpenChange }: AIDialogProps) => {
  const navigate = useNavigate();

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-[80dvw] max-h-[70dvh] sm:max-w-[425px] rounded-2xl border-none shadow-xl">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-center">Escolha uma opção</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="flex flex-col gap-4 py-4">
          <div className="relative">
            {/* Removed the "Novo" badge that was here */}
            <Button
              className="w-full flex items-center justify-between bg-gradient-to-r from-primary/90 to-primary hover:from-primary hover:to-primary/90 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
              onClick={() => {
                onOpenChange(false);
                navigate('/dr-will');
              }}
            >
              <div className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                <span>Conversar com Will</span>
              </div>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
          <Button
            className="w-full flex items-center justify-between rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:bg-gray-100"
            variant="outline"
            onClick={() => {
              onOpenChange(false);
              navigate('/ai-assistant');
            }}
          >
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              <span>DxBrain</span>
            </div>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel className="w-full sm:w-auto rounded-xl transition-all duration-300 hover:bg-gray-100 hover:scale-[1.02]">
            Voltar ao MENU
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
