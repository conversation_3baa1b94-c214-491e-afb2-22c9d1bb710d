import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, Home, HelpCircle, LogIn } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface MobileMenuProps {
  isAuthenticated?: boolean;
  onLoginClick?: () => void;
}

const MobileMenu = ({ isAuthenticated, onLoginClick }: MobileMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const menuItems = [
    {
      icon: Home,
      label: "Início",
      action: () => navigate("/")
    },
    {
      icon: HelpCircle,
      label: "Como funciona?",
      action: () => navigate("/como-funciona")
    }
  ];

  const handleItemClick = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  return (
    <div className="md:hidden">
      {/* <PERSON><PERSON> */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="p-2"
      >
        {isOpen ? (
          <X className="h-5 w-5" />
        ) : (
          <Menu className="h-5 w-5" />
        )}
      </Button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu Content */}
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className="fixed top-0 right-0 h-full w-64 bg-[#FEF7CD] border-l-2 border-black shadow-card z-50"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-8">
                  <div className="bg-white border-2 border-black px-3 py-1 shadow-card-sm">
                    <span className="font-bold text-lg tracking-tight">Med EVO</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="p-2"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Menu Items */}
                <nav className="space-y-4">
                  {menuItems.map((item, index) => (
                    <motion.button
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleItemClick(item.action)}
                      className="w-full flex items-center gap-3 p-3 rounded-lg bg-white border-2 border-black shadow-card-sm hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      <item.icon className="h-5 w-5" />
                      <span className="font-medium">{item.label}</span>
                    </motion.button>
                  ))}

                  {/* Login/Platform Button */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: menuItems.length * 0.1 }}
                    className="pt-4 border-t border-black/20"
                  >
                    {isAuthenticated ? (
                      <Button
                        onClick={() => handleItemClick(() => navigate("/plataformadeestudos"))}
                        className="w-full bg-black text-white hover:bg-black/90 border-2 border-black font-bold py-3 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        Acessar Plataforma
                      </Button>
                    ) : (
                      <Button
                        onClick={() => {
                          setIsOpen(false);
                          onLoginClick?.();
                        }}
                        className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black font-bold py-3 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        <LogIn className="h-4 w-4 mr-2" />
                        Entrar
                      </Button>
                    )}
                  </motion.div>
                </nav>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MobileMenu;
