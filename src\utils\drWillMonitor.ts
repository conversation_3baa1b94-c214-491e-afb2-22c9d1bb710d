// Sistema de monitoramento para Dr. Will
// Detecta problemas em tempo real e fornece diagnósticos

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
}

interface ThreadState {
  id: string;
  messageCount: number;
  lastActivity: number;
  loadAttempts: number;
  errors: string[];
}

class DrWillMonitor {
  private metrics: PerformanceMetric[] = [];
  private threadStates = new Map<string, ThreadState>();
  private maxMetrics = 100;
  
  // Thresholds para alertas
  private readonly SLOW_OPERATION_MS = 2000;
  private readonly MAX_LOAD_ATTEMPTS = 3;
  private readonly ERROR_THRESHOLD = 5; // 5 erros em 1 minuto

  // Registrar métrica de performance
  recordMetric(operation: string, duration: number, success: boolean, error?: string) {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: Date.now(),
      success,
      error
    };

    this.metrics.push(metric);
    
    // <PERSON><PERSON> apenas as métricas mais recentes
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Alertas automáticos (removidos para produção)
    // if (duration > this.SLOW_OPERATION_MS) {
    //   console.warn(`⚠️ [MONITOR] Slow operation detected: ${operation} took ${duration}ms`);
    // }

    if (!success && error) {
      console.error(`❌ [MONITOR] Operation failed: ${operation}`, error);
      this.checkErrorRate();
    }
  }

  // Monitorar estado de thread
  updateThreadState(threadId: string, messageCount: number, error?: string) {
    const existing = this.threadStates.get(threadId);
    const now = Date.now();

    if (existing) {
      existing.messageCount = messageCount;
      existing.lastActivity = now;
      existing.loadAttempts += 1;
      
      if (error) {
        existing.errors.push(error);
      }

      // Silencioso - apenas monitorar internamente
    } else {
      this.threadStates.set(threadId, {
        id: threadId,
        messageCount,
        lastActivity: now,
        loadAttempts: 1,
        errors: error ? [error] : []
      });
    }
  }

  // Verificar taxa de erro
  private checkErrorRate() {
    const oneMinuteAgo = Date.now() - 60000;
    const recentErrors = this.metrics.filter(m => 
      !m.success && m.timestamp > oneMinuteAgo
    );

    if (recentErrors.length >= this.ERROR_THRESHOLD) {
      console.error(`❌ [MONITOR] High error rate detected: ${recentErrors.length} errors in last minute`);
    }
  }

  // Diagnóstico completo
  getDiagnostics() {
    const now = Date.now();
    const lastMinute = this.metrics.filter(m => now - m.timestamp < 60000);
    const lastHour = this.metrics.filter(m => now - m.timestamp < 3600000);

    const successRate = lastMinute.length > 0 
      ? (lastMinute.filter(m => m.success).length / lastMinute.length) * 100 
      : 100;

    const avgDuration = lastMinute.length > 0
      ? lastMinute.reduce((sum, m) => sum + m.duration, 0) / lastMinute.length
      : 0;

    const slowOperations = lastMinute.filter(m => m.duration > this.SLOW_OPERATION_MS);
    const errors = lastMinute.filter(m => !m.success);

    return {
      timestamp: new Date().toISOString(),
      performance: {
        successRate: Math.round(successRate),
        avgDuration: Math.round(avgDuration),
        slowOperations: slowOperations.length,
        totalOperations: lastMinute.length
      },
      errors: {
        lastMinute: errors.length,
        lastHour: lastHour.filter(m => !m.success).length,
        recent: errors.slice(-3).map(e => ({
          operation: e.operation,
          error: e.error,
          timestamp: new Date(e.timestamp).toLocaleTimeString()
        }))
      },
      threads: {
        active: this.threadStates.size,
        problematic: Array.from(this.threadStates.values())
          .filter(t => t.loadAttempts > 2 || t.errors.length > 0)
          .map(t => ({
            id: t.id.slice(0, 8) + '...',
            loadAttempts: t.loadAttempts,
            errors: t.errors.length,
            lastActivity: new Date(t.lastActivity).toLocaleTimeString()
          }))
      }
    };
  }

  // Limpar dados antigos
  cleanup() {
    const oneHourAgo = Date.now() - 3600000;
    
    // Limpar métricas antigas
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
    
    // Limpar threads inativos
    for (const [threadId, state] of this.threadStates.entries()) {
      if (state.lastActivity < oneHourAgo) {
        this.threadStates.delete(threadId);
      }
    }
  }

  // Verificar saúde geral do sistema
  getHealthStatus(): 'healthy' | 'warning' | 'critical' {
    const diagnostics = this.getDiagnostics();
    
    if (diagnostics.performance.successRate < 50 || diagnostics.errors.lastMinute > 10) {
      return 'critical';
    }
    
    if (diagnostics.performance.successRate < 80 || diagnostics.performance.slowOperations > 3) {
      return 'warning';
    }
    
    return 'healthy';
  }

  // Log de status periódico
  logStatus() {
    const status = this.getHealthStatus();
    const diagnostics = this.getDiagnostics();
    
    if (status === 'critical') {
      console.error('❌ [MONITOR] System health: CRITICAL', diagnostics);
    }
    // Logs de warning e healthy removidos para produção
    // else if (status === 'warning') {
    //   console.warn('⚠️ [MONITOR] System health: WARNING');
    // }
  }
}

// Instância global
export const drWillMonitor = new DrWillMonitor();

// Limpeza automática a cada 30 minutos
setInterval(() => {
  drWillMonitor.cleanup();
}, 30 * 60 * 1000);

// Status log a cada 5 minutos (apenas em desenvolvimento)
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    drWillMonitor.logStatus();
  }, 5 * 60 * 1000);
}

// Helper para medir performance de operações
export const measureOperation = async <T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> => {
  const startTime = Date.now();
  let success = false;
  let error: string | undefined;
  
  try {
    const result = await fn();
    success = true;
    return result;
  } catch (e) {
    error = e instanceof Error ? e.message : String(e);
    throw e;
  } finally {
    const duration = Date.now() - startTime;
    drWillMonitor.recordMetric(operation, duration, success, error);
  }
};
