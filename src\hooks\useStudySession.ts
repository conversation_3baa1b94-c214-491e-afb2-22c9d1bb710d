import { useState, useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { StudySessionRow } from "@/types/study-session";
import { useUserData } from "@/hooks/useUserData";
import { useQueryClient } from '@tanstack/react-query';

export const useStudySession = () => {
  const [activeSession, setActiveSession] = useState<StudySessionRow | null>(null);
  const { toast } = useToast();
  const { domain, isResidencia, isAdmin } = useUserData();
  const queryClient = useQueryClient();

  // Removido useEffect desnecessário - agora usa useUserData centralizado

  const createSession = useCallback(async (userId: string, questionIds: string[], title: string) => {
    console.log('🏗️ [useStudySession] createSession chamado:', {
      userId,
      questionIds_length: questionIds.length,
      title,
      questionIds: questionIds.slice(0, 3) // Primeiros 3 IDs
    });

    try {
      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      console.log('👤 [useStudySession] Verificação de usuário:', {
        user_exists: !!user,
        user_id: user?.id,
        matches_userId: user?.id === userId
      });

      if (!user) {
        console.error('❌ [useStudySession] Usuário não autenticado');
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar logado para criar uma sessão de estudo.",
          variant: "destructive"
        });
        return null;
      }

      // Verificar se o userId fornecido corresponde ao usuário autenticado
      if (user.id !== userId && !isAdmin) {
        toast({
          title: "Erro de permissão",
          description: "Você não tem permissão para criar sessões para outro usuário.",
          variant: "destructive"
        });
        return null;
      }



      if (!questionIds.length) {
        console.error('❌ [useStudySession] Nenhuma questão fornecida');
        throw new Error('No questions selected for the session');
      }

      console.log('🔍 [useStudySession] Validando questões...');

      // Skip verification if we have too many IDs to avoid URL length issues
      let validQuestionIds = questionIds;
      let firstQuestion = null;

      if (questionIds.length > 100) {
        // Just get the first question details for categorization
        const { data: firstQuestionData, error: firstQuestionError } = await supabase
          .from('questions')
          .select('specialty_id, theme_id, focus_id')
          .eq('id', questionIds[0])
          .single();

        if (firstQuestionError) {
          // Error getting first question details
        } else {
          firstQuestion = firstQuestionData;
        }
      } else {
        // Verify that we have valid question IDs before proceeding (only for smaller lists)
        const { data: questions, error: questionsError } = await supabase
          .from('questions')
          .select('id, specialty_id, theme_id, focus_id')
          .in('id', questionIds);

        if (questionsError) {
          throw questionsError;
        }

        // Filter out any invalid IDs that weren't found
        validQuestionIds = questions.map(q => q.id);
        firstQuestion = questions[0];

        if (!validQuestionIds.length) {
          throw new Error('No valid questions found for the session');
        }
      }

      console.log('💾 [useStudySession] Criando sessão no banco:', {
        user_id: userId,
        questions_count: validQuestionIds.length,
        title,
        domain,
        firstQuestion_id: firstQuestion?.id
      });

      // Create the session with valid question IDs
      const { data: newSession, error } = await supabase
        .from('study_sessions')
        .insert({
          user_id: userId,
          questions: validQuestionIds,
          total_questions: validQuestionIds.length,
          current_question_index: 0,
          stats: {
            correct_answers: 0,
            incorrect_answers: 0,
            time_spent: 0,
            by_specialty: {},
            by_theme: {},
            by_focus: {}
          },
          status: 'in_progress',
          title,
          specialty_id: firstQuestion?.specialty_id,
          theme_id: firstQuestion?.theme_id,
          focus_id: firstQuestion?.focus_id,
          knowledge_domain: domain
        })
        .select()
        .single();

      console.log('📊 [useStudySession] Resultado da inserção:', {
        success: !error,
        session_id: newSession?.id,
        error: error?.message
      });

      if (error) {
        console.error('❌ [useStudySession] Erro ao criar sessão:', error);
        throw error;
      }

      setActiveSession(newSession);
      console.log('✅ [useStudySession] Sessão criada com sucesso:', newSession.id);
      return newSession;

    } catch (error: any) {
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
      return null;
    }
  }, [toast, domain, isResidencia]);

  const updateSessionProgress = async (
    sessionId: string,
    questionId: string,
    isCorrect: boolean,
    timeSpent: number = 0
  ): Promise<boolean> => {
    // Esta função agora apenas retorna true para manter compatibilidade
    // A lógica real de salvamento foi movida para useAnswerSubmission para evitar duplicação

    return true;
  };

  const completeSession = async (sessionId: string): Promise<void> => {
    try {
      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar logado para completar a sessão.",
          variant: "destructive"
        });
        return;
      }

      const { data: sessionData, error: sessionError } = await supabase
        .from("study_sessions")
        .select("*")
        .eq("id", sessionId)
        .single();

      if (sessionError) {
        // Se o erro for relacionado à tabela não existir ou permissão negada, apenas continuamos
        if (sessionError.code === '42P01') {
          // Tentamos continuar mesmo sem os dados da sessão
        } else if (sessionError.code === '42501') {
          // Tentamos continuar mesmo sem os dados da sessão
        } else {
          throw sessionError;
        }
      }

      // Verificar se o usuário tem permissão para completar esta sessão
      if (sessionData && sessionData.user_id !== user.id && !isAdmin) {
        toast({
          title: "Erro de permissão",
          description: "Você não tem permissão para completar sessões de outro usuário.",
          variant: "destructive"
        });
        return;
      }

      const { error: updateError } = await supabase
        .from("study_sessions")
        .update({
          status: "completed",
          end_time: new Date().toISOString()
        })
        .eq("id", sessionId);

      if (updateError) {
        throw updateError;
      }

      // ✅ Invalidar caches após completar sessão
      queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey[0] as string;
          return key === 'study-sessions' || key === 'user-statistics' || key === 'user-study-stats';
        }
      });
    } catch (error: any) {
      toast({
        title: "Erro ao completar sessão",
        description: "Não foi possível completar a sessão de estudos",
        variant: "destructive"
      });
    }
  };

  return {
    activeSession,
    setActiveSession,
    createSession,
    updateSessionProgress,
    completeSession
  };
};
