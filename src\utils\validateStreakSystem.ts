/**
 * Script de validação rápida do sistema de sequência
 * Execute no console do navegador para testar
 */

import { supabase } from '@/integrations/supabase/client';

export const validateStreakSystem = async () => {
  console.log('🧪 Iniciando validação do sistema de sequência...');
  
  const results = {
    jsMethod: { success: false, time: 0, error: null },
    sqlMethod: { success: false, time: 0, error: null },
    dataConsistency: { success: false, details: null }
  };

  // Obter usuário atual
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('❌ Usuário não autenticado');
    return results;
  }

  // Teste 1: Método JavaScript
  try {
    const startTime = performance.now();

    const { calculateImprovedStreakStats } = await import('./sessionTransformers');
    const jsResult = await calculateImprovedStreakStats(user.id, supabase);

    const endTime = performance.now();
    results.jsMethod = {
      success: true,
      time: endTime - startTime,
      error: null,
      result: jsResult
    };
  } catch (error) {
    results.jsMethod = {
      success: false,
      time: 0,
      error: error.message
    };
  }

  // Teste 2: Método SQL
  try {
    const startTime = performance.now();

    const { data, error } = await supabase
      .rpc('calculate_user_study_streak', {
        p_user_id: user.id,
        p_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
      });

    const endTime = performance.now();

    if (error) throw error;

    results.sqlMethod = {
      success: true,
      time: endTime - startTime,
      error: null,
      result: data?.[0] || { current_streak: 0, max_streak: 0 }
    };
  } catch (error) {
    results.sqlMethod = {
      success: false,
      time: 0,
      error: error.message
    };
  }

  // Teste 3: Consistência de dados
  console.log('🔄 Verificando consistência...');
  if (results.jsMethod.success && results.sqlMethod.success) {
    const jsResult = results.jsMethod.result;
    const sqlResult = results.sqlMethod.result;
    
    const consistent = 
      jsResult.currentStreak === sqlResult.current_streak &&
      jsResult.maxStreak === sqlResult.max_streak;
    
    results.dataConsistency = {
      success: consistent,
      details: {
        js: jsResult,
        sql: sqlResult,
        consistent
      }
    };
    
    if (consistent) {
      console.log('✅ Dados consistentes entre métodos');
    } else {
      console.warn('⚠️ Inconsistência detectada:', {
        javascript: jsResult,
        sql: sqlResult
      });
    }
  }

  // Teste 4: Dados de atividade
  console.log('🔄 Verificando dados de atividade...');
  try {
    const [sessionsResult, answersResult, scheduleResult] = await Promise.all([
      supabase
        .from('study_sessions')
        .select('id, completed_at, status')
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .limit(5),
      
      supabase
        .from('user_answers')
        .select('id, created_at')
        .eq('user_id', user.id)
        .limit(5),
      
      supabase
        .from('study_schedule_items')
        .select('id, last_revision_date, study_status, study_schedules!inner(user_id)')
        .eq('study_schedules.user_id', user.id)
        .eq('study_status', 'completed')
        .limit(5)
    ]);

    const activitySummary = {
      sessions: sessionsResult.data?.length || 0,
      answers: answersResult.data?.length || 0,
      scheduleItems: scheduleResult.data?.length || 0,
      totalActivities: (sessionsResult.data?.length || 0) + 
                      (answersResult.data?.length || 0) + 
                      (scheduleResult.data?.length || 0)
    };

    console.log('📊 Resumo de atividades:', activitySummary);
    results.activitySummary = activitySummary;

  } catch (error) {
    console.error('❌ Erro ao verificar atividades:', error);
  }

  // Relatório final
  console.log('\n📋 RELATÓRIO DE VALIDAÇÃO');
  console.log('================================');
  console.log(`JavaScript: ${results.jsMethod.success ? '✅' : '❌'} (${results.jsMethod.time.toFixed(2)}ms)`);
  console.log(`SQL: ${results.sqlMethod.success ? '✅' : '❌'} (${results.sqlMethod.time.toFixed(2)}ms)`);
  console.log(`Consistência: ${results.dataConsistency.success ? '✅' : '❌'}`);
  
  if (results.sqlMethod.success && results.jsMethod.success) {
    const improvement = ((results.jsMethod.time - results.sqlMethod.time) / results.jsMethod.time * 100);
    console.log(`Performance: SQL é ${improvement.toFixed(1)}% mais rápido`);
  }

  return results;
};

/**
 * Teste de carga simples
 */
export const loadTest = async (iterations = 10) => {
  console.log(`🚀 Iniciando teste de carga (${iterations} iterações)...`);
  
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('❌ Usuário não autenticado');
    return;
  }

  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    
    try {
      const { calculateImprovedStreakStats } = await import('./sessionTransformers');
      await calculateImprovedStreakStats(user.id, supabase);
      
      const endTime = performance.now();
      times.push(endTime - startTime);
      
      console.log(`Iteração ${i + 1}/${iterations}: ${(endTime - startTime).toFixed(2)}ms`);
    } catch (error) {
      console.error(`❌ Erro na iteração ${i + 1}:`, error);
    }
  }

  const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);

  console.log('\n📊 RESULTADOS DO TESTE DE CARGA');
  console.log('================================');
  console.log(`Iterações: ${iterations}`);
  console.log(`Tempo médio: ${avgTime.toFixed(2)}ms`);
  console.log(`Tempo mínimo: ${minTime.toFixed(2)}ms`);
  console.log(`Tempo máximo: ${maxTime.toFixed(2)}ms`);
  console.log(`Desvio padrão: ${Math.sqrt(times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / times.length).toFixed(2)}ms`);

  return {
    iterations,
    times,
    avgTime,
    minTime,
    maxTime
  };
};

/**
 * Executar no console do navegador:
 * 
 * import { validateStreakSystem, loadTest } from './src/utils/validateStreakSystem';
 * 
 * // Validação básica
 * validateStreakSystem();
 * 
 * // Teste de carga
 * loadTest(20);
 */
