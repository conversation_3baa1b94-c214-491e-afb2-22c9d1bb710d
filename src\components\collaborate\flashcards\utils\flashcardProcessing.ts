
import { shuffle } from "@/lib/utils";

export const processCard = (card: any, question: any) => {
  // Extract the correct hierarchy data from the source question
  const baseCard = {
    specialty_id: question.specialty_id,
    theme_id: question.theme_id,
    focus_id: question.focus_id,
    hierarchy: {
      specialty: { name: question.specialty?.name || "Não especificada" },
      theme: { name: question.theme?.name || "Não especificado" },
      focus: { name: question.focus?.name || "Não especificada" }
    },
    source_question_id: question.id // Track which question this card was derived from
  };

  const hierarchyPath =
    `${baseCard.hierarchy.specialty.name} > ${baseCard.hierarchy.theme?.name || "Geral"} > ${baseCard.hierarchy.focus?.name || "Geral"}`;

  // Remove QID prefix from front content if it exists
  let frontContent = card.front;
  if (frontContent) {
    // Remove the #QID#ID# prefix pattern
    frontContent = frontContent.replace(/^#QID#[a-f0-9-]+#\s*/i, '');

    // Remove "Categoria:" line if present
    frontContent = frontContent.replace(/\n\nCategoria:.*$/m, '').trim();
  }

  if (card.statement && (card.answer === "V" || card.answer === "F")) {
    return {
      ...baseCard,
      front: card.statement,
      back: card.answer === "V" ? "Verdadeiro" : "Falso",
    };
  }

  if (card.question && card.options && card.answer) {
    const optionsFormatted = card.options
      .map((opt: string, idx: number) => `${String.fromCharCode(65 + idx)}) ${opt}`)
      .join("\n");
    return {
      ...baseCard,
      front: card.question,
      back: card.answer,
    };
  }

  if (card.question && card.answer) {
    return {
      ...baseCard,
      front: card.question,
      back: card.answer,
    };
  }

  if (card.front && card.back) {
    return {
      ...baseCard,
      front: frontContent,
      back: card.back,
    };
  }

  return {
    ...baseCard,
    front: "Formato não suportado",
    back: "Formato não suportado",
  };
};

// Função auxiliar para garantir que temos um array válido
const ensureValidArray = (data: any): any[] => {
  if (!data) return [];

  if (Array.isArray(data)) return data;

  // Se for objeto, verifica se tem propriedades que poderiam conter arrays
  if (typeof data === 'object') {
    if (data.generated && Array.isArray(data.generated)) return data.generated;
    if (data.flashcards && Array.isArray(data.flashcards)) return data.flashcards;
    if (data.cards && Array.isArray(data.cards)) return data.cards;
    if (data.items && Array.isArray(data.items)) return data.items;

    // Se tiver apenas front e back, retorna como único item de array
    if (data.front && data.back) return [data];

    // Tenta extrair valores de objeto que parecem ser arrays
    const possibleArrays = Object.values(data).filter(v => Array.isArray(v));
    if (possibleArrays.length > 0) {
      return possibleArrays[0];
    }
  }

  // Se todas as tentativas falharem, retorna array vazio
  console.warn("⚠️ [parseGeneratedContent] Não foi possível extrair um array válido dos dados");
  return [];
};

export const parseGeneratedContent = async (
  data: any,
  questionMap?: Record<string, any> | null,
  shuffledQuestions?: any[] | null,
  customProcessor?: (card: any) => any
): Promise<any[]> => {
  let iaFlashcards = [];

  console.log(`🔍 [parseGeneratedContent] Analisando dados recebidos:`, JSON.stringify(data).substring(0, 200) + "...");

  // Check if data.generated is an array and has items
  if (data.generated && Array.isArray(data.generated) && data.generated.length > 0) {
    console.log(`✅ [useFlashcardGeneration] Processando ${data.generated.length} flashcards recebidos como array`);
    console.log(`📊 [useFlashcardGeneration] Estrutura do array recebido:`, JSON.stringify(data.generated.slice(0, 2)));

    iaFlashcards = [...data.generated]; // Use all items from the array

    if (customProcessor) {
      iaFlashcards = iaFlashcards.map(customProcessor);
    } else if (questionMap && shuffledQuestions) {
      iaFlashcards = iaFlashcards.map((card: any, index: number) => {
        // Check if card already has correct metadata from the edge function
        if (card.source_question_id && card.specialty_id && card.hierarchy) {
          console.log(`✅ [useFlashcardGeneration] Flashcard ${index+1} já tem metadados corretos da fonte: ${card.source_question_id}`);
          return card;
        }

        // If card has source_question_id but missing other metadata
        if (card.source_question_id && questionMap[card.source_question_id]) {
          console.log(`🔗 [useFlashcardGeneration] Flashcard ${index+1} usando questão fonte: ${card.source_question_id}`);
          return processCard(card, questionMap[card.source_question_id]);
        }

        // Extract source_question_id from QID pattern in front property if available
        if (card.front && typeof card.front === 'string') {
          const qidMatch = card.front.match(/#QID#([a-f0-9-]+)#/i);
          if (qidMatch && qidMatch[1] && questionMap[qidMatch[1]]) {
            const extractedQuestionId = qidMatch[1];
            console.log(`🔍 [useFlashcardGeneration] Extraído ID de questão do conteúdo: ${extractedQuestionId}`);
            card.source_question_id = extractedQuestionId;
            return processCard(card, questionMap[extractedQuestionId]);
          }
        }

        // Fallback to cycling through questions if no source_question_id
        const questionIndex = index % shuffledQuestions.length;
        const sourceQuestion = shuffledQuestions[questionIndex];

        console.log(`⚠️ [useFlashcardGeneration] Flashcard ${index+1} não tem fonte específica, usando questão ${questionIndex+1} (${sourceQuestion.id})`);

        return processCard(card, sourceQuestion);
      });
    }
  }
  // Handle string response - this is the key part that's failing
  else if (typeof data.generated === "string" || typeof data.generatedText === "string") {
    const jsonText = typeof data.generated === "string" ? data.generated : data.generatedText;
    console.log(`⚠️ [useFlashcardGeneration] Resposta recebida como string, tentando parsear JSON: ${jsonText.substring(0, 150)}...`);

    try {
      // Remove markdown code block indicators
      const cleanJson = jsonText.replace(/```(json)?\s*/g, '').replace(/\s*```$/g, '');

      // Try to parse the JSON
      let parsed;
      try {
        parsed = JSON.parse(cleanJson);
        console.log(`✅ [useFlashcardGeneration] JSON parseado com sucesso: ${typeof parsed}`);
      } catch (e) {
        // Try to extract JSON array pattern
        const arrayMatch = cleanJson.match(/\[\s*\{[\s\S]*\}\s*\]/);
        if (arrayMatch) {
          parsed = JSON.parse(arrayMatch[0]);
          console.log(`✅ [useFlashcardGeneration] Extraído array JSON com sucesso: ${parsed.length} itens`);
        } else {
          throw new Error(`Erro ao parsear JSON: ${e.message}`);
        }
      }

      // Ensure we have an array
      if (!Array.isArray(parsed)) {
        parsed = ensureValidArray(parsed);
      }

      // Process the parsed flashcards
      iaFlashcards = parsed;
      console.log(`✅ [useFlashcardGeneration] Obtidos ${iaFlashcards.length} flashcards da string JSON`);

      if (customProcessor) {
        iaFlashcards = iaFlashcards.map(customProcessor);
      } else if (questionMap && shuffledQuestions) {
        iaFlashcards = iaFlashcards.map((card: any, index: number) => {
          // Process QID from front text
          if (card.front && typeof card.front === 'string') {
            const qidMatch = card.front.match(/#QID#([a-f0-9-]+)#/i);
            if (qidMatch && qidMatch[1] && questionMap[qidMatch[1]]) {
              const extractedQuestionId = qidMatch[1];
              console.log(`🔍 [useFlashcardGeneration] Extraído ID de questão do conteúdo: ${extractedQuestionId}`);
              card.source_question_id = extractedQuestionId;
              return processCard(card, questionMap[extractedQuestionId]);
            }
          }

          // Process card based on source_question_id
          if (card.source_question_id && questionMap[card.source_question_id]) {
            return processCard(card, questionMap[card.source_question_id]);
          }

          // Fallback to a question from the collection
          const questionIndex = index % shuffledQuestions.length;
          const sourceQuestion = shuffledQuestions[questionIndex];
          return processCard(card, sourceQuestion);
        });
      }
    } catch (e) {
      console.error(`❌ [useFlashcardGeneration] Erro ao processar string JSON:`, e);
      throw new Error(`Falha ao processar resposta da IA: ${e.message}`);
    }
  }
  // Check other possible locations for flashcard data
  else if (data.generatedText) {
    // Similar handling as above for data.generatedText
    console.log(`⚠️ [useFlashcardGeneration] Campo generatedText detectado, processando...`);
    const result = await parseGeneratedContent(
      { generated: data.generatedText },
      questionMap,
      shuffledQuestions,
      customProcessor
    );
    return result;
  }
  else if (data.data && Array.isArray(data.data)) {
    console.log(`⚠️ [useFlashcardGeneration] Usando array em data.data com ${data.data.length} itens`);
    iaFlashcards = data.data;
  }
  else if (data.flashcards && Array.isArray(data.flashcards)) {
    console.log(`⚠️ [useFlashcardGeneration] Usando array em data.flashcards com ${data.flashcards.length} itens`);
    iaFlashcards = data.flashcards;
  }

  // Última verificação para garantir que temos um array válido
  if (!Array.isArray(iaFlashcards) || iaFlashcards.length === 0) {
    // Try to find any array inside data
    const foundArray = ensureValidArray(data);
    if (foundArray.length > 0) {
      iaFlashcards = foundArray;
      console.log(`⚠️ [useFlashcardGeneration] Encontrado array em objeto de dados com ${iaFlashcards.length} itens`);
    } else {
      console.warn(`⚠️ [useFlashcardGeneration] Não foi possível encontrar flashcards nos dados`);
      iaFlashcards = [];
    }
  }



  return iaFlashcards;
};
