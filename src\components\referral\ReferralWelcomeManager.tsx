import React, { useEffect, useState } from 'react';
import { useReferralStatus } from '@/hooks/useReferralStatus';
import { ReferralWelcomeDialog } from './ReferralWelcomeDialog';

interface ReferralWelcomeManagerProps {
  onComplete?: () => void;
  children?: React.ReactNode;
}

export const ReferralWelcomeManager: React.FC<ReferralWelcomeManagerProps> = ({
  onComplete,
  children
}) => {
  const {
    isReferred,
    referrerName,
    referralCode,
    showNotification,
    isLoading,
    markNotificationAsShown
  } = useReferralStatus();

  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    // Mostrar o dialog se o usuário foi referenciado e deve mostrar notificação
    if (showNotification && isReferred && referralCode && !isLoading) {
      // Mostrar dialog imediatamente (referrerName pode ser 'Um amigo' como fallback)
      setDialogOpen(true);
    }
  }, [showNotification, isReferred, referrerName, referralCode, isLoading]);

  const handleDialogClose = (open: boolean) => {
    setDialogOpen(open);
    if (!open) {
      markNotificationAsShown();
      // Chamar callback quando o dialog for fechado
      if (onComplete) {
        onComplete();
      }
    }
  };

  return (
    <>
      {children}
      {/* Renderizar dialog sempre que necessário */}
      {showNotification && isReferred && referralCode && (
        <ReferralWelcomeDialog
          open={dialogOpen}
          onOpenChange={handleDialogClose}
          referrerName={referrerName || 'Um amigo'}
          referralCode={referralCode}
        />
      )}
    </>
  );
};
