
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useUserStudyStats } from "@/hooks/useUserStudyStats";
import type { StudyTopic } from "@/types/study-schedule";
import type { TopicEditDialogProps, TopicFormData } from "./types";
import { useCategoryData } from "./useCategoryData";
import TopicSelectionStep from "./TopicSelectionStep";
import ManualTopicForm from "./ManualTopicForm";
import TimeSettingsStep from "./TimeSettingsStep";
import TopicReviewStep from "./TopicReviewStep";

export const TopicEditDialog = ({
  open,
  onOpenChange,
  topic,
  onSave,
  categories,
  scheduleId,
  isCreating = false,
  isManual = false,
}: TopicEditDialogProps) => {
  // Removed excessive logging - only log when dialog opens
  if (open) {
    console.log("🔍 [TopicEditDialog] Dialog opened:", {
      isCreating,
      isManual,
      topicId: topic.id
    });
  }

  // State for form data
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialty, setSelectedSpecialty] = useState(topic.specialty);
  const [selectedTheme, setSelectedTheme] = useState(topic.theme);
  const [selectedFocus, setSelectedFocus] = useState(topic.focus);
  const [startTime, setStartTime] = useState(topic.startTime);
  const [duration, setDuration] = useState(topic.duration.replace(" horas", ""));
  const [activity, setActivity] = useState(topic.activity);
  
  // Manual input fields
  const [manualSpecialty, setManualSpecialty] = useState(topic.specialty || "");
  const [manualTheme, setManualTheme] = useState(topic.theme || "");
  const [manualFocus, setManualFocus] = useState(topic.focus || "");
  
  // UI state
  const [activeTab, setActiveTab] = useState<"topic" | "time" | "help">("topic");
  const [step, setStep] = useState(1);
  const [expandedSection, setExpandedSection] = useState<"specialty" | "theme" | "focus">("specialty");

  // Fetch user study statistics
  const { data: studyStats, isLoading: isLoadingStats } = useUserStudyStats();
  
  // Use custom hook to fetch categories data
  console.log('🔥 [TopicEditDialog] CALLING useCategoryData with:', {
    categoriesLength: categories.length,
    selectedSpecialty,
    selectedTheme
  });

  const {
    specialties,
    themes,
    focuses,
    shouldShowFocus,
    isLoading,
    domain
  } = useCategoryData(categories, selectedSpecialty, selectedTheme);

  console.log('🔥 [TopicEditDialog] useCategoryData RETURNED:', {
    specialtiesLength: specialties.length,
    themesLength: themes.length,
    focusesLength: focuses.length,
    domain,
    isLoading,
    shouldShowFocus
  });

  // LOGS AGRESSIVOS PARA DEBUG
  console.log('🔥 [TopicEditDialog] AGGRESSIVE DEBUG:', {
    open,
    categoriesLength: categories.length,
    selectedSpecialty,
    selectedTheme,
    selectedFocus,
    specialtiesLength: specialties.length,
    themesLength: themes.length,
    focusesLength: focuses.length,
    domain,
    isLoading,
    shouldShowFocus
  });

  // Log específico quando o dialog está aberto
  if (open) {
    console.log('🔥 [TopicEditDialog] DIALOG IS OPEN - Categories data:', {
      categories: categories.length,
      specialties: specialties.length,
      themes: themes.length,
      focuses: focuses.length,
      'focuses array': focuses.map(f => f.name)
    });
  }

  // Filter specialties, themes, and focuses based on search term
  const filteredSpecialties = specialties.filter(
    (specialty) => specialty.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredThemes = themes.filter(
    (theme) => theme.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFocuses = focuses.filter(
    (focus) => focus.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Update expanded section when selections change
  useEffect(() => {
    if (expandedSection === "specialty" && selectedSpecialty && !selectedTheme) {
      setExpandedSection("theme");
    } else if (expandedSection === "theme" && selectedTheme && !selectedFocus && shouldShowFocus) {
      setExpandedSection("focus");
    }
  }, [selectedSpecialty, selectedTheme, selectedFocus, expandedSection, shouldShowFocus]);

  // Handle form submission
  const handleSave = () => {
    console.log("🔥 [TopicEditDialog] HANDLE SAVE CALLED");
    console.log("🔥 [TopicEditDialog] isManual:", isManual);
    console.log("🔥 [TopicEditDialog] shouldShowFocus:", shouldShowFocus);
    console.log("🔥 [TopicEditDialog] SELECTED VALUES:", {
      selectedSpecialty,
      selectedTheme,
      selectedFocus,
      manualSpecialty,
      manualTheme,
      manualFocus
    });

    if (isManual) {
      // For manual entries
      if (shouldShowFocus) {
        // Normal case - require all three fields
        if (!manualSpecialty || !manualTheme || !manualFocus) {
          console.error("❗ Campos obrigatórios não preenchidos (manual)");
          return; 
        }
      } else {
        // Oftalmologia case - only require specialty and theme
        if (!manualSpecialty || !manualTheme) {
          console.error("❗ Campos obrigatórios não preenchidos (manual)");
          return;
        }
      }

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId: topic.scheduleId || scheduleId,
        specialty: manualSpecialty,
        theme: manualTheme,
        focus: shouldShowFocus ? manualFocus : "N/A", // Use default value for focus in oftalmologia
        startTime,
        duration: `${duration} horas`,
        activity,
        is_manual: true,
      };

      console.log("🔥 [TopicEditDialog] MANUAL TOPIC OBJECT:", updatedTopic);
      onSave(updatedTopic);
      onOpenChange(false);
    } else {
      console.log("🔥 [TopicEditDialog] SAVING PLATFORM TOPIC");
      // For platform entries
      if (shouldShowFocus) {
        // Normal case - require all three fields
        if (!selectedSpecialty || !selectedTheme || !selectedFocus) {
          console.error("❗ Campos obrigatórios não preenchidos (plataforma)");
          return;
        }
      } else {
        // Oftalmologia case - only require specialty and theme
        if (!selectedSpecialty || !selectedTheme) {
          console.error("❗ Campos obrigatórios não preenchidos (plataforma)");
          return;
        }
      }
      
      const selectedSpecialtyObj = specialties.find(
        (s) => s.name === selectedSpecialty
      );
      const selectedThemeObj = themes.find((t) => t.name === selectedTheme);
      
      // For oftalmologia, we don't need a focus
      const selectedFocusObj = shouldShowFocus 
        ? focuses.find((f) => f.name === selectedFocus)
        : undefined;

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId: topic.scheduleId || scheduleId,
        specialty: selectedSpecialty,
        specialtyId: selectedSpecialtyObj?.id,
        theme: selectedTheme,
        themeId: selectedThemeObj?.id,
        focus: shouldShowFocus ? selectedFocus : "N/A",
        focusId: shouldShowFocus ? selectedFocusObj?.id : undefined,
        startTime,
        duration: `${duration} horas`,
        activity,
        is_manual: false,
      };

      console.log("🔥 [TopicEditDialog] PLATFORM TOPIC OBJECT:", updatedTopic);
      console.log("🔥 [TopicEditDialog] CALLING onSave with topic:", updatedTopic.specialty, ">", updatedTopic.theme, ">", updatedTopic.focus);
      onSave(updatedTopic);
      console.log("🔥 [TopicEditDialog] CLOSING DIALOG");
      onOpenChange(false);
    }
  };

  // Navigation between steps
  const nextStep = () => {
    if (step < 3) {
      setStep(step + 1);
      if (step + 1 === 2) {
        setActiveTab("time");
      } else if (step + 1 === 3) {
        setActiveTab("help");
      }
    } else {
      handleSave();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
      if (step - 1 === 1) {
        setActiveTab("topic");
      } else if (step - 1 === 2) {
        setActiveTab("time");
      }
    }
  };

  // Helper functions
  const getDialogTitle = () => {
    if (isCreating) {
      return `${step}/3: ${isManual ? "Criando Tópico Manual" : "Selecionando Tópico da Plataforma"}`;
    } else {
      return "Editar Tópico de Estudo";
    }
  };

  const canContinue = () => {
    if (step === 1) {
      if (isManual) {
        return shouldShowFocus
          ? !!manualSpecialty && !!manualTheme && !!manualFocus 
          : !!manualSpecialty && !!manualTheme;
      } else {
        return shouldShowFocus
          ? !!selectedSpecialty && !!selectedTheme && !!selectedFocus
          : !!selectedSpecialty && !!selectedTheme;
      }
    }
    if (step === 2) {
      return !!startTime && !!duration;
    }
    return true;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md w-[calc(100%-2rem)] p-0 gap-0 overflow-hidden max-h-[90vh] flex flex-col">
        <DialogHeader className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-6 relative">
          <DialogTitle className="text-xl font-bold text-center">{getDialogTitle()}</DialogTitle>
          <DialogDescription className="text-white/90 text-center mt-1">
            {step === 1 && (isManual 
              ? "Complete os detalhes do seu tópico personalizado" 
              : "Selecione um tema específico para estudar")}
            {step === 2 && "Configure quando e quanto tempo deseja estudar"}
            {step === 3 && "Revise e confirme seu tópico de estudo"}
          </DialogDescription>
          <DialogClose className="absolute right-4 top-4 rounded-full bg-white/20 p-1 opacity-70 hover:opacity-100">
            <X className="h-4 w-4" />
          </DialogClose>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="flex-1 flex flex-col">
            <TabsList className="flex-none grid w-full grid-cols-3 rounded-none border-b">
              <TabsTrigger value="topic" disabled={step !== 1}>
                1. Tópico
              </TabsTrigger>
              <TabsTrigger value="time" disabled={step !== 2}>
                2. Horário
              </TabsTrigger>
              <TabsTrigger value="help" disabled={step !== 3}>
                3. Revisão
              </TabsTrigger>
            </TabsList>

            <TabsContent value="topic" className="flex-1 p-0">
              {isManual ? (
                <ManualTopicForm
                  manualSpecialty={manualSpecialty}
                  setManualSpecialty={setManualSpecialty}
                  manualTheme={manualTheme}
                  setManualTheme={setManualTheme}
                  manualFocus={manualFocus}
                  setManualFocus={setManualFocus}
                  domain={domain}
                  shouldShowFocus={shouldShowFocus}
                />
              ) : (
                <TopicSelectionStep
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  selectedSpecialty={selectedSpecialty}
                  setSelectedSpecialty={setSelectedSpecialty}
                  selectedTheme={selectedTheme}
                  setSelectedTheme={setSelectedTheme}
                  selectedFocus={selectedFocus}
                  setSelectedFocus={setSelectedFocus}
                  filteredSpecialties={filteredSpecialties}
                  filteredThemes={filteredThemes}
                  filteredFocuses={filteredFocuses}
                  expandedSection={expandedSection}
                  setExpandedSection={setExpandedSection}
                  studyStats={studyStats}
                  isLoading={isLoadingStats}
                  domain={domain}
                  shouldShowFocus={shouldShowFocus}
                />
              )}
            </TabsContent>

            <TabsContent value="time" className="flex-1 p-0">
              <TimeSettingsStep
                startTime={startTime}
                setStartTime={setStartTime}
                duration={duration}
                setDuration={setDuration}
                activity={activity}
                setActivity={setActivity}
              />
            </TabsContent>

            <TabsContent value="help" className="flex-1 p-0">
              <TopicReviewStep
                isManual={isManual}
                manualSpecialty={manualSpecialty}
                manualTheme={manualTheme}
                manualFocus={manualFocus}
                selectedSpecialty={selectedSpecialty}
                selectedTheme={selectedTheme}
                selectedFocus={selectedFocus}
                startTime={startTime}
                duration={duration}
                activity={activity}
                shouldShowFocus={shouldShowFocus}
              />
            </TabsContent>
          </Tabs>
        </div>

        <div className="border-t p-4 flex justify-between">
          <Button
            variant="outline"
            onClick={step > 1 ? prevStep : () => onOpenChange(false)}
          >
            {step > 1 ? "Voltar" : "Cancelar"}
          </Button>
          
          <Button 
            onClick={step < 3 ? nextStep : handleSave}
            disabled={!canContinue()}
            variant="duolingo"
            className="px-8"
          >
            {step < 3 ? "Continuar" : "Confirmar"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TopicEditDialog;
