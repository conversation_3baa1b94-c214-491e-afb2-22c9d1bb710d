
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";


import { ScrollArea } from "@/components/ui/scroll-area";
import { Check, Search, X, HelpCircle, CheckCircle, Info } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import type { StudyTopic } from "@/types/study-schedule";
import type { FilterOption } from "@/components/filters/types";
import { useUserStudyStats } from "@/hooks/useUserStudyStats";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useDomain } from "@/hooks/useDomain";
import { supabase } from "@/integrations/supabase/client";

interface TopicEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic;
  onSave: (updatedTopic: StudyTopic) => void;
  categories: FilterOption[];
  scheduleId: string;
  isCreating?: boolean;
  isManual?: boolean;
}

// Estilo para quebra de linha forçada
const forceWordBreakStyle = {
  wordWrap: 'break-word' as const,
  overflowWrap: 'break-word' as const,
  wordBreak: 'break-word' as const,
  hyphens: 'auto' as const,
  whiteSpace: 'normal' as const,
  maxWidth: '100%',
};

const TopicEditDialogComponent = ({
  open,
  onOpenChange,
  topic,
  onSave,
  categories,
  scheduleId,
  isCreating = false,
  isManual = false,
}: TopicEditDialogProps) => {
  console.log('🔧 [TopicEditDialog] Renderizado com:', { open, topic, scheduleId, isCreating, isManual });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialty, setSelectedSpecialty] = useState(topic.specialty);
  const [selectedTheme, setSelectedTheme] = useState(topic.theme);
  const [selectedFocus, setSelectedFocus] = useState(topic.focus);
  const [startTime, setStartTime] = useState(topic.startTime);
  // Função para extrair valor numérico da duração
  const parseDuration = (durationStr: string): string => {
    if (!durationStr) return "2";

    // Se já é um número, retorna como string
    if (!isNaN(Number(durationStr))) return durationStr;

    // Extrair horas de diferentes formatos
    if (durationStr.includes("horas")) {
      const match = durationStr.match(/(\d+(?:\.\d+)?)/);
      return match ? match[1] : "2";
    }

    if (durationStr.includes("minutos")) {
      const match = durationStr.match(/(\d+)/);
      const minutes = match ? parseInt(match[1]) : 120;
      return String(minutes / 60);
    }

    return "2"; // Default
  };

  const [duration, setDuration] = useState(parseDuration(topic.duration));

  // Função para formatar a duração para exibição no input
  const formatDurationForDisplay = (durationValue: string): string => {
    const num = parseFloat(durationValue);
    if (num < 1) {
      return String(num * 60); // Converter para minutos
    }
    return durationValue; // Manter em horas
  };

  // Função para obter a unidade da duração
  const getDurationUnit = (durationValue: string): string => {
    const num = parseFloat(durationValue);
    return num < 1 ? "minutos" : "horas";
  };

  // Função para converter valor do input de volta para horas
  const handleDurationChange = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return;

    // Se o valor atual está em minutos (< 1 hora), converter de volta para horas
    if (parseFloat(duration) < 1) {
      setDuration(String(num / 60));
    } else {
      setDuration(value);
    }
  };
  const [activity, setActivity] = useState(topic.activity);
  const [activeTab, setActiveTab] = useState<"topic" | "time" | "help">("topic");
  const [step, setStep] = useState(1);
  const { domain, isResidencia, isReady } = useDomain();

  // Determine if we should show the focus section
  const shouldShowFocus = domain !== "oftalmologia";

  const [manualSpecialty, setManualSpecialty] = useState(topic.specialty || "");
  const [manualTheme, setManualTheme] = useState(topic.theme || "");
  const [manualFocus, setManualFocus] = useState(topic.focus || "");

  const [expandedSection, setExpandedSection] = useState<"specialty" | "theme" | "focus">("specialty");
  const [isManualNavigation, setIsManualNavigation] = useState(false);

  const { data: studyStats, isLoading: isLoadingStats } = useUserStudyStats();



  // useEffect para fetch de questões (DESABILITADO para debug)
  // useEffect(() => {
  //   if (domain && isReady) {
  //     const fetchQuestionsByDomain = async () => {
  //       // Buscar apenas IDs das questões sem joins para evitar múltiplas requisições
  //       const { data, error } = await supabase
  //         .from('questions')
  //         .select('id, knowledge_domain, specialty_id')
  //         .eq('knowledge_domain', domain)
  //         .limit(5);

  //       if (error) {
  //         // Error handling - silent for production
  //       }
  //     };

  //     fetchQuestionsByDomain();
  //   }
  // }, [domain, isReady]);

  const specialties = React.useMemo(() => {
    if (!domain || !isReady) {
      return [];
    }

    // For revalida domain, we should display all specialties except Oftalmologia
    // For residencia domain, we should also display all specialties except Oftalmologia
    // For titulo (specialty) domain, we should display only that specialty
    if (domain === 'revalida' || domain === 'residencia') {
      return categories.filter(cat =>
        cat.type === "specialty" &&
        cat.name !== "Oftalmologia"
      );
    } else {
      return categories.filter(cat =>
        cat.type === "specialty" &&
        cat.name.toLowerCase() === domain.toLowerCase()
      );
    }
  }, [categories, domain, isReady]);

  const themes = categories.filter(
    (cat) =>
      cat.type === "theme" &&
      cat.parentId === specialties.find((s) => s.name === selectedSpecialty)?.id
  );

  const focuses = categories.filter(
    (cat) =>
      cat.type === "focus" &&
      cat.parentId === themes.find((t) => t.name === selectedTheme)?.id
  );



  // Filtros simples baseados apenas no termo de busca
  // Removemos a verificação de hasQuestions para mostrar todos os focos disponíveis
  const filteredSpecialties = specialties.filter(
    (specialty) =>
      specialty.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredThemes = themes.filter(
    (theme) =>
      theme.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFocuses = focuses.filter(
    (focus) =>
      focus.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // useEffect para auto-navegação apenas quando há seleções novas
  useEffect(() => {
    // Só executar se o dialog estiver aberto
    if (!open) return;

    // DESABILITA auto-navegação se o usuário já tem tema selecionado (está navegando em um fluxo existente)
    if (selectedTheme && selectedSpecialty) {
      return;
    }

    // Só faz navegação automática se não for navegação manual
    if (!isManualNavigation) {
      // Auto-navega para theme apenas se acabou de selecionar specialty e está na seção specialty
      if (expandedSection === "specialty" && selectedSpecialty && !selectedTheme) {
        setExpandedSection("theme");
      }
      // Auto-navega para focus apenas se acabou de selecionar theme e está na seção theme
      else if (expandedSection === "theme" && selectedTheme && !selectedFocus && shouldShowFocus) {
        setExpandedSection("focus");
      }
    }
  }, [open, selectedSpecialty, selectedTheme, selectedFocus, expandedSection, isManualNavigation, shouldShowFocus]);

  // useEffect separado para resetar o flag de navegação manual
  useEffect(() => {
    if (isManualNavigation && open) {
      const timer = setTimeout(() => {
        setIsManualNavigation(false);
      }, 3000);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [isManualNavigation, open]);

  const isItemStudied = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ): boolean => {
    if (!studyStats) return false;

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    return statsMap.some((stat) => stat.name === name);
  };

  const getStatsText = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    if (!studyStats) return "";

    const statsMap = {
      specialty: studyStats.specialty,
      theme: studyStats.theme,
      focus: studyStats.focus,
    }[type];

    const stats = statsMap.find((s) => s.name === name);
    if (!stats) return "";

    return ` (${stats.correct}/${stats.total})`;
  };

  const getBadgeStyle = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    const studied = isItemStudied(name, type);
    return studied
      ? "bg-green-100 text-green-800 hover:bg-green-200"
      : "bg-blue-100 text-blue-800 hover:bg-blue-200";
  };

  const getStatusEmoji = (
    name: string,
    type: "specialty" | "theme" | "focus"
  ) => {
    const studied = isItemStudied(name, type);
    return studied ? "✅" : "🆕";
  };

  const handleSave = () => {
    if (isManual) {
      // For manual entries
      if (shouldShowFocus) {
        // Normal case - require all three fields
        if (!manualSpecialty || !manualTheme || !manualFocus) {
          return;
        }
      } else {
        // Oftalmologia case - only require specialty and theme
        if (!manualSpecialty || !manualTheme) {
          return;
        }
      }

      // Convert decimal hours to minutes for better compatibility
      const durationMinutes = parseFloat(duration) * 60;
      let formattedDuration;

      if (durationMinutes < 60) {
        // For durations less than 1 hour, use "X minutos" format
        formattedDuration = `${durationMinutes} minutos`;
      } else if (durationMinutes % 60 === 0) {
        // For whole hours, use "X horas" format
        formattedDuration = `${Math.floor(durationMinutes / 60)} horas`;
      } else {
        // For hours and minutes, use "X horas e Y minutos" format
        formattedDuration = `${Math.floor(durationMinutes / 60)} horas e ${durationMinutes % 60} minutos`;
      }

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId,
        specialty: manualSpecialty,
        theme: manualTheme,
        focus: shouldShowFocus ? manualFocus : "N/A", // Use default value for focus in oftalmologia
        startTime,
        duration: formattedDuration,
        activity,
        is_manual: true,
      };


      onSave(updatedTopic);
      onOpenChange(false);
    } else {
      // For platform entries
      if (shouldShowFocus) {
        // Normal case - require all three fields
        if (!selectedSpecialty || !selectedTheme || !selectedFocus) {
          return;
        }
      } else {
        // Oftalmologia case - only require specialty and theme
        if (!selectedSpecialty || !selectedTheme) {
          return;
        }
      }

      const selectedSpecialtyObj = specialties.find(
        (s) => s.name === selectedSpecialty
      );
      const selectedThemeObj = themes.find((t) => t.name === selectedTheme);

      // For oftalmologia, we don't need a focus
      const selectedFocusObj = shouldShowFocus
        ? focuses.find((f) => f.name === selectedFocus)
        : undefined;

      // Convert decimal hours to minutes for better compatibility
      const durationMinutes = parseFloat(duration) * 60;
      let formattedDuration;

      if (durationMinutes < 60) {
        // For durations less than 1 hour, use "X minutos" format
        formattedDuration = `${durationMinutes} minutos`;
      } else if (durationMinutes % 60 === 0) {
        // For whole hours, use "X horas" format
        formattedDuration = `${Math.floor(durationMinutes / 60)} horas`;
      } else {
        // For hours and minutes, use "X horas e Y minutos" format
        formattedDuration = `${Math.floor(durationMinutes / 60)} horas e ${durationMinutes % 60} minutos`;
      }

      const updatedTopic: StudyTopic = {
        ...topic,
        scheduleId,
        specialty: selectedSpecialty,
        specialtyId: selectedSpecialtyObj?.id,
        theme: selectedTheme,
        themeId: selectedThemeObj?.id,
        focus: shouldShowFocus ? selectedFocus : "N/A",
        focusId: shouldShowFocus ? selectedFocusObj?.id : undefined,
        startTime,
        duration: formattedDuration,
        activity,
        is_manual: false,
      };


      onSave(updatedTopic);
      onOpenChange(false);
    }
  };

  const nextStep = () => {
    if (step < 3) {
      setStep(step + 1);
      if (step + 1 === 2) {
        setActiveTab("time");
      } else if (step + 1 === 3) {
        setActiveTab("help");
      }
    } else {
      handleSave();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
      if (step - 1 === 1) {
        setActiveTab("topic");
      } else if (step - 1 === 2) {
        setActiveTab("time");
      }
    }
  };

  const getDialogTitle = () => {
    if (isCreating) {
      return `${step}/3: ${isManual ? "Criando Tópico Manual" : "Selecionando Tópico da Plataforma"}`;
    } else {
      return "Editar Tópico de Estudo";
    }
  };

  const canContinue = () => {
    if (step === 1) {
      if (isManual) {
        return shouldShowFocus
          ? !!manualSpecialty && !!manualTheme && !!manualFocus
          : !!manualSpecialty && !!manualTheme;
      } else {
        return shouldShowFocus
          ? !!selectedSpecialty && !!selectedTheme && !!selectedFocus
          : !!selectedSpecialty && !!selectedTheme;
      }
    }
    if (step === 2) {
      return !!startTime && !!duration;
    }
    return true;
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        console.log('🔧 [TopicEditDialog] Dialog onOpenChange:', newOpen);
        // Só permitir fechar se for clique no botão de fechar ou salvar
        if (!newOpen) {
          console.log('🔧 [TopicEditDialog] Tentativa de fechar dialog - verificando se é permitido');
        }
        onOpenChange(newOpen);
      }}
      modal={true}
    >
      <DialogContent
        className="w-[90dvw] max-w-[500px] max-h-[90dvh] p-0 gap-0 overflow-hidden border-2 border-black rounded-xl flex flex-col"
        onPointerDownOutside={(e) => {
          console.log('🔧 [TopicEditDialog] onPointerDownOutside chamado - BLOQUEADO');
          e.preventDefault();
          e.stopPropagation();
        }}
        onEscapeKeyDown={(e) => {
          console.log('🔧 [TopicEditDialog] onEscapeKeyDown chamado - BLOQUEADO');
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          console.log('🔧 [TopicEditDialog] onInteractOutside chamado - BLOQUEADO');
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <DialogHeader className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-4 sm:p-5 relative rounded-t-xl">
          <div className="flex items-center justify-between gap-3">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-base sm:text-lg font-bold text-center">{getDialogTitle()}</DialogTitle>
              <DialogDescription className="text-white/90 text-center mt-1 text-xs sm:text-sm">
                {step === 1 && (isManual
                  ? "Complete os detalhes do seu tópico personalizado"
                  : "Selecione um tema específico para estudar")}
                {step === 2 && "Configure quando e quanto tempo deseja estudar"}
                {step === 3 && "Revise e confirme seu tópico de estudo"}
              </DialogDescription>
            </div>

            {/* Single close button */}
            <button
              onClick={() => onOpenChange(false)}
              className="flex-shrink-0 p-1.5 sm:p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              aria-label="Fechar"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="flex-1 flex flex-col">
            <TabsList className="flex-none grid w-full grid-cols-3 rounded-none border-b">
              <TabsTrigger value="topic" disabled={step !== 1}>
                1. Tópico
              </TabsTrigger>
              <TabsTrigger value="time" disabled={step !== 2}>
                2. Horário
              </TabsTrigger>
              <TabsTrigger value="help" disabled={step !== 3}>
                3. Revisão
              </TabsTrigger>
            </TabsList>

            <TabsContent value="topic" className="flex-1 p-0">
              {isManual ? (
                <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
                  <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-4 sm:mb-6 flex items-start gap-2 sm:gap-3">
                    <HelpCircle className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <p className="text-xs sm:text-sm text-blue-700 break-words">
                      {shouldShowFocus
                        ? "Crie seu próprio tópico personalizado com detalhes específicos. Ideal para temas que não estão disponíveis na plataforma."
                        : "Crie seu próprio tópico personalizado com especialidade e tema. Para oftalmologia, não é necessário especificar o foco."}
                    </p>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="specialty" className="text-sm sm:text-base font-medium">Especialidade</Label>
                      <Input
                        id="specialty"
                        value={manualSpecialty}
                        onChange={(e) => setManualSpecialty(e.target.value)}
                        placeholder={`Ex: ${domain === 'residencia' ? 'Cardiologia' : domain}`}
                        className="border-2 focus:border-green-500 h-9 sm:h-10 text-sm"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="theme" className="text-sm sm:text-base font-medium">Tema</Label>
                      <Input
                        id="theme"
                        value={manualTheme}
                        onChange={(e) => setManualTheme(e.target.value)}
                        placeholder="Ex: Glaucoma"
                        className="border-2 focus:border-green-500 h-9 sm:h-10 text-sm"
                      />
                    </div>

                    {shouldShowFocus && (
                      <div className="space-y-2">
                        <Label htmlFor="focus" className="text-sm sm:text-base font-medium">Foco</Label>
                        <Input
                          id="focus"
                          value={manualFocus}
                          onChange={(e) => setManualFocus(e.target.value)}
                          placeholder="Ex: Pressão Ocular"
                          className="border-2 focus:border-green-500 h-9 sm:h-10 text-sm"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col h-full">
                  <div className="flex-none p-3 sm:p-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={`Buscar em ${domain?.toLowerCase() || ''}...`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9 border-2 focus:border-green-500 h-9 sm:h-10 text-sm"
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-100 rounded-lg mx-3 sm:mx-4 p-3 mb-3 sm:mb-4 flex items-start gap-2 sm:gap-3">
                    <HelpCircle className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <p className="text-xs sm:text-sm text-blue-700 break-words">
                      {shouldShowFocus
                        ? `Escolha um tópico de ${domain?.toLowerCase() || ''} para estudar. Selecione primeiro a especialidade, depois o tema e por fim o foco específico.`
                        : `Escolha um tópico de ${domain?.toLowerCase() || ''} para estudar. Selecione a especialidade e depois o tema.`}
                    </p>
                  </div>

                  <div className="p-3 space-y-4">
                    <div className={`border rounded-lg p-4 bg-white ${expandedSection === "specialty" ? "border-green-500" : ""}`}>
                      <Button
                        variant="ghost"
                        className="w-full flex justify-between items-start mb-2 min-h-[44px] h-auto py-3"
                        onClick={() => {
                          const newSection = expandedSection === "specialty" ? "theme" : "specialty";
                          setIsManualNavigation(true);
                          setExpandedSection(newSection);
                        }}
                      >
                        <div className="flex items-start min-w-0 flex-1">
                          <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0 mt-0.5">1</span>
                          <div className="min-w-0 flex-1">
                            <div className="font-semibold text-left">Especialidade</div>
                            {selectedSpecialty && (
                              <div className="text-sm text-green-600 leading-tight mt-1" style={forceWordBreakStyle}>({selectedSpecialty})</div>
                            )}
                          </div>
                        </div>
                        <span className="text-gray-400 flex-shrink-0 ml-3 mt-0.5">{expandedSection === "specialty" ? "▼" : "▶"}</span>
                      </Button>

                      {expandedSection === "specialty" && (
                        <ScrollArea className="h-[35dvh] min-h-[300px] max-h-[400px] w-full mt-2">
                          <div className="space-y-1 pr-4">
                            {filteredSpecialties.length > 0 ? (
                              filteredSpecialties.map((specialty) => (
                                <Button
                                  key={specialty.id}
                                  variant={selectedSpecialty === specialty.name ? "secondary" : "ghost"}
                                  className="w-full justify-start mb-1 min-h-[48px] p-3 h-auto"
                                  onClick={() => {
                                    setSelectedSpecialty(specialty.name);
                                    setSelectedTheme("");
                                    setSelectedFocus("");
                                    setExpandedSection("theme");
                                  }}
                                >
                                  <div className="flex items-start w-full gap-3">
                                    {selectedSpecialty === specialty.name && (
                                      <Check className="h-4 w-4 flex-shrink-0 mt-0.5" />
                                    )}
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-start gap-2 flex-wrap">
                                        <div className="text-left text-sm font-medium leading-tight flex-1 min-w-0" style={forceWordBreakStyle}>
                                          {specialty.name}
                                        </div>
                                        <Badge
                                          className={`text-xs flex-shrink-0 ${getBadgeStyle(
                                            specialty.name,
                                            "specialty"
                                          )}`}
                                        >
                                          {getStatusEmoji(specialty.name, "specialty")}
                                          {getStatsText(specialty.name, "specialty")}
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </Button>
                              ))
                            ) : (
                              <p className="text-gray-500 text-sm py-2">Nenhuma especialidade encontrada com o termo buscado.</p>
                            )}
                          </div>
                        </ScrollArea>
                      )}
                    </div>

                    {selectedSpecialty && (
                      <div className={`border rounded-lg p-4 bg-white ${expandedSection === "theme" ? "border-green-500" : ""}`}>
                        <Button
                          variant="ghost"
                          className="w-full flex justify-between items-start mb-2 min-h-[44px] h-auto py-3"
                          onClick={() => {
                            const newSection = expandedSection === "theme" ? "specialty" : "theme";
                            setIsManualNavigation(true);
                            setExpandedSection(newSection);
                          }}
                        >
                          <div className="flex items-start min-w-0 flex-1">
                            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0 mt-0.5">{shouldShowFocus ? "2" : "2"}</span>
                            <div className="min-w-0 flex-1">
                              <div className="font-semibold text-left">Tema</div>
                              {selectedTheme && (
                                <div className="text-sm text-green-600 leading-tight mt-1" style={forceWordBreakStyle}>({selectedTheme})</div>
                              )}
                            </div>
                          </div>
                          <span className="text-gray-400 flex-shrink-0 ml-3 mt-0.5">{expandedSection === "theme" ? "▼" : "▶"}</span>
                        </Button>

                        {expandedSection === "theme" && (
                          <ScrollArea className="h-[35dvh] min-h-[300px] max-h-[400px] w-full mt-2">
                            <div className="space-y-1 pr-4">
                              {filteredThemes.length === 0 ? (
                                <p className="text-gray-500 text-sm py-2">Nenhum tema encontrado para esta especialidade.</p>
                              ) : (
                                filteredThemes.map((theme) => (
                                  <Button
                                    key={theme.id}
                                    variant={selectedTheme === theme.name ? "secondary" : "ghost"}
                                    className="w-full justify-start mb-1 min-h-[48px] p-3 h-auto"
                                    onClick={() => {
                                      setSelectedTheme(theme.name);
                                      setSelectedFocus("");
                                      if (shouldShowFocus) {
                                        setExpandedSection("focus");
                                      }
                                    }}
                                  >
                                    <div className="flex items-start w-full gap-3">
                                      {selectedTheme === theme.name && (
                                        <Check className="h-4 w-4 flex-shrink-0 mt-0.5" />
                                      )}
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-start gap-2 flex-wrap">
                                          <div className="text-left text-sm font-medium leading-tight flex-1 min-w-0" style={forceWordBreakStyle}>
                                            {theme.name}
                                          </div>
                                          <Badge
                                            className={`text-xs flex-shrink-0 ${getBadgeStyle(
                                              theme.name,
                                              "theme"
                                            )}`}
                                          >
                                            {getStatusEmoji(theme.name, "theme")}
                                            {getStatsText(theme.name, "theme")}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </Button>
                                ))
                              )}
                            </div>
                          </ScrollArea>
                        )}
                      </div>
                    )}

                    {selectedTheme && shouldShowFocus && (
                      <div className={`border rounded-lg p-4 bg-white ${expandedSection === "focus" ? "border-green-500" : ""}`}>
                        <Button
                          variant="ghost"
                          className="w-full flex justify-between items-start mb-2 min-h-[44px] h-auto py-3"
                          onClick={() => {
                            const newSection = expandedSection === "focus" ? "theme" : "focus";
                            setIsManualNavigation(true);
                            setExpandedSection(newSection);
                          }}
                        >
                          <div className="flex items-start min-w-0 flex-1">
                            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2 flex-shrink-0 mt-0.5">3</span>
                            <div className="min-w-0 flex-1">
                              <div className="font-semibold text-left">Foco</div>
                              {selectedFocus && (
                                <div className="text-sm text-green-600 leading-tight mt-1" style={forceWordBreakStyle}>({selectedFocus})</div>
                              )}
                            </div>
                          </div>
                          <span className="text-gray-400 flex-shrink-0 ml-3 mt-0.5">{expandedSection === "focus" ? "▼" : "▶"}</span>
                        </Button>

                        {expandedSection === "focus" && (
                          <ScrollArea className="h-[35dvh] min-h-[300px] max-h-[400px] w-full mt-2">
                            <div className="space-y-1 pr-4">
                              {filteredFocuses.length === 0 ? (
                                <p className="text-gray-500 text-sm py-2">Nenhum foco encontrado para este tema.</p>
                              ) : (
                                filteredFocuses.map((focus) => (
                                  <Button
                                    key={focus.id}
                                    variant={selectedFocus === focus.name ? "secondary" : "ghost"}
                                    className="w-full justify-start mb-1 min-h-[48px] p-3 h-auto"
                                    onClick={() => {
                                      setSelectedFocus(focus.name);
                                    }}
                                  >
                                    <div className="flex items-start w-full gap-3">
                                      {selectedFocus === focus.name && (
                                        <Check className="h-4 w-4 flex-shrink-0 mt-0.5" />
                                      )}
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-start gap-2 flex-wrap">
                                          <div className="text-left text-sm font-medium leading-tight flex-1 min-w-0" style={forceWordBreakStyle}>
                                            {focus.name}
                                          </div>
                                          <Badge
                                            className={`text-xs flex-shrink-0 ${getBadgeStyle(
                                              focus.name,
                                              "focus"
                                            )}`}
                                          >
                                            {getStatusEmoji(focus.name, "focus")}
                                            {getStatsText(focus.name, "focus")}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </Button>
                                ))
                              )}
                            </div>
                          </ScrollArea>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="border rounded-lg p-3 bg-slate-50">
                    <div className="flex items-center gap-1 mb-1">
                      <Info className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium text-slate-700">Legenda:</span>
                    </div>
                    <div className="text-xs text-slate-600 space-y-1">
                      <div className="flex items-center gap-1">
                        <span>✅</span>
                        <span>Tópico já estudado (questões corretas/total)</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>🆕</span>
                        <span>Tópico ainda não estudado</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="time" className="p-6 space-y-6">
              <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-2 flex items-start gap-3">
                <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-blue-700 break-words">
                  Configure o horário e a duração do seu estudo. Recomendamos blocos de 30 minutos a 2 horas para melhor aproveitamento.
                </p>
              </div>

              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="startTime" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">1</span>
                    Horário de Início
                  </Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="max-w-xs border-2 focus:border-green-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">2</span>
                    Duração do Estudo
                  </Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="duration"
                      type="number"
                      min={parseFloat(duration) < 1 ? "15" : "0.5"}
                      step={parseFloat(duration) < 1 ? "15" : "0.5"}
                      value={formatDurationForDisplay(duration)}
                      onChange={(e) => handleDurationChange(e.target.value)}
                      className="max-w-[100px] border-2 focus:border-green-500"
                    />
                    <span className="text-sm text-gray-500">{getDurationUnit(duration)}</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {[
                      { value: 0.25, label: "15min" },
                      { value: 0.5, label: "30min" },
                      { value: 1, label: "1h" },
                      { value: 1.5, label: "1.5h" },
                      { value: 2, label: "2h" }
                    ].map(({ value, label }) => (
                      <Button
                        key={value}
                        type="button"
                        variant={duration === String(value) ? "secondary" : "outline"}
                        size="sm"
                        onClick={() => setDuration(String(value))}
                        className="px-3"
                      >
                        {label}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="activity" className="text-base font-medium flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">3</span>
                    Descrição da Atividade
                  </Label>
                  <Textarea
                    id="activity"
                    value={activity}
                    onChange={(e) => setActivity(e.target.value)}
                    placeholder="Ex: Leitura + Questões"
                    className="h-20 border-2 focus:border-green-500"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="help" className="p-6 space-y-6">
              <div className="bg-green-50 border border-green-100 rounded-lg p-4 mb-6 flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-green-700 break-words">
                  Revise os detalhes do seu tópico de estudo antes de confirmar.
                </p>
              </div>

              <div className="space-y-6">
                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Tópico de Estudo</h3>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Especialidade:</span>
                      <span>{isManual ? manualSpecialty : selectedSpecialty}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Tema:</span>
                      <span>{isManual ? manualTheme : selectedTheme}</span>
                    </div>
                    {shouldShowFocus && (
                      <div className="flex gap-2">
                        <span className="font-medium text-gray-500">Foco:</span>
                        <span>{isManual ? manualFocus : selectedFocus}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Horário</h3>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Início:</span>
                      <span>{startTime}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Duração:</span>
                      <span>{
                        parseFloat(duration) < 1
                          ? `${parseFloat(duration) * 60}min`
                          : parseFloat(duration) === 1
                            ? "1h"
                            : `${duration}h`
                      }</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-medium text-gray-500">Atividade:</span>
                      <span>{activity || "Não especificada"}</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-white">
                  <h3 className="font-semibold mb-2 text-green-700">Dicas</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Revise seu estudo regularmente para melhor retenção</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Combine teoria e prática para fixar o conhecimento</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Faça pausas entre os períodos de estudo</span>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="border-t p-4 flex justify-between">
          <Button
            variant="outline"
            onClick={step > 1 ? prevStep : () => onOpenChange(false)}
          >
            {step > 1 ? "Voltar" : "Cancelar"}
          </Button>

          <Button
            onClick={step < 3 ? nextStep : handleSave}
            disabled={!canContinue()}
            variant="duolingo"
            className="px-8"
          >
            {step < 3 ? "Continuar" : "Confirmar"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Memoizar o componente para evitar re-renders desnecessários
export const TopicEditDialog = React.memo(TopicEditDialogComponent);

export default TopicEditDialog;
