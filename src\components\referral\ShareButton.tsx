import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Share2,
  Copy,
  Users,
  Gift,
  ExternalLink,
  QrCode,
  MessageCircle,
  Mail,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useReferralSystem } from '@/hooks/useReferralSystem';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import QRCode from 'qrcode';

interface ShareButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showText?: boolean;
}

export const ShareButton: React.FC<ShareButtonProps> = ({
  variant = 'default',
  size = 'default',
  className = '',
  showText = true
}) => {
  const [enableQueries, setEnableQueries] = useState(false);

  const {
    referralData,
    referredUsers,
    isLoadingReferral,
    createReferralCode,
    isCreatingCode,
    hasReferralCode,
    totalReferrals,
    referralCode,
    generateShareLink,
    copyShareLink,
    shareLink
  } = useReferralSystem({ enableQueries });

  const [open, setOpen] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');

  const handleCreateReferralCode = async () => {
    try {
      await createReferralCode();
      setDialogMessage('Código de referência criado com sucesso!');
      setShowSuccessDialog(true);
    } catch (error: any) {
      setDialogMessage('Erro ao criar código: ' + error.message);
      setShowErrorDialog(true);
    }
  };

  const handleCopyLink = async () => {
    const success = await copyShareLink();
    if (success) {
      setDialogMessage('Link copiado para a área de transferência!');
      setShowSuccessDialog(true);
    } else {
      setDialogMessage('Erro ao copiar link');
      setShowErrorDialog(true);
    }
  };

  const handleGenerateQR = async () => {
    const link = generateShareLink();
    if (link) {
      try {
        const qr = await QRCode.toDataURL(link, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        setQrCodeUrl(qr);
      } catch (error) {
        console.error('Erro ao gerar QR Code:', error);
      }
    }
  };

  const handleOpenDialog = () => {
    setOpen(true);
    // Só carregar dados quando o dialog for aberto
    if (!enableQueries) {
      setEnableQueries(true);
    }
    if (hasReferralCode) {
      handleGenerateQR();
    }
  };

  const shareViaWhatsApp = () => {
    const link = generateShareLink();
    const message = `🎓 Venha estudar comigo na MedEvo!\n\nO melhor banco de questões:\n✅ Milhares de questões\n✅ Sistema inteligente de progresso\n✅ Estatísticas detalhadas\n\nCadastre-se pelo meu link: ${link}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const shareViaEmail = () => {
    const link = generateShareLink();
    const subject = 'Convite para MedEvo - Plataforma de Estudos';
    const body = `Olá!\n\nQuero te convidar para conhecer a MedEvo, uma plataforma incrível de questões para residência médica!\n\nCom ela você pode:\n• Praticar com milhares de questões\n• Acompanhar seu progresso detalhadamente\n• Estudar de forma inteligente e eficiente\n\nCadastre-se pelo meu link de convite: ${link}\n\nVamos estudar juntos!\n\nAbraços!`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(emailUrl);
  };

  if (isLoadingReferral) {
    return (
      <Button variant={variant} size={size} className={className} disabled>
        <Share2 className="h-4 w-4 mr-2" />
        {showText && 'Carregando...'}
      </Button>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={className}
          onClick={handleOpenDialog}
        >
          <Share2 className="h-4 w-4 mr-2" />
          {showText && 'Convidar Amigos'}
          {totalReferrals > 0 && (
            <Badge variant="secondary" className="ml-2">
              {totalReferrals}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-md w-[90dvw] max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500" />
            Convidar Amigos
          </DialogTitle>
          <DialogDescription>
            Compartilhe seu código de convite e ajude a expandir nossa comunidade de estudos.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 p-1">
          {!hasReferralCode ? (
            <div className="text-center space-y-4">
              <div className="p-4 bg-[#E6F2FF]/50 rounded-xl border border-black/20">
                <Gift className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-700">
                  Crie seu código de convite e compartilhe com amigos!
                </p>
              </div>
              <Button
                onClick={handleCreateReferralCode}
                disabled={isCreatingCode}
                className="w-full bg-[#E6F2FF] text-black hover:bg-[#CCE7FF] border-2 border-black/30 rounded-xl"
              >
                {isCreatingCode ? 'Criando...' : 'Criar Código de Convite'}
              </Button>
            </div>
          ) : (
            <>
              {/* Estatísticas */}
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-[#E6F3E6]/50 rounded-xl border border-black/20">
                  <div className="text-2xl font-bold text-green-700">
                    {totalReferrals}
                  </div>
                  <div className="text-xs text-gray-700 font-medium">
                    Amigos convidados
                  </div>
                </div>
                <div className="text-center p-3 bg-[#E6F2FF]/50 rounded-xl border border-black/20">
                  <div className="text-lg font-bold text-blue-700">
                    {referralCode}
                  </div>
                  <div className="text-xs text-gray-700 font-medium">
                    Seu código
                  </div>
                </div>
              </div>

              <Separator />

              {/* Opções de compartilhamento */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Compartilhar via:</h4>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={shareViaWhatsApp}
                    className="flex items-center gap-2 rounded-xl border-2 border-black/30"
                  >
                    <MessageCircle className="h-4 w-4 text-green-600" />
                    WhatsApp
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={shareViaEmail}
                    className="flex items-center gap-2 rounded-xl border-2 border-black/30"
                  >
                    <Mail className="h-4 w-4 text-blue-600" />
                    Email
                  </Button>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="w-full flex items-center gap-2 rounded-xl border-2 border-black/30"
                >
                  <Copy className="h-4 w-4" />
                  Copiar Link
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareLink()}
                  className="w-full flex items-center gap-2 rounded-xl border-2 border-black/30"
                >
                  <Share2 className="h-4 w-4" />
                  Compartilhar
                </Button>
              </div>

              {/* QR Code */}
              {qrCodeUrl && (
                <>
                  <Separator />
                  <div className="text-center space-y-2">
                    <h4 className="font-medium text-sm flex items-center justify-center gap-2">
                      <QrCode className="h-4 w-4" />
                      QR Code
                    </h4>
                    <img
                      src={qrCodeUrl}
                      alt="QR Code do convite"
                      className="mx-auto border-2 border-black/20 rounded-xl"
                    />
                    <p className="text-xs text-gray-500">
                      Escaneie para acessar o link de convite
                    </p>
                  </div>
                </>
              )}

              {/* Lista de amigos convidados */}
              {referredUsers && referredUsers.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Amigos convidados:</h4>
                    <div className="max-h-32 overflow-y-auto space-y-2">
                      {referredUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center justify-between p-3 bg-[#FEF7CD]/30 rounded-xl border border-black/10 text-sm"
                        >
                          <span className="font-medium">
                            {user.profiles?.full_name || 'Usuário'}
                          </span>
                          <span className="text-xs text-gray-600">
                            {new Date(user.referred_at).toLocaleDateString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </DialogContent>

      {/* Dialog de Sucesso */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
          <div className="w-full py-6 bg-gradient-to-br from-green-50 to-emerald-50 border-b border-black/20">
            <div className="flex flex-col items-center text-center gap-3 px-6">
              <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-green-200">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Sucesso!</h2>
            </div>
          </div>
          <div className="p-6 space-y-4">
            <DialogHeader>
              <DialogTitle className="sr-only">Operação realizada com sucesso</DialogTitle>
              <DialogDescription className="text-center text-gray-700">
                {dialogMessage}
              </DialogDescription>
            </DialogHeader>
            <Button
              onClick={() => setShowSuccessDialog(false)}
              className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl h-12 font-semibold"
            >
              Continuar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog de Erro */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
          <div className="w-full py-6 bg-gradient-to-br from-red-50 to-pink-50 border-b border-black/20">
            <div className="flex flex-col items-center text-center gap-3 px-6">
              <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-red-200">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Atenção</h2>
            </div>
          </div>
          <div className="p-6 space-y-4">
            <DialogHeader>
              <DialogTitle className="sr-only">Erro na operação</DialogTitle>
              <DialogDescription className="text-center text-gray-700">
                {dialogMessage}
              </DialogDescription>
            </DialogHeader>
            <Button
              onClick={() => setShowErrorDialog(false)}
              className="w-full bg-red-600 hover:bg-red-700 text-white rounded-xl h-12 font-semibold"
            >
              Entendi
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
};
