
export interface StudyPeriod {
  startTime: string;
  endTime: string;
}

export interface DayConfig {
  enabled: boolean;
  periods: StudyPeriod[];
}

export interface AIScheduleFormData {
  availableDays: {
    [key: string]: DayConfig;
  };
  weeksCount: number;
  topicDuration: "15" | "30" | "60";
  scheduleOption: "new" | "existing";
  targetWeek?: number;
  domain?: string; // Added domain field to pass through to the schedule generation
  // Novos campos para filtro por instituição
  generationMode?: 'random' | 'institution_based';
  institutionIds?: string[];
  startYear?: number;
  endYear?: number;
}

// Generation phases are used to track progress in the AIScheduleDialog
export type GenerationPhase = 
  | 'not_started'   // Initial state
  | 'creating_weeks' // Creating week structure in database
  | 'analyzing_specialties' // Analyzing available specialties
  | 'generating_topics' // Generating topics for each day
  | 'completing'    // Finalizing generation
  | 'completed'     // Generation completed successfully
  | 'error';        // Error occurred during generation

export const WEEK_DAYS = [
  { id: "Domingo", label: "<PERSON>" },
  { id: "Segunda-feira", label: "Segunda-feira" },
  { id: "Ter<PERSON>-feira", label: "Terça-feira" },
  { id: "Quarta-feira", label: "Quarta-feira" },
  { id: "Quinta-feira", label: "Quinta-feira" },
  { id: "Sexta-feira", label: "Sexta-feira" },
  { id: "Sábado", label: "Sábado" },
];

// Generation statistics to display after completion
export interface GenerationStats {
  topicsCreated: number;
  specialties: string[];
  timeSpent: number;
}

// Dialog visibility modes to ensure we always show progress when needed
export type DialogVisibilityMode =
  | 'form'         // Show the form (default)
  | 'progress'     // Show the progress view
  | 'success';     // Show the success view
