
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { BookOpen, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { ConfirmStudyDialog } from "./ConfirmStudyDialog";

interface RevisionButtonProps {
  topicId: string;
  studyStatus?: 'pending' | 'completed';
  revisionNumber?: number;
  nextRevisionDate?: string;
  onMarkStudied: (topicId: string) => void;
  isRevision?: boolean;
  topicName?: string;
}

export const RevisionButton = ({ 
  topicId, 
  studyStatus, 
  revisionNumber,
  nextRevisionDate,
  onMarkStudied,
  isRevision,
  topicName
}: RevisionButtonProps) => {
  const isMobile = useIsMobile();
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const getStatusBadge = () => {
    if (studyStatus === 'completed') {
      return (
        <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <Check className="h-3 w-3" />
          {revisionNumber ? `${revisionNumber}ª Revisão` : 'Estudado'}
        </Badge>
      );
    }
    return null;
  };

  const getButtonText = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const nextRevision = nextRevisionDate ? new Date(nextRevisionDate) : null;

    // Caso 1: Tópico original não estudado
    if (!isRevision && studyStatus !== 'completed') {
      return isMobile ? 'Marcar Estudado' : 'Marcar como Estudado';
    }

    // Caso 2: Tópico original já estudado
    if (!isRevision && studyStatus === 'completed') {
      return null; // Não mostra botão no tópico original após estudado
    }

    // Caso 3: É uma revisão e ainda não foi feita
    if (isRevision && studyStatus !== 'completed') {
      // Se é uma revisão futura, não mostra o botão até a data chegar
      if (nextRevisionDate && new Date(nextRevisionDate) > today) {
        return null;
      }
      
      // Texto melhorado para as revisões
      if (revisionNumber === 1) {
        return isMobile ? '1ª Rev.' : 'Primeira Revisão';
      } else if (revisionNumber === 2) {
        return isMobile ? '2ª Rev.' : 'Segunda Revisão';
      } else if (revisionNumber === 3) {
        return isMobile ? '3ª Rev.' : 'Terceira Revisão';
      } else {
        // Para o caso do primeiro estudo (que gera a primeira revisão)
        return isMobile ? 'Marcar Estudado' : 'Marcar como Estudado';
      }
    }

    // Caso 4: Revisão já feita
    if (isRevision && studyStatus === 'completed') {
      return null;
    }

    return null;
  };

  const handleClickStudyButton = () => {
    setDialogOpen(true);
  };

  const handleConfirmStudy = () => {
    onMarkStudied(topicId);
    setDialogOpen(false);
  };

  const buttonText = getButtonText();
  if (!buttonText) return null;

  return (
    <>
      <div className="flex items-center gap-2">
        {getStatusBadge()}
        <Button
          variant="duolingo"
          size="sm"
          className="gap-1.5 px-2.5 py-1 sm:px-3 sm:gap-2 whitespace-nowrap text-xs sm:text-sm h-7 sm:h-8 flex-shrink-0 rounded-xl font-medium shadow-sm"
          onClick={handleClickStudyButton}
          disabled={revisionNumber === 3}
        >
          <BookOpen className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
          {buttonText}
        </Button>
      </div>
      
      <ConfirmStudyDialog 
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onConfirm={handleConfirmStudy}
        topicName={topicName || "este tópico"}
      />
    </>
  );
};
