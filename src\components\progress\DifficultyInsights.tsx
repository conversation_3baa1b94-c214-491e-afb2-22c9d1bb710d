import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Brain,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  Lightbulb,
  Zap,
  Timer,
  TrendingUp,
  Award,
  BookOpen,
  ChevronLeft,
  ChevronRight,
  Star,
  ArrowRight,
  HelpCircle
} from 'lucide-react';
import { useDifficultyInsights, type DifficultyInsight, type SmartRecommendation } from '@/hooks/useDifficultyInsights';
import { useUserStatistics } from '@/hooks/useUserStatistics';
import { TimeExplanationDialog } from './TimeExplanationDialog';

// ✅ Função para traduzir tipos de questão
const translateQuestionFormat = (format: string): string => {
  const translations: Record<string, string> = {
    'ALTERNATIVAS': 'Múltipla Escolha',
    'DISSERTATIVA': 'Dissertativa',
    'VERDADEIRO_FALSO': 'Verdadeiro ou Falso',
    'LACUNAS': 'Preencher Lacunas',
    'ASSOCIACAO': 'Associação',
    'ORDENACAO': 'Ordenação',
    'UNKNOWN': 'Não Especificado'
  };

  return translations[format] || format;
};

// ✅ Função para encurtar nomes longos
const shortenCategoryName = (name: string, maxLength: number = 30): string => {
  if (name.length <= maxLength) return name;

  // Tentar pegar apenas a primeira parte antes de ":"
  const firstPart = name.split(':')[0];
  if (firstPart.length <= maxLength) return firstPart;

  // Se ainda for muito longo, truncar e adicionar "..."
  return name.substring(0, maxLength - 3) + '...';
};

const DifficultyBadge = ({ level }: { level: 'easy' | 'medium' | 'hard' }) => {
  const config = {
    easy: { color: 'bg-green-100 text-green-800', label: 'Fácil', icon: CheckCircle },
    medium: { color: 'bg-yellow-100 text-yellow-800', label: 'Médio', icon: Target },
    hard: { color: 'bg-red-100 text-red-800', label: 'Difícil', icon: AlertTriangle }
  };

  const { color, label, icon: Icon } = config[level];

  return (
    <Badge className={`${color} flex items-center gap-1`}>
      <Icon className="h-3 w-3" />
      {label}
    </Badge>
  );
};

const TimeEfficiencyBadge = ({ efficiency }: { efficiency: 'fast' | 'normal' | 'slow' }) => {
  const config = {
    fast: { color: 'bg-blue-100 text-blue-800', label: 'Rápido', icon: Zap },
    normal: { color: 'bg-gray-100 text-gray-800', label: 'Normal', icon: Clock },
    slow: { color: 'bg-orange-100 text-orange-800', label: 'Lento', icon: Timer }
  };

  const { color, label, icon: Icon } = config[efficiency];

  return (
    <Badge className={`${color} flex items-center gap-1`}>
      <Icon className="h-3 w-3" />
      {label}
    </Badge>
  );
};

const CategoryInsightCard = ({ insight }: { insight: DifficultyInsight }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 bg-white hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-medium text-gray-800">{insight.category}</h4>
          <p className="text-sm text-gray-500 capitalize">{insight.categoryType}</p>
        </div>
        <div className="flex gap-2">
          <DifficultyBadge level={insight.difficultyLevel} />
          <TimeEfficiencyBadge efficiency={insight.timeEfficiency} />
        </div>
      </div>

      <div className="space-y-3">
        {/* Precisão */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Precisão</span>
            <span className="font-medium">{insight.accuracy}%</span>
          </div>
          <Progress value={insight.accuracy} className="h-2" />
        </div>

        {/* Consistência */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Consistência</span>
            <span className="font-medium">{insight.consistencyScore}%</span>
          </div>
          <Progress value={insight.consistencyScore} className="h-2" />
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Tempo médio</span>
            <p className="font-medium">{insight.avgTimeSpent}s</p>
          </div>
          <div>
            <span className="text-gray-500">Questões</span>
            <p className="font-medium">{insight.totalQuestions}</p>
          </div>
        </div>

        {/* Dicas de melhoria */}
        {insight.improvementTips.length > 0 && (
          <div className="mt-3 p-2 bg-blue-50 rounded-md">
            <div className="flex items-center gap-1 mb-1">
              <Lightbulb className="h-3 w-3 text-blue-600" />
              <span className="text-xs font-medium text-blue-800">Dicas</span>
            </div>
            <ul className="text-xs text-blue-700 space-y-1">
              {insight.improvementTips.slice(0, 2).map((tip, index) => (
                <li key={index}>• {tip}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </motion.div>
  );
};

const SmartRecommendationCard = ({ recommendation, index }: { recommendation: SmartRecommendation; index: number }) => {
  const priorityConfig = {
    high: { color: 'bg-gradient-to-r from-red-50 to-red-100 border-red-200', badge: 'bg-red-500 text-white', icon: '🔥' },
    medium: { color: 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200', badge: 'bg-yellow-500 text-white', icon: '⚡' },
    low: { color: 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200', badge: 'bg-blue-500 text-white', icon: '💡' }
  };

  const typeLabels = {
    weakness: 'Ponto Fraco',
    strength: 'Ponto Forte',
    opportunity: 'Oportunidade',
    time_management: 'Gestão de Tempo'
  };

  const { color, badge, icon } = priorityConfig[recommendation.priority];

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={`border rounded-xl p-4 ${color} hover:shadow-md transition-all duration-200`}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-lg">
            {icon}
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0 mr-2">
              <div className="flex items-center gap-2 mb-1">
                <Badge className={`${badge} text-xs px-2 py-0.5`}>
                  {typeLabels[recommendation.type]}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {recommendation.priority === 'high' ? 'Alta' :
                   recommendation.priority === 'medium' ? 'Média' : 'Baixa'}
                </Badge>
              </div>
              <h4 className="font-medium text-gray-800 text-sm leading-tight">
                {recommendation.category}
              </h4>
            </div>
          </div>

          <p className="text-xs text-gray-700 mb-3 leading-relaxed">
            {recommendation.message}
          </p>

          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <ArrowRight className="h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0" />
              <span className="text-xs text-gray-700 leading-relaxed">
                <span className="font-medium text-blue-800">Ação: </span>
                {recommendation.action}
              </span>
            </div>

            <div className="flex items-start gap-2">
              <Star className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-xs text-gray-700 leading-relaxed">
                <span className="font-medium text-green-800">Resultado: </span>
                {recommendation.estimatedImpact}
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const QuestionTypeChart = ({ data }: { data: any[] }) => {
  const maxDifficulty = Math.max(...data.map(d => d.difficultyRating));

  return (
    <div className="space-y-4">
      {data.map((type) => (
        <div key={type.questionFormat} className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">{type.questionFormat}</span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>{type.accuracy.toFixed(1)}%</span>
              <span>•</span>
              <span>{type.avgTime}s</span>
              <span>•</span>
              <span>{type.totalQuestions} questões</span>
            </div>
          </div>

          <div className="flex gap-2">
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span>Precisão</span>
                <span>{type.accuracy.toFixed(1)}%</span>
              </div>
              <Progress value={type.accuracy} className="h-2" />
            </div>

            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span>Dificuldade</span>
                <span>{type.difficultyRating}/100</span>
              </div>
              <Progress
                value={type.difficultyRating}
                className="h-2"
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const DifficultyInsights = () => {
  const { data: difficultyData, isLoading, error } = useDifficultyInsights();
  const [currentPage, setCurrentPage] = useState(0);
  const ITEMS_PER_PAGE = 10;

  // ✅ IMPORTAR DADOS DO useUserStatistics PARA CONSISTÊNCIA
  const { data: userStats } = useUserStatistics();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !difficultyData) {
    return (
      <div className="text-center text-gray-500 p-8">
        <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
        <p>Dados insuficientes para análise de dificuldade</p>
      </div>
    );
  }

  const {
    questionTypeAnalysis,
    recommendations,
    overallDifficulty,
    weaknessAreas,
    strengthAreas
  } = difficultyData;

  // ✅ FILTRAR APENAS ESPECIALIDADES e limitar quantidade
  const topWeaknesses = weaknessAreas
    .filter(area => area.categoryType === 'specialty')
    .slice(0, 3);

  const topStrengths = strengthAreas
    .filter(area => area.categoryType === 'specialty')
    .slice(0, 3);

  // ✅ RECOMENDAÇÕES ÚNICAS E MELHORADAS - Sem encurtar nomes
  const allUniqueRecommendations = recommendations
    .filter(rec => rec.priority === 'high' || rec.priority === 'medium')
    .reduce((unique, rec) => {
      // Evitar duplicatas baseadas na categoria
      const exists = unique.find(existing => existing.category === rec.category);
      if (!exists) {
        unique.push(rec); // Manter nomes originais
      }
      return unique;
    }, [] as typeof recommendations);

  // ✅ PAGINAÇÃO
  const totalPages = Math.ceil(allUniqueRecommendations.length / ITEMS_PER_PAGE);
  const startIndex = currentPage * ITEMS_PER_PAGE;
  const currentRecommendations = allUniqueRecommendations.slice(startIndex, startIndex + ITEMS_PER_PAGE);

  return (
    <div className="space-y-6">
      {/* ✅ RESUMO COM ANÁLISE DE TEMPO DETALHADA */}
      <div className="space-y-4">
        {/* Categorias Mais Fácil e Mais Difícil */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1 mr-2">
                  <p className="text-xs text-gray-600">Mais Fácil</p>
                  <p className="text-sm font-bold text-green-600 truncate" title={overallDifficulty.easiestCategory}>
                    {overallDifficulty.easiestCategory}
                  </p>
                </div>
                <Award className="h-6 w-6 text-green-500 flex-shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-red-500">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1 mr-2">
                  <p className="text-xs text-gray-600">Mais Difícil</p>
                  <p className="text-sm font-bold text-red-600 truncate" title={overallDifficulty.hardestCategory}>
                    {overallDifficulty.hardestCategory}
                  </p>
                </div>
                <AlertTriangle className="h-6 w-6 text-red-500 flex-shrink-0" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Análise de Tempo Detalhada */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Análise de Tempo
            </h3>
            <TimeExplanationDialog>
              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </TimeExplanationDialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-1 gap-4 max-w-md mx-auto">
            {/* Tempo por Questão - Card único */}
            <Card className="border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <p className="text-sm font-medium text-gray-700">Tempo por Questão</p>
                    </div>
                    <p className="text-2xl font-bold text-blue-600">
                      {userStats ? Math.round(userStats.avg_response_time) : overallDifficulty.avgTimePerQuestion}s
                    </p>
                    <p className="text-xs text-blue-700">
                      {(() => {
                        const avgTime = userStats
                          ? Math.round(userStats.avg_response_time)
                          : overallDifficulty.avgTimePerQuestion;

                        if (avgTime <= 15) return '⚡ Muito rápido - cuidado com chutes';
                        if (avgTime <= 45) return '✅ Ritmo ideal para questões';
                        if (avgTime <= 90) return '⏱️ Ritmo normal, pode acelerar';
                        return '🐌 Muito lento, precisa treinar';
                      })()}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* ✅ RECOMENDAÇÕES INTELIGENTES COM PAGINAÇÃO */}
      {allUniqueRecommendations.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Recomendações Inteligentes
              </CardTitle>
              <Badge variant="outline" className="text-xs">
                {allUniqueRecommendations.length} total
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentRecommendations.map((rec, index) => (
                <SmartRecommendationCard
                  key={`${currentPage}-${index}`}
                  recommendation={rec}
                  index={index}
                />
              ))}
            </div>

            {/* ✅ PAGINAÇÃO */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-4 border-t">
                <div className="text-sm text-gray-600">
                  Página {currentPage + 1} de {totalPages}
                  <span className="text-gray-400 ml-2">
                    ({startIndex + 1}-{Math.min(startIndex + ITEMS_PER_PAGE, allUniqueRecommendations.length)} de {allUniqueRecommendations.length})
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                    disabled={currentPage === 0}
                    className="h-8 px-3"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <Button
                        key={i}
                        variant={currentPage === i ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(i)}
                        className="h-8 w-8 p-0"
                      >
                        {i + 1}
                      </Button>
                    ))}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                    disabled={currentPage === totalPages - 1}
                    className="h-8 px-3"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* ✅ ANÁLISE POR TIPO DE QUESTÃO - Compacta */}
      {questionTypeAnalysis.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Performance por Tipo de Questão
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {questionTypeAnalysis.slice(0, 4).map((type) => (
                <div key={type.questionFormat} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="min-w-0 flex-1 mr-3">
                    <h4 className="font-medium text-gray-800 text-sm">
                      {translateQuestionFormat(type.questionFormat)}
                    </h4>
                    <p className="text-xs text-gray-600">{type.totalQuestions} questões</p>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <p className="text-lg font-bold text-gray-800">{type.accuracy.toFixed(1)}%</p>
                    <p className="text-xs text-gray-500">{type.avgTime}s por questão</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ PONTOS FRACOS - Apenas especialidades, máximo 3 */}
      {topWeaknesses.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-orange-600">
              <AlertTriangle className="h-5 w-5" />
              Especialidades para Melhorar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topWeaknesses.map((weakness) => (
                <div key={weakness.categoryId} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="min-w-0 flex-1 mr-3">
                    <h4 className="font-medium text-gray-800 text-sm truncate" title={weakness.category}>
                      {shortenCategoryName(weakness.category, 25)}
                    </h4>
                    <p className="text-xs text-gray-600">{weakness.accuracy}% de acertos</p>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <DifficultyBadge level={weakness.difficultyLevel} />
                    <p className="text-xs text-gray-500 mt-1">{weakness.avgTimeSpent}s por questão</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ PONTOS FORTES - Apenas especialidades, máximo 3 */}
      {topStrengths.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-green-600">
              <Award className="h-5 w-5" />
              Suas Especialidades Fortes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topStrengths.map((strength) => (
                <div key={strength.categoryId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="min-w-0 flex-1 mr-3">
                    <h4 className="font-medium text-gray-800 text-sm truncate" title={strength.category}>
                      {shortenCategoryName(strength.category, 25)}
                    </h4>
                    <p className="text-xs text-gray-600">{strength.accuracy}% de acertos</p>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <DifficultyBadge level={strength.difficultyLevel} />
                    <p className="text-xs text-gray-500 mt-1">{strength.avgTimeSpent}s por questão</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
