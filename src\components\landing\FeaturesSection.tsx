
import React from "react";
import { motion } from "framer-motion";
import { Calendar, BarC<PERSON>, Book, Clock, Award, Brain, Check, Eye } from "lucide-react";

export function FeaturesSection() {
  const features = [
    {
      icon: Calendar,
      title: "Planner Automático",
      description: "Cronograma inteligente para seus estudos em oftalmologia",
      color: "bg-blue-500",
      items: ["Organização diária", "Adaptável", "Personalizado"]
    },
    {
      icon: Brain,
      title: "Revisões Espaçadas",
      description: "Sistema de revisões baseado em ciência para fixação definitiva do conteúdo",
      color: "bg-green-500",
      items: ["Curva do esquecimento", "Estudo eficiente", "Memorização efetiva"]
    },
    {
      icon: Bar<PERSON>hart,
      title: "<PERSON><PERSON><PERSON><PERSON> Desempenho",
      description: "Identifique seus pontos fracos em oftalmologia com dados precisos",
      color: "bg-purple-500",
      items: ["Gráficos detalhados", "Pontos fracos", "<PERSON><PERSON><PERSON><PERSON>"]
    },
    {
      icon: Book,
      title: "Banco de Questões",
      description: "Questões atualizadas e comentadas por oftalmologistas",
      color: "bg-yellow-500",
      items: ["Questões comentadas", "Conteúdo atualizado", "Tópicos específicos"]
    },
    {
      icon: Clock,
      title: "Simulados Realistas",
      description: "Pratique com simulações que reproduzem o ambiente real das provas",
      color: "bg-red-500",
      items: ["Cronômetro", "Interface similar", "Questões similares"]
    },
    {
      icon: Eye,
      title: "Conteúdo Especializado",
      description: "Conteúdo criado por oftalmologistas experientes",
      color: "bg-indigo-500",
      items: ["Catarata", "Glaucoma", "Retina e Estrabismo"]
    },
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <section className="py-20 px-4 text-white relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 to-indigo-900"></div>
      
      {/* Grid pattern */}
      <div className="absolute top-0 left-0 w-full h-full opacity-10">
        <div className="w-full h-full grid grid-cols-10 grid-rows-10">
          {[...Array(100)].map((_, i) => (
            <div key={i} className="border border-white/5"></div>
          ))}
        </div>
      </div>
      
      {/* Animated shapes */}
      <motion.div 
        className="absolute top-20 left-10 w-32 h-32 rounded-full bg-blue-500/20 blur-3xl"
        animate={{ 
          x: [0, 20, 0],
          y: [0, 30, 0],
        }} 
        transition={{ repeat: Infinity, duration: 15, ease: "easeInOut" }}
      ></motion.div>
      
      <motion.div 
        className="absolute bottom-40 right-20 w-40 h-40 rounded-full bg-purple-500/20 blur-3xl"
        animate={{ 
          x: [0, -30, 0],
          y: [0, -20, 0],
        }} 
        transition={{ repeat: Infinity, duration: 12, ease: "easeInOut", delay: 1 }}
      ></motion.div>
      
      <div className="container mx-auto relative">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          {/* Green circular badge */}
          <div className="inline-block mb-6">
            <span className="bg-green-500/30 text-white px-4 py-2 rounded-full text-sm font-medium">
              Funcionalidades
            </span>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Tudo que você precisa para seus estudos em oftalmologia
          </h2>
          
          <div className="relative">
            <p className="text-lg text-white/90 mb-6">
              Nossa plataforma oferece ferramentas completas para maximizar seu aprendizado e 
              garantir seu sucesso como oftalmologista
            </p>
            
            {/* Highlight box */}
            <motion.div 
              className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-3 border border-white/20"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <p className="text-sm">
                Subespecialidades disponíveis: <span className="font-medium">Catarata</span> | <span className="font-medium">Glaucoma</span> | <span className="font-medium">Retina</span> | <span className="font-medium">Estrabismo</span>
              </p>
            </motion.div>
          </div>
        </motion.div>

        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-colors relative group overflow-hidden"
              variants={item}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              {/* Corner shape */}
              <div className={`absolute -top-10 -right-10 w-20 h-20 ${feature.color} rounded-full opacity-10 group-hover:scale-150 transition-transform duration-500`}></div>
              
              {/* Bottom accent */}
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-white/0 via-white/20 to-white/0"></div>
              
              <div className={`${feature.color} w-12 h-12 rounded-lg flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform`}>
                <feature.icon className="h-6 w-6 text-white" />
              </div>
              
              <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
              <p className="text-white/80 mb-4">{feature.description}</p>
              
              {/* Feature items with check marks */}
              <ul className="space-y-2">
                {feature.items.map((item, idx) => (
                  <motion.li 
                    key={idx}
                    className="flex items-center gap-2 text-sm text-white/70"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: 0.5 + (idx * 0.1) }}
                  >
                    <div className="w-4 h-4 rounded-full bg-white/20 flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                    {item}
                  </motion.li>
                ))}
              </ul>
              
              {/* Interactive progress indicator */}
              <div className="mt-5 w-full h-1 bg-white/10 rounded-full overflow-hidden">
                <motion.div 
                  className={`h-full ${feature.color}`}
                  initial={{ width: 0 }}
                  whileInView={{ width: '60%' }}
                  viewport={{ once: true }}
                  transition={{ duration: 1, delay: 0.8 }}
                ></motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        {/* Call to action */}
        <motion.div 
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <motion.div 
            className="inline-block bg-white/10 backdrop-blur-sm px-6 py-4 rounded-lg border border-white/20 hover:bg-white/20 transition-colors cursor-pointer"
            whileHover={{ scale: 1.03 }}
            transition={{ type: "spring", stiffness: 400 }}
          >
            <p className="text-lg font-medium">
              Comece a estudar oftalmologia agora e transforme seu aprendizado
            </p>
            <p className="text-sm text-white/70 mt-1">
              Plataforma 100% gratuita para você estudar de forma inteligente
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
