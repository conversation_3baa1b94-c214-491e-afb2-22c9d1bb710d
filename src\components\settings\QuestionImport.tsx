
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { importQuestions } from "@/utils/importUtils";
import { useAuth } from "@/hooks/useAuth";
import { useNavigate } from "react-router-dom";

export const QuestionImport = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  // Verificar se o usuário é admin
  if (!user) {
    navigate("/");
    return null;
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsLoading(true);
      const fileContent = await file.text();
      const data = JSON.parse(fileContent);

      if (!Array.isArray(data.questions)) {
        throw new Error('O arquivo deve conter um array de questões');
      }

      // Validar answer_type de cada questão
      data.questions = data.questions.map((question: any) => ({
        ...question,
        answer_type: question.answer_type || 'ALTERNATIVAS'
      }));

      const importResults = await importQuestions(data);
      setResults(importResults);

      toast({
        title: "Importação concluída!",
        description: `${importResults.success} questões importadas com sucesso.${
          importResults.errors.length > 0 ? ` ${importResults.errors.length} erros encontrados.` : ''
        }`
      });

    } catch (error: any) {
      console.error('Error importing questions:', error);
      toast({
        title: "Erro na importação",
        description: error.message || 'Erro desconhecido ao importar questões',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      // Reset file input
      event.target.value = '';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Importar Questões</CardTitle>
        <CardDescription>
          Faça upload de um arquivo JSON contendo as questões a serem importadas.
          Cada questão deve ter um campo answer_type: "MULTIPLE_CHOICE" ou "DISCURSIVE"
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <input
              type="file"
              accept=".json"
              onChange={handleFileUpload}
              disabled={isLoading}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 disabled:opacity-50"
            />
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          </div>

          {results && (
            <div className="mt-4 space-y-4">
              <div className="rounded-lg bg-secondary/50 p-4">
                <h3 className="font-semibold mb-2">Resultados da Importação:</h3>
                <ul className="space-y-2 text-sm">
                  <li>✅ {results.success} questões importadas com sucesso</li>
                  {results.errors.length > 0 && (
                    <li>❌ {results.errors.length} erros encontrados</li>
                  )}
                  <li>📚 {results.created.specialties.size} especialidades</li>
                  <li>📝 {results.created.themes.size} temas</li>
                  <li>🎯 {results.created.focuses.size} focos</li>
                  <li>📍 {results.created.locations.size} localizações</li>
                  <li>📅 {results.created.years.size} anos</li>
                </ul>
              </div>

              {results.errors.length > 0 && (
                <div className="rounded-lg bg-destructive/10 p-4">
                  <h3 className="font-semibold mb-2 text-destructive">Erros:</h3>
                  <ul className="space-y-1 text-sm">
                    {results.errors.map((error: string, index: number) => (
                      <li key={index} className="text-destructive">{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
