
import { supabase } from "@/integrations/supabase/client";

/**
 * Busca questões que o usuário já acertou
 */
export const getUserCorrectAnswers = async (userId: string): Promise<string[]> => {
  try {
    // Verificar se o usuário está autenticado
    if (!userId) {
      console.log('🔍 [getUserCorrectAnswers] Usuário não fornecido');
      return [];
    }

    console.log('🔍 [getUserCorrectAnswers] Buscando questões acertadas para:', userId);

    const { data, error } = await supabase
      .from('user_answers')
      .select('question_id')
      .eq('user_id', userId)
      .eq('is_correct', true);

    if (error) {
      console.error('❌ [getUserCorrectAnswers] Erro ao buscar questões acertadas:', error);
      return [];
    }

    const correctAnswers = data?.map(answer => answer.question_id) || [];

    console.log('📊 [getUserCorrectAnswers] Questões acertadas encontradas:', {
      count: correctAnswers.length,
      first_few: correctAnswers.slice(0, 3)
    });

    return correctAnswers;
  } catch (error) {
    console.error('❌ [getUserCorrectAnswers] Erro inesperado:', error);
    return [];
  }
};

/**
 * Filtra questões com base nas categorias do tópico de estudo
 */
export const filterQuestionsByTopic = async (
  specialtyId?: string,
  themeId?: string,
  focusId?: string,
  limit: number = 10,
  years?: number[],
  institutionIds?: string[],
  randomSelection: boolean = false,
  domain?: string,
  hideAnswered: boolean = false,
  userId?: string
) => {
  try {
    // Buscar questões já acertadas se necessário
    let correctAnswers: string[] = [];
    if (hideAnswered && userId) {
      console.log('🔍 [filterQuestionsByTopic] Buscando questões acertadas para filtrar...');
      correctAnswers = await getUserCorrectAnswers(userId);
      console.log('📊 [filterQuestionsByTopic] Questões acertadas para filtrar:', {
        count: correctAnswers.length,
        hideAnswered,
        userId: userId.slice(0, 8)
      });
    }

    // Primeiro realizamos uma consulta de contagem
    let countQuery = supabase.from('questions').select('id', { count: 'exact' });

    // Aplica filtros
    if (specialtyId) countQuery = countQuery.eq('specialty_id', specialtyId);
    if (themeId) countQuery = countQuery.eq('theme_id', themeId);
    if (focusId) countQuery = countQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      countQuery = countQuery.eq('knowledge_domain', domain);
    }

    // Filtros adicionais para anos
    if (years && years.length > 0) {
      countQuery = countQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas
    if (hideAnswered && correctAnswers.length > 0) {
      console.log('🚫 [filterQuestionsByTopic] Aplicando filtro de questões acertadas na contagem:', correctAnswers.length);
      countQuery = countQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // ✅ CORREÇÃO: Usar método tradicional para filtro de instituições
    // A função RPC tem problemas, então usamos query direta
    if (institutionIds && institutionIds.length > 0) {
      console.log('🔍 [filterQuestionsByTopic] Usando método tradicional para filtro de instituições:', {
        institutionIds,
        specialtyId,
        themeId,
        focusId,
        domain,
        hideAnswered
      });

      // Aplicar filtro de instituições na query de contagem
      countQuery = countQuery.in('exam_location', institutionIds);
    }

    // ✅ FALLBACK: Para casos sem filtro de instituições, usar método tradicional
    const { count, error: countError } = await countQuery;

    if (countError) {
      throw countError;
    }

    const totalCount = count || 0;

    // Se não houver questões, retornamos imediatamente
    if (totalCount === 0) {
      return { data: [], count: 0 };
    }

    // Garantir que o limite não exceda o total disponível
    const effectiveLimit = Math.min(limit, totalCount);

    // Agora obtemos as questões aplicando o limite - sem joins para evitar múltiplas requisições
    let dataQuery = supabase.from('questions').select('*');

    // Aplica os mesmos filtros que usamos para contar
    if (specialtyId) dataQuery = dataQuery.eq('specialty_id', specialtyId);
    if (themeId) dataQuery = dataQuery.eq('theme_id', themeId);
    if (focusId) dataQuery = dataQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      dataQuery = dataQuery.eq('knowledge_domain', domain);
    }

    if (years && years.length > 0) {
      dataQuery = dataQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas na consulta de dados também
    if (hideAnswered && correctAnswers.length > 0) {
      console.log('🚫 [filterQuestionsByTopic] Aplicando filtro de questões acertadas nos dados:', correctAnswers.length);
      dataQuery = dataQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // ✅ NOVO: Aplicar filtro de instituições na query de dados também
    if (institutionIds && institutionIds.length > 0) {
      dataQuery = dataQuery.in('exam_location', institutionIds);
      console.log('🔍 [filterQuestionsByTopic] Aplicando filtro de instituições na query de dados:', institutionIds);
    }

    // Aplicar seleção aleatória se solicitado
    if (randomSelection) {
      dataQuery = dataQuery.order('id', { ascending: false, nullsFirst: false }).limit(totalCount);
    }

    const { data, error } = await dataQuery.limit(effectiveLimit);

    if (error) {
      throw error;
    }

    let finalData = data || [];

    // Se solicitada seleção aleatória, embaralhar os resultados e limitar à quantidade
    if (randomSelection && finalData.length > 0) {
      // Algoritmo de Fisher-Yates para embaralhar
      for (let i = finalData.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [finalData[i], finalData[j]] = [finalData[j], finalData[i]];
      }

      // Limitar ao effectiveLimit
      finalData = finalData.slice(0, effectiveLimit);
    }

    const result = {
      data: finalData,
      count: totalCount,
      questions: finalData // ✅ CORREÇÃO: Adicionar alias 'questions'
    };

    console.log('📊 [filterQuestionsByTopic] Resultado final:', {
      data_length: finalData.length,
      count: totalCount,
      has_questions: finalData.length > 0,
      first_question_id: finalData[0]?.id,
      result_keys: Object.keys(result)
    });

    return result;
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao filtrar questões:', error);
    return { data: [], count: 0, questions: [] }; // ✅ CORREÇÃO: Adicionar alias 'questions'
  }
};

/**
 * Obtém anos disponíveis para questões
 */
export const getAvailableYears = async () => {
  try {
    const { data, error } = await supabase.rpc('get_available_years');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
    return [];
  }
};

/**
 * Obtém instituições disponíveis
 */
export const getAvailableInstitutions = async () => {
  try {
    const { data, error } = await supabase
      .from('institutions')
      .select('id, name')
      .order('name');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter instituições:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter instituições:', error);
    return [];
  }
};
