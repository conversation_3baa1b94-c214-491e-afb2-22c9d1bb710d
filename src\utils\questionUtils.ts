
import { supabase } from "@/integrations/supabase/client";

/**
 * Busca questões que o usuário já acertou
 */
export const getUserCorrectAnswers = async (userId: string): Promise<string[]> => {
  try {
    // Verificar se o usuário está autenticado
    if (!userId) {
      return [];
    }

    const { data, error } = await supabase
      .from('user_answers')
      .select('question_id')
      .eq('user_id', userId)
      .eq('is_correct', true);

    if (error) {
      return [];
    }

    const correctAnswers = data?.map(answer => answer.question_id) || [];
    return correctAnswers;
  } catch (error) {
    return [];
  }
};

/**
 * Filtra questões com base nas categorias do tópico de estudo
 */
export const filterQuestionsByTopic = async (
  specialtyId?: string,
  themeId?: string,
  focusId?: string,
  limit: number = 10,
  years?: number[],
  institutionIds?: string[],
  randomSelection: boolean = false,
  domain?: string,
  hideAnswered: boolean = false,
  userId?: string
) => {
  try {
    // Buscar questões já acertadas se necessário
    let correctAnswers: string[] = [];
    if (hideAnswered && userId) {
      correctAnswers = await getUserCorrectAnswers(userId);
    }

    // Primeiro realizamos uma consulta de contagem
    let countQuery = supabase.from('questions').select('id', { count: 'exact' });

    // Aplica filtros
    if (specialtyId) countQuery = countQuery.eq('specialty_id', specialtyId);
    if (themeId) countQuery = countQuery.eq('theme_id', themeId);
    if (focusId) countQuery = countQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      countQuery = countQuery.eq('knowledge_domain', domain);
    }

    // Filtros adicionais para anos
    if (years && years.length > 0) {
      countQuery = countQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas
    if (hideAnswered && correctAnswers.length > 0) {
      countQuery = countQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // ✅ CORREÇÃO: Usar função RPC para filtro de instituições
    if (institutionIds && institutionIds.length > 0) {
      console.log('🔍 [filterQuestionsByTopic] Usando RPC para filtro de instituições:', {
        institutionIds,
        specialtyId,
        themeId,
        focusId,
        domain,
        hideAnswered
      });

      // Usar a função RPC get_filtered_question_count
      const rpcParams = {
        specialty_ids: specialtyId ? [specialtyId] : [],
        theme_ids: themeId ? [themeId] : [],
        focus_ids: focusId ? [focusId] : [],
        location_ids: institutionIds,
        years: years || [],
        question_types: [],
        question_formats: [],
        domain_filter: domain
      };

      if (hideAnswered && userId) {
        // Usar função que exclui questões acertadas
        const { data: countData, error: countError } = await supabase.rpc('get_filtered_question_count_excluding_answered', {
          ...rpcParams,
          user_id: userId
        });

        if (countError) throw countError;

        const totalCount = countData?.[0]?.total_count || 0;
        console.log('🔍 [filterQuestionsByTopic] Contagem com filtro (excluindo acertadas):', totalCount);

        if (totalCount === 0) {
          return { data: [], count: 0 };
        }

        // Buscar questões usando a função RPC
        const { data: questionsData, error: questionsError } = await supabase.rpc('get_filtered_questions_excluding_answered', {
          ...rpcParams,
          user_id: userId,
          page_number: 1,
          items_per_page: Math.min(limit, totalCount)
        });

        if (questionsError) throw questionsError;

        return {
          data: questionsData?.questions || [],
          count: totalCount
        };
      } else {
        // Usar função normal
        const { data: countData, error: countError } = await supabase.rpc('get_filtered_question_count', rpcParams);

        if (countError) throw countError;

        const totalCount = countData?.total_count || 0;
        console.log('🔍 [filterQuestionsByTopic] Contagem com filtro:', totalCount);

        if (totalCount === 0) {
          return { data: [], count: 0 };
        }

        // Buscar questões usando a função RPC
        const { data: questionsData, error: questionsError } = await supabase.rpc('get_filtered_questions', {
          ...rpcParams,
          page_number: 1,
          items_per_page: Math.min(limit, totalCount)
        });

        if (questionsError) throw questionsError;

        return {
          data: questionsData?.questions || [],
          count: totalCount
        };
      }
    }

    // ✅ FALLBACK: Para casos sem filtro de instituições, usar método tradicional
    const { count, error: countError } = await countQuery;

    if (countError) {
      throw countError;
    }

    const totalCount = count || 0;

    // Se não houver questões, retornamos imediatamente
    if (totalCount === 0) {
      return { data: [], count: 0 };
    }

    // Garantir que o limite não exceda o total disponível
    const effectiveLimit = Math.min(limit, totalCount);

    // Agora obtemos as questões aplicando o limite - sem joins para evitar múltiplas requisições
    let dataQuery = supabase.from('questions').select('*');

    // Aplica os mesmos filtros que usamos para contar
    if (specialtyId) dataQuery = dataQuery.eq('specialty_id', specialtyId);
    if (themeId) dataQuery = dataQuery.eq('theme_id', themeId);
    if (focusId) dataQuery = dataQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      dataQuery = dataQuery.eq('knowledge_domain', domain);
    }

    if (years && years.length > 0) {
      dataQuery = dataQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas na consulta de dados também
    if (hideAnswered && correctAnswers.length > 0) {
      dataQuery = dataQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // Aplicar seleção aleatória se solicitado
    if (randomSelection) {
      dataQuery = dataQuery.order('id', { ascending: false, nullsFirst: false }).limit(totalCount);
    }

    const { data, error } = await dataQuery.limit(effectiveLimit);

    if (error) {
      throw error;
    }

    let finalData = data || [];

    // Se solicitada seleção aleatória, embaralhar os resultados e limitar à quantidade
    if (randomSelection && finalData.length > 0) {
      // Algoritmo de Fisher-Yates para embaralhar
      for (let i = finalData.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [finalData[i], finalData[j]] = [finalData[j], finalData[i]];
      }

      // Limitar ao effectiveLimit
      finalData = finalData.slice(0, effectiveLimit);
    }

    return {
      data: finalData,
      count: totalCount
    };
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao filtrar questões:', error);
    return { data: [], count: 0 };
  }
};

/**
 * Obtém anos disponíveis para questões
 */
export const getAvailableYears = async () => {
  try {
    const { data, error } = await supabase.rpc('get_available_years');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
    return [];
  }
};

/**
 * Obtém instituições disponíveis
 */
export const getAvailableInstitutions = async () => {
  try {
    const { data, error } = await supabase
      .from('institutions')
      .select('id, name')
      .order('name');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter instituições:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter instituições:', error);
    return [];
  }
};
