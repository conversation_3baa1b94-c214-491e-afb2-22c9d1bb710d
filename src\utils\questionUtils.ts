
import { supabase } from "@/integrations/supabase/client";

/**
 * Busca questões que o usuário já acertou
 */
export const getUserCorrectAnswers = async (userId: string): Promise<string[]> => {
  try {
    // Verificar se o usuário está autenticado
    if (!userId) {
      console.log('🔍 [getUserCorrectAnswers] Usuário não fornecido');
      return [];
    }

    // ✅ LIMPEZA: Remover log desnecessário

    const { data, error } = await supabase
      .from('user_answers')
      .select('question_id')
      .eq('user_id', userId)
      .eq('is_correct', true);

    if (error) {
      console.error('❌ [getUserCorrectAnswers] Erro ao buscar questões acertadas:', error);
      return [];
    }

    const correctAnswers = data?.map(answer => answer.question_id) || [];

    // ✅ LIMPEZA: Remover log desnecessário

    return correctAnswers;
  } catch (error) {
    console.error('❌ [getUserCorrectAnswers] Erro inesperado:', error);
    return [];
  }
};

/**
 * Filtra questões com base nas categorias do tópico de estudo
 */
export const filterQuestionsByTopic = async (
  specialtyId?: string,
  themeId?: string,
  focusId?: string,
  limit: number = 10,
  years?: number[],
  institutionIds?: string[],
  randomSelection: boolean = false,
  domain?: string,
  hideAnswered: boolean = false,
  userId?: string,
  onlyIds: boolean = false // ✅ NOVO: Opção para retornar apenas IDs
) => {
  try {
    // Buscar questões já acertadas se necessário
    let correctAnswers: string[] = [];
    if (hideAnswered && userId) {
      // ✅ LIMPEZA: Remover logs desnecessários
      correctAnswers = await getUserCorrectAnswers(userId);
    }

    // Primeiro realizamos uma consulta de contagem
    let countQuery = supabase.from('questions').select('id', { count: 'exact' });

    // Aplica filtros
    if (specialtyId) countQuery = countQuery.eq('specialty_id', specialtyId);
    if (themeId) countQuery = countQuery.eq('theme_id', themeId);
    if (focusId) countQuery = countQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      countQuery = countQuery.eq('knowledge_domain', domain);
    }

    // Filtros adicionais para anos
    if (years && years.length > 0) {
      countQuery = countQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas
    if (hideAnswered && correctAnswers.length > 0) {
      // ✅ LIMPEZA: Remover log desnecessário
      countQuery = countQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // ✅ LIMPEZA: Usar método tradicional para filtro de instituições
    if (institutionIds && institutionIds.length > 0) {

      // Aplicar filtro de instituições na query de contagem
      countQuery = countQuery.in('exam_location', institutionIds);
    }

    // ✅ FALLBACK: Para casos sem filtro de instituições, usar método tradicional
    const { count, error: countError } = await countQuery;

    if (countError) {
      throw countError;
    }

    const totalCount = count || 0;

    // Se não houver questões, retornamos imediatamente
    if (totalCount === 0) {
      return { data: [], count: 0 };
    }

    // Garantir que o limite não exceda o total disponível
    const effectiveLimit = Math.min(limit, totalCount);

    // ✅ OTIMIZAÇÃO: Selecionar apenas IDs quando não precisamos dos dados completos
    const selectFields = onlyIds ? 'id' : '*';
    let dataQuery = supabase.from('questions').select(selectFields);

    // Aplica os mesmos filtros que usamos para contar
    if (specialtyId) dataQuery = dataQuery.eq('specialty_id', specialtyId);
    if (themeId) dataQuery = dataQuery.eq('theme_id', themeId);
    if (focusId) dataQuery = dataQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      dataQuery = dataQuery.eq('knowledge_domain', domain);
    }

    if (years && years.length > 0) {
      dataQuery = dataQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas na consulta de dados também
    if (hideAnswered && correctAnswers.length > 0) {
      dataQuery = dataQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // ✅ LIMPEZA: Aplicar filtro de instituições na query de dados
    if (institutionIds && institutionIds.length > 0) {
      dataQuery = dataQuery.in('exam_location', institutionIds);
    }

    // Aplicar seleção aleatória se solicitado
    if (randomSelection) {
      dataQuery = dataQuery.order('id', { ascending: false, nullsFirst: false }).limit(totalCount);
    }

    const { data, error } = await dataQuery.limit(effectiveLimit);

    if (error) {
      throw error;
    }

    let finalData = data || [];

    // Se solicitada seleção aleatória, embaralhar os resultados e limitar à quantidade
    if (randomSelection && finalData.length > 0) {
      // Algoritmo de Fisher-Yates para embaralhar
      for (let i = finalData.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [finalData[i], finalData[j]] = [finalData[j], finalData[i]];
      }

      // Limitar ao effectiveLimit
      finalData = finalData.slice(0, effectiveLimit);
    }

    const result = {
      data: finalData,
      count: totalCount,
      questions: finalData // ✅ CORREÇÃO: Adicionar alias 'questions'
    };

    // ✅ LIMPEZA: Remover log desnecessário

    return result;
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao filtrar questões:', error);
    return { data: [], count: 0, questions: [] }; // ✅ CORREÇÃO: Adicionar alias 'questions'
  }
};

/**
 * ✅ NOVA: Função otimizada para contar questões sem retornar dados
 */
export const countQuestionsByTopic = async (
  specialtyId?: string,
  themeId?: string,
  focusId?: string,
  years?: number[],
  institutionIds?: string[],
  domain?: string,
  hideAnswered: boolean = false,
  userId?: string
) => {
  try {
    // Buscar questões já acertadas se necessário
    let correctAnswers: string[] = [];
    if (hideAnswered && userId) {
      correctAnswers = await getUserCorrectAnswers(userId);
    }

    // Query de contagem otimizada
    let countQuery = supabase.from('questions').select('id', { count: 'exact' });

    // Aplica filtros
    if (specialtyId) countQuery = countQuery.eq('specialty_id', specialtyId);
    if (themeId) countQuery = countQuery.eq('theme_id', themeId);
    if (focusId) countQuery = countQuery.eq('focus_id', focusId);

    // Filtrar pelo domínio do usuário
    if (domain) {
      countQuery = countQuery.eq('knowledge_domain', domain);
    }

    // Filtros adicionais para anos
    if (years && years.length > 0) {
      countQuery = countQuery.in('exam_year', years);
    }

    // Filtrar questões já acertadas
    if (hideAnswered && correctAnswers.length > 0) {
      countQuery = countQuery.not('id', 'in', `(${correctAnswers.join(',')})`);
    }

    // Filtro de instituições
    if (institutionIds && institutionIds.length > 0) {
      countQuery = countQuery.in('exam_location', institutionIds);
    }

    const { count, error } = await countQuery;

    if (error) {
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao contar questões:', error);
    return 0;
  }
};

/**
 * Obtém anos disponíveis para questões
 */
export const getAvailableYears = async () => {
  try {
    const { data, error } = await supabase.rpc('get_available_years');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter anos disponíveis:', error);
    return [];
  }
};

/**
 * Obtém instituições disponíveis
 */
export const getAvailableInstitutions = async () => {
  try {
    const { data, error } = await supabase
      .from('institutions')
      .select('id, name')
      .order('name');

    if (error) {
      console.error('❌ [questionUtils] Erro ao obter instituições:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('❌ [questionUtils] Erro ao obter instituições:', error);
    return [];
  }
};
