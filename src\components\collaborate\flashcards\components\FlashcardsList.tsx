import { FlashcardItem } from "./FlashcardItem";
import { Pagination } from "./Pagination";
import type { FlashcardWithHierarchy } from "@/types/flashcard";

interface FlashcardsListProps {
  cards: FlashcardWithHierarchy[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onDelete: (id: string) => Promise<void>;
  hideStatusBadge?: boolean;
}

export const FlashcardsList = ({
  cards,
  currentPage,
  totalPages,
  onPageChange,
  onDelete,
  hideStatusBadge = false,
}: FlashcardsListProps) => {
  return (
    <div className="space-y-4">
      {cards.map((card) => (
        <FlashcardItem 
          key={card.id}
          card={card}
          onDelete={onDelete}
          hideStatusBadge={hideStatusBadge}
        />
      ))}

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
      />
    </div>
  );
};
