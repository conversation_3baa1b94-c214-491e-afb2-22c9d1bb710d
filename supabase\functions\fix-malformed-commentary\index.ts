import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 204, headers: corsHeaders });
  }

  try {
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('🔧 [fix-malformed-commentary] Starting cleanup of malformed JSON data');

    // Buscar questões com comentários mal formatados
    const { data: questions, error: fetchError } = await supabase
      .from('questions')
      .select('id, ai_commentary')
      .not('ai_commentary', 'is', null);

    if (fetchError) {
      throw new Error(`Failed to fetch questions: ${fetchError.message}`);
    }

    console.log(`📊 [fix-malformed-commentary] Found ${questions?.length || 0} questions with ai_commentary`);

    let fixedCount = 0;
    let errorCount = 0;

    for (const question of questions || []) {
      try {
        const commentary = question.ai_commentary;
        
        if (!commentary || typeof commentary !== 'object') {
          continue;
        }

        let needsUpdate = false;
        const updatedCommentary = { ...commentary };

        // Verificar e corrigir comentario_final
        if (commentary.comentario_final && typeof commentary.comentario_final === 'string') {
          const cleanedFinal = cleanMalformedJson(commentary.comentario_final);
          if (cleanedFinal !== commentary.comentario_final) {
            updatedCommentary.comentario_final = cleanedFinal;
            needsUpdate = true;
          }
        }

        // Verificar e corrigir comentários das alternativas
        if (commentary.alternativas && Array.isArray(commentary.alternativas)) {
          for (let i = 0; i < commentary.alternativas.length; i++) {
            const alt = commentary.alternativas[i];
            if (alt.comentario && typeof alt.comentario === 'string') {
              const cleanedComment = cleanMalformedJson(alt.comentario);
              if (cleanedComment !== alt.comentario) {
                updatedCommentary.alternativas[i] = {
                  ...alt,
                  comentario: cleanedComment
                };
                needsUpdate = true;
              }
            }
          }
        }

        // Atualizar no banco se necessário
        if (needsUpdate) {
          const { error: updateError } = await supabase
            .from('questions')
            .update({ ai_commentary: updatedCommentary })
            .eq('id', question.id);

          if (updateError) {
            console.error(`❌ [fix-malformed-commentary] Error updating question ${question.id}:`, updateError);
            errorCount++;
          } else {
            console.log(`✅ [fix-malformed-commentary] Fixed question ${question.id}`);
            fixedCount++;
          }
        }

      } catch (error) {
        console.error(`❌ [fix-malformed-commentary] Error processing question ${question.id}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [fix-malformed-commentary] Cleanup completed: ${fixedCount} fixed, ${errorCount} errors`);

    return new Response(JSON.stringify({
      success: true,
      message: `Cleanup completed successfully`,
      stats: {
        totalQuestions: questions?.length || 0,
        fixedCount,
        errorCount
      }
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    console.error('❌ [fix-malformed-commentary] Fatal error:', error);
    
    return new Response(JSON.stringify({
      error: "Failed to fix malformed commentary",
      details: error.message
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});

// Função para limpar JSON mal formatado
function cleanMalformedJson(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Verificar se o texto contém JSON mal formatado
  if (text.includes('```json') || (text.includes('"alternativas"') && text.includes('"comentario_final"'))) {
    try {
      // Tentar extrair o JSON do texto
      let jsonText = text;
      
      // Remover marcadores de código markdown
      jsonText = jsonText.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      // Remover tags HTML se existirem
      jsonText = jsonText.replace(/<[^>]*>/g, '');
      
      // Tentar parsear como JSON
      const parsed = JSON.parse(jsonText);
      
      // Se conseguiu parsear e tem a estrutura esperada, extrair o comentário final
      if (parsed.comentario_final && typeof parsed.comentario_final === 'string') {
        return parsed.comentario_final;
      }
      
      // Se não tem comentário final, tentar extrair de alternativas
      if (parsed.alternativas && Array.isArray(parsed.alternativas)) {
        const comments = parsed.alternativas
          .map((alt: any) => alt.comentario)
          .filter(Boolean)
          .join('<br><br>');
        return comments || 'Análise não disponível.';
      }
      
    } catch (e) {
      console.warn('Erro ao limpar JSON mal formatado:', e);
      
      // Fallback: tentar extrair texto útil usando regex
      const cleanText = text
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .replace(/<[^>]*>/g, '')
        .replace(/\{[\s\S]*"comentario_final":\s*"([^"]*)"[\s\S]*\}/g, '$1')
        .trim();
        
      return cleanText || 'Análise não disponível.';
    }
  }
  
  return text;
}
