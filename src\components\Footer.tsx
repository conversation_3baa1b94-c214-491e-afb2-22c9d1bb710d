import { Mail, Instagram, FileText, MessageSquare, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";


const Footer = () => {
  return (
    <footer className="hidden sm:block bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 backdrop-blur-sm mt-20">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center space-y-6">
          <Dialog>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                className="group relative overflow-hidden bg-gradient-to-r from-primary via-primary/90 to-primary hover:from-primary/90 hover:to-primary transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform text-white"
              >
                <span className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                <MessageSquare className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
       
                <Sparkles className="h-4 w-4 text-white animate-subtle-pulse" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Feedback</DialogTitle>
              </DialogHeader>
           
            </DialogContent>
          </Dialog>

          <p className="max-w-2xl mx-auto text-gray-600">
            Plataforma para profissionais de saúde com foco em pediatria, oferecendo informações atualizadas e práticas.
          </p>
          
          <div className="flex justify-center gap-6">
            <a 
              href="mailto:<EMAIL>" 
              className="text-primary hover:text-primary/80 transition-colors transform hover:scale-110"
            >
              <Mail size={24} />
            </a>
            <a 
              href="https://www.instagram.com/pedbookmed/" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="text-primary hover:text-primary/80 transition-colors transform hover:scale-110"
            >
              <Instagram size={24} />
            </a>
            <Link 
              to="/terms" 
              className="text-primary hover:text-primary/80 transition-colors transform hover:scale-110"
            >
              <FileText size={24} />
            </Link>
          </div>

          <div className="text-sm text-gray-500 space-y-2">
            <p>© 2024 PedBook. Todos os direitos reservados.</p>
            <p className="text-gray-600">
              Desenvolvido e desenhado com muito ❤️ e carinho por{" "}
              <a 
                href="https://www.instagram.com/wilsonnunesneto/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Wilson
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;