
import type { FlashcardState } from "@/types/flashcard";

export interface FlashcardFormProps {
  selectedSpecialty: string;
  selectedTheme: string;
  selectedFocus: string;
  onCreateSuccess?: () => void;
}

export interface HierarchySelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<{ id: string; name: string }>;
  placeholder: string;
  disabled?: boolean;
}

export interface FlashcardWithHierarchy {
  id: string;
  user_id: string;
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  extrafocus_id?: string;
  created_at: string;
  updated_at: string;
  current_state: FlashcardState;
  rejection_reason?: string | null;
  likes?: number;
  dislikes?: number;
  liked_by?: string[];
  disliked_by?: string[];
  is_shared: boolean;
  isImported?: boolean;
  hierarchy: {
    specialty: { id: string; name: string };
    theme?: { id: string; name: string };
    focus?: { id: string; name: string };
    extraFocus?: { id: string; name: string };
  };
}
