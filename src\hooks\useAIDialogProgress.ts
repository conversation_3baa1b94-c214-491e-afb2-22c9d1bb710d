import { useState, useRef } from 'react';

export enum GenerationPhase {
  NOT_STARTED = 0,
  CREATING_WEEKS = 1,
  ANALYZING_SPECIALTIES = 2,
  GENERATING_TOPICS = 3,
  COMPLETING = 4,
  COMPLETED = 5,
  ERROR = 6
}

export const useAIDialogProgress = () => {
  const [progress, setProgress] = useState(0);
  const [generationPhase, setGenerationPhase] = useState<GenerationPhase>(GenerationPhase.NOT_STARTED);
  const [isComplete, setIsComplete] = useState(false);
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);

  const resetProgress = () => {
    // Limpar timeouts existentes
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];
    
    // Reset do estado
    setProgress(0);
    setGenerationPhase(GenerationPhase.NOT_STARTED);
    setIsComplete(false);
  };

  const startProgress = () => {
    resetProgress();
    
    // Phase 1: Creating weeks (0-25%)
    timeoutsRef.current.push(setTimeout(() => {
      setGenerationPhase(GenerationPhase.CREATING_WEEKS);
      setProgress(25);
    }, 500));

    // Phase 2: Analyzing specialties (25-50%)
    timeoutsRef.current.push(setTimeout(() => {
      setGenerationPhase(GenerationPhase.ANALYZING_SPECIALTIES);
      setProgress(50);
    }, 2000));

    // Phase 3: Generating topics (50-85%)
    timeoutsRef.current.push(setTimeout(() => {
      setGenerationPhase(GenerationPhase.GENERATING_TOPICS);
      setProgress(75);
    }, 4000));

    // Phase 4: Completing (85-95%)
    timeoutsRef.current.push(setTimeout(() => {
      setGenerationPhase(GenerationPhase.COMPLETING);
      setProgress(90);
    }, 6000));
  };

  const completeProgress = () => {
    console.log('🎯 [useAIDialogProgress] completeProgress - Iniciando conclusão...');

    // Limpar timeouts pendentes
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];

    // Completar gradualmente para mostrar 100%
    setGenerationPhase(GenerationPhase.COMPLETING);
    setProgress(95);

    // Depois de 1 segundo, mostrar 100% e marcar como completo
    timeoutsRef.current.push(setTimeout(() => {
      console.log('🎯 [useAIDialogProgress] completeProgress - Definindo 100% e isComplete=true');
      setGenerationPhase(GenerationPhase.COMPLETED);
      setProgress(100);
      setIsComplete(true);
    }, 1000));
  };

  const errorProgress = () => {
    // Limpar timeouts pendentes
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];
    
    // Marcar como erro
    setGenerationPhase(GenerationPhase.ERROR);
    setIsComplete(false);
  };

  return {
    progress,
    generationPhase,
    isComplete,
    resetProgress,
    startProgress,
    completeProgress,
    errorProgress
  };
};
