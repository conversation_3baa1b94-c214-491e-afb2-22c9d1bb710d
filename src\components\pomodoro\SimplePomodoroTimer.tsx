import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Timer, Play, Pause, RefreshCw } from "lucide-react";
import { formatTime } from "@/utils/formatTime";
import { AudioPlayer } from "@/utils/audioUtils";
import { usePomodoroState } from "@/hooks/usePomodoroState";
import { PomodoroTimer } from "./PomodoroTimer";
import { toast } from "sonner";

export const SimplePomodoroTimer = () => {
  const { state, updateState, resetState } = usePomodoroState();
  const audioPlayerRef = useRef<AudioPlayer | null>(null);

  const handleTimeUpdate = (elapsedSeconds: number) => {
    const timeLeft = Math.max(0, (state.isWorkTime ? state.workTime * 60 : state.breakTime * 60) - elapsedSeconds);
    
    if (timeLeft !== state.timeLeft) {
      updateState({ timeLeft });

      if (timeLeft === 0) {
        updateState({ isRunning: false });
        audioPlayerRef.current?.playBell();
        
        if (state.isWorkTime) {
          updateState({
            isWorkTime: false,
            timeLeft: state.breakTime * 60
          });
        } else {
          updateState({
            isWorkTime: true,
            timeLeft: state.workTime * 60,
            cycles: state.cycles + 1
          });
        }
      }
    }
  };

  const handleTimeInputChange = (type: 'workTime' | 'breakTime', value: string) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue) || numValue < 1) {
      toast.error(`O tempo deve ser um número maior que 1`);
      return;
    }
    
    const maxValue = type === 'workTime' ? 60 : 30;
    if (numValue > maxValue) {
      toast.error(`O tempo máximo é ${maxValue} minutos`);
      return;
    }

    updateState({ 
      [type]: numValue,
      timeLeft: state.isWorkTime ? 
        (type === 'workTime' ? numValue * 60 : state.workTime * 60) :
        (type === 'breakTime' ? numValue * 60 : state.breakTime * 60)
    });
  };

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-card shadow-sm max-w-[280px]">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Timer className="h-4 w-4" />
          <span className="text-sm font-medium">Pomodoro Timer</span>
        </div>
        <div className="text-xs text-muted-foreground">
          Ciclos: {state.cycles}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-1">
          <Label htmlFor="workTime" className="text-xs">
            Trabalho (min)
          </Label>
          <Input
            id="workTime"
            type="number"
            min="1"
            max="60"
            value={state.workTime}
            onChange={(e) => handleTimeInputChange('workTime', e.target.value)}
            disabled={state.isRunning}
            className="h-8 text-sm"
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="breakTime" className="text-xs">
            Descanso (min)
          </Label>
          <Input
            id="breakTime"
            type="number"
            min="1"
            max="30"
            value={state.breakTime}
            onChange={(e) => handleTimeInputChange('breakTime', e.target.value)}
            disabled={state.isRunning}
            className="h-8 text-sm"
          />
        </div>
      </div>

      <div className="flex flex-col items-center gap-2">
        <div className="text-3xl font-mono font-bold text-primary">
          {formatTime(state.timeLeft)}
        </div>
        <div className="text-xs text-muted-foreground">
          {state.isWorkTime ? "Tempo de Trabalho" : "Tempo de Descanso"}
        </div>
        
        <PomodoroTimer
          isRunning={state.isRunning}
          isWorkTime={state.isWorkTime}
          workTime={state.workTime}
          breakTime={state.breakTime}
          onTimeUpdate={handleTimeUpdate}
          audioPlayerRef={audioPlayerRef}
        />

        <div className="flex gap-2 w-full">
          <Button 
            onClick={() => updateState({ isRunning: !state.isRunning })}
            variant={state.isRunning ? "destructive" : "default"}
            size="sm"
            className="flex-1 h-8 text-xs"
          >
            {state.isRunning ? (
              <>
                <Pause className="h-3 w-3 mr-1" />
                Pausar
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-1" />
                Iniciar
              </>
            )}
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={resetState}
            className="flex-1 h-8 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Reiniciar
          </Button>
        </div>
      </div>
    </div>
  );
};