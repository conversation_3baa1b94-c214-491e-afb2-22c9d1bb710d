import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { AvatarUpload } from "./AvatarUpload";

export const ProfileForm = ({ 
  fullName, 
  setFullName, 
  avatarUrl, 
  setAvatarUrl,
  loading,
  updateProfile 
}: {
  fullName: string;
  setFullName: (name: string) => void;
  avatarUrl: string;
  setAvatarUrl: (url: string) => void;
  loading: boolean;
  updateProfile: () => Promise<void>;
}) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-center mb-6">
        <AvatarUpload
          url={avatarUrl}
          onUpload={url => {
            setAvatarUrl(url);
            updateProfile();
          }}
        />
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nome completo</Label>
          <Input
            id="name"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            placeholder="Seu nome completo"
          />
        </div>

        <Button 
          onClick={updateProfile} 
          disabled={loading}
          className="w-full"
        >
          Atualizar Perfil
        </Button>
      </div>
    </div>
  );
};