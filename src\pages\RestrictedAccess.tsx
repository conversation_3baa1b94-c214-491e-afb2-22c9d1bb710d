
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, ArrowLeft, LockKeyhole, Info, CheckCircle, Clock, XCircle } from "lucide-react";
import Header from "@/components/Header";
import { PatternBackground } from "@/components/ui/pattern-background";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/useAuth";
import { useUserData } from "@/hooks/useUserData";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

const RestrictedAccessPage = () => {
  const { user } = useAuth();
  const { profile, isLoading } = useUserData();
  const { toast } = useToast();
  const [accessStatus, setAccessStatus] = useState<'none' | 'requested' | 'approved' | 'denied' | 'loading'>('loading');

  useEffect(() => {
    if (isLoading) {
      setAccessStatus('loading');
      return;
    }

    if (!user || !profile) {
      setAccessStatus('none');
      return;
    }

    // Usar dados centralizados do useUserData
    if (profile.premium) {
      setAccessStatus('approved');
    } else if (profile.premium_requested) {
      setAccessStatus('requested');
    } else if (profile.premium_requested_history) {
      // If they previously requested (premium_requested_history=true)
      // but currently have premium_requested=false and premium=false,
      // that means their request was denied
      setAccessStatus('denied');
    } else {
      setAccessStatus('none');
    }
  }, [user, profile, isLoading]);

  const handleContactSupport = () => {
    window.open("https://api.whatsapp.com/send?phone=5564993198433&text=Olá%20poderia%20me%20ajudar?", "_blank");
  };

  const handleRequestAccess = async () => {
    if (!user) {
      toast({
        title: "Você precisa estar logado",
        description: "Faça login para solicitar acesso à plataforma.",
        variant: "destructive",
      });
      return;
    }

    try {
      // First, update the premium_requested_history flag to true if it's not set yet
      await supabase
        .from('profiles')
        .update({ premium_requested_history: true })
        .eq('id', user.id);

      // Then handle the premium request
      const { error } = await supabase.rpc(
        'handle_premium_request',
        {
          user_id: user.id,
          action: 'request'
        }
      );

      if (error) throw error;

      toast({
        title: "Solicitação enviada",
        description: "Seu pedido de acesso foi recebido com sucesso. Em breve analisaremos sua solicitação.",
      });

      setAccessStatus('requested');
    } catch (error) {
      toast({
        title: "Erro ao solicitar acesso",
        description: "Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };

  const renderAccessContent = () => {
    if (accessStatus === 'loading') {
      return (
        <div className="flex items-center justify-center p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-hackathon-red"></div>
        </div>
      );
    }

    if (accessStatus === 'approved') {
      return (
        <div className="bg-green-50 border border-green-200 rounded-xl p-6 md:p-8 mb-8">
          <div className="flex items-start gap-4">
            <CheckCircle className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-bold text-gray-800 text-lg mb-2">Acesso Liberado!</h3>
              <p className="text-gray-600 mb-4">
                Você já tem acesso completo à plataforma de estudos. Aproveite todos os recursos disponíveis.
              </p>
              <Link to="/plataformadeestudos">
                <Button
                  className="gap-2 bg-[#58CC02] hover:bg-[#46a302] text-white border-b-2 border-[#46a302] hover:border-[#378700] active:border-b-0 active:mt-0.5 active:translate-y-px transition-all transform-gpu"
                  size="lg"
                >
                  Acessar a plataforma
                </Button>
              </Link>
            </div>
          </div>
        </div>
      );
    }

    if (accessStatus === 'requested') {
      return (
        <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 md:p-8 mb-8">
          <div className="flex items-start gap-4">
            <Clock className="h-6 w-6 text-amber-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-bold text-gray-800 text-lg mb-2">Solicitação em Análise</h3>
              <p className="text-gray-600 mb-4">
                Sua solicitação de acesso está em análise. Assim que for aprovada, você receberá acesso completo à plataforma.
              </p>
              <Button
                onClick={handleContactSupport}
                className="gap-2 text-amber-900 bg-amber-100 hover:bg-amber-200 border border-amber-300"
                size="sm"
              >
                <ExternalLink className="h-4 w-4" />
                Falar com o suporte
              </Button>
            </div>
          </div>
        </div>
      );
    }

    if (accessStatus === 'denied') {
      return (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 md:p-8 mb-8">
          <div className="flex items-start gap-4">
            <XCircle className="h-6 w-6 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-bold text-gray-800 text-lg mb-2">Acesso Negado</h3>
              <p className="text-gray-600 mb-4">
                Sua solicitação de acesso foi negada. Se você acredita que isso foi um erro, por favor entre em contato com nosso suporte.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleRequestAccess}
                  className="gap-2 bg-[#58CC02] hover:bg-[#46a302] text-white border-b-2 border-[#46a302] hover:border-[#378700] active:border-b-0 active:mt-0.5 active:translate-y-px transition-all transform-gpu"
                  size="sm"
                >
                  Solicitar Novamente
                </Button>

                <Button
                  onClick={handleContactSupport}
                  variant="outline"
                  className="gap-2 text-red-700 border-red-300 hover:bg-red-50"
                  size="sm"
                >
                  <ExternalLink className="h-4 w-4" />
                  Falar com o suporte
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Default case: accessStatus === 'none'
    return (
      <div className="bg-white rounded-xl border-2 border-black/10 shadow-card p-6 md:p-8">
        <div className="flex items-start gap-4 mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
          <Info className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-bold text-gray-800 mb-1">Como obter acesso?</h3>
            <p className="text-gray-600">
              Solicite acesso à plataforma de estudos clicando no botão abaixo. Nossa equipe analisará seu pedido em breve.
            </p>
          </div>
        </div>

        <p className="text-gray-700 mb-8">
          Nossa plataforma de estudos oferece conteúdo especializado para preparação em medicina. Para acessar todos os recursos, você precisa de uma assinatura ativa.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={handleRequestAccess}
            className="gap-2 bg-[#58CC02] hover:bg-[#46a302] text-white border-b-2 border-[#46a302] hover:border-[#378700] active:border-b-0 active:mt-0.5 active:translate-y-px transition-all transform-gpu"
            size="lg"
          >
            Solicitar Acesso
          </Button>

          <Button
            onClick={handleContactSupport}
            variant="outline"
            className="gap-2 w-full sm:w-auto"
            size="lg"
          >
            <ExternalLink className="h-5 w-5" />
            Falar com o suporte
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />

      <div className="container max-w-5xl mx-auto px-4 pt-16 md:pt-12 pb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <Link
            to="/"
            className="inline-flex items-center text-slate-700 hover:text-slate-900 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar para o início
          </Link>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-2xl mx-auto"
        >
          <div className="relative overflow-hidden rounded-xl border-2 border-black shadow-card mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-hackathon-red to-hackathon-red/80"></div>
            <PatternBackground className="absolute inset-0">
              <div className="absolute inset-0 bg-black/5"></div>
            </PatternBackground>

            <div className="relative p-6 md:p-8 text-white">
              <div className="bg-white inline-block p-4 rounded-full mb-4">
                <LockKeyhole className="h-10 w-10 text-hackathon-red" />
              </div>
              <h1 className="text-4xl md:text-5xl font-black mb-4">Acesso Restrito</h1>
              <p className="text-xl opacity-90 mb-2">Este conteúdo está com acesso restrito.</p>
            </div>
          </div>

          {renderAccessContent()}
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-12 text-center text-gray-500 text-sm"
        >
          <p>
            Se você já possui uma assinatura ativa e está tendo problemas para acessar,
            por favor entre em contato com nosso suporte técnico.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default RestrictedAccessPage;
