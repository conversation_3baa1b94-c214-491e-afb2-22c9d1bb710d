import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef } from 'react';

interface CacheMetrics {
  totalQueries: number;
  memoryUsage: number;
  hitRate: number;
  lastCleanup: Date;
}

/**
 * Hook para otimização inteligente do cache do React Query
 * Monitora uso de memória e limpa cache automaticamente
 */
export const useOptimizedCache = () => {
  const queryClient = useQueryClient();
  const metricsRef = useRef<CacheMetrics>({
    totalQueries: 0,
    memoryUsage: 0,
    hitRate: 0,
    lastCleanup: new Date()
  });

  // Limpeza inteligente do cache
  const intelligentCacheCleanup = useCallback(() => {
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();
    const now = new Date();
    
    let removedCount = 0;
    
    queries.forEach(query => {
      const lastAccess = query.state.dataUpdatedAt;
      const isOld = now.getTime() - lastAccess > 15 * 60 * 1000; // 15 minutos
      const isUnused = query.getObserversCount() === 0;
      const isLargeData = JSON.stringify(query.state.data || {}).length > 100000; // 100KB
      
      // Remover queries antigas, não utilizadas ou muito grandes
      if ((isOld && isUnused) || isLargeData) {
        queryCache.remove(query);
        removedCount++;
      }
    });

    metricsRef.current.lastCleanup = now;
    return removedCount;
  }, [queryClient]);

  // Otimização de cache baseada em padrões de uso
  const optimizeCacheSettings = useCallback(() => {
    const queries = queryClient.getQueryCache().getAll();
    
    queries.forEach(query => {
      const queryKey = JSON.stringify(query.queryKey);
      
      // Ajustar staleTime baseado no tipo de dados
      if (queryKey.includes('static-')) {
        // Dados estáticos: cache mais longo
        query.setOptions({
          staleTime: 60 * 60 * 1000, // 1 hora
          cacheTime: 2 * 60 * 60 * 1000, // 2 horas
        });
      } else if (queryKey.includes('user-answers') || queryKey.includes('streak')) {
        // Dados dinâmicos: cache mais curto
        query.setOptions({
          staleTime: 2 * 60 * 1000, // 2 minutos
          cacheTime: 5 * 60 * 1000, // 5 minutos
        });
      } else if (queryKey.includes('question-count')) {
        // Contagens: cache médio
        query.setOptions({
          staleTime: 5 * 60 * 1000, // 5 minutos
          cacheTime: 10 * 60 * 1000, // 10 minutos
        });
      }
    });
  }, [queryClient]);

  // Prefetch inteligente baseado em padrões de navegação
  const intelligentPrefetch = useCallback(() => {
    // Apenas invalidar queries existentes, não fazer prefetch sem queryFn
    const existingQueries = queryClient.getQueryCache().getAll();

    // Invalidar queries antigas para forçar refetch quando necessário
    existingQueries.forEach(query => {
      const lastAccess = query.state.dataUpdatedAt;
      const isStale = Date.now() - lastAccess > 30 * 60 * 1000; // 30 minutos

      if (isStale && query.getObserversCount() > 0) {
        queryClient.invalidateQueries({ queryKey: query.queryKey });
      }
    });
  }, [queryClient]);

  // Monitoramento de métricas
  const updateMetrics = useCallback(() => {
    const queries = queryClient.getQueryCache().getAll();
    const totalQueries = queries.length;
    
    // Estimar uso de memória
    const memoryUsage = queries.reduce((total, query) => {
      const dataSize = JSON.stringify(query.state.data || {}).length;
      return total + dataSize;
    }, 0);

    // Calcular hit rate
    const cacheHits = queries.filter(q => q.state.dataUpdatedAt > 0).length;
    const hitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;

    metricsRef.current = {
      totalQueries,
      memoryUsage: memoryUsage / 1024 / 1024, // MB
      hitRate,
      lastCleanup: metricsRef.current.lastCleanup
    };
  }, [queryClient]);

  // Limpeza automática baseada em condições
  useEffect(() => {
    const interval = setInterval(() => {
      updateMetrics();
      
      const { totalQueries, memoryUsage, lastCleanup } = metricsRef.current;
      const timeSinceCleanup = new Date().getTime() - lastCleanup.getTime();
      
      // Condições para limpeza automática
      const shouldCleanup = 
        totalQueries > 100 || // Muitas queries
        memoryUsage > 50 || // Mais de 50MB
        timeSinceCleanup > 30 * 60 * 1000; // 30 minutos desde última limpeza
      
      if (shouldCleanup) {
        const removed = intelligentCacheCleanup();
        if (removed > 0) {
          optimizeCacheSettings();
        }
      }
    }, 5 * 60 * 1000); // Verificar a cada 5 minutos

    return () => clearInterval(interval);
  }, [intelligentCacheCleanup, optimizeCacheSettings, updateMetrics]);

  // Limpeza manual
  const manualCleanup = useCallback(() => {
    const removed = intelligentCacheCleanup();
    optimizeCacheSettings();
    updateMetrics();
    return removed;
  }, [intelligentCacheCleanup, optimizeCacheSettings, updateMetrics]);

  // Invalidação seletiva
  const selectiveInvalidation = useCallback((patterns: string[]) => {
    patterns.forEach(pattern => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = JSON.stringify(query.queryKey);
          return queryKey.includes(pattern);
        }
      });
    });
  }, [queryClient]);

  return {
    metrics: metricsRef.current,
    manualCleanup,
    intelligentPrefetch,
    selectiveInvalidation,
    optimizeCacheSettings
  };
};

/**
 * Hook para invalidação inteligente baseada em ações do usuário
 */
export const useSmartInvalidation = () => {
  const queryClient = useQueryClient();

  const invalidateAfterAnswer = useCallback(() => {
    // Invalidar apenas dados relacionados a estatísticas do usuário
    queryClient.invalidateQueries({ queryKey: ['user-stats'] });
    queryClient.invalidateQueries({ queryKey: ['streak-stats'] });
    queryClient.invalidateQueries({ queryKey: ['temporal-insights'] });
  }, [queryClient]);

  const invalidateAfterSession = useCallback(() => {
    // Invalidar dados de sessão e progresso
    queryClient.invalidateQueries({ queryKey: ['study-sessions'] });
    queryClient.invalidateQueries({ queryKey: ['user-progress'] });
    invalidateAfterAnswer(); // Inclui invalidação de estatísticas
  }, [queryClient, invalidateAfterAnswer]);

  const invalidateAfterFlashcard = useCallback(() => {
    // Invalidar apenas dados de flashcards
    queryClient.invalidateQueries({ queryKey: ['flashcard-reviews'] });
    queryClient.invalidateQueries({ queryKey: ['flashcard-stats'] });
  }, [queryClient]);

  return {
    invalidateAfterAnswer,
    invalidateAfterSession,
    invalidateAfterFlashcard
  };
};
