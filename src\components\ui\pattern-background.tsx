
interface PatternBackgroundProps {
  children: React.ReactNode;
  className?: string;
}

export function PatternBackground({ children, className = "" }: PatternBackgroundProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10" />
      <div className="relative z-10">{children}</div>
    </div>
  );
}
