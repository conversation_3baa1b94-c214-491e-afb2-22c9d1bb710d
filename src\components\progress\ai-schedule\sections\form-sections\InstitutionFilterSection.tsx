import React from "react";
import { UseFormReturn } from "react-hook-form";
import { Building2, <PERSON>, <PERSON><PERSON>, Setting<PERSON> } from "lucide-react";
import { UserPreferencesDisplay } from "@/components/study/UserPreferencesDisplay";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";
import type { AIScheduleFormData } from "../../types";

interface InstitutionFilterSectionProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const InstitutionFilterSection = ({ form }: InstitutionFilterSectionProps) => {
  const { preferences } = useStudyPreferences();
  const generationMode = form.watch('generationMode') || 'institution_based';

  // Definir modo padrão baseado nas preferências do usuário
  React.useEffect(() => {
    if (preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown) {
      // Usuário tem preferências configuradas - usar modo baseado em preferências por padrão
      form.setValue('generationMode', 'institution_based');
    } else {
      // Usuário não tem preferências - usar modo aleatório por padrão
      form.setValue('generationMode', 'random');
    }
  }, [preferences, form]);

  return (
    <div className="p-4 sm:p-6 space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
      <div className="flex items-center gap-2 text-slate-800">
        <div className="p-2 rounded-full bg-green-100">
          <Building2 className="w-5 h-5 text-green-600" />
        </div>
        <h3 className="text-lg font-bold">Cronograma Personalizado</h3>
      </div>

      {/* Mostrar preferências do usuário */}
      <div className="mt-4">
        <UserPreferencesDisplay />
      </div>

      {/* Seleção de modo de geração */}
      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <Label className="text-sm font-semibold text-gray-800 mb-3 block">
          🎯 Modo de Geração do Cronograma
        </Label>

        <RadioGroup
          value={generationMode}
          onValueChange={(value) => {
            console.log('🎯 [InstitutionFilterSection] Modo selecionado:', value);
            form.setValue('generationMode', value as 'institution_based' | 'random');

            // Limpar campos específicos quando mudar o modo
            if (value === 'random') {
              form.setValue('institutionIds', []);
              form.setValue('startYear', undefined);
              form.setValue('endYear', undefined);
            }
          }}
          className="space-y-3"
        >
          {/* Modo baseado em preferências */}
          <div className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-white transition-colors">
            <RadioGroupItem value="institution_based" id="preference-mode" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="preference-mode" className="flex items-center gap-2 font-medium cursor-pointer">
                <Brain className="h-4 w-4 text-green-600" />
                Baseado em Preferências
                {preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Recomendado</span>
                )}
              </Label>
              <p className="text-xs text-gray-600 mt-1">
                Usa suas instituições alvo para priorizar temas mais frequentes nas provas
              </p>
              {preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown && (
                <p className="text-xs text-green-600 mt-1">
                  ✅ Suas {preferences.target_institutions.length} instituições serão usadas
                </p>
              )}
            </div>
          </div>

          {/* Modo aleatório */}
          <div className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-white transition-colors">
            <RadioGroupItem value="random" id="random-mode" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="random-mode" className="flex items-center gap-2 font-medium cursor-pointer">
                <Shuffle className="h-4 w-4 text-gray-600" />
                Aleatório
              </Label>
              <p className="text-xs text-gray-600 mt-1">
                Distribui todos os temas disponíveis de forma equilibrada (ignora preferências)
              </p>
            </div>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
};
