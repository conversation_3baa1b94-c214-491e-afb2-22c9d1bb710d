import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NavigationLockContextType {
  isNavigationLocked: boolean;
  lockNavigation: (reason: string) => void;
  unlockNavigation: () => void;
  lockReason: string;
}

const NavigationLockContext = createContext<NavigationLockContextType | undefined>(undefined);

interface NavigationLockProviderProps {
  children: ReactNode;
}

export const NavigationLockProvider: React.FC<NavigationLockProviderProps> = ({ children }) => {
  const [isNavigationLocked, setIsNavigationLocked] = useState(false);
  const [lockReason, setLockReason] = useState('');

  const lockNavigation = (reason: string) => {
    setIsNavigationLocked(true);
    setLockReason(reason);
  };

  const unlockNavigation = () => {
    setIsNavigationLocked(false);
    setLockReason('');
  };

  return (
    <NavigationLockContext.Provider value={{
      isNavigationLocked,
      lockNavigation,
      unlockNavigation,
      lockReason
    }}>
      {children}
    </NavigationLockContext.Provider>
  );
};

export const useNavigationLock = () => {
  const context = useContext(NavigationLockContext);
  if (context === undefined) {
    throw new Error('useNavigationLock must be used within a NavigationLockProvider');
  }
  return context;
};
