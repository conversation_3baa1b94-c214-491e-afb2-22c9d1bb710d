
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { motion } from "framer-motion";
import { Play, Shuffle, AlertCircle, Info, EyeOff } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useIsMobile } from "@/hooks/use-mobile";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { useState } from "react";

interface FilterActionsProps {
  questionCount: number;
  onShowRandomDialog: () => void;
  onStartStudy: () => void;
  excludeAnswered: boolean;
  onExcludeAnsweredChange: (value: boolean) => void;
  answeredQuestionsCount?: number;
  filteredExcludedCount?: number; // Nova propriedade para contar questões excluídas com o filtro atual
}

export const FilterActions = ({
  questionCount,
  onShowRandomDialog,
  onStartStudy,
  excludeAnswered,
  onExcludeAnsweredChange,
  answeredQuestionsCount = 0,
  filteredExcludedCount = 0 // Valor padrão
}: FilterActionsProps) => {
  const isMobile = useIsMobile();
  // O botão "Iniciar Estudos" só fica habilitado quando há questões selecionadas
  // e não excede o limite máximo (300)
  const hasValidQuestionCount = questionCount > 0 && questionCount <= 300;
  const MAX_QUESTIONS_ALLOWED = 300;
  const isOverLimit = questionCount > MAX_QUESTIONS_ALLOWED;
  const hasNoQuestions = questionCount === 0;
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);

  // Mensagem informativa para o botão de iniciar estudos
  const getStartButtonMessage = () => {
    if (questionCount === 0) {
      return "Selecione pelo menos uma questão para iniciar";
    } else if (questionCount > MAX_QUESTIONS_ALLOWED) {
      return `Limite excedido: Máximo de ${MAX_QUESTIONS_ALLOWED} questões permitido`;
    } else {
      return `${questionCount} questões selecionadas`;
    }
  };

  // Mensagem informativa para o switch de excluir questões acertadas
  const getExcludeAnsweredMessage = () => {
    if (answeredQuestionsCount === 0) {
      return "Você ainda não acertou nenhuma questão";
    } else if (excludeAnswered) {
      return `Excluindo ${answeredQuestionsCount} questões que você já acertou`;
    } else {
      return "Incluir questões que você já acertou";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="space-y-6"
    >
      {/* Opção para excluir questões já respondidas */}
      <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-4">
        <h4 className="text-sm font-semibold text-blue-800 mb-3 flex items-center gap-2">
          ⚙️ Configurações Avançadas
        </h4>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Switch
              id="exclude-answered"
              checked={excludeAnswered}
              onCheckedChange={onExcludeAnsweredChange}
              disabled={false}
            />
            <Label
              htmlFor="exclude-answered"
              className="flex items-center text-sm cursor-pointer text-blue-700"
            >
              <EyeOff className="h-4 w-4 mr-2 text-blue-600" />
              <span>Ocultar questões já acertadas</span>
            </Label>
          </div>

          <Info
            className="h-4 w-4 text-blue-400 cursor-help hover:text-blue-600 transition-colors"
            onClick={() => setHelpDialogOpen(true)}
          />
        </div>

        {excludeAnswered && filteredExcludedCount > 0 && (
          <div className="mt-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
            📊 {filteredExcludedCount} questões serão excluídas do resultado
          </div>
        )}
      </div>

      {/* Botões de Ação */}
      <div className="bg-gradient-to-r from-gray-50 to-white border-2 border-gray-200 rounded-xl p-4 sm:p-6" data-tutorial="study-options">
        <h4 className="text-sm font-semibold text-gray-800 mb-4 text-center flex items-center justify-center gap-2">
          🚀 Iniciar Estudos
        </h4>

        {/* Status Info */}
        {(hasNoQuestions || isOverLimit) && (
          <div className="mb-4 text-center px-2">
            <div className={`inline-block text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-lg border-2 max-w-full ${
              isOverLimit
                ? "bg-red-50 text-red-800 border-red-300"
                : "bg-amber-50 text-amber-800 border-amber-300"
            }`}>
              {isOverLimit ? (
                <div className="space-y-1">
                  <div className="font-semibold flex items-center justify-center gap-1 flex-wrap">
                    ⚠️ Muitas questões ({questionCount.toLocaleString()})
                  </div>
                  <div className="text-xs opacity-90 text-center">
                    📚 <span className="font-medium">Estudar:</span> Filtre até {MAX_QUESTIONS_ALLOWED}
                  </div>
                  <div className="text-xs opacity-90 text-center">
                    🎲 <span className="font-medium">Mix:</span> Sem limite
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center gap-1">
                  ℹ️ Selecione filtros para começar
                </div>
              )}
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row justify-center items-stretch gap-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={onShowRandomDialog}
                  disabled={hasNoQuestions}
                  className={`flex-1 sm:flex-none sm:w-[180px] border-2 border-black ${
                    hasNoQuestions
                      ? "bg-amber-50 text-amber-700 hover:bg-amber-100"
                      : "bg-[#FEF7CD] text-[#FF6B00] hover:bg-[#FEF7CD]/80"
                  } transition-all duration-300`}
                >
                  <Shuffle className="h-4 w-4 mr-2" />
                  Mix de Questões
                </Button>
              </TooltipTrigger>
              <TooltipContent className={hasNoQuestions ? "bg-amber-50 border-amber-200" : ""}>
                <p>{hasNoQuestions ? "Selecione pelo menos uma questão para criar um mix" : "Criar mix aleatório de questões filtradas"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={onStartStudy}
                  disabled={!hasValidQuestionCount}
                  className={`flex-1 sm:flex-none sm:w-[180px] shadow-md hover:shadow-lg transition-all duration-300 border-2 ${
                    hasValidQuestionCount
                      ? "bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white border-black"
                      : (isOverLimit
                          ? "bg-red-100 text-red-700 border-red-200 hover:bg-red-200"
                          : "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100")
                  }`}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Iniciar Estudos
                </Button>
              </TooltipTrigger>
              <TooltipContent className={
                isOverLimit ? "bg-red-50 border-red-200" :
                hasNoQuestions ? "bg-amber-50 border-amber-200" : ""
              }>
                <p>{getStartButtonMessage()}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Dialog de ajuda para explicar a funcionalidade */}
      <Dialog open={helpDialogOpen} onOpenChange={setHelpDialogOpen}>
        <DialogContent className="w-[95vw] max-w-md max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Ocultar questões já acertadas</DialogTitle>
            <DialogDescription>
              Esta funcionalidade permite filtrar questões que você já acertou anteriormente,
              evitando que apareçam novamente nos resultados da sua busca atual.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="rounded-md bg-blue-50 p-4 border border-blue-100">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Info className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">Como funciona</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Quando ativada, esta opção filtra automaticamente todas as questões que você já acertou em sessões de estudo anteriores.</li>
                      <li>Isso ajuda a focar em questões novas ou naquelas que você ainda não domina.</li>
                      <li>O sistema considera apenas questões que você respondeu corretamente.</li>
                      <li>O número ao lado da opção indica quantas questões serão excluídas dos resultados atuais.</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-sm text-gray-500">
              Esta funcionalidade está disponível apenas para usuários que já responderam pelo menos uma questão corretamente.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};
