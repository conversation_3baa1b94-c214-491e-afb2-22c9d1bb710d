import React from 'react';

interface FormattedQuestionContentProps {
  content: string;
  className?: string;
}

export const FormattedQuestionContent: React.FC<FormattedQuestionContentProps> = ({ 
  content, 
  className = "" 
}) => {
  const formatContent = (text: string): string => {
    let formattedText = text
      // 1. Corrigir entidades HTML (ambas as formas)
      .replace(/&#39;/g, "'")           // &#39; → '
      .replace(/&amp;#39;/g, "'")      // &amp;#39; → '
      .replace(/&#quot;/g, '"')        // &#quot; → "
      .replace(/&amp;quot;/g, '"')     // &amp;quot; → "
      .replace(/&#lt;/g, '<')          // &#lt; → <
      .replace(/&amp;lt;/g, '<')       // &amp;lt; → <
      .replace(/&#gt;/g, '>')          // &#gt; → >
      .replace(/&amp;gt;/g, '>')       // &amp;gt; → >
      .replace(/&#amp;/g, '&')         // &#amp; → &
      .replace(/&amp;amp;/g, '&')      // &amp;amp; → &
      .replace(/&nbsp;/g, ' ')          // &nbsp; → espaço

      // 2. Corrigir caracteres Unicode comuns
      .replace(/\\u00e9/g, 'é')
      .replace(/\\u00e1/g, 'á')
      .replace(/\\u00f3/g, 'ó')
      .replace(/\\u00e7/g, 'ç')
      .replace(/\\u00ea/g, 'ê')
      .replace(/\\u00e0/g, 'à')
      .replace(/\\u00f4/g, 'ô')
      .replace(/\\u00e2/g, 'â')
      .replace(/\\u00ed/g, 'í')
      .replace(/\\u00fa/g, 'ú')
      .replace(/\\u00f5/g, 'õ')
      .replace(/\\u00e3/g, 'ã')
      .replace(/\\u00fc/g, 'ü')
      .replace(/\\u00c1/g, 'Á')
      .replace(/\\u00c9/g, 'É')
      .replace(/\\u00cd/g, 'Í')
      .replace(/\\u00d3/g, 'Ó')
      .replace(/\\u00da/g, 'Ú')
      .replace(/\\u00c7/g, 'Ç')

      // 3. Corrigir aspas e caracteres especiais
      .replace(/\\u201c|\\u201d/g, '"')
      .replace(/\\u2018|\\u2019/g, "'")
      .replace(/\\u2013/g, '–')
      .replace(/\\u2014/g, '—')
      .replace(/\\u00b0/g, '°')
      .replace(/\\u00b2/g, '²')
      .replace(/\\u00b3/g, '³')
      .replace(/\\u00a0/g, ' ')

      // 4. Limpar espaços extras
      .replace(/\s+/g, ' ')
      .trim();

    // 5. Detectar pergunta apenas no final do texto (mais conservador)
    // Procurar por padrões que claramente indicam uma pergunta no final
    const questionMatch = formattedText.match(/^(.*?)\s+((?:Assinale|Qual|Marque|Indique)\s+[^.]*[.?:])\s*$/i);

    if (questionMatch) {
      const mainContent = questionMatch[1].trim();
      const question = questionMatch[2].trim();

      // Adicionar quebras de linha básicas apenas
      const formattedMainContent = mainContent
        .replace(/\.\s+([A-Z][a-z]{3,}[^.]{50,})/g, '.\n\n$1');

      return `${formattedMainContent}\n\n|||QUESTION|||${question}`;
    }

    // Se não encontrou pergunta clara, apenas adicionar quebras básicas
    return formattedText
      .replace(/\.\s+([A-Z][a-z]{3,}[^.]{50,})/g, '.\n\n$1');
  };

  const renderFormattedContent = (formattedText: string) => {
    // Separar o texto principal da pergunta
    const parts = formattedText.split('|||QUESTION|||');

    if (parts.length === 1) {
      // Não há pergunta separada, renderizar tudo junto
      return (
        <div
          className="text-gray-700 leading-relaxed whitespace-pre-line"
          dangerouslySetInnerHTML={{ __html: parts[0] }}
        />
      );
    }

    // Há pergunta separada
    const mainContent = parts[0].trim();
    const question = parts[1].trim();

    return (
      <>
        {mainContent && (
          <div
            className="text-gray-700 leading-relaxed mb-4 whitespace-pre-line"
            dangerouslySetInnerHTML={{ __html: mainContent }}
          />
        )}
        {question && (
          <div
            className="font-medium text-gray-900 mt-4 mb-2 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400"
            dangerouslySetInnerHTML={{ __html: question }}
          />
        )}
      </>
    );
  };

  const formattedContent = formatContent(content);

  return (
    <div className={`formatted-question-content ${className}`}>
      {renderFormattedContent(formattedContent)}
    </div>
  );
};

export default FormattedQuestionContent;
