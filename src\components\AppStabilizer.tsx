import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Componente simplificado para estabilizar queries ao navegar entre abas
 */
export const AppStabilizer = () => {
  const queryClient = useQueryClient();
  const stabilizationTimeoutRef = useRef<NodeJS.Timeout>();
  const isInitialized = useRef(false);

  useEffect(() => {
    if (isInitialized.current) return;
    isInitialized.current = true;

    // Limpeza simples de cache a cada 5 minutos
    const cleanupInterval = setInterval(() => {
      const queryCache = queryClient.getQueryCache();
      const queries = queryCache.getAll();

      // Remover queries antigas (mais de 5 minutos)
      queries.forEach(query => {
        const lastUpdated = query.state.dataUpdatedAt;
        const now = Date.now();

        if (lastUpdated && now - lastUpdated > 5 * 60 * 1000) {
          queryCache.remove(query);
        }
      });
    }, 5 * 60 * 1000); // 5 minutos

    return () => {
      clearInterval(cleanupInterval);
      clearTimeout(stabilizationTimeoutRef.current);
    };
  }, [queryClient]);



  return null; // Componente invisível
};
