import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useStaticStudyCategories } from '@/hooks/useStaticDataCache';

export interface DifficultyInsight {
  category: string;
  categoryId: string;
  categoryType: 'specialty' | 'theme' | 'focus';
  avgTimeSpent: number;
  difficultyLevel: 'easy' | 'medium' | 'hard';
  accuracy: number;
  totalQuestions: number;
  commonMistakePatterns: string[];
  improvementTips: string[];
  timeEfficiency: 'fast' | 'normal' | 'slow';
  consistencyScore: number;
}

export interface QuestionTypeAnalysis {
  questionFormat: string;
  accuracy: number;
  avgTime: number;
  totalQuestions: number;
  difficultyRating: number;
}

export interface SmartRecommendation {
  type: 'weakness' | 'strength' | 'opportunity' | 'time_management';
  category: string;
  message: string;
  action: string;
  priority: 'high' | 'medium' | 'low';
  estimatedImpact: string;
}

export interface DifficultyData {
  categoryInsights: DifficultyInsight[];
  questionTypeAnalysis: QuestionTypeAnalysis[];
  recommendations: SmartRecommendation[];
  overallDifficulty: {
    easiestCategory: string;
    hardestCategory: string;
    avgTimePerQuestion: number;
    timeEfficiencyScore: number;
  };
  weaknessAreas: DifficultyInsight[];
  strengthAreas: DifficultyInsight[];
}

export const useDifficultyInsights = () => {
  const { user } = useAuth();
  const { data: staticCategories } = useStaticStudyCategories();

  return useQuery({
    queryKey: ['difficulty-insights', user?.id],
    queryFn: async (): Promise<DifficultyData> => {
      if (!user?.id) throw new Error('User not authenticated');
      if (!staticCategories) throw new Error("Static categories not loaded");



      // Criar mapeamento de categorias
      const allCategories = [
        ...staticCategories.specialties.map(s => ({ ...s, type: 'specialty' as const })),
        ...staticCategories.themes.map(t => ({ ...t, type: 'theme' as const })),
        ...staticCategories.focuses.map(f => ({ ...f, type: 'focus' as const }))
      ];

      const categoryMap = allCategories.reduce((acc, cat) => {
        acc[cat.id] = cat;
        return acc;
      }, {} as Record<string, any>);

      // Buscar respostas com dados de questões
      const { data: answersWithQuestions, error } = await supabase
        .from('user_answers')
        .select(`
          *,
          questions!inner(
            question_format,
            exam_year,
            exam_location
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;





      // Agrupar por categoria para análise
      const categoryStats: Record<string, {
        times: number[];
        correct: number;
        total: number;
        questionFormats: Record<string, number>;
        years: number[];
      }> = {};

      // Análise por tipo de questão
      const questionTypeStats: Record<string, {
        correct: number;
        total: number;
        totalTime: number;
        times: number[];
      }> = {};

      // ✅ USAR APENAS QUESTÕES ÚNICAS PARA ANÁLISE
      const uniqueAnswersForAnalysis = answersWithQuestions?.reduce((unique, answer) => {
        const exists = unique.find(existing => existing.question_id === answer.question_id);
        if (!exists) {
          unique.push(answer);
        }
        return unique;
      }, [] as typeof answersWithQuestions) || [];



      uniqueAnswersForAnalysis.forEach(answer => {
        const categories = [
          { id: answer.specialty_id, type: 'specialty' },
          { id: answer.theme_id, type: 'theme' },
          { id: answer.focus_id, type: 'focus' }
        ].filter(cat => cat.id);

        const timeSpent = answer.time_spent || 0;
        const questionFormat = answer.questions?.question_format || 'UNKNOWN';

        // Estatísticas por categoria
        categories.forEach(({ id }) => {
          if (!categoryStats[id]) {
            categoryStats[id] = {
              times: [],
              correct: 0,
              total: 0,
              questionFormats: {},
              years: []
            };
          }

          categoryStats[id].times.push(timeSpent);
          categoryStats[id].total++;
          if (answer.is_correct) categoryStats[id].correct++;

          categoryStats[id].questionFormats[questionFormat] =
            (categoryStats[id].questionFormats[questionFormat] || 0) + 1;

          if (answer.questions?.exam_year) {
            categoryStats[id].years.push(answer.questions.exam_year);
          }
        });

        // Estatísticas por tipo de questão
        if (!questionTypeStats[questionFormat]) {
          questionTypeStats[questionFormat] = {
            correct: 0,
            total: 0,
            totalTime: 0,
            times: []
          };
        }

        questionTypeStats[questionFormat].total++;
        questionTypeStats[questionFormat].totalTime += timeSpent;
        questionTypeStats[questionFormat].times.push(timeSpent);
        if (answer.is_correct) questionTypeStats[questionFormat].correct++;
      });

      // Processar insights de dificuldade por categoria
      const categoryInsights: DifficultyInsight[] = Object.entries(categoryStats)
        .map(([categoryId, stats]) => {
          const category = categoryMap[categoryId];
          if (!category || stats.total < 3) return null; // Mínimo de 3 questões para análise

          const avgTime = stats.times.reduce((sum, time) => sum + time, 0) / stats.times.length;
          const accuracy = (stats.correct / stats.total) * 100;

          // Calcular consistência (desvio padrão dos tempos)
          const timeVariance = stats.times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / stats.times.length;
          const timeStdDev = Math.sqrt(timeVariance);
          const consistencyScore = Math.max(0, 100 - (timeStdDev / avgTime) * 100);

          // Determinar nível de dificuldade
          let difficultyLevel: 'easy' | 'medium' | 'hard' = 'medium';
          if (accuracy >= 80 && avgTime <= 30) difficultyLevel = 'easy';
          else if (accuracy <= 50 || avgTime >= 60) difficultyLevel = 'hard';

          // Determinar eficiência de tempo
          let timeEfficiency: 'fast' | 'normal' | 'slow' = 'normal';
          if (avgTime <= 20) timeEfficiency = 'fast';
          else if (avgTime >= 45) timeEfficiency = 'slow';

          // Gerar dicas de melhoria
          const improvementTips: string[] = [];
          if (accuracy < 70) {
            improvementTips.push('Revisar conceitos fundamentais desta área');
            improvementTips.push('Praticar mais questões similares');
          }
          if (avgTime > 40) {
            improvementTips.push('Trabalhar na velocidade de resolução');
            improvementTips.push('Praticar técnicas de leitura rápida');
          }
          if (consistencyScore < 70) {
            improvementTips.push('Focar na consistência de performance');
            improvementTips.push('Estabelecer rotina de estudos regular');
          }

          // Identificar padrões de erro
          const commonMistakePatterns: string[] = [];
          const mostCommonFormat = Object.entries(stats.questionFormats)
            .sort(([,a], [,b]) => b - a)[0];

          if (mostCommonFormat && accuracy < 60) {
            commonMistakePatterns.push(`Dificuldade em questões ${mostCommonFormat[0]}`);
          }

          return {
            category: category.name,
            categoryId,
            categoryType: category.type,
            avgTimeSpent: Math.round(avgTime),
            difficultyLevel,
            accuracy: Math.round(accuracy * 10) / 10,
            totalQuestions: stats.total,
            commonMistakePatterns,
            improvementTips,
            timeEfficiency,
            consistencyScore: Math.round(consistencyScore)
          };
        })
        .filter(Boolean) as DifficultyInsight[];

      // Processar análise por tipo de questão
      const questionTypeAnalysis: QuestionTypeAnalysis[] = Object.entries(questionTypeStats)
        .map(([format, stats]) => {
          const accuracy = (stats.correct / stats.total) * 100;
          const avgTime = stats.totalTime / stats.total;

          // Calcular rating de dificuldade (0-100, onde 100 é mais difícil)
          const timeScore = Math.min(100, (avgTime / 60) * 100); // Normalizar para 60s
          const accuracyScore = 100 - accuracy;
          const difficultyRating = (timeScore + accuracyScore) / 2;

          return {
            questionFormat: format,
            accuracy: Math.round(accuracy * 10) / 10,
            avgTime: Math.round(avgTime),
            totalQuestions: stats.total,
            difficultyRating: Math.round(difficultyRating)
          };
        })
        .sort((a, b) => b.difficultyRating - a.difficultyRating);

      // Gerar recomendações inteligentes
      const recommendations: SmartRecommendation[] = [];

      // Identificar pontos fracos
      const weaknessAreas = categoryInsights
        .filter(insight => insight.accuracy < 60 || insight.difficultyLevel === 'hard')
        .sort((a, b) => a.accuracy - b.accuracy)
        .slice(0, 3);

      // Identificar pontos fortes
      const strengthAreas = categoryInsights
        .filter(insight => insight.accuracy >= 80 && insight.difficultyLevel === 'easy')
        .sort((a, b) => b.accuracy - a.accuracy)
        .slice(0, 3);

      // Recomendações para pontos fracos
      weaknessAreas.forEach(weakness => {
        recommendations.push({
          type: 'weakness',
          category: weakness.category,
          message: `${weakness.category} precisa de atenção especial (${weakness.accuracy}% de acertos)`,
          action: `Dedicar 30min diários para revisar ${weakness.category}`,
          priority: weakness.accuracy < 40 ? 'high' : 'medium',
          estimatedImpact: 'Melhoria de 15-25% em 2 semanas'
        });
      });

      // Recomendações para eficiência de tempo
      const slowCategories = categoryInsights.filter(i => i.timeEfficiency === 'slow');
      if (slowCategories.length > 0) {
        recommendations.push({
          type: 'time_management',
          category: 'Gestão de Tempo',
          message: `Você está gastando muito tempo em algumas categorias`,
          action: 'Praticar resolução cronometrada de questões',
          priority: 'medium',
          estimatedImpact: 'Redução de 20-30% no tempo por questão'
        });
      }

      // ✅ USAR APENAS QUESTÕES ÚNICAS (como useUserStatistics)
      const uniqueAnswers = answersWithQuestions?.reduce((unique, answer) => {
        const exists = unique.find(existing => existing.question_id === answer.question_id);
        if (!exists) {
          unique.push(answer);
        }
        return unique;
      }, [] as typeof answersWithQuestions) || [];

      const allTimes = uniqueAnswers.map(answer => answer.time_spent || 0);
      const totalTimeSpent = allTimes.reduce((sum, time) => sum + time, 0);
      const overallAvgTime = allTimes.length > 0 ? totalTimeSpent / allTimes.length : 0;



      const easiestCategory = categoryInsights
        .filter(i => i.totalQuestions >= 5)
        .sort((a, b) => b.accuracy - a.accuracy)[0]?.category || 'N/A';

      const hardestCategory = categoryInsights
        .filter(i => i.totalQuestions >= 5)
        .sort((a, b) => a.accuracy - b.accuracy)[0]?.category || 'N/A';

      const timeEfficiencyScore = Math.round(
        100 - Math.min(100, (overallAvgTime / 45) * 100)
      );

      const result: DifficultyData = {
        categoryInsights,
        questionTypeAnalysis,
        recommendations,
        overallDifficulty: {
          easiestCategory,
          hardestCategory,
          avgTimePerQuestion: Math.round(overallAvgTime),
          timeEfficiencyScore
        },
        weaknessAreas,
        strengthAreas
      };



      return result;
    },
    enabled: !!user?.id && !!staticCategories,
    retry: 1,
    staleTime: 0,
    cacheTime: 10 * 60 * 1000,
  });
};
