
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle, Info, Loader2, AlertTriangle, BookOpen, Target, Zap, Settings } from "lucide-react";
import { motion } from "framer-motion";
import { filterQuestionsByTopic } from "@/utils/questionUtils";
import { useUser } from "@supabase/auth-helpers-react";
import { useDomain } from "@/hooks/useDomain";

interface StudyOptionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  maxQuestions: number;
  minQuestions?: number;
  availableYears: number[];
  availableInstitutions: { id: string; name: string }[];
  onStartStudy: (quantity: number, hideAnswered?: boolean) => Promise<void>;
  totalTopics?: number;
  // Props para recalcular questões
  specialtyId?: string;
  themeId?: string;
  focusId?: string;
  // Props para múltiplos tópicos
  allTopicsData?: Array<{
    topic: any;
    questionCount: number;
    questions: any[];
  }>;
}

export const StudyOptionsDialog = ({
  open,
  onOpenChange,
  maxQuestions,
  minQuestions = 1,
  availableYears,
  availableInstitutions,
  onStartStudy,
  totalTopics = 0,
  specialtyId,
  themeId,
  focusId,
  allTopicsData
}: StudyOptionsDialogProps) => {
  const [quantity, setQuantity] = useState(minQuestions > 10 ? minQuestions : 10);
  const [loading, setLoading] = useState(false);
  const [hideAnswered, setHideAnswered] = useState(false);
  const [filteredTotal, setFilteredTotal] = useState(maxQuestions);
  const [recalculating, setRecalculating] = useState(false);

  const user = useUser();
  const { domain } = useDomain();

  // Recalcular questões quando hideAnswered muda
  useEffect(() => {
    const recalculateQuestions = async () => {
      if (!user?.id) return;

      setRecalculating(true);

      try {
        // Se é um tópico específico (totalTopics === 1)
        if (totalTopics === 1 && specialtyId && themeId && focusId) {
          const result = await filterQuestionsByTopic(
            specialtyId,
            themeId,
            focusId,
            1000,
            undefined,
            undefined,
            false,
            domain,
            hideAnswered,
            user.id
          );

          setFilteredTotal(result.count);
        } else if (totalTopics > 1 && allTopicsData && hideAnswered) {
          // Para múltiplos tópicos, recalcular cada tópico
          const { getUserCorrectAnswers } = await import('@/utils/questionUtils');
          const correctAnswers = await getUserCorrectAnswers(user.id);

          // Calcular total filtrado
          let totalFiltered = 0;
          allTopicsData.forEach(({ questions }) => {
            const filteredQuestions = questions.filter(q => !correctAnswers.includes(q.id));
            totalFiltered += filteredQuestions.length;
          });

          setFilteredTotal(totalFiltered);
        } else {
          // Sem filtro ou dados insuficientes
          setFilteredTotal(maxQuestions);
        }
      } catch (error) {
        setFilteredTotal(maxQuestions);
      } finally {
        setRecalculating(false);
      }
    };

    recalculateQuestions();
  }, [hideAnswered, specialtyId, themeId, focusId, user?.id, domain, maxQuestions, totalTopics, allTopicsData]);

  // Limitar o máximo de questões a 25, mesmo quando houver mais disponíveis
  const HARD_LIMIT = 25;
  const displayTotalQuestions = filteredTotal;
  const effectiveMaxQuestions = Math.max(minQuestions, Math.min(filteredTotal, HARD_LIMIT));

  // Ajustar a quantidade se exceder o máximo disponível quando o maxQuestions prop mudar
  useEffect(() => {
    if (quantity > effectiveMaxQuestions) {
      setQuantity(effectiveMaxQuestions);
    }
  }, [effectiveMaxQuestions, maxQuestions]);

  // Inicializar a quantidade com um valor razoável quando o diálogo abrir
  useEffect(() => {
    if (open) {
      const initialQuantity = Math.min(
        Math.max(minQuestions, 10),
        effectiveMaxQuestions
      );
      setQuantity(initialQuantity);
    }
  }, [open, minQuestions, effectiveMaxQuestions, displayTotalQuestions]);

  const handleStartStudy = async () => {
    try {
      setLoading(true);
      // Garantir que não excedemos o máximo de questões disponíveis
      const finalQuantity = Math.min(quantity, effectiveMaxQuestions);
      await onStartStudy(finalQuantity, hideAnswered);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[85dvw] sm:max-w-lg max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto bg-gradient-to-br from-white to-blue-50 border-2 border-black shadow-2xl">
        <DialogHeader className="text-center pb-6 border-b border-blue-200">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center gap-3 mb-3"
          >
            <div className="p-3 bg-gradient-to-r from-blue-500 to-green-500 rounded-full border-2 border-black">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
              Opções de Estudo
            </DialogTitle>
          </motion.div>
          <DialogDescription className="text-base text-gray-600">
            {totalTopics > 1
              ? `Configure seu estudo personalizado com questões dos ${totalTopics} tópicos disponíveis hoje.`
              : "Configure seu estudo personalizado com as questões disponíveis."}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {maxQuestions === 0 ? (
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 flex items-start gap-2 text-sm text-amber-700">
              <AlertTriangle className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Nenhuma questão disponível</p>
                <p className="mt-1">Não encontramos questões para este tópico no momento.</p>
              </div>
            </div>
          ) : maxQuestions < 3 ? (
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 flex items-start gap-2 text-sm text-amber-700">
              <Info className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Poucas questões disponíveis</p>
                <p className="mt-1">Existem apenas {maxQuestions} questões disponíveis para este tópico.</p>
              </div>
            </div>
          ) : null}

          {totalTopics > 1 && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 flex items-start gap-2 text-sm text-blue-700">
              <Info className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Pelo menos uma questão de cada tópico</p>
                <p className="mt-1">Garantimos que você terá no mínimo {minQuestions} questões (uma por tópico) para um estudo balanceado.</p>
              </div>
            </div>
          )}

          {/* Seção da Quantidade */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-500" />
              <Label htmlFor="quantity" className="text-sm font-bold text-gray-700">
                Quantidade de Questões
              </Label>
            </div>

            {/* Card com informações */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-blue-700 font-medium">Questões selecionadas:</span>
                <div className="flex items-center gap-2">
                  <div className={`px-3 py-1 rounded-full border-2 border-black font-bold text-lg ${
                    quantity >= effectiveMaxQuestions
                      ? "bg-gradient-to-r from-amber-400 to-orange-400 text-white"
                      : "bg-gradient-to-r from-blue-500 to-green-500 text-white"
                  }`}>
                    {quantity}
                    {quantity >= effectiveMaxQuestions ? " (máx)" : ""}
                  </div>
                  <Zap className="h-4 w-4 text-blue-500" />
                </div>
              </div>
              <Slider
                id="quantity"
                min={minQuestions}
                max={effectiveMaxQuestions}
                step={1}
                value={[quantity]}
                onValueChange={(value) => setQuantity(value[0])}
                className="py-2"
              />

              <div className="flex justify-between text-xs text-blue-600 font-medium mt-2">
                <span>Mín: {minQuestions}</span>
                <span>Máx: {effectiveMaxQuestions}{maxQuestions > HARD_LIMIT ? ` (total: ${displayTotalQuestions})` : ''}</span>
              </div>
            </div>

            {maxQuestions > HARD_LIMIT && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 flex items-start gap-2 text-xs text-amber-700 mt-3">
                <Info className="h-4 w-4 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Limite de sessão:</p>
                  <p>O máximo por sessão é {HARD_LIMIT} questões, embora existam {displayTotalQuestions} disponíveis no total.</p>
                </div>
              </div>
            )}
          </motion.div>

          {/* Filtro para ocultar questões já acertadas */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-3"
          >
            <div className="flex items-center gap-2">
              <BookOpen className="h-4 w-4 text-green-500" />
              <Label className="text-sm font-bold text-gray-700">
                Filtros Avançados
              </Label>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="hide-answered"
                  checked={hideAnswered}
                  onCheckedChange={(checked) => setHideAnswered(checked as boolean)}
                  className="border-2 border-green-400 data-[state=checked]:bg-green-500"
                />
                <div className="flex-1">
                  <Label
                    htmlFor="hide-answered"
                    className="text-sm font-bold text-green-700 leading-none cursor-pointer"
                  >
                    Ocultar questões que já acertei
                  </Label>
                  <p className="text-xs text-green-600 mt-1">
                    Remove questões que você já respondeu corretamente em sessões anteriores
                  </p>
                </div>
              </div>

              {recalculating && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 flex items-start gap-2 text-xs text-blue-700 mt-3">
                  <Loader2 className="h-4 w-4 flex-shrink-0 mt-0.5 animate-spin" />
                  <p className="font-medium">Recalculando total de questões...</p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Botão de Ação */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="pt-6 border-t border-blue-200"
          >
            <Button
              onClick={handleStartStudy}
              disabled={loading || maxQuestions === 0}
              className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-bold border-2 border-black shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5 py-3 text-lg"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Preparando sessão...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Iniciar Estudo
                </>
              )}
            </Button>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
