import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { AIScheduleOptions } from "@/types/study-schedule";
import type { GenerationStats } from "./types";
import { useState } from "react";
import { useDomain } from "@/hooks/useDomain";
import { useInstitutionBasedSchedule } from "@/hooks/useInstitutionBasedSchedule";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";

export const useAISchedule = (
  setGenerationStats: (stats: GenerationStats | null) => void,
  addWeeks: (numberOfWeeks: number) => Promise<boolean>
) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { domain: userDomain } = useDomain();
  const { generateScheduleByInstitution } = useInstitutionBasedSchedule();
  const { preferences } = useStudyPreferences();

  const generateAISchedule = async (options: AIScheduleOptions) => {
    console.log('🚀 [useAISchedule] ===== INICIANDO GERAÇÃO DE CRONOGRAMA =====');
    console.log('🚀 [useAISchedule] Opções recebidas:', {
      generationMode: options.generationMode,
      institutionIds: options.institutionIds,
      startYear: options.startYear,
      endYear: options.endYear,
      weeksCount: options.weeksCount,
      scheduleOption: options.scheduleOption,
      domain: options.domain
    });

    setIsLoading(true);
    setGenerationStats(null);
    const startTime = performance.now();

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      console.log('🔍 [useAISchedule] Verificando preferências do usuário:', {
        hasPreferences: !!preferences,
        preferences_completed: preferences?.preferences_completed,
        target_institutions_count: preferences?.target_institutions?.length || 0,
        target_institutions_unknown: preferences?.target_institutions_unknown,
        target_institutions: preferences?.target_institutions?.map(inst => ({ id: inst.id, name: inst.name })) || []
      });

      // Se modo baseado em instituições OU se usuário tem preferências de instituições, usar o novo sistema
      const shouldUseInstitutionBased = options.generationMode === 'institution_based' ||
        (preferences?.target_institutions && preferences.target_institutions.length > 0 && !preferences.target_institutions_unknown);

      console.log('🎯 [useAISchedule] Decisão de modo de geração:', {
        shouldUseInstitutionBased,
        reason: options.generationMode === 'institution_based' ? 'Modo explicitamente definido como institution_based' :
                (preferences?.target_institutions && preferences.target_institutions.length > 0 && !preferences.target_institutions_unknown) ? 'Usuário tem preferências válidas' :
                'Fallback para modo aleatório'
      });

      if (shouldUseInstitutionBased) {
        // Usar preferências do usuário se não foram especificadas nas opções
        const institutionOptions = {
          ...options,
          generationMode: 'institution_based' as const,
          institutionIds: options.institutionIds?.length ? options.institutionIds :
            preferences?.target_institutions?.map(inst => inst.id) || [],
          startYear: options.startYear,
          endYear: options.endYear
        };

        console.log('🏥 [useAISchedule] Usando modo baseado em instituições:', {
          institutionIds: institutionOptions.institutionIds,
          institutionNames: institutionOptions.institutionIds.map(id =>
            preferences?.target_institutions?.find(inst => inst.id === id)?.name || `ID: ${id}`
          ),
          startYear: institutionOptions.startYear,
          endYear: institutionOptions.endYear
        });

        return await generateInstitutionBasedSchedule(institutionOptions, startTime);
      }

      console.log('🎲 [useAISchedule] Usando modo aleatório (padrão)');
      console.log('🎲 [useAISchedule] Motivo: generationMode =', options.generationMode, 'ou usuário sem preferências válidas');

      // Caso contrário, usar o sistema aleatório padrão (código existente)

      // Handle existing week or create new weeks
      if (options.scheduleOption === "existing" && options.targetWeek) {
        const { data: existingSchedules, error } = await supabase
          .from('study_schedules')
          .select('*')
          .eq('user_id', user.id)
          .eq('week_number', options.targetWeek);

        if (error) {
          throw new Error(`Error checking week ${options.targetWeek}: ${error.message}`);
        }

        if (!existingSchedules || existingSchedules.length === 0) {
          throw new Error(`Week ${options.targetWeek} not found. Please create this week first or select a different week.`);
        }
      }
      else if (options.scheduleOption === "new") {
        const weeksCreated = await addWeeks(options.weeksCount);

        if (!weeksCreated) {
          throw new Error('Failed to create new weeks');
        }

        // Semanas criadas com sucesso
      }

      // Use the domain from options if provided, otherwise use the user's domain from the hook
      const domain = options.domain || userDomain || 'residencia';

      // Fetch all categories (specialties, themes, focuses)
      const { data: domainSpecialties, error: specialtiesError } = await supabase
        .from('study_categories')
        .select(`
          id,
          name,
          type,
          parent_id
        `)
        .eq('type', 'specialty');

      if (specialtiesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar especialidades:', specialtiesError);
        throw specialtiesError;
      }



      if (!domainSpecialties || domainSpecialties.length === 0) {
        throw new Error('No specialties found');
      }




      const { data: allThemes, error: themesError } = await supabase
        .from('study_categories')
        .select(`
          id,
          name,
          type,
          parent_id
        `)
        .eq('type', 'theme')
        .in('parent_id', domainSpecialties.map(t => t.id));

      if (themesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar temas:', themesError);
        throw themesError;
      }



      if (!allThemes || allThemes.length === 0) {
        throw new Error('No themes found for the specialties');
      }




      const { data: allFocuses, error: focusesError } = await supabase
        .from('study_categories')
        .select(`
          id,
          name,
          type,
          parent_id
        `)
        .eq('type', 'focus')
        .in('parent_id', allThemes.map(t => t.id));

      if (focusesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar focos:', focusesError);
        throw focusesError;
      }

      const isOftalmologiaDomain = domain === 'oftalmologia';

      if (!isOftalmologiaDomain && (!allFocuses || allFocuses.length === 0)) {
        throw new Error('No focuses found for the themes');
      }



      // Filter categories by domain

      const { data: questionsInDomain, error: questionsError } = await supabase
        .from('questions')
        .select(`
          id,
          specialty_id,
          theme_id,
          focus_id,
          knowledge_domain
        `)
        .eq('knowledge_domain', domain);

      if (questionsError) {
        console.error('🤖 [useAISchedule] Erro ao buscar questões:', questionsError);
        throw questionsError;
      }

      // Create sets of valid category IDs based on questions
      const validSpecialtyIds = new Set(questionsInDomain?.map(q => q.specialty_id) || []);
      const validThemeIds = new Set(questionsInDomain?.map(q => q.theme_id) || []);
      const validFocusIds = new Set(questionsInDomain?.map(q => q.focus_id) || []);

      // Filter categories based on which ones have questions
      const filteredSpecialties = domainSpecialties.filter(s => validSpecialtyIds.has(s.id));
      const filteredThemes = allThemes.filter(t => validThemeIds.has(t.id));
      const filteredFocuses = (allFocuses || []).filter(f => validFocusIds.has(f.id));



      if (filteredSpecialties.length === 0 || filteredThemes.length === 0) {
        throw new Error(`Not enough categories found for domain ${domain}`);
      }

      if (!isOftalmologiaDomain && filteredFocuses.length === 0) {
        throw new Error(`Not enough categories found for domain ${domain}`);
      }

      // Prepare day schedules
      // Declarar topicDurationMinutes antes de usar
      const topicDurationMinutes = parseInt(options.topicDuration);

      const getConvertedDayName = (dayName: string) => {
        const conversion = {
          'Domingo': 'domingo',
          'Segunda-feira': 'segunda-feira',
          'Terça-feira': 'terça-feira',
          'Quarta-feira': 'quarta-feira',
          'Quinta-feira': 'quinta-feira',
          'Sexta-feira': 'sexta-feira',
          'Sábado': 'sábado'
        };
        return conversion[dayName as keyof typeof conversion] || dayName.toLowerCase();
      };

      const daySchedules: {[key: string]: {startTime: string, endTime: string}[]} = {};

      Object.entries(options.availableDays).forEach(([day, config]) => {
        if (config.enabled && config.periods.length > 0) {
          const validPeriods = config.periods.filter(p => {
            if (!p.startTime || !p.endTime) return false;

            // Validar formato de horário
            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
            if (!timeRegex.test(p.startTime) || !timeRegex.test(p.endTime)) return false;

            // Converter para minutos
            const startMinutes = p.startTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
            const endMinutes = p.endTime.split(':').map(Number).reduce((h, m) => h * 60 + m);

            // Validar que início é menor que fim
            if (startMinutes >= endMinutes) return false;

            // Validar que tem tempo suficiente para pelo menos 1 tópico
            const periodMinutes = endMinutes - startMinutes;
            return periodMinutes >= topicDurationMinutes;
          });

          if (validPeriods.length > 0) {
            daySchedules[getConvertedDayName(day)] = validPeriods;
          }
        }
      });

      // Get target schedules (weeks) - refetch to make sure we have the latest data
      const { data: schedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('*')
        .eq('user_id', user.id)
        .order('week_number', { ascending: true });

      if (schedulesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar cronogramas:', schedulesError);
        throw schedulesError;
      }

      if (!schedules || schedules.length === 0) {
        throw new Error('No weeks available for schedule generation. Please try adding weeks again.');
      }

      let targetSchedules = schedules;

      if (options.scheduleOption === "existing" && options.targetWeek) {
        targetSchedules = schedules.filter(s => s.week_number === options.targetWeek);
      } else if (options.scheduleOption === "new") {
        // Get the newly added weeks (the last n weeks, where n is options.weeksCount)
        targetSchedules = schedules.slice(-options.weeksCount);
      }

      if (!targetSchedules.length) {
        throw new Error('No weeks available for schedule generation. Please try adding weeks again.');
      }



      // Generate topics for each day
      const daysOfWeek = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];

      let topicsCreated = 0;
      // Track which specialties, themes, and focuses are actually used
      const specialtiesUsed = new Set<string>();
      const themesUsed = new Set<string>();
      const focusesUsed = new Set<string>();

      const getRandomItems = <T>(items: T[], count: number): T[] => {
        const result: T[] = [];
        const copyItems = [...items];

        for (let i = 0; i < count; i++) {
          if (copyItems.length === 0) break;

          const randomIndex = Math.floor(Math.random() * copyItems.length);
          result.push(copyItems[randomIndex]);
          copyItems.splice(randomIndex, 1);

          if (copyItems.length === 0 && i < count - 1) {
            copyItems.push(...items);
          }
        }

        return result;
      };

      // Generate topics for each schedule (week)

      for (const schedule of targetSchedules) {
        for (const [dayName, periods] of Object.entries(daySchedules)) {
          const dayIndex = daysOfWeek.indexOf(dayName);
          if (dayIndex === -1) continue;

          const weekStartDate = new Date(schedule.week_start_date);
          const dayDate = new Date(weekStartDate);
          dayDate.setDate(weekStartDate.getDate() + dayIndex);

          for (const period of periods) {
            if (!period.startTime || !period.endTime) {
              continue;
            }

            const startTimeParts = period.startTime.split(':').map(Number);
            const endTimeParts = period.endTime.split(':').map(Number);

            const startDate = new Date(dayDate);
            startDate.setHours(startTimeParts[0], startTimeParts[1], 0);

            const endDate = new Date(dayDate);
            endDate.setHours(endTimeParts[0], endTimeParts[1], 0);

            const periodMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60);
            const topicsCount = Math.floor(periodMinutes / topicDurationMinutes);

            if (topicsCount <= 0) {
              continue;
            }

            let randomItems;
            if (isOftalmologiaDomain) {
              randomItems = getRandomItems(filteredThemes, topicsCount);
            } else {
              randomItems = getRandomItems(filteredFocuses, topicsCount);
            }

            let currentTime = new Date(startDate);
            for (let i = 0; i < randomItems.length; i++) {
              const item = randomItems[i];
              let parentSpecialty, parentTheme, topic;

              if (isOftalmologiaDomain) {
                parentTheme = item;
                parentSpecialty = filteredSpecialties.find(s => s.id === parentTheme.parent_id);

                if (!parentSpecialty) continue;

                topic = {
                  specialty_name: parentSpecialty.name,
                  specialty_id: parentSpecialty.id,
                  theme_name: parentTheme.name,
                  theme_id: parentTheme.id,
                  focus_name: "Geral",
                  focus_id: null
                };

                specialtiesUsed.add(parentSpecialty.name);
                themesUsed.add(parentTheme.name);
              } else {
                const focus = item;
                parentTheme = filteredThemes.find(t => t.id === focus.parent_id);
                if (!parentTheme) continue;

                parentSpecialty = filteredSpecialties.find(s => s.id === parentTheme.parent_id);
                if (!parentSpecialty) continue;

                topic = {
                  specialty_name: parentSpecialty.name,
                  specialty_id: parentSpecialty.id,
                  theme_name: parentTheme.name,
                  theme_id: parentTheme.id,
                  focus_name: focus.name,
                  focus_id: focus.id
                };

                specialtiesUsed.add(parentSpecialty.name);
                themesUsed.add(parentTheme.name);
                focusesUsed.add(focus.name);
              }

              const topicStartTime = currentTime.toTimeString().substring(0, 5);

              currentTime = new Date(currentTime.getTime() + topicDurationMinutes * 60 * 1000);

              const difficulty = ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)] as 'Fácil' | 'Médio' | 'Difícil';

              const duration = `${topicDurationMinutes} minutos`;

              const activitiesOpcoes = [
                "Estudo Teórico",
                "Resolver Questões",
                "Estudo Teórico + Resolver Questões",
                "Revisão de Conteúdo",
                "Resumo do Tema"
              ];

              const activity = activitiesOpcoes[Math.floor(Math.random() * activitiesOpcoes.length)];

              const newItem = {
                schedule_id: schedule.id,
                day_of_week: dayName,
                topic: `${topic.specialty_name} - ${topic.theme_name}`,
                specialty_name: topic.specialty_name,
                specialty_id: topic.specialty_id,
                theme_name: topic.theme_name,
                theme_id: topic.theme_id,
                focus_name: topic.focus_name,
                focus_id: topic.focus_id,
                difficulty,
                activity_description: activity,
                start_time: topicStartTime,
                duration,
                type: 'study',
                activity_type: 'study',
                week_number: schedule.week_number,
                study_status: 'pending'
              };

              try {
                const { error } = await supabase
                  .from('study_schedule_items')
                  .insert(newItem);

                if (error) {
                  continue;
                }

                topicsCreated++;
              } catch (insertError) {
                // Silently continue on insert errors
              }
            }
          }
        }
      }

      const endTime = performance.now();
      const timeSpent = (endTime - startTime) / 1000;

      setGenerationStats({
        totalTopics: topicsCreated,
        totalWeeks: targetSchedules.length,
        specialties: Array.from(specialtiesUsed),
        themes: Array.from(themesUsed),
        focuses: Array.from(focusesUsed),
        totalHours: 0, // Será calculado depois se necessário
        generationTime: timeSpent,
        domain: domain,
        totalSpecialties: filteredSpecialties.length,
        totalThemes: filteredThemes.length,
        totalFocuses: filteredFocuses.length
      });



      return true; // Return true to indicate success
    } catch (error: any) {


      toast({
        title: "Erro ao gerar cronograma",
        description: error.message,
        variant: "destructive"
      });

      return false; // Return false to indicate failure
    } finally {
      setIsLoading(false);
    }
  };

  const generateInstitutionBasedSchedule = async (options: AIScheduleOptions, startTime: number) => {
    try {
      // Converter AIScheduleOptions para InstitutionScheduleOptions
      const institutionOptions = {
        ...options,
        institutionIds: options.institutionIds || [],
        startYear: options.startYear,
        endYear: options.endYear,
        generationMode: options.generationMode || 'random' as const,
        domain: options.domain || userDomain || 'residencia' // ✅ Adicionar domain padrão
      };

      console.log('🔄 [generateInstitutionBasedSchedule] Opções convertidas:', {
        ...institutionOptions,
        institutionIds: institutionOptions.institutionIds.length + ' instituições'
      });

      // Usar o hook de geração baseada em instituições
      const schedule = await generateScheduleByInstitution(institutionOptions, (progress) => {
        // Callback de progresso pode ser usado aqui se necessário
      });

      const endTime = performance.now();
      const timeSpent = (endTime - startTime) / 1000;

      // Calcular estatísticas básicas
      const specialtiesUsed = new Set(schedule.map(topic => topic.specialty));
      const themesUsed = new Set(schedule.map(topic => topic.theme));
      const focusesUsed = new Set(schedule.map(topic => topic.focus));

      // ✅ NOVO: Extrair informações de repetição do primeiro tópico (todos têm as mesmas)
      const firstTopic = schedule[0] as any;

      setGenerationStats({
        totalTopics: schedule.length,
        totalWeeks: options.weeksCount,
        specialties: Array.from(specialtiesUsed),
        themes: Array.from(themesUsed),
        focuses: Array.from(focusesUsed),
        totalHours: 0,
        generationTime: timeSpent,
        domain: options.domain || userDomain || 'residencia',
        totalSpecialties: specialtiesUsed.size,
        totalThemes: themesUsed.size,
        totalFocuses: focusesUsed.size,
        // ✅ NOVO: Informações de repetição
        focusRepetitionWarning: firstTopic?.focusRepetitionWarning || false,
        availableFocusesCount: firstTopic?.availableFocusesCount || 0,
        totalSlotsCount: firstTopic?.totalSlotsCount || 0
      });

      return true;
    } catch (error: any) {
      toast({
        title: "Erro ao gerar cronograma baseado em instituições",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    isLoading,
    generateAISchedule
  };
};
