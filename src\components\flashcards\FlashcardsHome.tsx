
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { Users, BookOpen, Share2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { DailyReviewButton } from "./DailyReviewButton";
import { FSRSInfoDialog } from "./FSRSInfoDialog";

export const FlashcardsHome = () => {
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in">
        {/* Header Section */}
        <div className="relative">
          <div className="inline-block transform -rotate-2 mb-4">
            <div className="bg-hackathon-yellow border-2 border-black px-4 py-1 text-black font-bold tracking-wide text-sm shadow-card-sm">
              FLASHCARDS
            </div>
          </div>
          
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-5xl font-black leading-none mb-2">
                <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
                  Aprenda com Flashcards
                </span>
              </h1>
              <p className="text-xl text-gray-700 max-w-lg">
                Escolha uma das opções abaixo para começar a estudar
              </p>
            </div>
            <FSRSInfoDialog />
          </div>
        </div>

        {/* Daily Review Button — AGORA NO TOPO */}
        <DailyReviewButton />

        {/* Cards Grid */}
        <div className="grid md:grid-cols-3 gap-6">
          {/* Study Card */}
          <Card 
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group"
            onClick={() => navigate("/flashcards/study")}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-hackathon-green/10 rounded-lg group-hover:bg-hackathon-green/20 transition-colors">
                  <BookOpen className="h-8 w-8 text-hackathon-green" />
                </div>
                <h2 className="text-2xl font-bold">Estudar</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              Revise seus flashcards personalizados utilizando um sistema de repetição espaçada
            </p>
            <Button 
              className="w-full bg-hackathon-green hover:bg-hackathon-green/90 text-black border-2 border-black font-bold shadow-button group-hover:shadow-button-hover"
            >
              Acessar
            </Button>
          </Card>

          {/* Collaborate Card */}
          <Card 
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group"
            onClick={() => navigate("/collaborate/flashcards")}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-hackathon-red/10 rounded-lg group-hover:bg-hackathon-red/20 transition-colors">
                  <Users className="h-8 w-8 text-hackathon-red" />
                </div>
                <h2 className="text-2xl font-bold">Gerenciar Flashcards</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              Crie, edite e compartilhe flashcards com a galera
            </p>
            <Button 
              variant="default"
              className="w-full bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black font-bold shadow-button group-hover:shadow-button-hover"
            >
              Acessar
            </Button>
          </Card>

          {/* Shared Cards */}
          <Card 
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group"
            onClick={() => navigate("/collaborative/flashcards")}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-hackathon-yellow/10 rounded-lg group-hover:bg-hackathon-yellow/20 transition-colors">
                  <Share2 className="h-8 w-8 text-hackathon-yellow" />
                </div>
                <h2 className="text-2xl font-bold">Cards Compartilhados</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              Explore e estude flashcards criados por outros estudantes
            </p>
            <Button 
              variant="default"
              className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 text-black border-2 border-black font-bold shadow-button group-hover:shadow-button-hover"
            >
              Acessar
            </Button>
          </Card>
        </div>
      </div>
    </div>
  );
};
