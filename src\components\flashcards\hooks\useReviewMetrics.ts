import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { FSRSCalculator } from "@/utils/fsrs/fsrsCalculator";
import type { ResponseMetrics } from "@/utils/fsrs/types";
import type { Flashcard } from "@/types/flashcard";

export const useReviewMetrics = (currentCard: Flashcard) => {
  const [currentReview, setCurrentReview] = useState<any>(null);
  const [preCalculatedMetrics, setPreCalculatedMetrics] = useState<ResponseMetrics | null>(null);

  useEffect(() => {
    loadCurrentReview();
  }, [currentCard]);

  const loadCurrentReview = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error("Erro ao obter usuário:", userError);
        return;
      }

      const { data: review, error: reviewError } = await supabase
        .from('flashcards_reviews')
        .select('*')
        .eq('card_id', currentCard.id)
        .eq('user_id', user.id)
        .maybeSingle();

      if (reviewError) {
        console.error("Erro ao obter revisão do flashcard:", reviewError);
        return;
      }

      //console.log('📊 [ReviewButtons] Review carregada do banco:', review);
      setCurrentReview(review || null);
      calculateAllMetrics(review);
    } catch (e) {
      console.error("Erro inesperado ao carregar revisão:", e);
    }
  };

  const calculateAllMetrics = (review: any) => {
    const currentMetrics = {
      stability: review?.stability || 1.0,
      difficulty: review?.difficulty || 5.0,
      retrievability: review?.retrievability || 0.9,
      intervalInDays: review?.intervalindays || 1,
      nextReviewDate: review?.next_review_date || new Date(),
      lastReviewDate: review?.last_review_date
    };

    //console.log('🔄 [ReviewButtons] Métricas atuais para cálculo:', currentMetrics);

    const metrics: ResponseMetrics = {
      error: calculateMetricsForResponse('error', currentMetrics),
      hard: calculateMetricsForResponse('hard', currentMetrics),
      medium: calculateMetricsForResponse('medium', currentMetrics),
      easy: calculateMetricsForResponse('easy', currentMetrics)
    };

    //console.log('✨ [ReviewButtons] Métricas pré-calculadas:', metrics);
    setPreCalculatedMetrics(metrics);
  };

  const calculateMetricsForResponse = (
    response: 'error' | 'hard' | 'medium' | 'easy',
    currentMetrics: any
  ) => {
    const fsrsCalculator = new FSRSCalculator(currentMetrics);
    const updatedMetrics = fsrsCalculator.calculateNewMetrics(response);
    const nextReviewDate = new Date(new Date().getTime() + (updatedMetrics.intervalInDays * 24 * 60 * 60 * 1000));
    updatedMetrics.nextReviewDate = nextReviewDate;
    return updatedMetrics;
  };

  return {
    currentReview,
    preCalculatedMetrics
  };
};