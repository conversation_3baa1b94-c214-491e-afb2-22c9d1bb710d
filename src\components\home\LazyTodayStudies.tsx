import React, { useState, useEffect, useRef } from 'react';
import TodayStudies from './TodayStudies';

/**
 * Wrapper lazy para TodayStudies que só carrega dados quando visível
 */
const LazyTodayStudies: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [canLoad, setCanLoad] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Delay inicial para evitar carregamento imediato
  useEffect(() => {
    const timer = setTimeout(() => {
      setCanLoad(true);
    }, 3000); // 3 segundos de delay

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!canLoad) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          // Desconectar observer após carregar
          observer.disconnect();
        }
      },
      {
        rootMargin: '-50px', // Só carrega quando realmente visível
        threshold: 0.3
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasLoaded, canLoad]);

  return (
    <div ref={ref}>
      {isVisible ? (
        <TodayStudies enabled={true} />
      ) : (
        <div className="bg-white rounded-xl p-4 sm:p-5 shadow-sm border border-gray-100">
          {/* Header skeleton */}
          <div className="flex items-center justify-between mb-4">
            <div className="h-5 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-32"></div>
            <div className="h-6 w-6 bg-gradient-to-r from-blue-300 to-blue-200 rounded-full animate-pulse"></div>
          </div>

          {/* Schedule items skeleton */}
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3 p-3 rounded-lg border border-gray-100">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-300 rounded-full animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-3/4 mb-2"></div>
                  <div className="h-3 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LazyTodayStudies;
