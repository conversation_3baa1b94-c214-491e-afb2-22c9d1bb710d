import { Tables } from './tables';

export type Database = {
  public: {
    Tables: {
      pedbook_profiles: {
        Row: Tables['pedbook_profiles'];
        Insert: Partial<Tables['pedbook_profiles']>;
        Update: Partial<Tables['pedbook_profiles']>;
      };
      pedbook_medication_categories: {
        Row: Tables['pedbook_medication_categories'];
        Insert: Partial<Tables['pedbook_medication_categories']>;
        Update: Partial<Tables['pedbook_medication_categories']>;
      };
      pedbook_medications: {
        Row: Tables['pedbook_medications'];
        Insert: Partial<Tables['pedbook_medications']>;
        Update: Partial<Tables['pedbook_medications']>;
      };
      pedbook_medication_dosages: {
        Row: Tables['pedbook_medication_dosages'];
        Insert: Partial<Tables['pedbook_medication_dosages']>;
        Update: Partial<Tables['pedbook_medication_dosages']>;
      };
    };
    Views: Record<string, never>;
    Functions: Record<string, never>;
    Enums: Record<string, never>;
    CompositeTypes: Record<string, never>;
  };
};