import { supabase } from "@/integrations/supabase/client";
import { FSRSMetrics } from "@/utils/fsrs/types";
import { toast } from "sonner";

export const useFlashcardDatabase = () => {
  const updateFlashcardReview = async (
    userId: string,
    cardId: string,
    metrics: FSRSMetrics,
    nextReviewDate: Date,
    isCorrect: boolean
  ) => {

    try {
      // Primeiro buscar a revisão atual para ter os contadores corretos
      const { data: currentReview } = await supabase
        .from('flashcards_reviews')
        .select('total_reviews, correct_reviews')
        .eq('user_id', userId)
        .eq('card_id', cardId)
        .single();

      const reviewData = {
        user_id: userId,
        card_id: cardId,
        stability: metrics.stability,
        difficulty: metrics.difficulty,
        retrievability: metrics.retrievability,
        next_review_date: nextReviewDate.toISOString().split('T')[0],
        last_review_date: new Date().toISOString(),
        intervalindays: Math.ceil(metrics.intervalInDays),
        total_reviews: currentReview ? currentReview.total_reviews + 1 : 1,
        correct_reviews: currentReview 
          ? (isCorrect ? currentReview.correct_reviews + 1 : currentReview.correct_reviews)
          : (isCorrect ? 1 : 0)
      };

      console.log('📝 [useFlashcardDatabase] Review data to save:', reviewData);

      const { error } = await supabase
        .from('flashcards_reviews')
        .upsert({
          user_id: reviewData.user_id,
          card_id: reviewData.card_id,
          stability: reviewData.stability,
          difficulty: reviewData.difficulty,
          retrievability: reviewData.retrievability,
          next_review_date: reviewData.next_review_date,
          last_review_date: reviewData.last_review_date,
          intervalindays: reviewData.intervalindays,
          total_reviews: reviewData.total_reviews,
          correct_reviews: reviewData.correct_reviews
        }, {
          onConflict: 'user_id,card_id'
        });

      if (error) throw error;

      console.log('✅ [useFlashcardDatabase] Review saved successfully:', {
        stability: metrics.stability,
        difficulty: metrics.difficulty,
        next_review: nextReviewDate,
        interval: `${Math.ceil(metrics.intervalInDays)} days`,
        total_reviews: reviewData.total_reviews,
        correct_reviews: reviewData.correct_reviews
      });

      return true;
    } catch (error) {
      console.error('❌ [useFlashcardDatabase] Error saving review:', error);
      toast.error("Error updating review");
      return false;
    }
  };

  return { updateFlashcardReview };
};