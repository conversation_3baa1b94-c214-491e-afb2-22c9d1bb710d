
import { useState } from "react";
import { motion } from "framer-motion";
import { ChevronRight, Sparkles, LightbulbIcon, Lightbulb, BookOpen, Calendar, BrainCircuit, CheckCircle, TrendingUp, Zap, HelpCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAutoRedirect } from "@/hooks/useAuthRedirect";
import { AuthLoadingScreen } from "@/components/common/AuthLoadingScreen";
import AuthDialog from "@/components/auth/AuthDialog";
import FloatingActionButton from "@/components/mobile/FloatingActionButton";
import { useWelcomeFlow } from "@/hooks/useWelcomeFlow";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const Index = () => {
  const navigate = useNavigate();
  const { isChecking, isAuthenticated } = useAutoRedirect();
  const { showWelcome, isLoading: welcomeLoading, grantPremiumAccess, userName } = useWelcomeFlow();
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [showSignupDialog, setShowSignupDialog] = useState(false);

  const handleStartClick = () => {
    if (isAuthenticated) {
      navigate("/plataformadeestudos");
    } else {
      setShowAuthDialog(true);
    }
  };

  // Mostrar loading enquanto verifica autenticação e perfil
  if (isChecking || welcomeLoading) {
    return <AuthLoadingScreen message="Verificando autenticação..." />;
  }

  // Mostrar Welcome Screen se necessário
  if (showWelcome && isAuthenticated) {
    return (
      <WelcomeScreen
        onGetPremiumAccess={async () => {
          const success = await grantPremiumAccess();
          if (success) {
            navigate("/onboarding");
          }
        }}
        userName={userName}
      />
    );
  }

  const advantages = [
    {
      title: "Acesse o banco de questões de residência gratuitamente",
      description: "Centenas de questões de residência médica com comentários detalhados para maximizar seu aprendizado.",
      icon: BookOpen
    },
    {
      title: "Monte um cronograma personalizado",
      description: "Crie um plano de estudos adaptado às suas necessidades e especialidade-alvo para otimizar seu tempo.",
      icon: Calendar
    },
    {
      title: "Potencialize seus estudos com nossa IA",
      description: "Use nossa inteligência artificial para gerar resumos, explicações detalhadas e recomendações personalizadas.",
      icon: BrainCircuit
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-[#FEF7CD]">
      {/* Mobile-First Header */}
      <header className="sticky top-0 z-50 bg-[#FEF7CD]/95 backdrop-blur-sm border-b border-black/10">
        <div className="container max-w-7xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <div className="flex items-center">
              <div className="relative">
                <div className="bg-white border-2 border-black px-3 py-1 md:px-4 md:py-1 shadow-card-sm">
                  <span className="font-bold text-lg md:text-2xl tracking-tight">Med EVO</span>
                </div>
                <div className="absolute -right-2 -top-2 md:-right-3 md:-top-3">
                  <span className="bg-hackathon-red text-white text-xs px-1.5 py-0.5 md:px-2 rounded-full border border-black font-bold shadow-sm">
                    beta
                  </span>
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-4">
              <div className="bg-hackathon-green border-2 border-black px-4 py-1.5 rounded-md font-medium shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
                Início
              </div>
              <button
                onClick={() => navigate("/como-funciona")}
                className="bg-white border-2 border-black px-4 py-1.5 rounded-md font-medium shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                Como funciona?
              </button>
            </nav>

            {/* Desktop CTA Button */}
            <div className="hidden md:block">
              {isAuthenticated ? (
                <Button
                  onClick={() => navigate("/plataformadeestudos")}
                  className="bg-black text-white hover:bg-black/90 border-2 border-black rounded-md px-6 font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  Acessar Plataforma
                </Button>
              ) : (
                <Button
                  onClick={() => setShowAuthDialog(true)}
                  className="bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black px-6 rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  Entrar
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Mobile-First Simplified Main Content */}
      <main className="md:hidden container max-w-md mx-auto px-4 flex-grow">
        <div className="py-8 text-center">
          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <div className="inline-block transform -rotate-1 mb-4">
              <div className="bg-white border-2 border-black px-4 py-2 text-hackathon-red font-bold tracking-wide text-sm shadow-card-sm">
                PLATAFORMA DE ESTUDOS
              </div>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              <span className="text-hackathon-red">Residência Médica</span>
            </h1>
            <p className="text-gray-600 text-sm leading-relaxed">
              Plataforma de questões para conquistar sua vaga na residência
            </p>
          </motion.div>

          {/* Quick Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8"
          >
            <div className="flex justify-center items-center gap-4 text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3 text-hackathon-red" />
                <span>100 mil+ questões</span>
              </div>
              <div className="flex items-center gap-1">
                <Sparkles className="h-3 w-3 text-hackathon-red" />
                <span>Gratuito</span>
              </div>
            </div>
          </motion.div>

          {/* Auth Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-3"
          >
            {isAuthenticated ? (
              <Button
                onClick={() => navigate("/plataformadeestudos")}
                className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black py-4 rounded-xl font-bold text-lg shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                Acessar Plataforma
              </Button>
            ) : (
              <>
                <Button
                  onClick={() => setShowAuthDialog(true)}
                  className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black py-4 rounded-xl font-bold text-lg shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  Entrar
                </Button>
                <Button
                  onClick={() => setShowSignupDialog(true)}
                  variant="outline"
                  className="w-full border-2 border-black bg-white hover:bg-gray-50 text-black py-2 rounded-xl font-medium text-sm shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  Criar Conta
                </Button>
              </>
            )}
          </motion.div>

          {/* Trust Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-6"
          >
            <p className="text-xs text-gray-500 mb-4">
              Esta plataforma está associada ao PedBook, use os mesmos dados para acessar.
            </p>

            {/* Como funciona button - Mobile only */}
            <div className="flex justify-center">
              <Button
                onClick={() => navigate("/como-funciona")}
                variant="outline"
                className="inline-flex items-center gap-2 px-6 py-2.5 rounded-lg bg-white border-2 border-black text-gray-800 text-sm font-medium shadow-card-sm hover:bg-gray-50 hover:translate-y-0.5 hover:shadow-sm transition-all duration-200"
              >
                <HelpCircle className="h-4 w-4" />
                <span>Como funciona?</span>
              </Button>
            </div>
          </motion.div>
        </div>

      </main>

      {/* Desktop version - hidden on mobile */}
      <main className="hidden md:block container max-w-7xl mx-auto px-4 flex-grow">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center py-8 md:py-12 lg:py-16">
          {/* Hero Content */}
          <div className="relative z-10 text-center lg:text-left">
            {/* Mobile Badge */}
            <div className="inline-block transform -rotate-1 mb-4">
              <div className="bg-white border-2 border-black px-3 py-1 md:px-4 text-hackathon-red font-bold tracking-wide text-xs md:text-sm shadow-card-sm">
                PLATAFORMA DE ESTUDOS PARA
              </div>
            </div>

            {/* Mobile-Optimized Title */}
            <h1 className="font-black leading-tight mb-6">
              {/* Desktop: Keep original */}
              <div className="text-5xl lg:text-6xl xl:text-7xl">
                <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1 mb-3">
                  Residência
                </span>
                <span className="block text-hackathon-red">
                  Médica
                </span>
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-4xl font-bold">&</span>
                  <span className="inline-block bg-hackathon-yellow border-2 border-black px-3 py-1 text-black text-3xl lg:text-4xl font-bold transform rotate-1 shadow-card-sm">
                    Título de Especialista
                  </span>
                </div>
              </div>
            </h1>

            {/* Mobile-optimized description */}
            <p className="text-gray-700 mb-8 max-w-lg mx-auto lg:mx-0">
              {/* Desktop: Keep original */}
              <span className="text-xl">
                Criado para{' '}
                <span className="underline decoration-hackathon-green decoration-4 font-medium">estudantes de medicina</span>{' '}
                e{' '}
                <span className="underline decoration-hackathon-red decoration-4 font-medium">médicos</span>
              </span>
            </p>

            {/* Mobile-First CTA Buttons */}
            <div className="mb-8 md:mb-12">
              {/* Desktop: Keep original layout */}
              <div className="flex flex-row gap-4">
                <Button
                  onClick={handleStartClick}
                  className="bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black px-8 py-6 rounded-md font-bold text-lg shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  Acessar Gratuitamente!
                </Button>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      className="bg-hackathon-red hover:bg-hackathon-red/90 border-2 border-black text-white px-8 py-6 rounded-md font-bold text-lg shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      Conheça as Vantagens
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[80dvw] sm:max-w-[600px] border-2 border-black bg-[#FEF7CD]">
                    <DialogHeader>
                      <DialogTitle className="text-2xl font-bold">Vantagens da plataforma Med EVO</DialogTitle>
                      <DialogDescription className="text-gray-700">
                        Conheça os recursos exclusivos para impulsionar sua preparação para residência médica
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-6 py-4">
                      {advantages.map((advantage, index) => (
                        <div key={index} className="flex gap-4">
                          <div className="icon-container bg-hackathon-yellow">
                            <advantage.icon className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="text-lg font-bold">{advantage.title}</h3>
                            <p className="text-gray-700">{advantage.description}</p>
                          </div>
                        </div>
                      ))}

                      <Button
                        onClick={handleStartClick}
                        className="w-full mt-6 bg-hackathon-green hover:bg-hackathon-green/90 border-2 border-black text-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        Comece agora
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Mobile-optimized stats */}
            <div className="text-center lg:text-left">
              {/* Desktop: Keep original */}
              <div className="flex items-center justify-start gap-3">
                <div className="flex -space-x-1">
                  <div className="w-10 h-10 rounded-full border-2 border-black bg-blue-100 flex items-center justify-center text-blue-600 font-bold">M</div>
                  <div className="w-10 h-10 rounded-full border-2 border-black bg-green-100 flex items-center justify-center text-green-600 font-bold">D</div>
                  <div className="w-10 h-10 rounded-full border-2 border-black bg-purple-100 flex items-center justify-center text-purple-600 font-bold">R</div>
                </div>
                <span className="text-gray-700 font-medium">
                  Mais de <span className="font-bold">1.000 estudantes</span>
                </span>
              </div>
            </div>
          </div>

          {/* Image section - Desktop only */}
          <div className="relative order-first lg:order-last">
            <div className="relative z-10 transform rotate-1">
              <img
                src="/indexmedevo.webp"
                alt="Médicos estudando"
                className="w-full h-auto object-cover rounded-lg shadow-card"
              />

              {/* Code snippet - desktop only */}
              <div className="absolute right-4 bottom-0 translate-y-6 z-20">
                <div className="bg-black text-white text-xs px-3 py-1.5 rounded-md font-mono shadow-md">
                  function prepararResidencia() &#123; ... &#125;
                </div>
              </div>
            </div>

            {/* Lightbulb icon - desktop only */}
            <div className="absolute top-16 -right-4 z-20 animate-bounce">
              <div className="p-1.5 bg-hackathon-yellow rounded-full border-2 border-black">
                <LightbulbIcon className="h-6 w-6 text-black" />
              </div>
            </div>
          </div>
        </div>

        {/* Desktop feature tags */}
        <div className="mb-8 md:mb-12 lg:mb-14">
          <div className="flex flex-wrap justify-center gap-3">
            <div className="flex items-center gap-2 bg-white border-2 border-black px-4 py-2 rounded-md shadow-card-sm">
              <span className="text-sm font-bold">Banco de questões</span>
            </div>
            <div className="flex items-center gap-2 bg-white border-2 border-black px-4 py-2 rounded-md shadow-card-sm">
              <span className="text-sm font-bold">Preparação personalizada</span>
            </div>
            <div className="flex items-center gap-2 bg-white border-2 border-black px-4 py-2 rounded-md shadow-card-sm">
              <span className="text-sm font-bold">Comentários de especialistas</span>
            </div>
            <div className="flex items-center gap-2 bg-white border-2 border-black px-4 py-2 rounded-md shadow-card-sm">
              <span className="text-sm font-bold">Performance em tempo real</span>
            </div>
          </div>
        </div>
      </main>

      {/* Compact horizontal footer */}
      <footer className="bg-hackathon-yellow py-3 border-t-2 border-black mt-auto">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="flex justify-center items-center gap-4">
            <a
              href="https://pedb.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/faviconx.webp"
                alt="PedBook"
                className="h-6"
              />
            </a>
            <a
              href="https://medunity.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/logo-med-unity-sem-fund.webp"
                alt="Med Unity"
                className="h-6"
              />
            </a>
            <span className="text-xs text-gray-600 ml-2">
              © 2025 Med EVO
            </span>
          </div>
        </div>
      </footer>

      {/* Floating Action Button for Mobile */}
      <FloatingActionButton
        isAuthenticated={isAuthenticated}
        onLoginClick={() => setShowAuthDialog(true)}
      />

      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
        defaultMode="signin"
        onSuccess={() => setShowAuthDialog(false)}
      />

      <AuthDialog
        open={showSignupDialog}
        onOpenChange={setShowSignupDialog}
        hidden={true}
        defaultMode="signup"
        onSuccess={() => setShowSignupDialog(false)}
      />
    </div>
  );
};

export default Index;
