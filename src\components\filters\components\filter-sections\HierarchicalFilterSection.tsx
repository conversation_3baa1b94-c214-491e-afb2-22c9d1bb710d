
import { FilterItem } from "../../FilterItem";
import { useDomain } from "@/hooks/useDomain";

interface HierarchicalFilterSectionProps {
  specialties: any[];
  themes: any[];
  focuses: any[];   
  selectedFilters: any;
  expandedItems: string[];
  questionCounts: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
  onToggleExpand: (id: string) => void;
  onToggleFilter: (id: string, type: string) => void;
  searchTerm: string;
  isResidencia?: boolean;
}

export const HierarchicalFilterSection = ({
  specialties,
  themes,
  focuses,
  selectedFilters,
  expandedItems,
  questionCounts,
  onToggleExpand,
  onToggleFilter,
  searchTerm,
  isResidencia
}: HierarchicalFilterSectionProps) => {
  const { domain } = useDomain();
  const searchTermLower = searchTerm.toLowerCase();



  // Only include focuses that have a count > 0
  const validFocuses = focuses.filter(focus =>
    (questionCounts.totalCounts[focus.id] || 0) > 0
  );

  // Filter focuses by search term from the valid ones
  const matchingFocuses = validFocuses.filter(focus =>
    focus.name.toLowerCase().includes(searchTermLower)
  );

  // Only include themes that have a count > 0
  const validThemes = themes.filter(theme =>
    (questionCounts.totalCounts[theme.id] || 0) > 0
  );
  
  // Filter themes by search term or if they have matching focuses
  const filteredThemes = validThemes.filter(theme => {
    const themeMatches = theme.name.toLowerCase().includes(searchTermLower);
    const hasMatchingFocuses = matchingFocuses.some(focus => focus.theme_id === theme.id);
    return themeMatches || hasMatchingFocuses || !searchTerm;
  });

  // Only if it's residencia or revalida with specialties, we filter specialties
  const shouldShowSpecialties = isResidencia || domain === 'revalida';
  
  const validSpecialties = shouldShowSpecialties ? specialties.filter(specialty => 
    (questionCounts.totalCounts[specialty.id] || 0) > 0
  ) : [];

  // Filter specialties by search term or if they have matching themes
  const filteredSpecialties = shouldShowSpecialties ? validSpecialties.filter(specialty => {
    const specialtyMatches = specialty.name.toLowerCase().includes(searchTermLower);
    const hasMatchingThemes = filteredThemes.some(theme => 
      theme.parent_id === specialty.id || theme.specialty_id === specialty.id
    );
    return specialtyMatches || hasMatchingThemes || !searchTerm;
  }) : [];

  // Find the last selected item of each type for auto-scroll
  const lastSelectedSpecialty = selectedFilters.specialties?.[selectedFilters.specialties.length - 1];
  const lastSelectedTheme = selectedFilters.themes?.[selectedFilters.themes.length - 1];
  const lastSelectedFocus = selectedFilters.focuses?.[selectedFilters.focuses.length - 1];

  // Make sure we always have elements to render
  if (shouldShowSpecialties && (!filteredSpecialties || filteredSpecialties.length === 0)) {
    return (
      <div className="p-2 text-center text-gray-500">
        Nenhuma especialidade encontrada para os filtros selecionados
      </div>
    );
  }
  
  if (!shouldShowSpecialties && (!filteredThemes || filteredThemes.length === 0)) {
    return (
      <div className="p-2 text-center text-gray-500">
        Nenhum tema encontrado para os filtros selecionados
      </div>
    );
  }

  if (shouldShowSpecialties) {
    // Render the specialty > theme > focus hierarchy for residencia and revalida
    return (
      <>
        {filteredSpecialties.map(specialty => {
          // Find themes that belong to this specialty
          const specialtyThemes = filteredThemes.filter(theme => {
            return theme.parent_id === specialty.id || theme.specialty_id === specialty.id;
          });
          
          return (
            <div key={specialty.id} className="mb-1">
              <FilterItem
                item={{ ...specialty, type: "specialty" }}
                level={0}
                isExpanded={expandedItems.includes(specialty.id)}
                isSelected={selectedFilters.specialties?.includes(specialty.id)}
                questionCount={{
                  total: questionCounts.totalCounts[specialty.id] || 0,
                  filtered: questionCounts.filteredCounts[specialty.id] || 0
                }}
                hasChildren={specialtyThemes.length > 0}
                onToggleExpand={onToggleExpand}
                onToggleSelect={onToggleFilter}
                shouldScrollIntoView={specialty.id === lastSelectedSpecialty}
              />
              
              {expandedItems.includes(specialty.id) && (
                specialtyThemes.map(theme => {
                  // Find focuses that belong to this theme
                  const themeFocuses = validFocuses.filter(focus => {
                    const focusMatches = focus.name.toLowerCase().includes(searchTermLower);
                    return focus.theme_id === theme.id && (focusMatches || !searchTerm);
                  });

                  return (
                    <div key={theme.id} className="mb-1">
                      <FilterItem
                        item={{ ...theme, type: "theme" }}
                        level={1}
                        isExpanded={expandedItems.includes(theme.id)}
                        isSelected={selectedFilters.themes?.includes(theme.id)}
                        questionCount={{
                          total: questionCounts.totalCounts[theme.id] || 0,
                          filtered: questionCounts.filteredCounts[theme.id] || 0
                        }}
                        hasChildren={themeFocuses.length > 0}
                        onToggleExpand={onToggleExpand}
                        onToggleSelect={onToggleFilter}
                        shouldScrollIntoView={theme.id === lastSelectedTheme}
                      />
                      
                      {expandedItems.includes(theme.id) && (
                        themeFocuses.map(focus => (
                          <div key={focus.id} className="mb-1">
                            <FilterItem
                              item={{ ...focus, type: "focus" }}
                              level={2}
                              isExpanded={expandedItems.includes(focus.id)}
                              isSelected={selectedFilters.focuses?.includes(focus.id)}
                              questionCount={{
                                total: questionCounts.totalCounts[focus.id] || 0,
                                filtered: questionCounts.filteredCounts[focus.id] || 0
                              }}
                              hasChildren={false}
                              onToggleExpand={onToggleExpand}
                              onToggleSelect={onToggleFilter}
                              shouldScrollIntoView={focus.id === lastSelectedFocus}
                            />
                          </div>
                        ))
                      )}
                    </div>
                  );
                })
              )}
            </div>
          );
        })}
      </>
    );
  } else {
    // Render the theme > focus hierarchy for specialty
    return (
      <>
        {filteredThemes.map(theme => {
          const themeFocuses = validFocuses.filter(focus => {
            const focusMatches = focus.name.toLowerCase().includes(searchTermLower);
            return focus.theme_id === theme.id && (focusMatches || !searchTerm);
          });



          return (
            <div key={theme.id} className="mb-1">
              <FilterItem
                item={{ ...theme, type: "theme" }}
                level={0} // Temas estão no nível 0 (topo)
                isExpanded={expandedItems.includes(theme.id)}
                isSelected={selectedFilters.themes?.includes(theme.id)}
                questionCount={{
                  total: questionCounts.totalCounts[theme.id] || 0,
                  filtered: questionCounts.filteredCounts[theme.id] || 0
                }}
                hasChildren={themeFocuses.length > 0}
                onToggleExpand={onToggleExpand}
                onToggleSelect={onToggleFilter}
                shouldScrollIntoView={theme.id === lastSelectedTheme}
              />
              
              {expandedItems.includes(theme.id) && (
                themeFocuses.map(focus => (
                  <div key={focus.id} className="mb-1">
                    <FilterItem
                      item={{ ...focus, type: "focus" }}
                      level={1}
                      isExpanded={false}
                      isSelected={selectedFilters.focuses?.includes(focus.id)}
                      questionCount={{
                        total: questionCounts.totalCounts[focus.id] || 0,
                        filtered: questionCounts.filteredCounts[focus.id] || 0
                      }}
                      hasChildren={false}
                      onToggleExpand={onToggleExpand}
                      onToggleSelect={onToggleFilter}
                      shouldScrollIntoView={focus.id === lastSelectedFocus}
                    />
                  </div>
                ))
              )}
            </div>
          );
        })}
      </>
    );
  }
};
