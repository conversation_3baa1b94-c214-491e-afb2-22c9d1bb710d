
import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { QuestionSolver } from "@/components/question/QuestionSolver";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { Question } from "@/types/question";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import QuestionList from "@/components/QuestionList";
import { useDomain } from "@/hooks/useDomain";
import { useTransformedQuestions } from "@/hooks/useTransformedQuestions";
import { Loader2 } from "lucide-react";

const Questions = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { domain } = useDomain();
  const [questionsData, setQuestionsData] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [lastSessionId, setLastSessionId] = useState<string | null>(null);

  // Usar hook otimizado para transformação
  const questions = useTransformedQuestions(questionsData);

  useEffect(() => {
    // Evitar execuções duplicadas
    if (!sessionId) {
      return;
    }

    // 🎯 Detectar mudança de sessão ou navegação forçada
    const isSessionChange = lastSessionId && lastSessionId !== sessionId;
    const isForceRefresh = location.state?.forceRefresh;

    if (isSessionChange || isForceRefresh) {
      // Limpar dados anteriores para forçar recarregamento
      setQuestionsData(null);
      setIsLoading(true);
    }

    // Evitar execução se já carregou questões para esta sessão (e não é mudança forçada)
    if (questions.length > 0 && sessionId && !isSessionChange && !isForceRefresh) {
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);

        const {
          data: { user },
          error: userError
        } = await supabase.auth.getUser();

        if (userError) {
          throw userError;
        }

        setUserId(user?.id || null);

        if (!sessionId) {
          setIsLoading(false);
          return;
        }

        //console.log(`🔍 [Questions] Loading session ${sessionId} with domain: ${domain}`);

        const { data: session, error: sessionError } = await supabase
          .from("study_sessions")
          .select("*, knowledge_domain")
          .eq("id", sessionId)
          .maybeSingle();

        if (sessionError) {
          throw sessionError;
        }

        if (!session) {
          toast({
            title: "Sessão não encontrada",
            description: "A sessão que você está tentando acessar não existe",
            variant: "destructive",
          });
          navigate("/questions");
          return;
        }

        if (!session.questions || session.questions.length === 0) {
          throw new Error("Nenhuma questão encontrada nesta sessão");
        }

        // Use the domain from the session if available, otherwise use the domain from useDomain
        const queryDomain = session.knowledge_domain || domain;
        // Carregando questões para domain: ${queryDomain}, sessionId: ${sessionId}

        // Use RPC function to avoid URL length issues with large question lists
        let questionsData;
        let questionsError;

        if (session.questions.length > 100) {
          // Use RPC function for large lists
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_questions_by_ids', {
            question_ids: session.questions,
            domain_filter: queryDomain
          });

          if (rpcError) {
            questionsError = rpcError;
          } else {
            // Transform RPC result to match expected format
            questionsData = rpcData?.map((q: any) => ({
              ...q,
              specialty: q.specialty_name ? { id: q.specialty_id, name: q.specialty_name } : null,
              theme: q.theme_name ? { id: q.theme_id, name: q.theme_name } : null,
              focus: q.focus_name ? { id: q.focus_id, name: q.focus_name } : null,
              location: q.location_name ? { id: q.exam_location, name: q.location_name } : null
            }));
          }
        } else {
          // Use regular query for smaller lists
          const { data, error } = await supabase
            .from("questions")
            .select(`
              *,
              specialty:study_categories!questions_specialty_id_fkey(id, name),
              theme:study_categories!questions_theme_id_fkey(id, name),
              focus:study_categories!questions_focus_id_fkey(id, name),
              location:exam_locations!questions_location_id_fkey(id, name)
            `)
            .in("id", session.questions)
            .eq("knowledge_domain", queryDomain);

          questionsData = data;
          questionsError = error;
        }

        if (questionsError) {
          throw questionsError;
        }

        if (!questionsData?.length) {
          throw new Error("Nenhuma questão encontrada para esta sessão");
        }

        // Questões carregadas: ${questionsData.length} questions for domain: ${queryDomain}

        // Armazenar dados brutos - a transformação será feita pelo hook otimizado
        setQuestionsData(questionsData);

        // 🎯 Atualizar lastSessionId após carregamento bem-sucedido
        setLastSessionId(sessionId);

      } catch (error: any) {
        toast({
          title: "Erro ao carregar questões",
          description: error.message || "Ocorreu um erro ao carregar as questões",
          variant: "destructive",
        });
        navigate("/questions");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [sessionId, lastSessionId, location.state]); // Adicionadas dependências para detectar mudanças

  if (isLoading) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className="container mx-auto px-4 pt-16 md:py-8">
          <div className="flex flex-col items-center justify-center p-8 rounded-lg shadow-sm bg-white/30 backdrop-blur-sm">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-xl font-semibold mb-2">Carregando questões...</h2>
            <p className="text-muted-foreground text-center">Aguarde enquanto preparamos suas questões de estudo</p>
          </div>
        </div>
      </>
    );
  }

  if (!sessionId) {
    return <QuestionList />;
  }

  if (!questions.length) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className="container mx-auto px-4 pt-16 md:py-8">
          <h2 className="text-xl font-semibold mb-4">
            Nenhuma questão encontrada
          </h2>
          <Button onClick={() => navigate("/questions")}>
            Voltar para Filtros
          </Button>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <StudyNavBar className="mb-8" />
      <div className="container mx-auto px-4 pt-16 md:py-8" style={{paddingRight:0,paddingLeft:0}}>
        {userId && sessionId && (
          <QuestionSolver
            questions={questions}
            sessionId={sessionId}
            userId={userId}
          />
        )}
      </div>
    </>
  );
};

export default Questions;
