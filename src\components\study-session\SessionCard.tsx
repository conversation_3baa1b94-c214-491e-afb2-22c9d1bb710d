
import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, CheckCircle2, PlayCircle, Clock, Trash2, XCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useIsMobile } from "@/hooks/use-mobile";

interface SessionCardProps {
  session: {
    id: string;
    started_at: string;
    total_questions: number;
    current_question_index: number;
    status: "in_progress" | "completed" | "abandoned";
    stats: {
      correct_answers: number;
      total_questions: number;
    };
    specialty_name?: string;
    title?: string;
  };
  onDelete: (id: string) => void;
  onNavigate: (id: string) => void;
}

export const SessionCard = ({ session, onDelete, onNavigate }: SessionCardProps) => {
  const isMobile = useIsMobile();

  const dateFormatted = formatDistanceToNow(new Date(session.started_at), {
    addSuffix: true,
    locale: ptBR,
  });

  const isComplete = session.status === "completed";
  const isAbandoned = session.status === "abandoned";
  const progress = Math.round(
    (session.current_question_index / session.total_questions) * 100
  );

  const accuracyPercent = isComplete && session.stats.total_questions > 0
    ? Math.round((session.stats.correct_answers / session.stats.total_questions) * 100)
    : 0;

  const getAccuracyColor = (percent: number) => {
    if (percent >= 80) return "text-green-600";
    if (percent >= 60) return "text-amber-500";
    return "text-red-500";
  };

  const allQuestionsAnswered = session.current_question_index >= session.total_questions;

  const questionsDisplay = allQuestionsAnswered
    ? "Todas questões"
    : `${session.current_question_index}/${session.total_questions}`;

  return (
    <Card className="p-3 sm:p-4 bg-white border-2 border-black rounded-lg shadow-card-sm hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
        <div className="flex flex-col space-y-2">
          <div className="flex items-center gap-2">
            <div className={`p-1.5 rounded-full border-2 border-black ${
              isComplete ? 'bg-green-500' :
              isAbandoned ? 'bg-orange-500' :
              'bg-blue-500'
            }`}>
              {isComplete ? (
                <CheckCircle2 className="h-3 w-3 text-white" />
              ) : isAbandoned ? (
                <XCircle className="h-3 w-3 text-white" />
              ) : (
                <PlayCircle className="h-3 w-3 text-white" />
              )}
            </div>
            <h3 className="font-bold text-gray-800 text-sm sm:text-base line-clamp-1">
              {session.title || session.specialty_name || "Sessão de estudo"}
            </h3>
            {isComplete ? (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-green-100 text-green-700 border-2 border-green-300">
                <CheckCircle2 className="h-3 w-3 mr-1" />
                Concluído
              </span>
            ) : isAbandoned ? (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-orange-100 text-orange-700 border-2 border-orange-300">
                <XCircle className="h-3 w-3 mr-1" />
                Interrompido
              </span>
            ) : (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-blue-100 text-blue-700 border-2 border-blue-300">
                <PlayCircle className="h-3 w-3 mr-1" />
                Em progresso
              </span>
            )}
          </div>

          <div className="flex items-center gap-1 sm:gap-2 mt-1 text-gray-500 text-xs sm:text-sm">
            <CalendarIcon className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
            <span className="truncate">{dateFormatted}</span>
          </div>

          <div className="mt-2 sm:mt-3 mb-1 sm:mb-2 w-full h-1.5 sm:h-2 bg-gray-100 rounded-full overflow-hidden border border-gray-300">
            <div
              className="h-full rounded-full bg-hackathon-yellow"
              style={{ width: `${progress}%` }}
            />
          </div>

          <div className="grid grid-cols-2 gap-2 sm:gap-3 mt-1.5 sm:mt-2">
            <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm border border-gray-200">
              {isComplete ? (
                <>
                  <div className={`flex items-center gap-1 ${getAccuracyColor(accuracyPercent)} font-medium text-xs sm:text-sm`}>
                    <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span>{accuracyPercent}%</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-1 hidden sm:inline">de acertos</span>
                </>
              ) : (
                <>
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-hackathon-yellow flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-gray-700 truncate ml-1">
                    {progress}% concluído
                  </span>
                </>
              )}
            </div>

            <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm border border-gray-200">
              <div className="flex gap-1 items-center">
                {allQuestionsAnswered ? (
                  <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 flex-shrink-0" />
                ) : (
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
                )}
                <span className={`text-xs font-medium truncate ${allQuestionsAnswered ? 'text-green-600' : 'text-gray-700'}`}>
                  {questionsDisplay}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-2 self-end sm:self-center mt-2 sm:mt-0">
          <Button
            onClick={() => onNavigate(session.id)}
            className="border-2 border-black text-black bg-hackathon-yellow hover:bg-hackathon-yellow/90 h-8 px-2 sm:px-3 text-xs sm:text-sm font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
          >
            <PlayCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            <span className="truncate">
              {isComplete ? "Ver" : isAbandoned ? "Retomar" : "Continuar"}
            </span>
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="border-2 border-black text-black bg-hackathon-red/10 hover:bg-hackathon-red/20 h-8 px-2 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 text-hackathon-red" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="w-[80dvw] max-w-md border-2 border-black">
              <AlertDialogHeader>
                <AlertDialogTitle>Remover sessão</AlertDialogTitle>
                <AlertDialogDescription>
                  Tem certeza que deseja remover esta sessão de estudo? Esta ação não pode ser desfeita.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel className="border-2 border-black">Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete(session.id)}
                  className="bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black"
                >
                  Remover
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
    </Card>
  );
};
