import { useState, useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import type { StudySessionRow } from "@/types/study-session";

export const useStudySession = () => {
  const [activeSession, setActiveSession] = useState<StudySessionRow | null>(null);
  const { toast } = useToast();

  const createSession = useCallback(async (userId: string, questionIds: string[], title: string) => {
    try {
      const { data: newSession, error } = await supabase
        .from('study_sessions')
        .insert({
          user_id: userId,
          questions: questionIds,
          total_questions: questionIds.length,
          current_question_index: 0,
          stats: {
            correct_answers: 0,
            incorrect_answers: 0,
            time_spent: 0,
            by_specialty: {},
            by_theme: {},
            by_focus: {}
          },
          status: 'in_progress',
          title
        })
        .select()
        .single();

      if (error) throw error;

      setActiveSession(newSession);
      return newSession;

    } catch (error: any) {
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const updateSessionProgress = useCallback(async (
    sessionId: string,
    questionId: string,
    isCorrect: boolean,
    timeSpent: number
  ) => {
    try {
      const { data: question, error: questionError } = await supabase
        .from('questions')
        .select('specialty_id, theme_id, focus_id')
        .eq('id', questionId)
        .single();

      if (questionError) throw questionError;

      const { error: eventError } = await supabase
        .from('session_events')
        .insert({
          session_id: sessionId,
          question_id: questionId,
          response_status: isCorrect,
          response_time: timeSpent,
          specialty_id: question.specialty_id,
          theme_id: question.theme_id,
          focus_id: question.focus_id
        });

      if (eventError) throw eventError;

      const { data: currentStats, error: statsError } = await supabase
        .rpc('get_session_statistics', { p_session_id: sessionId as any });

      if (statsError) throw statsError;

      const { error: updateError } = await supabase
        .from('study_sessions')
        .update({
          stats: currentStats[0],
          total_correct: currentStats[0].total_correct,
          total_incorrect: currentStats[0].total_incorrect,
          avg_response_time: currentStats[0].avg_response_time
        })
        .eq('id', sessionId);

      if (updateError) throw updateError;

      return true;
    } catch (error: any) {
      toast({
        title: "Erro ao atualizar progresso",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const completeSession = useCallback(async (sessionId: string) => {
    try {
      const { error } = await supabase
        .from('study_sessions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) throw error;
      return true;
    } catch (error: any) {
      toast({
        title: "Erro ao finalizar sessão",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  return {
    activeSession,
    setActiveSession,
    createSession,
    updateSessionProgress,
    completeSession
  };
};
