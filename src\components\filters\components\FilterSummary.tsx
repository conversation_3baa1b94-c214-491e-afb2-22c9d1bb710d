
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { SelectedFilters, FilterOption, filterTypeToKey } from "@/components/filters/types";

interface FilterSummaryProps {
  selectedFilters: SelectedFilters;
  availableFilters: {
    specialties: any[];
    themes: any[];
    focuses: any[];
    locations: any[];
    years: number[];
  };
  onRemoveFilter: (id: string, type: FilterOption['type']) => void;
  onClearAllFilters?: () => void;
  totalQuestions: number;
  isLoading: boolean;
}

export const FilterSummary = ({
  selectedFilters,
  availableFilters,
  onRemoveFilter,
  onClearAllFilters,
  totalQuestions,
  isLoading
}: FilterSummaryProps) => {
  const getItemName = (id: string, type: FilterOption['type']) => {
    if (!availableFilters || !id) return id;

    if (type === 'year') return id;

    if (type === 'question_type') {
      const typeMap: {[key: string]: string} = {
        'teorica-1': 'Teórica I',
        'teorica-2': 'Teórica II',
        'teorico-pratica': 'Teórico-Prática'
      };
      return typeMap[id] || id;
    }

    const filterKey = filterTypeToKey(type);
    const items = availableFilters[filterKey] || [];
    const item = items.find((i: any) => i.id === id);

    return item?.name || id;
  };

  const renderFilterTags = (type: FilterOption['type']) => {
    const filterKey = filterTypeToKey(type);
    return (selectedFilters[filterKey] || []).map((id: string) => (
      <Badge
        key={`${type}-${id}`}
        variant="outline"
        className="px-3 py-1.5 text-sm font-medium border-2 border-black bg-[#FEF7CD] text-gray-800 rounded-md hover:bg-[#FEF7CD]/80 transition-colors cursor-pointer"
        onClick={() => onRemoveFilter(id, type)}
      >
        {getItemName(id, type)}
        <X className="h-3.5 w-3.5 ml-2 text-gray-500" />
      </Badge>
    ));
  };

  // Only render the component if there are any selected filters
  const hasFilters = Object.values(selectedFilters).some(filters => filters && filters.length > 0);

  if (!hasFilters) return null;

  return (
    <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-gray-300/70 shadow-md p-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            🏷️ Filtros Aplicados
          </h3>
          {hasFilters && (
            <button
              onClick={onClearAllFilters}
              className="text-xs text-[#FF6B00] hover:text-[#FF4D00] transition-colors font-medium"
            >
              Limpar todos
            </button>
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          {renderFilterTags('specialty')}
          {renderFilterTags('theme')}
          {renderFilterTags('focus')}
          {renderFilterTags('location')}
          {renderFilterTags('year')}
          {renderFilterTags('question_type')}
        </div>
      </div>
    </div>
  );
};
