
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { HelpCircle } from "lucide-react";
import { ManualTopicFormProps } from "./types";

export const ManualTopicForm = ({
  manualSpecialty,
  setManualSpecialty,
  manualTheme,
  setManualTheme,
  manualFocus,
  setManualFocus,
  domain,
  shouldShowFocus
}: ManualTopicFormProps) => {
  return (
    <div className="p-6 space-y-4">
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-6 flex items-start gap-3">
        <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
        <p className="text-sm text-blue-700 break-words">
          {shouldShowFocus 
            ? "Crie seu próprio tópico personalizado com detalhes específicos. Ideal para temas que não estão disponíveis na plataforma."
            : "Crie seu próprio tópico personalizado com especialidade e tema. Para oftalmologia, não é necessário especificar o foco."}
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="specialty" className="text-base font-medium">Especialidade</Label>
          <Input
            id="specialty"
            value={manualSpecialty}
            onChange={(e) => setManualSpecialty(e.target.value)}
            placeholder={`Ex: ${domain === 'residencia' ? 'Cardiologia' : domain}`}
            className="border-2 focus:border-green-500"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="theme" className="text-base font-medium">Tema</Label>
          <Input
            id="theme"
            value={manualTheme}
            onChange={(e) => setManualTheme(e.target.value)}
            placeholder="Ex: Glaucoma"
            className="border-2 focus:border-green-500"
          />
        </div>
        
        {shouldShowFocus && (
          <div className="space-y-2">
            <Label htmlFor="focus" className="text-base font-medium">Foco</Label>
            <Input
              id="focus"
              value={manualFocus}
              onChange={(e) => setManualFocus(e.target.value)}
              placeholder="Ex: Pressão Ocular"
              className="border-2 focus:border-green-500"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ManualTopicForm;
