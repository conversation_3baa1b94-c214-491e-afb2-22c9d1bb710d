import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { FlashcardResponse, SelectedFilters } from "@/types/flashcard";
import { useFlashcardLoader } from "./useFlashcardLoader";
import { useFlashcardStats } from "./useFlashcardStats";
import { useFlashcardMetrics } from "./useFlashcardMetrics";
import { useFlashcardDatabase } from "./useFlashcardDatabase";
import { calculateNextReviewDate } from "./utils/dateCalculations";

export const useFlashcardReview = (filters: SelectedFilters) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { data: flashcards = [], isLoading } = useFlashcardLoader(filters);
  const { sessionStats, updateStats } = useFlashcardStats();
  const { calculateNewMetrics } = useFlashcardMetrics();
  const { updateFlashcardReview } = useFlashcardDatabase();

  const handleResponse = async (response: FlashcardResponse) => {
    try {
      const currentCard = flashcards[currentIndex];
      console.log('📝 [useFlashcardReview] Processando resposta:', {
        cardId: currentCard.id,
        response,
        metricas_atuais: {
          stability: currentCard.stability,
          difficulty: currentCard.difficulty,
          retrievability: currentCard.retrievability
        }
      });

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const intervalInDays = typeof currentCard.intervalindays === 'string' 
        ? parseInt(currentCard.intervalindays) 
        : (currentCard.intervalindays || 1);

      const newMetrics = calculateNewMetrics({
        stability: currentCard.stability || 1.0,
        difficulty: currentCard.difficulty || 5.0,
        retrievability: currentCard.retrievability || 0.9,
        lastReviewDate: currentCard.last_review_date,
        intervalInDays,
        nextReviewDate: new Date()
      }, response);

      const nextReviewDate = calculateNextReviewDate(response, newMetrics);
      const isCorrect = response === 'easy' || response === 'medium';

      console.log('🎯 [useFlashcardReview] Métricas finais:', {
        metricas_antigas: {
          stability: currentCard.stability,
          difficulty: currentCard.difficulty
        },
        metricas_novas: newMetrics,
        proxima_revisao: nextReviewDate,
        isCorrect
      });

      const success = await updateFlashcardReview(
        user.id,
        currentCard.id,
        newMetrics,
        nextReviewDate,
        isCorrect
      );

      if (success) {
        updateStats(isCorrect);
        setCurrentIndex(prev => prev + 1);
      }

      return success;
    } catch (error: any) {
      console.error('❌ [useFlashcardReview] Erro ao processar resposta:', error);
      toast.error("Erro ao processar resposta");
      return false;
    }
  };

  return {
    flashcards,
    currentIndex,
    isLoading,
    handleResponse,
    sessionStats
  };
};