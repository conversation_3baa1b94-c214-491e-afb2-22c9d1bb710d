
export interface SessionFilters {
  specialty_id?: string;
  specialty_name?: string;
  theme_id?: string;
  theme_name?: string;
  focus_id?: string;
  focus_name?: string;
}

export interface FlashcardSession {
  id: string;
  user_id: string;
  cards: string[];
  status: 'in_progress' | 'completed';
  start_time: string;
  end_time: string;
  created_at: string;
  updated_at: string;
  total_cards: number;
  correct_cards: number;
  filters: SessionFilters;
  flashcards_session_cards?: Array<{
    card_id: string;
    review_status: string;
  }>;
}

export const parseSessionFilters = (filters: any): SessionFilters => {
  if (!filters) return {};
  
  try {
    if (typeof filters === 'string') {
      return JSON.parse(filters);
    }
    return filters;
  } catch (e) {
    console.error('Error parsing session filters:', e);
    return {};
  }
};
