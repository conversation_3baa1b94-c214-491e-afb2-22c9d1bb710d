
import { But<PERSON> } from "@/components/ui/button";
import { Wrench } from "lucide-react";
import { useState } from "react";
import { useScheduleFix } from "@/hooks/useScheduleFix";

export const FixScheduleDatesButton = () => {
  const [isFixing, setIsFixing] = useState(false);
  const { fixWeekBoundaries } = useScheduleFix();

  const handleFix = async () => {
    setIsFixing(true);
    try {
      await fixWeekBoundaries();
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="flex items-center gap-2 text-sm border-gray-300 text-gray-600 hover:bg-gray-100"
      onClick={handleFix}
      disabled={isFixing}
    >
      <Wrench className="h-3.5 w-3.5" />
      {isFixing ? "Corrigindo..." : "Corrigir datas"}
    </Button>
  );
};
