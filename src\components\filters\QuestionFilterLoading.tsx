
import { useEffect, useState } from "react";
import { Loader2, Search } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Card } from "@/components/ui/card";

// Mensagens rotativas simples
const LOADING_MESSAGES = [
  "🚀 Preparando filtros personalizados para você...",
  "🔎 Consultando especialidades e temas...",
  "💡 Buscando as melhores questões…",
  "🤓 Montando o seu estudo!",
  "🕑 Só mais alguns segundos!",
  "⚡ Otimizando seu aprendizado...",
  "🗂️ Trazendo questões atualizadas...",
];

export function QuestionFilterLoading() {
  const [progress, setProgress] = useState(10);
  const [msgIndex, setMsgIndex] = useState(0);

  useEffect(() => {
    // Muda mensagem a cada 1.25s
    const msgInt = setInterval(() => {
      setMsgIndex((prev) => (prev + 1) % LOADING_MESSAGES.length);
    }, 1250);

    // Simula barra de progresso gradual
    const progInt = setInterval(() => {
      setProgress((prev) =>
        prev >= 95 ? prev : prev + Math.floor(Math.random() * 8 + 3)
      );
    }, 350);

    return () => {
      clearInterval(msgInt);
      clearInterval(progInt);
    };
  }, []);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-[#FEF7CD] via-[#f8fafc] to-[#FEF7CD] flex items-center justify-center p-4">
      {/* Background pattern para evitar áreas em branco */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f59e0b' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <Card className="relative max-w-lg w-full bg-white/95 backdrop-blur-sm border-2 border-yellow-400/70 shadow-xl rounded-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-6 text-center">
          <div className="flex items-center justify-center mb-3">
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
              <Search className="h-6 w-6 text-white" />
            </div>
          </div>
          <h2 className="text-xl font-bold text-white mb-1">
            Preparando Filtros
          </h2>
          <p className="text-yellow-100 text-sm">
            Carregando filtros e questões – aguarde um instante!
          </p>
        </div>

        {/* Conteúdo */}
        <div className="p-6 space-y-6">
          {/* Spinner central */}
          <div className="flex justify-center">
            <div className="bg-yellow-100 rounded-full p-4">
              <Loader2 className="h-12 w-12 animate-spin text-yellow-600" />
            </div>
          </div>

          {/* Barra de progresso */}
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600 font-medium">Progresso</span>
              <span className="text-yellow-600 font-bold">{progress}%</span>
            </div>
            <Progress
              value={progress}
              className="h-3 bg-yellow-100 rounded-full"
            />
          </div>

          {/* Mensagem atual */}
          <div className="text-center">
            <span
              className="text-yellow-900 font-semibold text-base transition-all duration-300"
              key={msgIndex}
            >
              {LOADING_MESSAGES[msgIndex]}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );
}
