import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ThumbsUp, ThumbsDown, Check, UserPlus } from "lucide-react";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";
import { cn } from "@/lib/utils";

interface FlashcardGridProps {
  flashcards: FlashcardWithHierarchy[];
  onSelect: (cardId: string) => void;
  selectedCards: string[];
  importedCardIds?: string[];
  onLike?: (cardId: string) => void;
  onDislike?: (cardId: string) => void;
}

export const FlashcardGrid = ({ flashcards, onSelect, selectedCards, importedCardIds = [], onLike, onDislike }: FlashcardGridProps) => {
  return (
    <div className="grid gap-4">
      {flashcards.map(card => {
        const isSelected = selectedCards.includes(card.id);
        const isImported = importedCardIds.includes(card.id) || card.isImported;
        const likedBy = card.liked_by || [];
        const dislikedBy = card.disliked_by || [];
        const likes = typeof card.likes === "number" ? card.likes : likedBy.length;
        const dislikes = typeof card.dislikes === "number" ? card.dislikes : dislikedBy.length;
        const importCount = typeof card.import_count === "number" ? card.import_count : 0;

        return (
          <Card
            key={card.id}
            className={cn(
              "p-4 transition-all hover:shadow-md relative",
              isSelected && "border-primary",
              isImported && "opacity-60 border-green-500"
            )}
          >
            {isImported && (
              <span className="absolute top-2 left-2 bg-green-200 text-green-800 px-2 text-xs rounded font-bold border border-green-400 z-10">Importado</span>
            )}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium mb-2">{card.front}</h4>
                <p className="text-sm text-muted-foreground">{card.back}</p>
              </div>
              <Button
                variant={isSelected ? "default" : "outline"}
                size="icon"
                onClick={() => {
                  if (!isImported) onSelect(card.id);
                }}
                className="ml-4"
                disabled={isImported}
                aria-label={isImported ? "Este card já foi importado" : "Selecionar card"}
              >
                {isSelected ? (
                  <Check className="h-4 w-4" />
                ) : (
                  isImported ? <Check className="h-4 w-4 text-green-600" /> : <span className="transform transition-transform">+</span>
                )}
              </Button>
            </div>
            <div className="mt-3 flex items-center gap-6 text-sm font-semibold">
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="icon" className="rounded-full" onClick={() => onLike?.(card.id)} disabled={isImported}>
                  <ThumbsUp className="w-4 h-4" /> 
                </Button>
                <span>{likes}</span>
                <Button variant="ghost" size="icon" className="rounded-full" onClick={() => onDislike?.(card.id)} disabled={isImported}>
                  <ThumbsDown className="w-4 h-4" />
                </Button>
                <span>{dislikes}</span>
              </div>
              <div className="flex items-center gap-2 ml-4">
                <UserPlus className="w-4 h-4 text-green-600" />
                <span>{importCount}</span>
              </div>
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              <p>Especialidade: {card.hierarchy.specialty?.name}</p>
              {card.hierarchy.theme && <p>Tema: {card.hierarchy.theme.name}</p>}
              {card.hierarchy.focus && <p>Foco: {card.hierarchy.focus.name}</p>}
              {card.hierarchy.extraFocus && <p>Extra Foco: {card.hierarchy.extraFocus.name}</p>}
            </div>
          </Card>
        );
      })}
    </div>
  );
};
