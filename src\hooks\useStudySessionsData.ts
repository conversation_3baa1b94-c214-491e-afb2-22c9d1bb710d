import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface StudySessionsData {
  // Dados básicos das sessões
  sessions: {
    id: string;
    title: string;
    status: string;
    started_at: string;
    completed_at: string | null;
    total_questions: number;
    stats: any;
  }[];
  
  // Contadores
  totalSessions: number;
  completedSessions: number;
  inProgressSessions: number;
  
  // Dados para streak
  completedDates: string[];
  
  // Sessão ativa (se houver)
  activeSession: any | null;
}

/**
 * Hook consolidado para dados de sessões de estudo
 * Substitui múltiplas queries separadas por uma única query otimizada
 */
export const useStudySessionsData = () => {
  const { user } = useAuth();

  const {
    data,
    isLoading,
    error
  } = useQuery({
    queryKey: ['study-sessions-consolidated', user?.id],
    queryFn: async (): Promise<StudySessionsData> => {
      if (!user?.id) {
        return {
          sessions: [],
          totalSessions: 0,
          completedSessions: 0,
          inProgressSessions: 0,
          completedDates: [],
          activeSession: null
        };
      }

      // Query única otimizada para buscar todos os dados necessários
      const { data: sessions, error } = await supabase
        .from('study_sessions')
        .select(`
          id,
          title,
          status,
          started_at,
          completed_at,
          total_questions,
          stats,
          current_question_index
        `)
        .eq('user_id', user.id)
        .in('status', ['completed', 'in_progress', 'abandoned'])
        .order('started_at', { ascending: false });

      if (error) {
        console.error('❌ [useStudySessionsData] Error loading sessions:', error);
        throw error;
      }

      console.log('✅ [useStudySessionsData] Sessions loaded:', {
        total: sessions?.length || 0,
        size: `${JSON.stringify(sessions).length / 1024}KB`
      });

      const sessionsData = sessions || [];

      // Processar dados
      const completedSessions = sessionsData.filter(s => s.status === 'completed');
      const inProgressSessions = sessionsData.filter(s => s.status === 'in_progress');
      
      // Extrair datas de conclusão para cálculo de streak
      const completedDates = completedSessions
        .filter(s => s.completed_at)
        .map(s => new Date(s.completed_at!).toISOString().split('T')[0])
        .filter((date, index, array) => array.indexOf(date) === index) // Remove duplicatas
        .sort();

      // Encontrar sessão ativa (mais recente em progresso)
      const activeSession = inProgressSessions.length > 0 ? inProgressSessions[0] : null;

      return {
        sessions: sessionsData,
        totalSessions: sessionsData.length,
        completedSessions: completedSessions.length,
        inProgressSessions: inProgressSessions.length,
        completedDates,
        activeSession
      };
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutos (reduzido)
    cacheTime: 10 * 60 * 1000, // 10 minutos (reduzido)
    refetchOnWindowFocus: true, // Ativado para refresh automático
    refetchOnReconnect: true
  });

  return {
    data: data || {
      sessions: [],
      totalSessions: 0,
      completedSessions: 0,
      inProgressSessions: 0,
      completedDates: [],
      activeSession: null
    },
    isLoading,
    error,
    
    // Dados específicos para conveniência
    sessions: data?.sessions || [],
    totalSessions: data?.totalSessions || 0,
    completedSessions: data?.completedSessions || 0,
    inProgressSessions: data?.inProgressSessions || 0,
    completedDates: data?.completedDates || [],
    activeSession: data?.activeSession || null,
    
    // Estados derivados
    hasActiveSessions: (data?.inProgressSessions || 0) > 0,
    hasCompletedSessions: (data?.completedSessions || 0) > 0,
    recentSessions: data?.sessions.slice(0, 10) || []
  };
};
