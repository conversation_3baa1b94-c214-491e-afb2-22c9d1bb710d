
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useGroqAnalysis } from "@/hooks/useGroqAnalysis";
import { Brain, Loader2, Refresh<PERSON>w, AlertTriangle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { PerformanceOverview } from "./PerformanceOverview";
import { StudySchedule } from "./StudySchedule";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useUserStatistics } from "@/hooks/useUserStatistics";

export const AIRecommendations = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [shouldFetch, setShouldFetch] = useState(false);
  const [showHoursDialog, setShowHoursDialog] = useState(false);
  const [studyHours, setStudyHours] = useState<number>(4);
  const [hasGeneratedOnce, setHasGeneratedOnce] = useState(false);
  const [isGeneratingSchedule, setIsGeneratingSchedule] = useState(false);

  const { data: analysis, isLoading, error, refetch } = useGroqAnalysis();
  const { data: userStats, isLoading: statsLoading } = useUserStatistics();

  useEffect(() => {
    if (error) {
      console.error("Error loading recommendations:", error);
      toast.error("Não foi possível carregar suas recomendações personalizadas.");
      setIsGeneratingSchedule(false);
    }
  }, [error]);

  const handleGenerateRecommendations = () => {
    if (!userStats || userStats.total_questions < 50) {
      toast.error(
        "Você precisa responder pelo menos 50 questões para gerar recomendações personalizadas.",
        {
          description: "Continue estudando para desbloquear esta funcionalidade!"
        }
      );
      return;
    }
    setShowHoursDialog(true);
  };

  const handleConfirmHours = async () => {
    console.log("🚀 Gerando recomendações para", studyHours, "horas por dia");
    try {
      setShowHoursDialog(false);
      setIsGeneratingSchedule(true);
      setShouldFetch(true);
      await refetch();
      setHasGeneratedOnce(true);
      toast.success("Recomendações geradas com sucesso!");
    } catch (error) {
      console.error("Error generating recommendations:", error);
      toast.error("Erro ao gerar recomendações. Tente novamente.");
    } finally {
      // The loading state will be set to false after the schedule is fully loaded
      // This happens in the useEffect below
    }
  };

  // Add an effect to track when loading is complete
  useEffect(() => {
    if (!isLoading && shouldFetch && analysis) {
      // Only set loading to false when we have the data and we're not loading anymore
      setTimeout(() => {
        setIsGeneratingSchedule(false);
        setShouldFetch(false);
      }, 1000); // Small delay to ensure everything is properly rendered
    }
  }, [isLoading, shouldFetch, analysis]);

  if (isLoading || statsLoading || isGeneratingSchedule) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[400px] space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-center text-lg font-medium">
          {isGeneratingSchedule ? "Gerando seu cronograma personalizado..." : "Carregando recomendações..."}
        </p>
        {isGeneratingSchedule && (
          <p className="text-center text-sm text-muted-foreground max-w-md">
            Estamos analisando seu perfil e criando um cronograma ideal para seus estudos. 
            Isso pode levar alguns instantes.
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold flex items-center gap-2">
          <Brain className="w-8 h-8" />
          Recomendações Personalizadas
        </h2>
        <Button 
          onClick={handleGenerateRecommendations}
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Gerar Recomendações
        </Button>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertTriangle className="h-4 w-4 text-blue-500" />
        <AlertDescription className="text-blue-700">
          Esta ferramenta está em fase beta e pode gerar cronogramas semanais ou mensais baseados em suas estatísticas de estudo.
          Para uma análise mais precisa, é necessário ter respondido pelo menos 50 questões.
        </AlertDescription>
      </Alert>

      {!hasGeneratedOnce && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-800">
              Bem-vindo ao Sistema de Recomendações IA
            </h2>
            <p className="text-blue-700 mb-4">
              Este sistema analisa seu desempenho nos estudos e gera recomendações personalizadas
              focadas nas áreas que mais precisam de atenção.
            </p>
            <ul className="list-disc pl-6 space-y-2 text-blue-700">
              <li>Análise de pontos fortes e fracos</li>
              <li>Cronograma adaptado à sua disponibilidade</li>
              <li>Foco nas áreas que precisam de mais atenção</li>
            </ul>
          </CardContent>
        </Card>
      )}

      {analysis && (
        <div className="space-y-6">
          <PerformanceOverview
            specialties={analysis.specialties || []}
            themes={analysis.themes || []}
            focuses={analysis.focuses || []}
          />
          
          {analysis.weekly_plan && (
            <StudySchedule weeklyPlan={analysis.weekly_plan.map(day => ({
              ...day,
              topics: day.topics.map(topic => ({
                ...topic,
                type: topic.type as 'theory' | 'questions' | 'flashcards',
                startTime: '09:00'
              }))
            }))} />
          )}
        </div>
      )}

      <Dialog open={showHoursDialog} onOpenChange={setShowHoursDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tempo Disponível para Estudo</DialogTitle>
            <DialogDescription>
              Para gerar recomendações mais precisas, informe quantas horas por dia você tem disponível para estudar.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="studyHours">Horas disponíveis por dia</Label>
              <Input
                id="studyHours"
                type="number"
                min={1}
                max={24}
                value={studyHours}
                onChange={(e) => setStudyHours(Number(e.target.value))}
              />
            </div>
            <Button onClick={handleConfirmHours} className="w-full">
              Confirmar e Gerar Recomendações
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
