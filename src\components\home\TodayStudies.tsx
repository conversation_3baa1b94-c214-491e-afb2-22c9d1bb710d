import React, { useEffect, useState, lazy, Suspense } from 'react';
import { useStudySchedule } from '@/hooks/useStudySchedule';
import { useStudySession } from '@/hooks/useStudySession';
import { BookOpenCheck, CheckCircle, Clock, Flame, Calendar, PlusCircle, Shuffle, ChevronLeft, ChevronRight } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@supabase/auth-helpers-react';
import { useFeedbackDialog } from "@/components/ui/feedback-dialog";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { useIsMobile } from '@/hooks/use-mobile';
import { useDomain } from '@/hooks/useDomain';
import { usePageVisibility } from '@/hooks/usePageVisibility';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { BookOpen } from "lucide-react";
import { adjustToBrazilTimezone } from '@/utils/formatTime';

// Lazy imports para componentes pesados
const FlashcardDailySummary = lazy(() => import('./FlashcardDailySummary').then(module => ({ default: module.FlashcardDailySummary })));
const StudyOptionsDialog = lazy(() => import('@/components/study/StudyOptionsDialog').then(module => ({ default: module.StudyOptionsDialog })));
const ConfirmStudyDialog = lazy(() => import('@/components/progress/ConfirmStudyDialog').then(module => ({ default: module.ConfirmStudyDialog })));
const TOPICS_PER_PAGE = 3;
interface TodayStudiesProps {
  enabled?: boolean;
}

const TodayStudies: React.FC<TodayStudiesProps> = ({ enabled = true }) => {
  const {
    weeklySchedule,
    markTopicAsStudied,
    loadCurrentSchedule
  } = useStudySchedule();
  const { createSession } = useStudySession();
  const navigate = useNavigate();
  const user = useUser();
  const { showFeedback } = useFeedbackDialog();
  const { toast } = useToast();
  const [loadingState, setLoadingState] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [availableInstitutions, setAvailableInstitutions] = useState<{
    id: string;
    name: string;
  }[]>([]);
  const [maxQuestions, setMaxQuestions] = useState(10);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const isMobile = useIsMobile();
  const [allTopicsData, setAllTopicsData] = useState<{
    topic: any;
    questionCount: number;
    questions: any[];
  }[]>([]);
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedTopicName, setSelectedTopicName] = useState<string>("");
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [studySuccessMessage, setStudySuccessMessage] = useState("");
  const [nextRevisionInfo, setNextRevisionInfo] = useState<{
    date: string;
    dayOfWeek: string;
    revisionNumber: number;
    daysUntil: number;
  } | null>(null);
  const [isLastRevision, setIsLastRevision] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [buttonUpdateKey, setButtonUpdateKey] = useState(0);
  const {
    domain,
    isResidencia,
    isReady
  } = useDomain();
  const today = adjustToBrazilTimezone(new Date());
  const weekDays = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
  const currentDay = weekDays[today.getDay()];

  // Auto-refresh quando o usuário volta para a aba
  usePageVisibility({
    queryKeys: ['study-schedule'],
    onVisible: () => {
      if (enabled) {
        loadCurrentSchedule();
      }
    },
    delay: 1000
  });
  useEffect(() => {
    if (!weeklySchedule && enabled) {
      loadCurrentSchedule();
    }
  }, [enabled]);

  // 🔄 TRIGGER DE ATUALIZAÇÃO EM TEMPO REAL
  useEffect(() => {
    console.log('🔄 [TodayStudies] refreshTrigger mudou:', refreshTrigger);
    if (refreshTrigger > 0) {
      console.log('💥 [TodayStudies] CHAMANDO loadCurrentSchedule devido ao refreshTrigger!');
      loadCurrentSchedule();
    }
  }, [refreshTrigger, loadCurrentSchedule]);

  // ✅ REMOVIDO useEffect que forçava re-render desnecessário
  // useEffect(() => {
  //   if (weeklySchedule) {
  //     setCurrentPage(prev => prev); // Força re-render sem mudar valor
  //   }
  // }, [weeklySchedule]);

  // Lazy load filter options only when needed
  const loadFilterOptions = async () => {
    if (availableYears.length === 0) {
      const { getAvailableYears, getAvailableInstitutions } = await import('@/utils/questionUtils');
      const years = await getAvailableYears();
      const institutions = await getAvailableInstitutions();
      setAvailableYears(years);
      setAvailableInstitutions(institutions);
    }
  };
  useEffect(() => {
    if (weeklySchedule) {
      const foundDaySchedule = weeklySchedule.recommendations.find(day => day.day === currentDay);
      if (foundDaySchedule) {}
    }
  }, [weeklySchedule, currentDay]);

  const todayStudies = weeklySchedule?.recommendations.find(day => {
    const normalizedDayName = day.day.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
    const normalizedCurrentDay = currentDay.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
    return normalizedDayName === normalizedCurrentDay;
  });

  // 🔄 ATUALIZAÇÃO SUAVE DOS BOTÕES (apenas quando necessário)
  useEffect(() => {
    if (todayStudies?.topics && refreshTrigger > 0) {
      // Atualiza apenas quando há um trigger específico
      setButtonUpdateKey(prev => prev + 1);
    }
  }, [refreshTrigger, todayStudies?.topics]);
  const hasNoStudies = !todayStudies || todayStudies.topics.length === 0;

  // Separar tópicos da plataforma dos manuais
  const platformTopics = todayStudies?.topics?.filter(topic => !topic.is_manual) || [];
  const manualTopics = todayStudies?.topics?.filter(topic => topic.is_manual) || [];

  const totalTopics = todayStudies?.topics?.length || 0;
  const totalPages = Math.ceil(totalTopics / TOPICS_PER_PAGE);
  const currentTopics = todayStudies?.topics ? todayStudies.topics.slice((currentPage - 1) * TOPICS_PER_PAGE, currentPage * TOPICS_PER_PAGE) : [];
  const getTopicTitle = (topic: any) => {
    if (topic.focus && topic.focus !== "Geral" && topic.focus !== "N/A") {
      return topic.focus;
    } else if (topic.theme && topic.theme !== "Geral") {
      return topic.theme;
    } else if (topic.specialty) {
      return topic.specialty;
    }
    return "Estudo";
  };
  const formatDuration = (duration: string) => {
    if (!duration) return '';
    if (duration.includes('hora') || duration.includes('hour')) {
      const hoursMatch = duration.match(/(\d+)/);
      const hours = hoursMatch ? hoursMatch[1] : '0';
      return `${hours}h`;
    }
    if (duration.includes(':')) {
      const [hours, minutes] = duration.split(':');
      return minutes === '00' ? `${hours}h` : `${hours}h${minutes}m`;
    }
    const minutesMatch = duration.match(/(\d+)/);
    const minutes = minutesMatch ? minutesMatch[1] : '0';
    return `${minutes}m`;
  };
  const formatNextRevisionDate = (date: string) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  };
  const formatRevisionNumber = (num: number) => {
    switch (num) {
      case 1:
        return "primeira";
      case 2:
        return "segunda";
      case 3:
        return "terceira";
      default:
        return `${num}ª`;
    }
  };
  const formatTotalTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (remainingMinutes === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h${remainingMinutes}min`;
      }
    }
  };
  const handleMarkAsStudied = async (topicId: string, topicName?: string) => {
    setSelectedTopicId(topicId);
    setSelectedTopicName(topicName || "este tópico");
    setConfirmDialogOpen(true);
  };
  const handleConfirmStudied = async () => {
    if (selectedTopicId) {
      setConfirmDialogOpen(false);
      const result = await markTopicAsStudied(selectedTopicId);
      if (result && result.success) {
        // 🔄 ATUALIZAÇÃO IMEDIATA E SUAVE (sem reload completo)
        setRefreshTrigger(prev => prev + 1);

        if (result.isLastRevision) {
          setIsLastRevision(true);
          setStudySuccessMessage(result.message);
        } else if (result.nextRevision) {
          setIsLastRevision(false);
          setNextRevisionInfo(result.nextRevision);
          setStudySuccessMessage(`O tópico "${selectedTopicName}" foi marcado como estudado com sucesso!`);
        } else {
          setIsLastRevision(false);
          setNextRevisionInfo(null);
          setStudySuccessMessage(result.message);
        }
        setSuccessDialogOpen(true);
      } else {
        showFeedback({
          title: "Erro",
          description: result?.message || "Ocorreu um erro ao marcar o tópico como estudado.",
          type: "error"
        });
      }
    }
  };
  const handlePracticeQuestions = async (topic: any) => {
    try {
      setLoadingQuestions(true);
      await loadFilterOptions(); // Load only when needed

      const { filterQuestionsByTopic } = await import('@/utils/questionUtils');
      const result = await filterQuestionsByTopic(
        topic.specialtyId,
        topic.themeId,
        topic.focusId,
        100,
        undefined,
        undefined,
        false,
        domain,
        false, // hideAnswered será aplicado depois no dialog
        user?.id
      );
      setMaxQuestions(result.count);
      setAllTopicsData([{
        topic,
        questionCount: result.count,
        questions: result.data
      }]);
      setDialogOpen(true);
    } catch (error) {
      showFeedback({
        title: "Erro",
        description: "Erro ao carregar questões. Tente novamente.",
        type: "error"
      });
    } finally {
      setLoadingQuestions(false);
    }
  };
  const handleStudyAllTopics = async () => {
    if (!todayStudies || !user?.id) return;

    // Filtrar apenas tópicos da plataforma (que têm questões)
    const platformTopicsOnly = todayStudies.topics.filter(topic => !topic.is_manual);

    if (platformTopicsOnly.length === 0) {
      toast({
        title: "Nenhum tópico da plataforma",
        description: "Não há tópicos com questões disponíveis para praticar hoje.",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoadingQuestions(true);
      await loadFilterOptions(); // Load filter options

      const { filterQuestionsByTopic } = await import('@/utils/questionUtils');
      const topicsData = await Promise.all(platformTopicsOnly.map(async topic => {
        const result = await filterQuestionsByTopic(
          topic.specialtyId,
          topic.themeId,
          topic.focusId,
          1000, // Aumentar limite para buscar todas as questões disponíveis
          undefined,
          undefined,
          false,
          domain,
          false, // hideAnswered será aplicado depois no dialog
          user?.id
        );
        return {
          topic,
          questionCount: result.count,
          questions: result.data
        };
      }));
      const validTopicsData = topicsData.filter(t => t.questions.length > 0);
      if (validTopicsData.length === 0) {
        toast({
          title: "Nenhuma questão disponível",
          description: `Não encontramos questões para os tópicos de hoje no domínio ${domain}.`,
          variant: "destructive"
        });
        setLoadingQuestions(false);
        return;
      }
      setAllTopicsData(validTopicsData);
      const totalQuestionsAvailable = validTopicsData.reduce((total, t) => total + t.questionCount, 0);

      // Não limitar o total - mostrar todas as questões disponíveis
      const effectiveMaxQuestions = totalQuestionsAvailable;
      const minQuestions = validTopicsData.length;
      setMaxQuestions(effectiveMaxQuestions);
      setDialogOpen(true);
    } catch (error) {
      toast({
        title: "Erro ao preparar questões",
        description: "Não foi possível carregar as questões para os tópicos de hoje.",
        variant: "destructive"
      });
    } finally {
      setLoadingQuestions(false);
    }
  };
  const handleStartStudy = async (quantity: number, hideAnswered: boolean = false, institutionIds?: string[]) => {
    if (!user?.id || allTopicsData.length === 0) {
      return;
    }

    try {
      setLoadingState(true);
      if (allTopicsData.length === 1) {
        const {
          topic,
          questions
        } = allTopicsData[0];
        if (!questions.length) {
          toast({
            title: "Nenhuma questão encontrada",
            description: "Não encontramos questões com os critérios selecionados.",
            variant: "destructive"
          });
          return;
        }

        // Filtrar questões já acertadas se solicitado
        let availableQuestions = questions;
        if (hideAnswered) {
          // Buscar questões já acertadas
          const { getUserCorrectAnswers } = await import('@/utils/questionUtils');
          const correctAnswers = await getUserCorrectAnswers(user.id);
          availableQuestions = questions.filter(q => !correctAnswers.includes(q.id));
        }

        // ✅ NOVO: Filtrar por instituições se solicitado
        if (institutionIds && institutionIds.length > 0) {
          availableQuestions = availableQuestions.filter(q =>
            q.institution_id && institutionIds.includes(q.institution_id)
          );
        }

        const shuffledQuestions = [...availableQuestions].sort(() => 0.5 - Math.random());
        const selectedQuestions = shuffledQuestions.slice(0, Math.min(quantity, availableQuestions.length));
        const title = getTopicTitle(topic);

        const session = await createSession(user.id, selectedQuestions.map(q => q.id), title);
        if (session) {
          navigate(`/questions/${session.id}`);
        } else {
          throw new Error('Erro ao criar sessão de estudo');
        }
        return;
      }

      const selectedQuestions: any[] = [];

      // Buscar questões já acertadas uma vez se necessário
      let correctAnswers: string[] = [];
      if (hideAnswered) {
        const { getUserCorrectAnswers } = await import('@/utils/questionUtils');
        correctAnswers = await getUserCorrectAnswers(user.id);
      }

      // Nova lógica de distribuição inteligente
      const topicsWithQuestions = allTopicsData.map(({ topic, questions }) => {
        // Filtrar questões já acertadas se solicitado
        let availableQuestions = questions;
        if (hideAnswered) {
          availableQuestions = questions.filter(q => !correctAnswers.includes(q.id));
        }

        // ✅ NOVO: Filtrar por instituições se solicitado
        if (institutionIds && institutionIds.length > 0) {
          availableQuestions = availableQuestions.filter(q =>
            q.institution_id && institutionIds.includes(q.institution_id)
          );
        }

        return {
          topic,
          topicName: getTopicTitle(topic),
          questions: availableQuestions,
          count: availableQuestions.length
        };
      }).filter(t => t.count > 0); // Apenas tópicos com questões disponíveis

      if (topicsWithQuestions.length === 0) {
        toast({
          title: "Nenhuma questão disponível",
          description: "Não há questões disponíveis para os tópicos selecionados.",
          variant: "destructive"
        });
        return;
      }

      // Calcular distribuição ideal
      const totalTopics = topicsWithQuestions.length;
      const baseQuestionsPerTopic = Math.floor(quantity / totalTopics);
      let extraQuestions = quantity % totalTopics;

      // Distribuir questões de forma inteligente
      topicsWithQuestions.forEach((topicData, index) => {
        const plannedQuestions = baseQuestionsPerTopic + (extraQuestions > 0 ? 1 : 0);
        const maxQuestionsForTopic = Math.min(
          topicData.count, // Não pode exceder questões disponíveis
          plannedQuestions // Distribuição planejada
        );

        if (extraQuestions > 0 && maxQuestionsForTopic === plannedQuestions) {
          extraQuestions--;
        }

        // Selecionar questões aleatórias para este tópico
        const shuffledQuestions = [...topicData.questions].sort(() => 0.5 - Math.random());
        const selectedFromTopic = shuffledQuestions.slice(0, maxQuestionsForTopic);
        selectedQuestions.push(...selectedFromTopic);
      });

      // Se ainda precisamos de mais questões (alguns tópicos tinham poucas questões)
      const stillNeeded = quantity - selectedQuestions.length;
      if (stillNeeded > 0) {
        // Coletar questões restantes de todos os tópicos
        const allRemainingQuestions: any[] = [];
        topicsWithQuestions.forEach(topicData => {
          const usedIds = new Set(selectedQuestions.map(q => q.id));
          const remaining = topicData.questions.filter(q => !usedIds.has(q.id));
          allRemainingQuestions.push(...remaining);
        });

        // Embaralhar e pegar as que faltam
        const shuffledRemaining = allRemainingQuestions.sort(() => 0.5 - Math.random());
        const additionalQuestions = shuffledRemaining.slice(0, stillNeeded);
        selectedQuestions.push(...additionalQuestions);
      }
      const finalQuestions = selectedQuestions.slice(0, quantity);

      if (finalQuestions.length === 0) {
        toast({
          title: "Nenhuma questão encontrada",
          description: "Não encontramos questões suficientes para criar uma sessão.",
          variant: "destructive"
        });
        return;
      }

      const session = await createSession(user.id, finalQuestions.map(q => q.id), "Mix de Todos os Tópicos");

      if (session) {
        navigate(`/questions/${session.id}`);
      } else {
        throw new Error('Erro ao criar sessão de estudo');
      }
    } catch (error) {
      toast({
        title: "Erro ao preparar sessão de estudo",
        description: "Ocorreu um erro ao preparar a sessão de estudo.",
        variant: "destructive"
      });
    } finally {
      setLoadingState(false);
      setDialogOpen(false);
    }
  };
  const navigateToSchedule = () => {
    navigate('/schedule');
  };
  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };
  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };
  if (hasNoStudies) {
    return <motion.div initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      delay: 0.3
    }}>
        <Card className="rounded-lg border-2 border-black bg-white shadow-card-sm">
          <div className="flex items-center justify-between p-4 sm:p-6 border-b-2 border-black">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-hackathon-red rounded-full border-2 border-black">
                <Flame className="h-6 w-6 text-white" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">Estudos do Dia</h2>
            </div>
          </div>

          <Suspense fallback={<div className="h-16 bg-gray-100 rounded-lg animate-pulse"></div>}>
            <FlashcardDailySummary />
          </Suspense>

          <div className="flex flex-col items-center justify-center py-10 text-center p-4 sm:p-6">
            <div className="p-4 rounded-full bg-gray-50 mb-4 border-2 border-black">
              <Calendar className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-medium text-gray-700 mb-2">
              Nenhum tema de estudo agendado para hoje
            </h3>
            <p className="text-gray-500 mb-6 max-w-md">
              Adicione temas ao seu cronograma de estudos para organizar sua rotina de aprendizado
            </p>
            <Button onClick={() => navigate('/schedule')} className="bg-black hover:bg-black/90 text-white gap-2 border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
              <PlusCircle className="h-4 w-4" />
              Adicionar ao Cronograma
            </Button>
          </div>
        </Card>
      </motion.div>;
  }
  return <motion.div initial={{
    opacity: 0,
    y: 20
  }} animate={{
    opacity: 1,
    y: 0
  }} transition={{
    delay: 0.3
  }}>
      <Card className="rounded-lg border-2 border-black bg-white shadow-card-sm">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6 p-4 sm:p-6 border-b-2 border-black">
          <div className="flex items-center gap-3">
            <div className="p-2 sm:p-3 bg-hackathon-red rounded-full border-2 border-black">
              <Flame className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Estudos do Dia</h2>
          </div>

          <div className="flex items-center gap-3 w-full sm:w-auto">
            {todayStudies?.totalHours && <div className="flex items-center gap-1 text-black bg-hackathon-yellow p-1.5 sm:p-2 rounded-full text-xs sm:text-sm border-2 border-black font-bold ml-auto sm:ml-0">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                <span>{formatTotalTime(todayStudies.totalHours)}</span>
              </div>}
          </div>
        </div>

        <Suspense fallback={<div className="h-16 bg-gray-100 rounded-lg animate-pulse"></div>}>
          <FlashcardDailySummary />
        </Suspense>

        <div className="space-y-3 p-4 sm:p-6 pt-0 sm:pt-0">
          {currentTopics.map((topic, index) => <motion.div key={`${topic.id}-${buttonUpdateKey}`} initial={{
          opacity: 0,
          y: 10
        }} animate={{
          opacity: 1,
          y: 0
        }} transition={{
          delay: 0.1 * index
        }} className={`p-3 sm:p-4 rounded-lg border-2 border-black shadow-card-xs ${topic.study_status === 'completed' ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-white'}`}>
              <div className="flex-1">
                <div className="flex justify-between items-start mb-1">
                  <div className="flex items-center gap-2 flex-wrap">
                    <h3 className="font-bold text-gray-800 text-sm sm:text-base line-clamp-2">
                      {getTopicTitle(topic)}
                    </h3>
                    <span className="text-xs sm:text-sm font-medium px-2 py-0.5 bg-hackathon-yellow rounded-full text-black whitespace-nowrap border-2 border-black">
                      {formatDuration(topic.duration)}
                    </span>
                    {/* Indicador de origem */}
                    <span className={`text-xs font-medium px-2 py-0.5 rounded-full whitespace-nowrap border-2 border-black ${
                      topic.is_manual
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-green-100 text-green-800 border-green-300'
                    }`}>
                      {topic.is_manual ? '📝 Manual' : '🎯 Plataforma'}
                    </span>
                  </div>
                </div>

                <p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-1">
                  {topic.activity}
                </p>

                <div className="flex flex-wrap justify-between items-center mt-3 w-full">
                  {topic.study_status === 'completed' ? <span className="text-xs sm:text-sm text-green-600 flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                      <span className="line-clamp-1">
                        Estudado {topic.next_revision_date && `(Próxima: ${formatNextRevisionDate(topic.next_revision_date)})`}
                      </span>
                    </span> : <div className="flex flex-row gap-2 w-full overflow-hidden">
                      <Button variant="outline" size="sm" className="text-xs sm:text-sm text-black hover:text-black/90 border-2 border-black rounded-md hover:bg-gray-50 transition-colors flex-1 truncate" onClick={e => {
                  e.stopPropagation();
                  handleMarkAsStudied(topic.id, getTopicTitle(topic));
                }}>
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                        <span className="whitespace-nowrap">Marcar estudo</span>
                      </Button>

                      {/* Mostrar botão Praticar apenas para tópicos da plataforma */}
                      {!topic.is_manual && (
                        <Button size="sm" className="text-xs sm:text-sm font-bold text-black bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all flex-1 truncate" onClick={() => handlePracticeQuestions(topic)} disabled={loadingState || loadingQuestions}>
                          {loadingQuestions ? <span>Carregando...</span> : <>
                              <BookOpenCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                              <span className="whitespace-nowrap">Praticar</span>
                            </>}
                        </Button>
                      )}
                    </div>}
                </div>
              </div>
            </motion.div>)}
        </div>

        {totalTopics > TOPICS_PER_PAGE && <div className="mt-2 sm:mt-4 flex justify-center p-4 sm:p-6 pt-0 sm:pt-0 border-t-2 border-black">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={currentPage === 1} className={`${currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} border-2 border-black bg-white hover:bg-gray-50 text-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}>
                <ChevronLeft className="h-4 w-4" />
                <span className="ml-1 hidden sm:inline">Anterior</span>
              </Button>

              <div className="flex items-center px-3 py-1.5 rounded-md text-xs sm:text-sm font-medium">
                {isMobile ? <span>{currentPage}/{totalPages}</span> : <span>Página {currentPage} de {totalPages}</span>}
              </div>

              <Button variant="outline" size="sm" onClick={handleNextPage} disabled={currentPage === totalPages} className={`${currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"} border-2 border-black bg-white hover:bg-gray-50 text-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}>
                <span className="mr-1 hidden sm:inline">Próximo</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>}

        {/* Mostrar botão "Estudar Todos os Tópicos" apenas se há tópicos da plataforma */}
        {platformTopics.length > 0 && (
          <div className={`${totalTopics > TOPICS_PER_PAGE ? 'mt-0' : 'mt-2 sm:mt-4'} flex justify-center p-4 sm:p-6 pt-0 sm:pt-0 ${totalTopics > TOPICS_PER_PAGE ? '' : 'border-t-2 border-black'}`}>
            <Button onClick={handleStudyAllTopics} className="bg-black hover:bg-black/90 text-white px-4 sm:px-6 py-1.5 sm:py-2 rounded-full shadow-button border-2 border-black hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm h-auto font-bold" disabled={loadingQuestions}>
              {loadingQuestions ? <span>Carregando...</span> : <>
                  <Shuffle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  Estudar Todos os Tópicos ({platformTopics.length})
                </>}
            </Button>
          </div>
        )}

        {/* Mostrar informação quando há apenas estudos manuais */}
        {platformTopics.length === 0 && manualTopics.length > 0 && (
          <div className="mt-2 sm:mt-4 p-4 sm:p-6 pt-0 sm:pt-0 border-t-2 border-black">
            <div className="text-center p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
              <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm text-blue-800 font-medium">
                Hoje você tem apenas estudos manuais agendados
              </p>
              <p className="text-xs text-blue-600 mt-1">
                Estudos manuais não possuem questões para praticar na plataforma
              </p>
            </div>
          </div>
        )}

        <ConfirmStudyDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen} onConfirm={handleConfirmStudied} topicName={selectedTopicName} />

        <AlertDialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
          <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
            <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-green-100">
              <div className="flex items-center gap-3">
                <div className="bg-white p-2 rounded-full border-2 border-black">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <AlertDialogTitle className="text-xl font-bold text-black">
                  {isLastRevision ? "Revisões Concluídas" : "Tópico estudado"}
                </AlertDialogTitle>
              </div>
            </AlertDialogHeader>

            <div className="p-4 sm:p-6">
              <AlertDialogDescription className="text-base text-gray-700">
                {studySuccessMessage}
              </AlertDialogDescription>

              {nextRevisionInfo && <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">
                    Próxima Revisão Agendada
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                      <span>
                        <strong>Data:</strong> {nextRevisionInfo.date} ({nextRevisionInfo.dayOfWeek})
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-blue-600" />
                      <span>
                        <strong>Em:</strong> {nextRevisionInfo.daysUntil} dias
                      </span>
                    </div>
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                      <span>
                        <strong>Revisão:</strong> {formatRevisionNumber(nextRevisionInfo.revisionNumber)}
                      </span>
                    </div>
                  </div>
                </div>}

              <AlertDialogFooter className="flex justify-end mt-6">
                <AlertDialogAction className="bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
                  OK
                </AlertDialogAction>
              </AlertDialogFooter>
            </div>
          </AlertDialogContent>
        </AlertDialog>

        <Suspense fallback={null}>
          <StudyOptionsDialog
            open={dialogOpen}
            onOpenChange={setDialogOpen}
            maxQuestions={maxQuestions}
            minQuestions={allTopicsData.length > 0 ? allTopicsData.length : 1}
            availableYears={availableYears}
            availableInstitutions={availableInstitutions}
            onStartStudy={handleStartStudy}
            totalTopics={allTopicsData.length}
            specialtyId={allTopicsData[0]?.topic?.specialtyId}
            themeId={allTopicsData[0]?.topic?.themeId}
            focusId={allTopicsData[0]?.topic?.focusId}
            allTopicsData={allTopicsData}
          />
        </Suspense>
      </Card>
    </motion.div>;
};
export default TodayStudies;