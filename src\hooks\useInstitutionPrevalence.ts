import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface PrevalenceData {
  specialty_id: string;
  specialty_name: string;
  theme_id: string;
  theme_name: string;
  focus_id: string;
  focus_name: string;
  question_count: number;
  percentage: number;
}

export interface InstitutionStats {
  institution_id: string;
  institution_name: string;
  total_questions: number;
  specialties: PrevalenceData[];
  themes: PrevalenceData[];
  focuses: PrevalenceData[];
}

export interface PrevalenceFilters {
  institutionIds: string[];
  startYear?: number;
  endYear?: number;
  domain?: string;
}

export const useInstitutionPrevalence = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculatePrevalence = async (filters: PrevalenceFilters): Promise<InstitutionStats[]> => {

    setIsLoading(true);
    setError(null);

    try {
      const results: InstitutionStats[] = [];

      for (const institutionId of filters.institutionIds) {

        // Buscar informações da instituição
        const { data: institution, error: institutionError } = await supabase
          .from('exam_locations')
          .select('id, name')
          .eq('id', institutionId)
          .single();

        if (institutionError) {
          console.error('❌ [useInstitutionPrevalence] Erro ao buscar instituição:', institutionError);
          throw new Error(`Erro ao buscar instituição: ${institutionError.message}`);
        }



        // Construir query base para questões
        let questionsQuery = supabase
          .from('questions')
          .select(`
            id,
            specialty_id,
            theme_id,
            focus_id,
            exam_year
          `)
          .eq('exam_location', institutionId);

        // Aplicar filtros de ano
        if (filters.startYear) {
          questionsQuery = questionsQuery.gte('exam_year', filters.startYear);
        }
        if (filters.endYear) {
          questionsQuery = questionsQuery.lte('exam_year', filters.endYear);
        }

        // Aplicar filtro de domínio
        if (filters.domain) {
          questionsQuery = questionsQuery.eq('knowledge_domain', filters.domain);
        }



        const { data: questions, error: questionsError } = await questionsQuery;

        if (questionsError) {
          console.error('❌ [useInstitutionPrevalence] Erro ao buscar questões:', questionsError);
          throw new Error(`Erro ao buscar questões: ${questionsError.message}`);
        }

        const totalQuestions = questions?.length || 0;

        if (totalQuestions === 0) {
          results.push({
            institution_id: institutionId,
            institution_name: institution.name,
            total_questions: 0,
            specialties: [],
            themes: [],
            focuses: []
          });
          continue;
        }

        // Buscar nomes das categorias
        const specialtyIds = [...new Set(questions?.map(q => q.specialty_id).filter(Boolean))];
        const themeIds = [...new Set(questions?.map(q => q.theme_id).filter(Boolean))];
        const focusIds = [...new Set(questions?.map(q => q.focus_id).filter(Boolean))];

        const [specialtyNames, themeNames, focusNames] = await Promise.all([
          specialtyIds.length > 0 ? supabase
            .from('study_categories')
            .select('id, name')
            .in('id', specialtyIds) : Promise.resolve({ data: [] }),
          themeIds.length > 0 ? supabase
            .from('study_categories')
            .select('id, name')
            .in('id', themeIds) : Promise.resolve({ data: [] }),
          focusIds.length > 0 ? supabase
            .from('study_categories')
            .select('id, name')
            .in('id', focusIds) : Promise.resolve({ data: [] })
        ]);

        // Criar mapas de ID para nome
        const specialtyNameMap = new Map(specialtyNames.data?.map(s => [s.id, s.name]) || []);
        const themeNameMap = new Map(themeNames.data?.map(t => [t.id, t.name]) || []);
        const focusNameMap = new Map(focusNames.data?.map(f => [f.id, f.name]) || []);

        // Calcular prevalência por especialidade
        const specialtyStats = new Map<string, { name: string; count: number }>();
        const themeStats = new Map<string, { name: string; count: number }>();
        const focusStats = new Map<string, { name: string; count: number }>();

        questions?.forEach(question => {
          // Especialidades
          if (question.specialty_id) {
            const key = question.specialty_id;
            const name = specialtyNameMap.get(key) || 'Desconhecido';
            const current = specialtyStats.get(key) || { name, count: 0 };
            specialtyStats.set(key, { ...current, count: current.count + 1 });
          }

          // Temas
          if (question.theme_id) {
            const key = question.theme_id;
            const name = themeNameMap.get(key) || 'Desconhecido';
            const current = themeStats.get(key) || { name, count: 0 };
            themeStats.set(key, { ...current, count: current.count + 1 });
          }

          // Focos
          if (question.focus_id) {
            const key = question.focus_id;
            const name = focusNameMap.get(key) || 'Desconhecido';
            const current = focusStats.get(key) || { name, count: 0 };
            focusStats.set(key, { ...current, count: current.count + 1 });
          }
        });

        // Converter para arrays ordenados por prevalência
        const specialties: PrevalenceData[] = Array.from(specialtyStats.entries())
          .map(([id, data]) => ({
            specialty_id: id,
            specialty_name: data.name,
            theme_id: '',
            theme_name: '',
            focus_id: '',
            focus_name: '',
            question_count: data.count,
            percentage: (data.count / totalQuestions) * 100
          }))
          .sort((a, b) => b.percentage - a.percentage);

        const themes: PrevalenceData[] = Array.from(themeStats.entries())
          .map(([id, data]) => ({
            specialty_id: '',
            specialty_name: '',
            theme_id: id,
            theme_name: data.name,
            focus_id: '',
            focus_name: '',
            question_count: data.count,
            percentage: (data.count / totalQuestions) * 100
          }))
          .sort((a, b) => b.percentage - a.percentage);

        // ✅ CORREÇÃO: Buscar hierarquia real de cada foco
        const focusHierarchyMap = new Map();

        // Buscar hierarquia de cada foco de forma mais simples
        if (focusIds.length > 0) {

          // Buscar focos e seus temas pais
          const { data: focusData } = await supabase
            .from('study_categories')
            .select('id, name, parent_id')
            .in('id', focusIds);

          if (focusData) {
            // Buscar temas únicos
            const themeIds = [...new Set(focusData.map(f => f.parent_id).filter(Boolean))];
            const { data: themeData } = await supabase
              .from('study_categories')
              .select('id, name, parent_id')
              .in('id', themeIds);

            // Buscar especialidades únicas
            const specialtyIds = [...new Set(themeData?.map(t => t.parent_id).filter(Boolean) || [])];
            const { data: specialtyData } = await supabase
              .from('study_categories')
              .select('id, name')
              .in('id', specialtyIds);

            // Criar mapas
            const themeMap = new Map(themeData?.map(t => [t.id, t]) || []);
            const specialtyMap = new Map(specialtyData?.map(s => [s.id, s]) || []);

            // Montar hierarquia
            focusData.forEach(focus => {
              const theme = themeMap.get(focus.parent_id);
              const specialty = theme ? specialtyMap.get(theme.parent_id) : null;

              focusHierarchyMap.set(focus.id, {
                focus_id: focus.id,
                focus_name: focus.name,
                theme_id: theme?.id || '',
                theme_name: theme?.name || '',
                specialty_id: specialty?.id || '',
                specialty_name: specialty?.name || ''
              });


            });
          }
        }



        const focuses: PrevalenceData[] = Array.from(focusStats.entries())
          .map(([id, data]) => {
            const hierarchy = focusHierarchyMap.get(id) || {
              focus_id: id,
              focus_name: data.name,
              theme_id: '',
              theme_name: '',
              specialty_id: '',
              specialty_name: ''
            };

            return {
              specialty_id: hierarchy.specialty_id,
              specialty_name: hierarchy.specialty_name,
              theme_id: hierarchy.theme_id,
              theme_name: hierarchy.theme_name,
              focus_id: id,
              focus_name: data.name,
              question_count: data.count,
              percentage: (data.count / totalQuestions) * 100
            };
          })
          .sort((a, b) => b.percentage - a.percentage);





        results.push({
          institution_id: institutionId,
          institution_name: institution.name,
          total_questions: totalQuestions,
          specialties,
          themes,
          focuses
        });
      }



      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getInstitutions = async (): Promise<{ id: string; name: string }[]> => {
    const { data, error } = await supabase
      .from('exam_locations')
      .select('id, name')
      .order('name');

    if (error) {
      throw new Error(`Erro ao buscar instituições: ${error.message}`);
    }

    return data || [];
  };

  return {
    calculatePrevalence,
    getInstitutions,
    isLoading,
    error
  };
};
