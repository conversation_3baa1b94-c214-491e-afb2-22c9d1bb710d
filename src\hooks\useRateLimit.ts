import React, { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface RateLimitResult {
  allowed: boolean;
  current_count: number;
  max_requests: number;
  window_minutes: number;
  reset_time: string;
  remaining_requests: number;
}

interface RateLimitState {
  isChecking: boolean;
  lastResult: RateLimitResult | null;
  error: string | null;
}

type ActionType = 'questions' | 'sessions' | 'flashcards' | 'ai_requests';

export const useRateLimit = () => {
  const [state, setState] = useState<RateLimitState>({
    isChecking: false,
    lastResult: null,
    error: null,
  });

  const checkRateLimit = useCallback(async (actionType: ActionType): Promise<boolean> => {
    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      // Mapear tipo de ação para função RPC correspondente
      const functionMap = {
        questions: 'check_questions_rate_limit',
        sessions: 'check_sessions_rate_limit',
        flashcards: 'check_flashcards_rate_limit',
        ai_requests: 'check_ai_rate_limit',
      };

      const { data, error } = await supabase.rpc(functionMap[actionType], {
        p_user_id: user.id,
      });

      if (error) {
        console.error(`❌ [useRateLimit] Error checking ${actionType} rate limit:`, error);
        throw error;
      }

      const result = data as RateLimitResult;
      setState(prev => ({ ...prev, lastResult: result, isChecking: false }));

      // Handle rate limit exceeded silently
      if (!result.allowed) {
        // Rate limit exceeded - handled silently
      }

      return result.allowed;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        isChecking: false
      }));

      // Em caso de erro, permitir a ação (fail-safe)
      return true;
    }
  }, []);

  const checkQuestionsLimit = useCallback(() => checkRateLimit('questions'), [checkRateLimit]);
  const checkSessionsLimit = useCallback(() => checkRateLimit('sessions'), [checkRateLimit]);
  const checkFlashcardsLimit = useCallback(() => checkRateLimit('flashcards'), [checkRateLimit]);
  const checkAILimit = useCallback(() => checkRateLimit('ai_requests'), [checkRateLimit]);

  const getRemainingRequests = useCallback((actionType: ActionType): number => {
    return state.lastResult?.remaining_requests || 0;
  }, [state.lastResult]);

  const getResetTime = useCallback((): Date | null => {
    return state.lastResult?.reset_time ? new Date(state.lastResult.reset_time) : null;
  }, [state.lastResult]);

  const isNearLimit = useCallback((actionType: ActionType, threshold: number = 0.8): boolean => {
    if (!state.lastResult) return false;
    const usageRatio = state.lastResult.current_count / state.lastResult.max_requests;
    return usageRatio >= threshold;
  }, [state.lastResult]);

  return {
    // Estado
    isChecking: state.isChecking,
    lastResult: state.lastResult,
    error: state.error,

    // Funções de verificação
    checkRateLimit,
    checkQuestionsLimit,
    checkSessionsLimit,
    checkFlashcardsLimit,
    checkAILimit,

    // Utilitários
    getRemainingRequests,
    getResetTime,
    isNearLimit,
  };
};

// Hook específico para questões (mais usado)
export const useQuestionsRateLimit = () => {
  const { checkQuestionsLimit, isChecking, lastResult, error } = useRateLimit();

  return {
    checkLimit: checkQuestionsLimit,
    isChecking,
    remaining: lastResult?.remaining_requests || 0,
    isNearLimit: lastResult ? (lastResult.current_count / lastResult.max_requests) >= 0.8 : false,
    error,
  };
};

// Hook específico para IA (crítico)
export const useAIRateLimit = () => {
  const { checkAILimit, isChecking, lastResult, error } = useRateLimit();

  return {
    checkLimit: checkAILimit,
    isChecking,
    remaining: lastResult?.remaining_requests || 0,
    isNearLimit: lastResult ? (lastResult.current_count / lastResult.max_requests) >= 0.8 : false,
    resetTime: lastResult?.reset_time ? new Date(lastResult.reset_time) : null,
    error,
  };
};

// Utilitário para labels amigáveis
function getActionLabel(actionType: ActionType): string {
  const labels = {
    questions: 'questões',
    sessions: 'sessões de estudo',
    flashcards: 'flashcards',
    ai_requests: 'requisições de IA',
  };
  return labels[actionType];
}

// HOC para proteger componentes com rate limiting
export function withRateLimit<P extends object>(
  Component: React.ComponentType<P>,
  actionType: ActionType,
  showWarning: boolean = true
) {
  return function WrappedComponent(props: P) {
    const { checkRateLimit, isNearLimit } = useRateLimit();

    React.useEffect(() => {
      if (showWarning && isNearLimit(actionType)) {
        // Near rate limit - handled silently
      }
    }, [isNearLimit]);

    return React.createElement(Component, props);
  };
}
