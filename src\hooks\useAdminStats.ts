
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface AdminStats {
  userCount: number;
  questionCount: number;
  permissionCount: number;
  loading: boolean;
  error: Error | null;
}

export const useAdminStats = (): AdminStats => {
  const { 
    data: userCount = 0, 
    isLoading: isLoadingUsers,
    error: userError 
  } = useQuery({
    queryKey: ['admin-stats-users'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });
      
      if (error) throw error;
      return count || 0;
    }
  });

  const { 
    data: questionCount = 0, 
    isLoading: isLoadingQuestions,
    error: questionError 
  } = useQuery({
    queryKey: ['admin-stats-questions'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('questions')
        .select('*', { count: 'exact', head: true });
      
      if (error) throw error;
      return count || 0;
    }
  });

  const { 
    data: permissionCount = 0, 
    isLoading: isLoadingPermissions,
    error: permissionError 
  } = useQuery({
    queryKey: ['admin-stats-permissions'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('admin_permissions')
        .select('*', { count: 'exact', head: true });
      
      if (error) throw error;
      return count || 0;
    }
  });

  const loading = isLoadingUsers || isLoadingQuestions || isLoadingPermissions;
  const error = userError || questionError || permissionError;

  return {
    userCount,
    questionCount,
    permissionCount,
    loading,
    error: error as Error | null
  };
};
