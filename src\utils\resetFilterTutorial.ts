import { supabase } from '@/integrations/supabase/client';

/**
 * Função para resetar o tutorial de filtros para um usuário específico
 * Útil para testes e desenvolvimento
 */
export const resetFilterTutorial = async (userId: string) => {
  try {
    console.log("🔄 [resetFilterTutorial] Resetting filter tutorial for user:", userId);

    const { error } = await supabase
      .from('user_preferences')
      .update({ 
        filter_tutorial_completed: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error("❌ [resetFilterTutorial] Error resetting tutorial:", error);
      return false;
    }

    console.log("✅ [resetFilterTutorial] Filter tutorial reset successfully");
    return true;
  } catch (error) {
    console.error("❌ [resetFilterTutorial] Unexpected error:", error);
    return false;
  }
};

/**
 * Função para resetar o tutorial de filtros para o usuário atual
 * Pode ser chamada no console do navegador para testes
 */
export const resetCurrentUserFilterTutorial = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    console.error("❌ No user logged in");
    return false;
  }

  return resetFilterTutorial(user.id);
};

// Expor função globalmente para facilitar testes
if (typeof window !== 'undefined') {
  (window as any).resetFilterTutorial = resetCurrentUserFilterTutorial;
}
