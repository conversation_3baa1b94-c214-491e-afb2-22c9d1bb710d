
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders } from "../_shared/cors.ts";

const openAiKey = Deno.env.get("OPENAI_API_KEY");
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { questionText, alternatives, themes, focuses } = await req.json();

    if (!questionText || !themes || !focuses || themes.length === 0) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!openAiKey) {
      return new Response(
        JSON.stringify({ error: "OpenAI API key not configured" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
    
    // Prepare the content for the OpenAI request
    const themesFormatted = themes.map((t) => `- ID: ${t.id}, Name: ${t.name}`).join('\n');
    const focusesFormatted = focuses.map((f) => `- ID: ${f.id}, Name: ${f.name}, ThemeID: ${f.themeId}`).join('\n');
    const alternativesFormatted = alternatives && alternatives.length ? 
      alternatives.map((a, i) => `${String.fromCharCode(65 + i)}) ${a}`).join('\n\n') :
      'No alternatives provided';

    // Make request to OpenAI API
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${openAiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are a medical content classifier specialized in categorizing medical questions 
            into their appropriate themes and focuses. You will be given a question text with its alternatives 
            and must determine which theme and focus it belongs to from the provided lists. Your response must 
            follow the exact JSON format specified.`
          },
          {
            role: "user",
            content: `Analyze the following question and determine the most appropriate theme and focus:
            
            Question: ${questionText}
            
            Alternatives:
            ${alternativesFormatted}
            
            Available themes:
            ${themesFormatted}
            
            Available focuses:
            ${focusesFormatted}
            
            Only respond with a JSON object in this format:
            {
              "themeId": "the-theme-id",
              "themeName": "Theme Name",
              "focusId": "the-focus-id",
              "focusName": "Focus Name",
              "confidence": 0.9  // A value between 0 and 1 representing your confidence
            }
            
            The focus MUST belong to the theme you select. Make sure the IDs match correctly.`
          }
        ],
        temperature: 0.2,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("OpenAI API error:", response.status, errorText);
      throw new Error(`OpenAI API error: ${response.status} ${errorText}`);
    }

    const aiResponse = await response.json();
    let suggestion;

    try {
      if (!aiResponse.choices || !aiResponse.choices[0] || !aiResponse.choices[0].message || !aiResponse.choices[0].message.content) {
        throw new Error("Invalid AI response format");
      }

      const content = aiResponse.choices[0].message.content;
      suggestion = JSON.parse(content);
      
      // Basic validation
      if (!suggestion.themeId || !suggestion.focusId) {
        throw new Error("Invalid AI response format: missing themeId or focusId");
      }
      
      // Create a mapping of focusId to themeId for quick validation
      const focusThemeMap = {};
      focuses.forEach((f) => {
        focusThemeMap[f.id] = f.themeId;
      });
      
      // Verify that the focus belongs to the selected theme
      if (focusThemeMap[suggestion.focusId] !== suggestion.themeId) {
        console.log(`Focus ID ${suggestion.focusId} doesn't belong to theme ${suggestion.themeId}.`);
        
        // Find valid focuses for the selected theme
        const validFocuses = focuses.filter((f) => f.themeId === suggestion.themeId);
        
        if (validFocuses.length > 0) {
          // Use the first valid focus as a fallback
          const fallbackFocus = validFocuses[0];
          console.log(`Using fallback focus: ${fallbackFocus.id}`);
          
          suggestion.focusId = fallbackFocus.id;
          suggestion.focusName = fallbackFocus.name;
          // Reduce confidence when using fallback
          suggestion.confidence = Math.max(0.6, suggestion.confidence * 0.8);
        } else {
          throw new Error("No valid focuses found for the selected theme");
        }
      }
    } catch (e) {
      console.error("Error parsing AI response:", e);
      return new Response(
        JSON.stringify({ error: `Failed to parse AI response: ${e.message}` }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    return new Response(JSON.stringify({ suggestion }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });

  } catch (error) {
    console.error("Error in analyze-question-hierarchy function:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
