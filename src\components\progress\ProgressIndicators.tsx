
import { Card, CardContent } from "@/components/ui/card";
import { Target, Calendar, Clock, CalendarDays } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

interface ProgressIndicatorsProps {
  stats: {
    totalQuestions: number;
    correctAnswers: number;
    streakDays: number;
    averageTimePerQuestion: number;
  };
}

export const ProgressIndicators = ({ stats }: ProgressIndicatorsProps) => {
  const accuracy = stats.totalQuestions > 0 
    ? (stats.correctAnswers / stats.totalQuestions) * 100 
    : 0;

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"
    >
      {/* Accuracy Card */}
      <Card className="border-2 border-black shadow-card-sm bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-hackathon-yellow border-b-2 border-black flex items-center justify-center">
              <Target className="h-8 w-8 text-black" />
            </div>
            <div className="p-4 text-center">
              <p className="text-sm font-medium text-gray-700">Precisão Geral</p>
              <div className="flex items-end justify-center gap-1 mt-1">
                <p className="text-3xl font-bold text-black">{accuracy.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Streak Card */}
      <Card className="border-2 border-black shadow-card-sm bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-hackathon-red border-b-2 border-black flex items-center justify-center">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <div className="p-4 text-center">
              <p className="text-sm font-medium text-gray-700">Sequência de Estudos</p>
              <div className="flex items-end justify-center gap-1 mt-1">
                <p className="text-3xl font-bold text-black">{stats.streakDays}</p>
                <p className="text-lg text-gray-600 mb-1">dias</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Average Time Card */}
      <Card className="border-2 border-black shadow-card-sm bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-hackathon-green border-b-2 border-black flex items-center justify-center">
              <Clock className="h-8 w-8 text-black" />
            </div>
            <div className="p-4 text-center relative">
              <div className="flex items-center justify-center gap-2">
                <p className="text-sm font-medium text-gray-700">Tempo Médio por Questão</p>
                <Badge className="bg-hackathon-yellow text-black absolute top-1 right-2 border border-black">
                  <CalendarDays className="h-3 w-3 mr-1" />
                  Cronograma
                </Badge>
              </div>
              <div className="flex items-end justify-center gap-1 mt-2">
                <p className="text-3xl font-bold text-black">{Math.round(stats.averageTimePerQuestion) || 0}</p>
                <p className="text-lg text-gray-600 mb-1">sec</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
