import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { diagram_definition, title } = req.body;

  if (!diagram_definition) {
    return res.status(400).json({ error: 'diagram_definition is required' });
  }

  try {
    // Usar a ferramenta render-mermaid do sistema
    const response = await fetch('http://localhost:3000/render-mermaid', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        diagram_definition,
        title: title || 'Mermaid Diagram'
      })
    });

    if (response.ok) {
      const result = await response.text();
      res.setHeader('Content-Type', 'text/html');
      return res.status(200).send(result);
    } else {
      throw new Error('Falha na renderização do Mermaid');
    }
  } catch (error) {
    console.error('Erro ao renderizar Mermaid:', error);
    
    // Fallback: retornar HTML com código Mermaid para renderização client-side
    const fallbackHtml = `
      <div class="mermaid-fallback p-4">
        <div class="bg-blue-50 border border-blue-200 rounded p-3 mb-3">
          <p class="text-blue-700 text-sm font-medium">Renderização via Mermaid.js</p>
        </div>
        <div class="mermaid">
${diagram_definition}
        </div>
        <script>
          // Inicializar Mermaid se disponível
          if (typeof mermaid !== 'undefined') {
            mermaid.initialize({ startOnLoad: true, theme: 'default' });
            mermaid.init();
          }
        </script>
      </div>
    `;
    
    res.setHeader('Content-Type', 'text/html');
    return res.status(200).send(fallbackHtml);
  }
}
