
import React, { useState } from "react";
import { motion } from "framer-motion";
import { ChevronRight, Monitor, Search, CheckCircle, Award, MessageSquare, BookOpen, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { StudyPlatformTutorial } from "@/components/study/StudyPlatformTutorial";

const HowItWorks = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "Encontre questões específicas",
      description: "Utilize nosso sistema de busca avançado para encontrar questões específicas para seu estudo.",
      icon: Search,
      details: [
        "Acesse a seção \"Questões\" no menu principal",
        "Use os filtros para selecionar por especialidade, tema ou nível de dificuldade",
        "Digite palavras-chave na barra de pesquisa para encontrar conteúdos específicos",
        "Salve suas buscas frequentes para acesso rápido no futuro"
      ],
      demoComponent: () => (
        <div className="p-4 bg-white border-2 border-black rounded-lg">
          <div className="flex gap-3 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Buscar questões por palavras-chave..."
                  className="w-full pl-10 pr-4 py-2 border-2 border-gray-300 rounded-md focus:border-primary focus:outline-none"
                />
              </div>
            </div>
            <Button variant="outline" className="border-2 border-gray-300">
              Filtros <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          <div className="space-y-3">
            <div className="flex gap-2 flex-wrap">
              <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium border border-blue-300">
                Cardiologia
              </div>
              <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium border border-green-300">
                Pediatria
              </div>
              <div className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium border border-purple-300">
                Oftalmologia
              </div>
              <div className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium border border-red-300">
                AMRIGS 2023
              </div>
            </div>

            <div className="animate-pulse">
              <div className="h-16 bg-gray-100 border-2 border-gray-200 rounded-md mb-3"></div>
              <div className="h-16 bg-gray-100 border-2 border-gray-200 rounded-md mb-3"></div>
              <div className="h-16 bg-gray-100 border-2 border-gray-200 rounded-md"></div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Resolva questões comentadas",
      description: "Teste seus conhecimentos com questões de exames anteriores e aprenda com explicações detalhadas.",
      icon: CheckCircle,
      details: [
        "Selecione questões de acordo com o tema que deseja estudar",
        "Leia atentamente cada questão e escolha sua resposta",
        "Confira imediatamente o resultado com explicação detalhada",
        "Revise os tópicos relacionados para aprofundar seu conhecimento"
      ],
      demoComponent: () => (
        <div className="p-4 bg-white border-2 border-black rounded-lg">
          <div className="mb-4 font-medium">
            Em paciente de 45 anos, com quadro de pneumonia comunitária, qual o antibiótico mais indicado para tratamento ambulatorial?
          </div>

          <div className="space-y-2 mb-6">
            {["Azitromicina", "Amoxicilina + Clavulanato", "Ciprofloxacino", "Ceftriaxona IV", "Ampicilina"].map((option, idx) => (
              <div
                key={idx}
                className={`p-3 border-2 rounded-md cursor-pointer transition-colors ${
                  idx === 1 ? "border-green-500 bg-green-50" : "border-gray-200 hover:border-gray-400"
                }`}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3 ${
                    idx === 1 ? "border-green-500 bg-green-500 text-white" : "border-gray-400"
                  }`}>
                    {idx === 1 && <CheckCircle className="h-3 w-3" />}
                  </div>
                  {option}
                </div>
              </div>
            ))}
          </div>

          <div className="p-3 bg-green-50 border-2 border-green-200 rounded-md">
            <p className="font-medium text-green-800">Explicação:</p>
            <p className="text-sm text-gray-700">
              A Amoxicilina + Clavulanato é a escolha preferencial para tratamento ambulatorial
              de pneumonia comunitária em adultos com comorbidades, devido ao seu espectro de
              ação contra patógenos respiratórios comuns...
            </p>
          </div>
        </div>
      )
    },
    {
      title: "Acompanhe seu progresso",
      description: "Visualize estatísticas detalhadas sobre seu desempenho e identifique áreas para melhorar.",
      icon: Award,
      details: [
        "Acesse a aba \"Progresso\" para visualizar seu desempenho",
        "Acompanhe gráficos de evolução por especialidade e tema",
        "Identifique seus pontos fortes e áreas que precisam de mais atenção",
        "Estabeleça metas de estudo baseadas nos dados estatísticos"
      ],
      demoComponent: () => (
        <div className="p-4 bg-white border-2 border-black rounded-lg">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 bg-hackathon-yellow/20 border-2 border-hackathon-yellow rounded-md text-center">
              <div className="text-3xl font-bold">78%</div>
              <div className="text-sm text-gray-600">Acertos gerais</div>
            </div>
            <div className="p-3 bg-hackathon-green/20 border-2 border-hackathon-green rounded-md text-center">
              <div className="text-3xl font-bold">32</div>
              <div className="text-sm text-gray-600">Dias de estudo</div>
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Desempenho por especialidade</h4>
            {[
              { name: "Cardiologia", value: 85 },
              { name: "Pediatria", value: 67 },
              { name: "Oftalmologia", value: 92 }
            ].map((item, idx) => (
              <div key={idx} className="mb-2">
                <div className="flex justify-between text-xs mb-1">
                  <span>{item.name}</span>
                  <span className="font-medium">{item.value}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full rounded-full ${
                      item.value > 80 ? "bg-green-500" : item.value > 60 ? "bg-yellow-500" : "bg-red-500"
                    }`}
                    style={{ width: `${item.value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>

          <div className="h-28 bg-gray-100 rounded-md border-2 border-gray-200 flex items-center justify-center">
            <div className="text-gray-400 text-sm">Gráfico de evolução</div>
          </div>
        </div>
      )
    },
    {
      title: "Acesse comentários detalhados",
      description: "Cada questão possui comentários explicativos feitos por especialistas da área.",
      icon: MessageSquare,
      details: [
        "Após responder cada questão, acesse os comentários detalhados",
        "Leia explicações feitas por especialistas da área médica",
        "Encontre referências bibliográficas para aprofundar seus estudos",
        "Participe da comunidade adicionando seus próprios insights"
      ],
      demoComponent: () => (
        <div className="p-4 bg-white border-2 border-black rounded-lg">
          <div className="p-3 bg-blue-50 border-2 border-blue-200 rounded-md mb-4">
            <div className="flex items-start mb-2">
              <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="font-bold text-blue-600">A</span>
              </div>
              <div>
                <p className="font-medium">Análise da Questão</p>
                <p className="text-sm text-gray-700">
                  Esta questão avalia o conhecimento sobre antibioticoterapia empírica para pneumonia
                  adquirida na comunidade em pacientes ambulatoriais.
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-3 mb-3">
            <h4 className="font-medium mb-2 flex items-center">
              <MessageSquare className="h-4 w-4 mr-2" />
              Comentários da comunidade
            </h4>

            <div className="space-y-3">
              <div className="p-2 bg-gray-50 border border-gray-200 rounded-md">
                <div className="flex items-center mb-1">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                    <span className="font-bold text-purple-600 text-xs">M</span>
                  </div>
                  <div className="text-sm font-medium">Dr. Marcos Silva</div>
                </div>
                <p className="text-xs text-gray-700">
                  Lembrem-se que em pacientes idosos ou com comorbidades, a escolha empírica deve cobrir também patógenos atípicos...
                </p>
              </div>

              <div className="p-2 bg-gray-50 border border-gray-200 rounded-md">
                <div className="flex items-center mb-1">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                    <span className="font-bold text-green-600 text-xs">C</span>
                  </div>
                  <div className="text-sm font-medium">Dra. Carla Mendes</div>
                </div>
                <p className="text-xs text-gray-700">
                  Excelente questão! Vale ressaltar que o GOLD 2023 recomenda considerar também a resistência local aos antimicrobianos...
                </p>
              </div>
            </div>
          </div>

          <div className="flex w-full">
            <input
              type="text"
              placeholder="Adicione seu comentário..."
              className="flex-1 border-2 border-gray-300 rounded-l-md px-3 py-1 text-sm focus:outline-none focus:border-primary min-w-0"
            />
            <Button className="rounded-l-none bg-primary flex-shrink-0">Enviar</Button>
          </div>
        </div>
      )
    },
    {
      title: "Estude com um plano personalizado",
      description: "Nossa plataforma cria um cronograma de estudos adaptado às suas necessidades.",
      icon: BookOpen,
      details: [
        "Configure seu perfil com seus objetivos e especialidade-alvo",
        "A plataforma criará um cronograma personalizado baseado em suas necessidades",
        "Receba lembretes diários sobre os tópicos programados para estudo",
        "Ajuste seu cronograma conforme sua evolução e disponibilidade"
      ],
      demoComponent: () => (
        <div className="p-4 bg-white border-2 border-black rounded-lg">
          <div className="mb-4">
            <h4 className="font-medium text-sm mb-2">Cronograma Semanal</h4>

            <div className="border-2 border-gray-200 rounded-md overflow-hidden">
              <div className="bg-hackathon-yellow/20 p-2 border-b-2 border-gray-200">
                <span className="font-medium">Segunda-feira</span>
              </div>

              <div className="p-3 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="w-2 h-8 bg-green-500 rounded-full mr-3"></div>
                  <div>
                    <div className="font-medium">Cardiologia: Insuficiência Cardíaca</div>
                    <div className="text-xs text-gray-500 flex items-center">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Teoria
                      <span className="mx-2">•</span>
                      <span>08:00 - 09:30</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="w-2 h-8 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <div className="font-medium">Pediatria: Doenças Exantemáticas</div>
                    <div className="text-xs text-gray-500 flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Questões
                      <span className="mx-2">•</span>
                      <span>10:00 - 11:00</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-hackathon-yellow/20 p-2 border-b-2 border-gray-200">
                <span className="font-medium">Terça-feira</span>
              </div>

              <div className="p-3">
                <div className="flex items-center">
                  <div className="w-2 h-8 bg-purple-500 rounded-full mr-3"></div>
                  <div>
                    <div className="font-medium">Oftalmologia: Glaucoma</div>
                    <div className="text-xs text-gray-500 flex items-center">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Teoria
                      <span className="mx-2">•</span>
                      <span>14:00 - 15:30</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Button className="w-full bg-hackathon-green border-2 border-black hover:bg-hackathon-green/90 text-black font-medium">
            Ver cronograma completo
          </Button>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Return to home page on completion
      navigate("/");
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Get the current step's icon component
  const StepIcon = steps[currentStep].icon;

  return (
    <div className="min-h-screen bg-[#FEF7CD] flex flex-col">
      {/* Header */}
      <header className="container max-w-7xl mx-auto pt-6 px-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <div className="relative cursor-pointer" onClick={() => navigate("/")}>
              <div className="bg-white border-2 border-black px-4 py-1 shadow-card-sm">
                <span className="font-bold text-2xl tracking-tight">Med EVO</span>
              </div>
              <div className="absolute -right-2 -top-2">
                <span className="bg-hackathon-red text-white text-xs px-2 py-0.5 rounded border border-black font-bold">
                  beta
                </span>
              </div>
            </div>
          </div>

          <Button
            onClick={() => navigate("/")}
            className="bg-white border-2 border-black text-black hover:bg-white/90 px-4 py-1.5 rounded-md font-medium shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar para início
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="container max-w-7xl mx-auto px-4 py-12 flex-grow">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-black mb-4">Como funciona a plataforma</h1>
          <p className="text-xl max-w-2xl mx-auto text-gray-700">
            Conheça passo a passo como utilizar a Med EVO para maximizar seus estudos para residência médica
          </p>
        </div>

        {/* Mobile progress indicator (only visible on mobile) */}
        <div className="md:hidden flex justify-center mb-6">
          <div className="flex items-center gap-2">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep ? 'bg-hackathon-yellow border border-black' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-10 h-full">
          {/* Step Navigation - Only visible on desktop */}
          <div className="hidden md:block w-full md:w-1/3 space-y-4 md:max-h-[600px] md:overflow-y-auto p-2">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`flex items-start gap-3 p-3 rounded-lg ${currentStep === index ? 'bg-white border-2 border-black shadow-button' : 'border-2 border-transparent hover:bg-white/50'} transition-colors cursor-pointer`}
                onClick={() => setCurrentStep(index)}
              >
                <div className={`icon-container ${currentStep === index ? 'bg-hackathon-yellow' : 'bg-hackathon-yellow/50'}`}>
                  {React.createElement(step.icon, { className: "h-5 w-5" })}
                </div>
                <div>
                  <h3 className="font-bold">{step.title}</h3>
                  <p className="text-sm text-gray-700">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Step Content */}
          <div className="w-full md:w-2/3 flex flex-col">
            <div className="flex-grow">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="bg-white border-2 border-black rounded-xl p-6 shadow-card-sm"
              >
                <h2 className="text-2xl font-bold mb-6 flex items-center gap-3">
                  <div className="icon-container bg-hackathon-yellow md:hidden">
                    <StepIcon className="h-5 w-5" />
                  </div>
                  {steps[currentStep].title}
                </h2>

                <div className="relative">
                  <div className="mb-6">
                    {steps[currentStep].demoComponent()}
                  </div>
                  <div className="absolute -bottom-4 -right-4">
                    <div className="bg-black text-white text-xs px-3 py-1.5 rounded-md font-mono">
                      {
                        currentStep === 0 ? "buscarQuestao(residenciaMedica)" :
                        currentStep === 1 ? "resolverQuestao(nivelDificuldade)" :
                        currentStep === 2 ? "monitorarProgresso() { ... }" :
                        currentStep === 3 ? "lerComentario(questaoId)" :
                        "criarCronograma(usuario)"
                      }
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <p className="text-gray-700 text-lg">
                    {steps[currentStep].description}
                  </p>

                  <div className="mt-6">
                    <h3 className="font-bold text-lg mb-2">Como utilizar:</h3>
                    <ul className="list-disc pl-5 space-y-2 text-gray-700">
                      {steps[currentStep].details.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Call-to-action section at the end of the tour */}
                {currentStep === steps.length - 1 && (
                  <div className="mt-8 p-4 bg-hackathon-yellow/20 border-2 border-black rounded-lg">
                    <h3 className="text-lg font-bold mb-2">Experimente a plataforma agora!</h3>
                    <p className="text-sm text-gray-700">
                      Agora que você conhece as principais funcionalidades, está pronto para iniciar sua jornada de estudos com a Med EVO.
                    </p>
                  </div>
                )}
              </motion.div>
            </div>

            {/* Navigation Controls */}
            <div className="mt-8 flex justify-between">
              <Button
                onClick={prevStep}
                disabled={currentStep === 0}
                className={`${currentStep === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-hackathon-red hover:bg-hackathon-red/90'} border-2 border-black text-white rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}
              >
                Anterior
              </Button>
              <Button
                onClick={nextStep}
                className="bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all flex items-center gap-2"
              >
                {currentStep < steps.length - 1 ? 'Próximo' : 'Finalizar tour'}
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Compact horizontal footer */}
      <footer className="bg-hackathon-yellow py-3 border-t-2 border-black mt-auto">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="flex justify-center items-center gap-4">
            <a
              href="https://pedb.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/faviconx.webp"
                alt="PedBook"
                className="h-6"
              />
            </a>
            <a
              href="https://medunity.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/logo-med-unity-sem-fund.webp"
                alt="Med Unity"
                className="h-6"
              />
            </a>
            <span className="text-xs text-gray-600 ml-2">
              © 2025 Med EVO
            </span>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HowItWorks;
