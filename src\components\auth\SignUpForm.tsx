
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { supabase } from "@/integrations/supabase/client";
import { useFeedbackDialog } from "@/components/ui/feedback-dialog";
import { SignUpFormFields } from "./SignUpFormFields";
import { SignUpFormCheckboxes } from "./SignUpFormCheckboxes";
import { useNavigate } from "react-router-dom";

const formSchema = z.object({
  fullName: z.string().min(2, "Nome muito curto"),
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
  formationArea: z.string().min(2, "Área de formação muito curta"),
  graduationYear: z.string().min(4, "Ano de formação inválido"),
  type: z.enum(["student", "professional"]),
  isAdult: z.boolean().refine((val) => val === true, "Você deve ter mais de 17 anos"),
  acceptTerms: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
});

interface SignUpFormProps {
  onModeChange: () => void;
  onSuccess: () => void;
}

const SignUpForm = ({ onModeChange, onSuccess }: SignUpFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showFeedback } = useFeedbackDialog();
  const navigate = useNavigate();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      formationArea: "",
      graduationYear: "",
      type: "professional",
      isAdult: false,
      acceptTerms: false,
    },
  });

  // Função de welcome email removida - não existe no Supabase
  // const sendWelcomeEmail = async (email: string, name: string) => {
  //   // Função removida pois não existe no backend
  // };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);

      const signUpData = {
        email: values.email,
        password: values.password,
        options: {
          data: {
            full_name: values.fullName,
            formation_area: values.formationArea,
            graduation_year: values.graduationYear,
            is_student: values.type === "student",
            is_professional: values.type === "professional",
          },
        },
      };

      const { data, error } = await supabase.auth.signUp(signUpData);

      if (error) {
        console.error("❌ [SignUpForm] Signup error:", {
          error: error.message,
          code: error.status,
          details: error
        });

        // Verificar diferentes variações da mensagem de usuário já registrado
        const isUserAlreadyRegistered =
          error.message === "User already registered" ||
          error.message.includes("already registered") ||
          error.message.includes("already exists") ||
          error.message.includes("já cadastrado") ||
          error.status === 422;

        if (isUserAlreadyRegistered) {
          showFeedback(
            "error",
            "Email já cadastrado 📧",
            "Este email já está em uso. Clique em 'Já tem uma conta?' para fazer login."
          );
          return;
        }

        // Outros erros específicos
        if (error.message.includes("Password should be at least")) {
          showFeedback(
            "error",
            "Senha muito fraca 🔒",
            "A senha deve ter pelo menos 6 caracteres."
          );
          return;
        }

        if (error.message.includes("Invalid email")) {
          showFeedback(
            "error",
            "Email inválido 📧",
            "Por favor, digite um email válido."
          );
          return;
        }

        if (error.message.includes("Email rate limit exceeded")) {
          showFeedback(
            "error",
            "Muitas tentativas 🚫",
            "Aguarde alguns minutos antes de tentar novamente."
          );
          return;
        }

        // Erro genérico
        showFeedback(
          "error",
          "Erro ao criar conta",
          error.message || "Verifique os dados e tente novamente."
        );
        return;
      }

      // Aguardar um pouco para garantir que o trigger seja executado
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verificar se o perfil foi criado
      if (data.user?.id) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, created_at')
          .eq('id', data.user.id)
          .single();
      }

      showFeedback(
        "success",
        "Conta criada com sucesso! 🎉",
        "Bem-vindo à plataforma! Redirecionando..."
      );

      onSuccess();

      // Redirect to onboarding page after successful signup
      navigate("/onboarding");
    } catch (error: any) {

      showFeedback(
        "error",
        "Erro inesperado",
        "Algo deu errado. Tente novamente em alguns instantes."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4 py-2 pb-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <SignUpFormFields form={form} />
          <SignUpFormCheckboxes form={form} />
          <div className="flex flex-col gap-3">
            <Button
              type="submit"
              className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black font-bold py-3 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              disabled={isLoading}
            >
              {isLoading ? "Criando conta..." : "Criar conta"}
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full text-sm border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
              onClick={onModeChange}
            >
              Já tem uma conta? <span className="font-bold">Entre</span>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SignUpForm;
