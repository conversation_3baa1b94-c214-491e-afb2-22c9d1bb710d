
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Shuffle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useStudySession } from "@/hooks/useStudySession";
import { useDomain } from "@/hooks/useDomain";

interface MixAllQuestionsButtonProps {
  disabled?: boolean;
  filterCount?: number;
}

const MixAllQuestionsButton = ({ disabled = false, filterCount = 0 }: MixAllQuestionsButtonProps) => {
  const [open, setOpen] = useState(false);
  const [quantity, setQuantity] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { createSession } = useStudySession();
  const { domain } = useDomain();
  const MAX_QUESTIONS = 100;

  const handleStartMixedQuestions = async () => {
    try {
      setIsLoading(true);

      if (!user?.id) {

        return;
      }

      if (quantity <= 0) {

        return;
      }

      if (quantity > MAX_QUESTIONS) {

        return;
      }


      const { data: questions, error } = await supabase
        .rpc('get_random_questions', {
          p_quantity: quantity,
          p_domain: domain
        });

      if (error) throw error;

      if (!questions?.length) {
        console.error('❌ [MixAllQuestionsButton] Não foi possível obter questões aleatórias');
        return;
      }

      console.log('✅ [MixAllQuestionsButton] Questões obtidas:', questions.length);

      const session = await createSession(
        user.id,
        questions.map(q => q.id),
        "Mix Completo de Questões"
      );

      if (!session?.id) {
        throw new Error("Erro ao criar sessão de estudos");
      }

      console.log('✅ [MixAllQuestionsButton] Sessão criada:', session.id);

      setOpen(false);
      navigate(`/questions/${session.id}`);

    } catch (error: any) {

    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setOpen(true)}
        disabled={disabled}
        className={`w-full ${
          disabled
            ? "bg-gray-100 text-gray-400"
            : "bg-white hover:bg-gray-50"
        }`}
      >
        <Shuffle className="mr-2 h-4 w-4" />
        Mix de {filterCount > 0 ? filterCount : "Todas"} Questões
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="w-[95vw] max-w-md max-h-[85vh] rounded-2xl sm:rounded-xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Mix Completo de Questões</DialogTitle>
            <DialogDescription>
              Escolha quantas questões aleatórias você quer resolver de todos os tópicos disponíveis
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <label htmlFor="quantity" className="text-sm font-medium">
                Quantidade de questões (máx. {MAX_QUESTIONS})
              </label>
              <Input
                id="quantity"
                type="number"
                min={1}
                max={MAX_QUESTIONS}
                value={quantity}
                onChange={(e) => {
                  const val = Number(e.target.value);
                  setQuantity(val > MAX_QUESTIONS ? MAX_QUESTIONS : val);
                }}
                className="w-full"
                placeholder="Digite a quantidade"
              />
            </div>

            <div className="flex flex-col gap-3 pt-2">
              <Button
                onClick={handleStartMixedQuestions}
                className="w-full order-1"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Carregando...
                  </>
                ) : (
                  'Começar'
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                className="w-full order-2"
              >
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MixAllQuestionsButton;
