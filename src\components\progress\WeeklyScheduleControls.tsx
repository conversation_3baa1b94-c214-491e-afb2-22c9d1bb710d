import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Info } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface AddWeeksDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSubmit: (numberOfWeeks: number) => void;
    isLoading?: boolean;
    onAdded?: () => void;
}

export const AddWeeksDialog = ({ open, onOpenChange, onSubmit, isLoading, onAdded }: AddWeeksDialogProps) => {
    const [numberOfWeeks, setNumberOfWeeks] = useState(1);
    const [previewWeeks, setPreviewWeeks] = useState<string>("");
    const [isLoadingPreview, setIsLoadingPreview] = useState(false);

    const formatDate = (date: Date) => {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');



        return `${day}/${month}`;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        await onSubmit(numberOfWeeks);
        if (onAdded) {
            onAdded();
        }
    };

    useEffect(() => {
        const generatePreview = async () => {
            if (numberOfWeeks <= 0) {
                setPreviewWeeks("");
                return;
            }

            setIsLoadingPreview(true);
            try {
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) {
                    setPreviewWeeks("Erro: Usuário não autenticado");
                    return;
                }

                const { data: existingWeeks } = await supabase
                    .from('study_schedules')
                    .select('week_number, week_start_date, week_end_date')
                    .eq('user_id', user.id)
                    .order('week_number', { ascending: true });

                let nextWeekNumber = 1;
                let nextWeekStartDate = new Date();

                if (!existingWeeks || existingWeeks.length === 0) {
                    const dayOfWeek = nextWeekStartDate.getDay();
                    nextWeekStartDate.setDate(nextWeekStartDate.getDate() - dayOfWeek);
                } else if (existingWeeks && existingWeeks.length > 0) {
                    const lastWeek = existingWeeks[existingWeeks.length - 1];
                    nextWeekNumber = lastWeek.week_number + 1;
                    const lastWeekEndDate = new Date(lastWeek.week_end_date);
                    nextWeekStartDate = new Date(lastWeekEndDate);
                    nextWeekStartDate.setDate(lastWeekEndDate.getDate() + 1);
                }

                let result = "";
                const firstWeekStartDate = new Date(nextWeekStartDate);

                for (let i = 0; i < numberOfWeeks; i++) {
                    const weekStart = new Date(firstWeekStartDate);
                    if (i > 0) {
                        weekStart.setDate(firstWeekStartDate.getDate() + (i * 7));
                    }

                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);

                    const weekNumber = nextWeekNumber + i;
                    const weekLine = `Semana ${weekNumber}: ${formatDate(weekStart)} - ${formatDate(weekEnd)}\n`;
                    result += weekLine;
                }

                setPreviewWeeks(result);
            } catch (error) {
                console.error("Erro ao gerar prévia das semanas:", error);
                setPreviewWeeks("Erro ao calcular as semanas");
            } finally {
                setIsLoadingPreview(false);
            }
        };

        generatePreview();
    }, [numberOfWeeks]);

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px] border-2 border-black rounded-xl">
                <DialogHeader>
                    <DialogTitle className="text-xl font-bold">Adicionar Semanas de Estudo</DialogTitle>
                    <DialogDescription>
                        Defina quantas semanas de estudo você deseja adicionar ao seu cronograma
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="numberOfWeeks">Número de Semanas</Label>
                        <Input
                            id="numberOfWeeks"
                            type="number"
                            min={1}
                            max={52}
                            value={numberOfWeeks}
                            onChange={(e) => setNumberOfWeeks(parseInt(e.target.value) || 1)}
                            className="border-2 border-gray-300"
                        />
                    </div>

                    <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
                        <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                        <div className="text-blue-700">
                            <p>As novas semanas serão adicionadas consecutivamente após a última semana existente no seu cronograma.</p>

                            {numberOfWeeks > 0 && (
                                <div className="mt-2 p-2 bg-white rounded border border-blue-100">
                                    {isLoadingPreview ? (
                                        <p className="text-center text-sm text-gray-500">Calculando semanas...</p>
                                    ) : (
                                        <>
                                            <p className="font-medium mb-1">Previsão das semanas a serem adicionadas:</p>
                                            <pre className="text-xs whitespace-pre-wrap">{previewWeeks}</pre>
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    <Button
                        type="submit"
                        className="w-full bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Adicionando...' : 'Adicionar Semanas'}
                    </Button>
                </form>
            </DialogContent>
        </Dialog>
    );
};
