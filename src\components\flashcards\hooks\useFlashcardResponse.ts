import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Flashcard, FlashcardResponse } from '@/types/flashcard';
import { toast } from 'sonner';

export const useFlashcardResponse = (sessionId: string) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleResponse = async (card: Flashcard, response: FlashcardResponse) => {

    setIsSubmitting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      // Atualizar o cartão da sessão
      const { error: sessionError } = await supabase
        .from('flashcards_session_cards')
        .upsert({
          session_id: sessionId,
          card_id: card.id,
          response,
          review_status: 'reviewed',
          last_review_time: new Date().toISOString()
        }, {
          onConflict: 'session_id,card_id'
        });

      if (sessionError) throw sessionError;

      return true;
    } catch (error: any) {
      toast.error(error.message || "Erro ao salvar resposta");
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleResponse,
    isSubmitting
  };
};