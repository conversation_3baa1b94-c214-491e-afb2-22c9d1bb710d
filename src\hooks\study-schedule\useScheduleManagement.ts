import { useState, useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useUser } from '@supabase/auth-helpers-react';
import { getWeekDates } from './useScheduleDates';
import type { StudyTopic, WeeklySchedule, DaySchedule } from "@/types/study-schedule";

/**
 * Hook for managing study schedules
 */
export const useScheduleManagement = () => {
  const [isLoading, setIsLoading] = useState(false);
  const user = useUser();

  /**
   * Function to load current schedule with pagination and optimization
   */
  const loadCurrentSchedule = useCallback(async (
    limit: number = 4,
    offset: number = 0,
    prioritizeCurrentWeek: boolean = true
  ): Promise<WeeklySchedule | null> => {
    if (!user) return null;

    try {
      setIsLoading(true);

      // First, get current week number to prioritize it
      let currentWeekNumber = 1;
      if (prioritizeCurrentWeek) {
        const currentDate = new Date();
        const { data: currentWeekSchedule } = await supabase
          .from('study_schedules')
          .select('week_number')
          .eq('user_id', user.id)
          .lte('week_start_date', currentDate.toISOString().split('T')[0])
          .gte('week_end_date', currentDate.toISOString().split('T')[0])
          .maybeSingle();

        if (currentWeekSchedule) {
          currentWeekNumber = currentWeekSchedule.week_number;
        }
      }

      // Fetch schedules with pagination, prioritizing current week
      let schedulesQuery = supabase
        .from('study_schedules')
        .select('id, week_number, week_start_date, week_end_date, status')
        .eq('user_id', user.id);

      if (prioritizeCurrentWeek && offset === 0) {
        // CORRIGIDO: Para primeira carga, carregar TODAS as semanas disponíveis
        // em vez de limitar apenas às próximas da semana atual

        // Não aplicar filtros de range - carregar todas as semanas
      } else {
        // For pagination, use offset/limit
        schedulesQuery = schedulesQuery
          .range(offset, offset + limit - 1);
      }

      const { data: schedules, error: scheduleError } = await schedulesQuery
        .order('week_number', { ascending: true });

      if (scheduleError) throw scheduleError;



      // If no schedules, return empty result
      if (!schedules || schedules.length === 0) {
        return { recommendations: [] };
      }

      // Get current schedule ID from latest schedule
      const currentScheduleId = schedules[schedules.length - 1].id;

      // Fetch study schedule items only for loaded schedules with optimized fields
      const { data: studyItems, error: studyItemsError } = await supabase
        .from('study_schedule_items')
        .select(`
          id,
          schedule_id,
          day_of_week,
          specialty_name,
          theme_name,
          focus_name,
          specialty_id,
          theme_id,
          focus_id,
          difficulty,
          activity_description,
          start_time,
          duration,
          study_status,
          is_manual,
          metadata
        `)
        .in('schedule_id', schedules.map(s => s.id))
        .order('day_order', { ascending: true });

      if (studyItemsError) throw studyItemsError;

      // 🔍 CRITICAL DEBUG: Verificar se metadata está sendo carregado
      console.log('🔍 [CRITICAL] Dados carregados do banco:', {
        totalItems: studyItems?.length || 0,
        firstItemMetadata: studyItems?.[0]?.metadata,
        hasMetadata: studyItems?.some(item => item.metadata && Object.keys(item.metadata).length > 0)
      });

      // Create day schedules for all days of all weeks, even if there are no topics
      const daysOfWeek = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
      const daySchedules: DaySchedule[] = [];

      for (const schedule of schedules) {
        for (const day of daysOfWeek) {
          // Find all study items for this schedule and day
          const items = (studyItems || []).filter(
            si => si.schedule_id === schedule.id && si.day_of_week === day
          );

          const topics: StudyTopic[] = items.map(item => {
            // ✅ NOVO: Extrair dados do metadata
            const metadata = item.metadata as any;
            const institutions = metadata?.institutions || [];
            const focusPrevalence = metadata?.focusPrevalence;

            // 🔍 CRITICAL DEBUG: Log para verificar extração
            console.log('🔍 [CRITICAL] Processando item (loadCurrentSchedule):', {
              focus: item.focus_name?.substring(0, 30) + '...',
              hasMetadata: !!metadata,
              metadata,
              institutions,
              focusPrevalence
            });

            return {
              id: item.id,
              scheduleId: item.schedule_id,
              specialty: item.specialty_name,
              theme: item.theme_name,
              focus: item.focus_name,
              specialtyId: item.specialty_id,
              themeId: item.theme_id,
              focusId: item.focus_id,
              difficulty: item.difficulty as 'Fácil' | 'Médio' | 'Difícil',
              activity: item.activity_description || "",
              startTime: item.start_time,
              duration: item.duration,
              study_status: item.study_status as 'pending' | 'completed',
              weekNumber: schedule.week_number,
              day: item.day_of_week,
              is_manual: item.is_manual,
              // ✅ NOVO: Adicionar dados das instituições
              institutions,
              focusPrevalence,
              // ✅ CORREÇÃO: Adicionar metadata completo para preservar dados
              metadata: metadata
            } as any;
          });

          daySchedules.push({
            day,
            scheduleId: schedule.id,
            weekStartDate: schedule.week_start_date,
            weekEndDate: schedule.week_end_date,
            weekNumber: schedule.week_number,
            totalHours: topics.reduce((acc, t) => {
              // Parse duration to minutes
              if (!t.duration) return acc;

              // If duration is in format "X hours", convert to minutes
              if (t.duration.includes('hora') || t.duration.includes('hour')) {
                const hoursMatch = t.duration.match(/(\d+)/);
                const hours = hoursMatch ? parseInt(hoursMatch[1], 10) : 0;
                return acc + (hours * 60);
              }

              // If duration is in format "HH:MM", convert to minutes
              if (t.duration.includes(':')) {
                const [hours, minutes] = t.duration.split(':').map(Number);
                return acc + (hours * 60 + minutes);
              }

              // If duration contains "minuto", extract the number
              if (t.duration.includes('minuto')) {
                const minutesMatch = t.duration.match(/(\d+)/);
                const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
                return acc + minutes;
              }

              // If duration is just a number, assume it's minutes
              const minutesMatch = t.duration.match(/(\d+)/);
              const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
              return acc + minutes;
            }, 0),
            topics
          });
        }
      }

      // Sort by week number and day order
      daySchedules.sort((a, b) => {
        if (a.weekNumber !== b.weekNumber) {
          return a.weekNumber - b.weekNumber;
        }
        return daysOfWeek.indexOf(a.day) - daysOfWeek.indexOf(b.day);
      });

      return {
        recommendations: daySchedules,
        currentScheduleId
      };
    } catch (error: any) {
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Function to load more weeks (for pagination/lazy loading)
   */
  const loadMoreWeeks = useCallback(async (
    currentWeeksCount: number,
    limit: number = 4
  ): Promise<WeeklySchedule | null> => {
    if (!user) return null;

    try {
      setIsLoading(true);

      // Fetch next batch of schedules
      const { data: schedules, error: scheduleError } = await supabase
        .from('study_schedules')
        .select('id, week_number, week_start_date, week_end_date, status')
        .eq('user_id', user.id)
        .gt('week_number', currentWeeksCount)
        .order('week_number', { ascending: true })
        .limit(limit);

      if (scheduleError) throw scheduleError;

      if (!schedules || schedules.length === 0) {
        return { recommendations: [] };
      }

      // Fetch study schedule items for new schedules
      const { data: studyItems, error: studyItemsError } = await supabase
        .from('study_schedule_items')
        .select(`
          id,
          schedule_id,
          day_of_week,
          specialty_name,
          theme_name,
          focus_name,
          specialty_id,
          theme_id,
          focus_id,
          difficulty,
          activity_description,
          start_time,
          duration,
          study_status,
          is_manual,
          metadata
        `)
        .in('schedule_id', schedules.map(s => s.id))
        .order('day_order', { ascending: true });

      if (studyItemsError) throw studyItemsError;

      // Process the new weeks data (same logic as loadCurrentSchedule)
      const daysOfWeek = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
      const daySchedules: DaySchedule[] = [];

      for (const schedule of schedules) {
        for (const day of daysOfWeek) {
          const items = (studyItems || []).filter(
            si => si.schedule_id === schedule.id && si.day_of_week === day
          );

          const topics: StudyTopic[] = items.map(item => {
            // ✅ NOVO: Extrair dados do metadata
            const metadata = item.metadata as any;
            const institutions = metadata?.institutions || [];
            const focusPrevalence = metadata?.focusPrevalence;

            // 🔍 CRITICAL DEBUG: Log para verificar extração
            console.log('🔍 [CRITICAL] Processando item:', {
              focus: item.focus_name?.substring(0, 30) + '...',
              hasMetadata: !!metadata,
              metadata,
              institutions,
              focusPrevalence
            });

            return {
              id: item.id,
              scheduleId: item.schedule_id,
              specialty: item.specialty_name,
              theme: item.theme_name,
              focus: item.focus_name,
              specialtyId: item.specialty_id,
              themeId: item.theme_id,
              focusId: item.focus_id,
              difficulty: item.difficulty as 'Fácil' | 'Médio' | 'Difícil',
              activity: item.activity_description || "",
              startTime: item.start_time,
              duration: item.duration,
              study_status: item.study_status as 'pending' | 'completed',
              weekNumber: schedule.week_number,
              day: item.day_of_week,
              is_manual: item.is_manual,
              // ✅ NOVO: Adicionar dados das instituições
              institutions,
              focusPrevalence,
              // ✅ CORREÇÃO: Adicionar metadata completo para preservar dados
              metadata: metadata
            } as any;
          });

          daySchedules.push({
            day,
            scheduleId: schedule.id,
            weekStartDate: schedule.week_start_date,
            weekEndDate: schedule.week_end_date,
            weekNumber: schedule.week_number,
            totalHours: topics.reduce((acc, t) => {
              if (!t.duration) return acc;
              if (t.duration.includes('hora') || t.duration.includes('hour')) {
                const hoursMatch = t.duration.match(/(\d+)/);
                const hours = hoursMatch ? parseInt(hoursMatch[1], 10) : 0;
                return acc + (hours * 60);
              }
              if (t.duration.includes(':')) {
                const [hours, minutes] = t.duration.split(':').map(Number);
                return acc + (hours * 60 + minutes);
              }
              // If duration contains "minuto", extract the number
              if (t.duration.includes('minuto')) {
                const minutesMatch = t.duration.match(/(\d+)/);
                const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
                return acc + minutes;
              }
              // If duration is just a number, assume it's minutes
              const minutesMatch = t.duration.match(/(\d+)/);
              const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
              return acc + minutes;
            }, 0),
            topics
          });
        }
      }

      daySchedules.sort((a, b) => {
        if (a.weekNumber !== b.weekNumber) {
          return a.weekNumber - b.weekNumber;
        }
        return daysOfWeek.indexOf(a.day) - daysOfWeek.indexOf(b.day);
      });

      return {
        recommendations: daySchedules,
        currentScheduleId: schedules[schedules.length - 1].id
      };
    } catch (error: any) {
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Function to add weeks to the schedule
   */
  const addWeeks = useCallback(async (numberOfWeeks: number) => {
    if (!user) {
      return false;
    }

    try {
      setIsLoading(true);

      // Get the current date
      const currentDate = new Date();

      // Get existing schedules
      const { data: existingSchedules } = await supabase
        .from('study_schedules')
        .select('week_number, week_end_date')
        .eq('user_id', user.id)
        .order('week_number', { ascending: true });

      let lastWeekNumber = 0;
      let startDate = new Date(currentDate);

      if (existingSchedules && existingSchedules.length > 0) {
        const lastSchedule = existingSchedules[existingSchedules.length - 1];
        lastWeekNumber = lastSchedule.week_number;

        // Usar UTC para evitar problemas de fuso horário
        const lastEndDate = new Date(lastSchedule.week_end_date + 'T12:00:00Z');

        // Garantir que a próxima semana comece no domingo após o último sábado
        startDate = new Date(lastEndDate);
        startDate.setUTCDate(lastEndDate.getUTCDate() + 1); // Adicionar 1 dia ao último sábado para obter o domingo

        // Verificar se a data de início é realmente um domingo (dia 0)
        if (startDate.getUTCDay() !== 0) {
          // Se não for domingo, ajustar para o próximo domingo
          const daysUntilSunday = 7 - startDate.getUTCDay();
          startDate.setUTCDate(startDate.getUTCDate() + daysUntilSunday);
        }
      } else {
        // If no existing schedules, adjust current date to the start of the week (Sunday)
        // Usar UTC para evitar problemas de fuso horário
        startDate = new Date(Date.UTC(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
          12, 0, 0, 0
        ));

        const dayOfWeek = startDate.getUTCDay();
        startDate.setUTCDate(startDate.getUTCDate() - dayOfWeek); // Go back to Sunday
      }

      // Save the original start date to use as a reference
      const firstWeekStartDate = new Date(startDate);

      for (let i = 0; i < numberOfWeeks; i++) {
        const weekNumber = lastWeekNumber + i + 1;

        // Calculate the start date for this specific week
        // For week 1, use the original start date
        // For subsequent weeks (2+), add 7*i days to the original start date
        const weekStartDate = new Date(firstWeekStartDate);
        if (i > 0) {
          weekStartDate.setUTCDate(firstWeekStartDate.getUTCDate() + (i * 7));
        }

        // Calculate week dates (start=Sunday, end=Saturday)
        // Sempre garantir que a semana comece no domingo
        const { startDate: weekStart, endDate: weekEnd } = getWeekDates(weekStartDate, false);

        // Converter para strings no formato ISO (YYYY-MM-DD)
        const weekStartStr = weekStart.toISOString().split('T')[0];
        const weekEndStr = weekEnd.toISOString().split('T')[0];



        const { data, error } = await supabase
          .from('study_schedules')
          .insert({
            user_id: user.id,
            week_number: weekNumber,
            week_start_date: weekStartStr,
            week_end_date: weekEndStr,
            status: 'active'
          })
          .select();

        if (error) {
          throw error;
        }
      }

      // Verify that the weeks were created
      const weeksCreated = await verifyWeeksCreated(numberOfWeeks);

      return weeksCreated;
    } catch (error: any) {
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Function to verify that weeks were created successfully
   */
  const verifyWeeksCreated = async (expectedNumber: number) => {
    if (!user) {
      return false;
    }

    let retries = 0;
    const maxRetries = 5;

    while (retries < maxRetries) {

      const { data: schedules, error } = await supabase
        .from('study_schedules')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(expectedNumber);

      if (schedules && schedules.length >= expectedNumber) {
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, 500));
      retries++;
    }

    return false;
  };

  /**
   * Function to delete a week from the schedule
   */
  const deleteWeek = useCallback(async (weekNumber: number) => {
    if (!user) return false;

    try {
      setIsLoading(true);

      // Get the schedule ID for this week
      const { data: schedule, error: scheduleError } = await supabase
        .from('study_schedules')
        .select('id')
        .eq('user_id', user.id)
        .eq('week_number', weekNumber)
        .single();

      if (scheduleError) throw scheduleError;

      // Delete study schedule items for the week
      const { error: deleteItemsError } = await supabase
        .from('study_schedule_items')
        .delete()
        .eq('schedule_id', schedule.id);

      if (deleteItemsError) {
        throw deleteItemsError;
      }

      // Delete the week from study_schedules
      const { error: deleteScheduleError } = await supabase
        .from('study_schedules')
        .delete()
        .eq('user_id', user.id)
        .eq('week_number', weekNumber);

      if (deleteScheduleError) {
        throw deleteScheduleError;
      }

      return true;
    } catch (error: any) {
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Function to delete all weeks from the schedule
   */
  const deleteAllWeeks = useCallback(async () => {
    if (!user) return false;

    try {
      setIsLoading(true);

      // Get all schedule IDs for the user
      const { data: schedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('id')
        .eq('user_id', user.id);

      if (schedulesError) throw schedulesError;

      if (!schedules || schedules.length === 0) {
        return true;
      }

      const scheduleIds = schedules.map(s => s.id);

      // Delete all study schedule items first
      const { error: deleteItemsError } = await supabase
        .from('study_schedule_items')
        .delete()
        .in('schedule_id', scheduleIds);

      if (deleteItemsError) throw deleteItemsError;

      // Then delete all weeks from study_schedules
      const { error: deleteSchedulesError } = await supabase
        .from('study_schedules')
        .delete()
        .eq('user_id', user.id);

      if (deleteSchedulesError) throw deleteSchedulesError;

      return true;
    } catch (error: any) {
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Function to update a topic in the schedule
   */
  const updateTopic = useCallback(async (topic: StudyTopic) => {
    if (!user) {
      return false;
    }

    try {
      setIsLoading(true);

      let topicExists = false;

      if (topic.id) {

        const { data, error } = await supabase
          .from('study_schedule_items')
          .select('id')
          .eq('id', topic.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          // ✅ Não falhar por causa deste erro - assumir que não existe
          topicExists = false;
        } else {
          topicExists = !!data;
        }
      }

      if (topic.id && topicExists) {


        const { error: updateError } = await supabase
          .from('study_schedule_items')
          .update({
            specialty_name: topic.specialty,
            theme_name: topic.theme,
            focus_name: topic.focus,
            start_time: topic.startTime,
            duration: topic.duration,
            activity_description: topic.activity,
            specialty_id: topic.specialtyId,
            theme_id: topic.themeId,
            focus_id: topic.focusId,
            is_manual: topic.is_manual,
            // ✅ NOVO: Atualizar metadata se existir
            ...(topic.institutions || topic.focusPrevalence ? {
              metadata: {
                institutions: topic.institutions || [],
                focusPrevalence: topic.focusPrevalence,
                isPersonalized: !!(topic.institutions && topic.institutions.length > 0),
                generationMode: topic.institutions && topic.institutions.length > 0 ? 'institution_based' : 'manual'
              }
            } : {})
          })
          .eq('id', topic.id);

        if (updateError) {
          throw updateError;
        }
      } else {
        // Novo tratamento para tópicos sem ID mas com scheduleId válido
        if (!topic.scheduleId || !topic.day || !topic.weekNumber) {
          throw new Error('Missing required topic fields for creation');
        }

        const newItem = {
          id: topic.id ?? crypto.randomUUID(), // garante criação segura
          schedule_id: topic.scheduleId,
          day_of_week: topic.day,
          topic: `${topic.specialty} - ${topic.theme}`,
          specialty_name: topic.specialty,
          specialty_id: topic.specialtyId,
          theme_name: topic.theme,
          theme_id: topic.themeId,
          focus_name: topic.focus,
          focus_id: topic.focusId,
          difficulty: topic.difficulty || 'Médio',
          activity_description: topic.activity,
          start_time: topic.startTime,
          duration: topic.duration,
          type: 'study',
          activity_type: 'study',
          week_number: topic.weekNumber,
          is_manual: topic.is_manual || false,
          study_status: 'pending',
          // ✅ NOVO: Adicionar metadata se existir
          ...(topic.institutions || topic.focusPrevalence ? {
            metadata: {
              institutions: topic.institutions || [],
              focusPrevalence: topic.focusPrevalence,
              isPersonalized: !!(topic.institutions && topic.institutions.length > 0),
              generationMode: topic.institutions && topic.institutions.length > 0 ? 'institution_based' : 'manual'
            }
          } : {})
        };

        const { error: insertError } = await supabase
          .from('study_schedule_items')
          .insert(newItem);

        if (insertError) {
          throw insertError;
        }
      }

      return true;

    } catch (error: any) {
      return false;

    } finally {
      setIsLoading(false);
    }
  }, [user]);


  return {
    isLoading,
    loadCurrentSchedule,
    loadMoreWeeks,
    addWeeks,
    deleteWeek,
    deleteAllWeeks,
    updateTopic
  };
};
