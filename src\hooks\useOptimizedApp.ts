import { useEffect } from 'react';
import { useOptimizedCache } from './useOptimizedCache';
import { useLocation } from 'react-router-dom';
import { preloadRouteComponents, preloadCriticalComponents, analyzeBundleUsage } from '@/utils/optimizedImports';

/**
 * Hook principal para integrar todas as otimizações da aplicação
 * Deve ser usado no componente raiz (App.tsx)
 */
export const useOptimizedApp = () => {
  const location = useLocation();
  const {
    metrics,
    manualCleanup,
    selectiveInvalidation,
    optimizeCacheSettings
  } = useOptimizedCache();

  // Preload de componentes críticos na inicialização
  useEffect(() => {
    preloadCriticalComponents();
    
    // Análise de bundle apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        analyzeBundleUsage();
      }, 2000);
    }
  }, []);

  // Preload baseado em mudanças de rota
  useEffect(() => {
    preloadRouteComponents(location.pathname);
  }, [location.pathname]);

  // Otimização automática do cache a cada 5 minutos
  useEffect(() => {
    const interval = setInterval(() => {
      if (metrics.totalQueries > 50 || metrics.memoryUsage > 30) {
        manualCleanup();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [metrics, manualCleanup]);

  // Otimização de cache após período de inatividade
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleUserActivity = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        // Apenas otimizar cache existente, não fazer prefetch
        if (metrics.totalQueries > 20) {
          manualCleanup();
        }
      }, 30000); // 30 segundos de inatividade
    };

    // Eventos que indicam atividade do usuário
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Iniciar o timer
    handleUserActivity();

    return () => {
      clearTimeout(timeoutId);
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, [metrics.totalQueries, manualCleanup]);

  return {
    cacheMetrics: metrics,
    optimizationActions: {
      manualCleanup,
      selectiveInvalidation,
      optimizeCacheSettings
    }
  };
};

/**
 * Hook para otimizações específicas de páginas
 */
export const usePageOptimizations = (pageName: string) => {
  const { selectiveInvalidation } = useOptimizedCache();

  // Invalidação específica baseada na página
  const invalidatePageData = () => {
    const pageInvalidationMap: Record<string, string[]> = {
      'study': ['user-stats', 'question-count'],
      'results': ['user-answers', 'session-stats'],
      'progress': ['study-schedule', 'user-progress'],
      'flashcards': ['flashcard-reviews', 'flashcard-stats'],
      'admin': ['admin-data', 'focus-consolidation']
    };

    const patterns = pageInvalidationMap[pageName];
    if (patterns) {
      selectiveInvalidation(patterns);
    }
  };

  return { invalidatePageData };
};
