
import { ChevronRight, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { FilterOption } from '../types';

interface HierarchicalFilterItemProps {
  item: FilterOption;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  questionCount: number;
  hasChildren: boolean;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, filterType: FilterOption['type']) => void;
}

export const HierarchicalFilterItem = ({
  item,
  level,
  isExpanded,
  isSelected,
  questionCount,
  hasChildren,
  onToggleExpand,
  onToggleSelect
}: HierarchicalFilterItemProps) => {
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleSelect(item.id, item.type);
  };

  return (
    <div 
      className={cn(
        'flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer',
        'hover:bg-[#FEF7CD]/50',
        isSelected && 'bg-[#FEF7CD]',
        level > 0 && 'ml-6'
      )}
    >
      {hasChildren && (
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-6 w-6 cursor-pointer"
          onClick={() => onToggleExpand(item.id)}
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      )}
      
      <div 
        className={cn(
          "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center cursor-pointer",
          isSelected 
            ? "bg-[#FF6B00] border-black text-white" 
            : "border-black hover:border-[#FF6B00]",
        )}
        onClick={handleSelect}
      >
        {isSelected && (
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            className="w-3.5 h-3.5"
          >
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        )}
      </div>
      
      <span className={cn(
        "flex-1 text-sm cursor-pointer",
        isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
      )} onClick={handleSelect}>
        {item.name}
      </span>
      
      <Badge 
        variant="secondary" 
        className={cn(
          "ml-2 min-w-[3rem] text-center transition-all duration-200",
          isSelected 
            ? "bg-[#FF6B00] text-white border-none" 
            : "bg-gray-100 text-gray-600"
        )}
      >
        {questionCount}
      </Badge>
    </div>
  );
};
