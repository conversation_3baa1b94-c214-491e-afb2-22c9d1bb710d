export interface FSRSMetrics {
  stability: number;       // Current stability value (days)
  difficulty: number;      // Current difficulty (1-10)
  retrievability: number;  // Current retrievability (0-1)
  intervalInDays: number;  // Intervalo em dias
  lastReviewDate?: Date | string;  // Última data de revisão
  nextReviewDate: Date;    // Próxima data de revisão
}

export interface FSRSParameters {
  w1: number;   // Initial stability weight
  w2: number;   // Initial difficulty weight
  w3: number;   // Previous memory impact weight
  w4: number;   // Minimum stability
  w5: number;   // Again response multiplier
  w6: number;   // Hard response multiplier
  w7: number;   // Good response multiplier
  w8: number;   // Easy response multiplier
  w9: number;   // Initial difficulty
  w10: number;  // Difficulty adjustment rate
  w11: number;  // Maximum difficulty
  w12: number;  // Minimum stability after fail
  w13: number;  // Forgetting factor
  w14: number;  // Stability decay rate
  w15: number;  // Target retention
  w16: number;  // Difficulty penalty
  w17: number;  // Stability boost
  w18: number;  // Initial stability modifier
  w19: number;  // Initial difficulty modifier
}

export type FlashcardResponse = 'error' | 'hard' | 'medium' | 'easy';

export interface PreCalculatedMetrics {
  stability: number;
  difficulty: number;
  retrievability: number;
  nextReviewDate: Date;
  intervalInDays: number;
}

export interface ResponseMetrics {
  error: PreCalculatedMetrics;
  hard: PreCalculatedMetrics;
  medium: PreCalculatedMetrics;
  easy: PreCalculatedMetrics;
}

// Interface para o objeto de revisão do flashcard
export interface FlashcardReviewData {
  user_id: string;
  card_id: string;
  stability: number;
  difficulty: number;
  retrievability: number;
  next_review_date: string;
  last_review_date: string;
  intervalindays: number;
  recall_probability?: number;
}