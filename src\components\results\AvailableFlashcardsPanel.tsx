
import React, { useState, useEffect, useMemo } from 'react';
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useParams } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { Info } from "lucide-react";
import { FlashcardGenerationPreview } from './FlashcardGenerationPreview';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

interface Stats {
  totalQuestions: number;
  incorrectQuestions: number;
}

interface AvailableFlashcardsPanelProps {
  stats: Stats;
  onImport: (count: number, cardIds: string[]) => void;
}

// ✅ Constantes para paginação otimizada
const CARDS_PER_PAGE = 12; // Número de cards por página
const VISIBLE_CARDS = 4; // Número de cards visíveis no scroll

interface FlashcardInfo {
  id: string;
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  specialty: string;
  theme: string;
  focus: string;
  imported?: boolean;
}

export const AvailableFlashcardsPanel = ({ stats, onImport }: AvailableFlashcardsPanelProps) => {
  const [availableCards, setAvailableCards] = useState<FlashcardInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [importedIndexes, setImportedIndexes] = useState<Set<number>>(new Set());
  const [scrollPosition, setScrollPosition] = useState(0);
  const [importedCardIds, setImportedCardIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCards, setTotalCards] = useState(0);
  const { sessionId } = useParams();
  const isMobile = useIsMobile();

  // ✅ Cálculos de paginação otimizados com useMemo
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(totalCards / CARDS_PER_PAGE);
    const startIndex = (currentPage - 1) * CARDS_PER_PAGE;
    const endIndex = startIndex + CARDS_PER_PAGE;
    const currentPageCards = availableCards.slice(startIndex, endIndex);

    return {
      totalPages,
      currentPageCards,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1
    };
  }, [availableCards, currentPage, totalCards]);

  const scrollAmount = isMobile ? 280 : 340; // Width of one card + margin, smaller for mobile

  const scrollLeft = () => {
    const newPosition = Math.max(0, scrollPosition - scrollAmount);
    setScrollPosition(newPosition);
  };

  const scrollRight = () => {
    const maxScrollPosition = availableCards.length * scrollAmount;
    const newPosition = Math.min(maxScrollPosition, scrollPosition + scrollAmount);
    setScrollPosition(newPosition);
  };

  useEffect(() => {
    fetchAvailableFlashcards();
  }, [sessionId]);

  // Update the parent component with the imported card count
  useEffect(() => {
    onImport(importedIndexes.size, importedCardIds);
  }, [importedIndexes.size, importedCardIds, onImport]);

  const fetchAvailableFlashcards = async () => {
    if (!sessionId) return;

    try {


      // Step 1: Get the session questions to extract specialty, theme, focus IDs
      const { data: events, error: eventsError } = await supabase
        .from('session_events')
        .select(`
          question_id,
          specialty_id,
          theme_id,
          focus_id
        `)
        .eq('session_id', sessionId);

      if (eventsError) throw eventsError;

      const sessionQuestions = events.map(event => ({
        id: event.question_id,
        specialty_id: event.specialty_id,
        theme_id: event.theme_id,
        focus_id: event.focus_id
      }));



      // Extract unique specialty, theme and focus IDs from the questions
      const specialtiesSet = new Set(sessionQuestions.map(q => q.specialty_id).filter(Boolean));
      const themesSet = new Set(sessionQuestions.map(q => q.theme_id).filter(Boolean));
      const focusesSet = new Set(sessionQuestions.map(q => q.focus_id).filter(Boolean));

      const specialties = Array.from(specialtiesSet);
      const themes = Array.from(themesSet);
      const focuses = Array.from(focusesSet);



      if (!specialties.length) {
        console.log('⚠️ [AvailableFlashcardsPanel] No specialties found in session questions');
        setLoading(false);
        return;
      }

      // Step 2: Get all shared flashcards that match these categories
      let query = supabase
        .from('flashcards_cards')
        .select(`
          *,
          specialty:flashcards_specialty(name),
          theme:flashcards_theme(name),
          focus:flashcards_focus(name)
        `)
        .eq('is_shared', true)
        .eq('current_state', 'available');

      if (specialties.length) {
        query = query.in('specialty_id', specialties);
      }

      const { data: cards, error: cardsError } = await query;

      if (cardsError) throw cardsError;



      // Step 3: Format the cards for display
      let formattedCards = cards?.map(card => ({
        id: card.id,
        front: card.front,
        back: card.back,
        front_image: card.front_image,
        back_image: card.back_image,
        specialty_id: card.specialty_id,
        theme_id: card.theme_id,
        focus_id: card.focus_id,
        specialty: card.specialty?.name || 'Desconhecida',
        theme: card.theme?.name || 'Desconhecido',
        focus: card.focus?.name || 'Desconhecido'
      })) || [];

      // Step 4: Filter cards based on session focus


      // Filter by specialty
      const cardsBySpecialty = formattedCards.filter(card =>
        specialties.includes(card.specialty_id)
      );
      // Further filter by theme if we have theme filters
      let filteredCards = cardsBySpecialty;
      if (themes.length) {
        filteredCards = cardsBySpecialty.filter(card => {
          return card.theme_id && themes.includes(card.theme_id);
        });
      }

      // Further filter by focus if we have focus filters
      if (focuses.length) {
        filteredCards = filteredCards.filter(card => {
          return card.focus_id && focuses.includes(card.focus_id);
        });
      }



      // Get user's already imported cards to mark them
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: userCards } = await supabase
          .from('flashcards_cards')
          .select('origin_id')
          .eq('user_id', user.id)
          .not('origin_id', 'is', null);

        if (userCards && userCards.length > 0) {
          const importedIds = userCards.map(c => c.origin_id).filter(Boolean);
          filteredCards = filteredCards.map(card => ({
            ...card,
            imported: importedIds.includes(card.id)
          }));
        }
      }

      setAvailableCards(filteredCards);
      setTotalCards(filteredCards.length);


    } catch (error) {
      console.error('❌ [AvailableFlashcardsPanel] Error fetching available flashcards:', error);
      toast.error("Erro ao carregar flashcards disponíveis");
    } finally {
      setLoading(false);
    }
  };

  const handleImportCard = (index: number, cardId: string) => {
    setImportedIndexes(prev => {
      const newSet = new Set(prev);
      newSet.add(index);
      return newSet;
    });
    setImportedCardIds(prev => [...prev, cardId]);

    // Notify the parent component about the import
    const newImportedCardIds = [...importedCardIds, cardId];
    onImport(importedIndexes.size + 1, newImportedCardIds);
  };

  return (
    <Card className="bg-white/90 border-2 border-gray-200 p-1 px-2 pb-6">
      <div className="mb-4 flex flex-col items-center">
        <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider text-center mb-2">
          Flashcards disponíveis na plataforma
        </h3>
        <Button variant="ghost" size="icon" className="absolute right-3 top-3">
          <Info className="h-5 w-5 text-gray-500" />
        </Button>
      </div>

      <div className="overflow-x-hidden relative">
        <div
          className="flex justify-center w-full"
          style={{ transform: `translate3d(-${scrollPosition}px, 0, 0)`, transition: 'transform 0.3s ease-out' }}
        >
          {/* ✅ Renderizar apenas cards da página atual */}
          {paginationData.currentPageCards.map((card, index) => {
            const globalIndex = (currentPage - 1) * CARDS_PER_PAGE + index;
            return (
              <div key={card.id} className={`${isMobile ? 'min-w-[280px] max-w-[300px]' : 'min-w-[320px] max-w-[340px]'} mr-5 last:mr-0 flex-shrink-0 snap-center`}>
                <FlashcardGenerationPreview
                  card={card}
                  isImported={importedIndexes.has(globalIndex) || Boolean(card.imported)}
                  onImport={(cardId) => handleImportCard(globalIndex, cardId)}
                />
              </div>
            );
          })}

          {availableCards.length === 0 && !loading && (
            <div className="w-full flex items-center justify-center py-10">
              <div className={`${isMobile ? 'w-[280px]' : 'w-[320px] md:w-[500px]'} flex flex-col items-center justify-center border border-dashed border-gray-300 rounded-lg p-6 text-center`}>
                <p className="text-gray-600 mb-2 px-2">
                  Ainda não existem flashcards compartilhados para este tema.
                </p>
                <p className="text-sm text-primary/80 px-2">
                  Seja o primeiro a criar e compartilhar flashcards utilizando a seção abaixo!
                </p>
              </div>
            </div>
          )}

          {loading && (
            <div className="w-full flex items-center justify-center">
              <div className={`${isMobile ? 'min-w-[280px] max-w-[300px]' : 'min-w-[320px] max-w-[340px]'} flex items-center justify-center border border-dashed border-gray-300 rounded-lg p-8`}>
                <div className="animate-pulse flex space-x-4">
                  <div className="flex-1 space-y-4 py-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* ✅ Controles de paginação e scroll */}
      {availableCards.length > 0 && (
        <div className="flex flex-col items-center gap-3 mt-4">
          {/* Scroll horizontal para cards da página atual */}
          {paginationData.currentPageCards.length > (isMobile ? 1 : 3) && (
            <div className="flex justify-center gap-3">
              <Button onClick={scrollLeft} variant="outline" size="icon">
                <span className="text-2xl">‹</span>
              </Button>
              <Button onClick={scrollRight} variant="outline" size="icon">
                <span className="text-2xl">›</span>
              </Button>
            </div>
          )}

          {/* Controles de paginação */}
          {paginationData.totalPages > 1 && (
            <div className="flex items-center gap-2">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={!paginationData.hasPrevPage}
                variant="outline"
                size="sm"
              >
                Anterior
              </Button>
              <span className="text-sm text-gray-600 px-3">
                Página {currentPage} de {paginationData.totalPages}
              </span>
              <Button
                onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
                disabled={!paginationData.hasNextPage}
                variant="outline"
                size="sm"
              >
                Próxima
              </Button>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};
