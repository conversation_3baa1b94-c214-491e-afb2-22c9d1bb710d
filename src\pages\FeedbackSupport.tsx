import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, MessageSquare, HeadphonesIcon, Star, Bug, Lightbulb, HelpCircle } from 'lucide-react';
import { FeedbackForm } from '@/components/feedback/FeedbackForm';
import { motion } from 'framer-motion';

const FeedbackSupport = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('feedback');

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      
      <div className="container max-w-4xl mx-auto px-4 py-8 space-y-8 animate-fade-in">
        {/* Header Section */}
        <header className="relative">
          <Button
            onClick={() => navigate('/plataformadeestudos')}
            variant="outline"
            className="mb-6 border-2 border-black shadow-card-sm hover:shadow-lg transition-all"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar ao Painel
          </Button>

          <div className="inline-block transform -rotate-2 mb-6">
            <div className="bg-hackathon-red border-2 border-black px-4 py-1 text-white font-bold tracking-wide text-sm shadow-card-sm">
              FEEDBACK & SUPORTE
            </div>
          </div>

          <div>
            <h1 className="text-5xl font-black leading-none mb-4">
              <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
                Sua Opinião
              </span>
              <span className="block text-hackathon-red mt-2">Importa!</span>
            </h1>
            <p className="text-xl text-gray-700 max-w-2xl leading-relaxed">
              Ajude-nos a melhorar a plataforma com seu feedback, sugestões ou entre em contato 
              com nosso suporte para qualquer dúvida.
            </p>
          </div>
        </header>

        {/* Main Content */}
        <div className="space-y-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-white border-2 border-black shadow-card-sm">
              <TabsTrigger 
                value="feedback" 
                className="data-[state=active]:bg-hackathon-yellow data-[state=active]:text-black font-bold"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Deixar Feedback
              </TabsTrigger>
              <TabsTrigger 
                value="support" 
                className="data-[state=active]:bg-hackathon-green data-[state=active]:text-black font-bold"
              >
                <HeadphonesIcon className="h-4 w-4 mr-2" />
                Suporte
              </TabsTrigger>
            </TabsList>

            <TabsContent value="feedback" className="mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="border-2 border-black shadow-card-sm">
                  <CardHeader className="bg-hackathon-yellow border-b-2 border-black">
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <Star className="h-6 w-6" />
                      Compartilhe sua Experiência
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <FeedbackForm />
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="support" className="mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* FAQ Section */}
                <Card className="border-2 border-black shadow-card-sm">
                  <CardHeader className="bg-hackathon-green border-b-2 border-black">
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <HelpCircle className="h-6 w-6" />
                      Perguntas Frequentes
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="border-l-4 border-hackathon-green pl-4">
                        <h4 className="font-semibold mb-2">Como funciona o sistema de questões?</h4>
                        <p className="text-gray-600 text-sm">
                          Nossa plataforma oferece questões selecionadas com análise detalhada, 
                          permitindo que você pratique e acompanhe seu progresso em tempo real.
                        </p>
                      </div>
                      
                      <div className="border-l-4 border-hackathon-green pl-4">
                        <h4 className="font-semibold mb-2">Como posso acompanhar meu progresso?</h4>
                        <p className="text-gray-600 text-sm">
                          Acesse a seção "Progresso" no painel principal para visualizar gráficos 
                          detalhados do seu desempenho e evolução ao longo do tempo.
                        </p>
                      </div>

                      <div className="border-l-4 border-hackathon-green pl-4">
                        <h4 className="font-semibold mb-2">Quando os flashcards estarão disponíveis?</h4>
                        <p className="text-gray-600 text-sm">
                          Os flashcards estão em fase final de desenvolvimento e serão lançados em breve. 
                          Você será notificado assim que estiverem disponíveis.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Contact Support */}
                <Card className="border-2 border-black shadow-card-sm">
                  <CardHeader className="bg-hackathon-blue border-b-2 border-black">
                    <CardTitle className="flex items-center gap-2 text-xl text-white">
                      <HeadphonesIcon className="h-6 w-6" />
                      Contato Direto
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className="text-gray-600 mb-4">
                      Não encontrou a resposta que procurava? Entre em contato conosco diretamente:
                    </p>
                    
                    <div className="space-y-3">
                      <a
                        href="mailto:<EMAIL>"
                        className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer"
                      >
                        <MessageSquare className="h-5 w-5 text-hackathon-blue" />
                        <div>
                          <p className="font-medium">Email</p>
                          <p className="text-sm text-gray-600"><EMAIL></p>
                        </div>
                      </a>

                      <a
                        href="https://wa.me/5564993198433"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer"
                      >
                        <HeadphonesIcon className="h-5 w-5 text-hackathon-green" />
                        <div>
                          <p className="font-medium">WhatsApp</p>
                          <p className="text-sm text-gray-600">(64) 99319-8433</p>
                        </div>
                      </a>
                    </div>

                    <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-blue-800 text-sm">
                        <strong>Horário de atendimento:</strong> Segunda a sexta, das 9h às 18h
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default FeedbackSupport;
