import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Theme {
  id: string;
  name: string;
}

interface Focus {
  id: string;
  name: string;
  theme_id: string;
}

export const FocusTab = () => {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [foci, setFoci] = useState<Focus[]>([]);
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [newFocus, setNewFocus] = useState("");

  useEffect(() => {
    fetchThemes();
  }, []);

  useEffect(() => {
    if (selectedTheme) {
      fetchFoci(selectedTheme);
    }
  }, [selectedTheme]);

  const fetchThemes = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_theme")
      .select("id, name")
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar temas");
      return;
    }

    setThemes(data || []);
  };

  const fetchFoci = async (themeId: string) => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_focus")
      .select("id, name, theme_id")
      .eq("theme_id", themeId)
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar focos");
      return;
    }

    setFoci(data || []);
  };

  const handleCreateFocus = async () => {
    if (!selectedTheme) {
      toast.error("Selecione um tema");
      return;
    }

    if (!newFocus.trim()) {
      toast.error("O nome do foco é obrigatório");
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from("flashcards_focus")
      .insert([{
        name: newFocus,
        theme_id: selectedTheme,
        user_id: user.id
      }]);

    if (error) {
      toast.error("Erro ao criar foco");
      return;
    }

    toast.success("Foco criado com sucesso!");
    setNewFocus("");
    fetchFoci(selectedTheme);
  };

  return (
    <div className="space-y-4">
      <Select value={selectedTheme} onValueChange={setSelectedTheme}>
        <SelectTrigger>
          <SelectValue placeholder="Selecione um tema" />
        </SelectTrigger>
        <SelectContent>
          {themes.map((theme) => (
            <SelectItem key={theme.id} value={theme.id}>
              {theme.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <div className="flex gap-4">
        <Input
          placeholder="Nome do Foco"
          value={newFocus}
          onChange={(e) => setNewFocus(e.target.value)}
        />
        <Button onClick={handleCreateFocus}>Criar Foco</Button>
      </div>

      <div className="grid gap-2">
        {foci.map((focus) => (
          <div
            key={focus.id}
            className="p-4 rounded-lg border bg-card text-card-foreground"
          >
            {focus.name}
          </div>
        ))}
      </div>
    </div>
  );
};