
import { useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "./ui/use-toast";
import { useUserData } from "@/hooks/useUserData";
import AuthDialog from "./auth/AuthDialog";
import { HeaderLogo } from "./header/HeaderLogo";
import { HeaderActions } from "./header/HeaderActions";

interface HeaderProps {
  hideSearch?: boolean;
}

const Header = ({ hideSearch }: HeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, isAdmin } = useUserData();
  const { toast } = useToast();

  // Removido useEffect e funções desnecessárias - agora usa useUserData centralizado

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast({
        variant: "destructive",
        title: "Erro ao sair",
        description: "Tente novamente mais tarde.",
      });
    } else {
      toast({
        title: "Logout realizado com sucesso",
        description: "Até logo!",
      });
    }
  };

  return (
    <header className="hidden sm:block w-full bg-white border-b-2 border-black">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <HeaderLogo />
          <div className="flex items-center gap-2">
            {user ? (
              <HeaderActions
                user={user}
                profile={profile}
                isAdmin={isAdmin}
                onLogout={handleLogout}
              />
            ) : (
              <AuthDialog />
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
