import { useState } from "react";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";

export const useFlashcardState = (flashcards: FlashcardWithHierarchy[]) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [sessionStatus, setSessionStatus] = useState<'in_progress' | 'completed'>('in_progress');

  const currentCard = flashcards[currentIndex];

  const advanceCard = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setIsFlipped(false);
    } else {
      setSessionStatus('completed');
    }
  };

  return {
    currentIndex,
    setCurrentIndex,
    isFlipped,
    setIsFlipped,
    sessionStatus,
    setSessionStatus,
    currentCard,
    advanceCard,
  };
};