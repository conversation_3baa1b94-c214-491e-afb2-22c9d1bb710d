
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#0EA5E9",
          hover: "#0284C7",
        },
        secondary: {
          DEFAULT: "#F8FAFC",
          hover: "#F1F5F9",
        },
        accent: {
          DEFAULT: "#64748B",
          hover: "#475569",
        },
        hackathon: {
          red: "#FF3B30",
          green: "#4ADE80",
          yellow: "#FFD60A",
          blue: "#0EA5E9",
          black: "#000000",
          lightBg: "#FEF7CD", // Updated to the new color
        },
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
        display: ["'SF Pro Display'", "Inter", "sans-serif"],
        mono: ["'SF Mono'", "monospace"],
      },
      boxShadow: {
        'button': '0 4px 0 0 rgba(0, 0, 0, 0.2)',
        'card': '6px 6px 0 0 rgba(0, 0, 0, 1)',
        'card-light': '4px 4px 0 0 rgba(0, 0, 0, 1)',
        'card-sm': '2px 2px 0 0 rgba(0, 0, 0, 1)',
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
      },
      animation: {
        fadeIn: "fadeIn 0.5s ease-out forwards",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
