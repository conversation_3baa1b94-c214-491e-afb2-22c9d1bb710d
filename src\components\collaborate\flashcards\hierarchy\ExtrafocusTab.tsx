import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Focus {
  id: string;
  name: string;
}

interface Extrafocus {
  id: string;
  name: string;
  focus_id: string;
}

export const ExtrafocusTab = () => {
  const [foci, setFoci] = useState<Focus[]>([]);
  const [extrafoci, setExtrafoci] = useState<Extrafocus[]>([]);
  const [selectedFocus, setSelectedFocus] = useState<string>("");
  const [newExtrafocus, setNewExtrafocus] = useState("");

  useEffect(() => {
    fetchFoci();
  }, []);

  useEffect(() => {
    if (selectedFocus) {
      fetchExtrafoci(selectedFocus);
    }
  }, [selectedFocus]);

  const fetchFoci = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_focus")
      .select("id, name")
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar focos");
      return;
    }

    setFoci(data || []);
  };

  const fetchExtrafoci = async (focusId: string) => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from("flashcards_extrafocus")
      .select("id, name, focus_id")
      .eq("focus_id", focusId)
      .eq("user_id", user.id);

    if (error) {
      toast.error("Erro ao carregar subfocos");
      return;
    }

    setExtrafoci(data || []);
  };

  const handleCreateExtrafocus = async () => {
    if (!selectedFocus) {
      toast.error("Selecione um foco");
      return;
    }

    if (!newExtrafocus.trim()) {
      toast.error("O nome do subfoco é obrigatório");
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from("flashcards_extrafocus")
      .insert([{
        name: newExtrafocus,
        focus_id: selectedFocus,
        user_id: user.id
      }]);

    if (error) {
      toast.error("Erro ao criar subfoco");
      return;
    }

    toast.success("Subfoco criado com sucesso!");
    setNewExtrafocus("");
    fetchExtrafoci(selectedFocus);
  };

  return (
    <div className="space-y-4">
      <Select value={selectedFocus} onValueChange={setSelectedFocus}>
        <SelectTrigger>
          <SelectValue placeholder="Selecione um foco" />
        </SelectTrigger>
        <SelectContent>
          {foci.map((focus) => (
            <SelectItem key={focus.id} value={focus.id}>
              {focus.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <div className="flex gap-4">
        <Input
          placeholder="Nome do Subfoco"
          value={newExtrafocus}
          onChange={(e) => setNewExtrafocus(e.target.value)}
        />
        <Button onClick={handleCreateExtrafocus}>Criar Subfoco</Button>
      </div>

      <div className="grid gap-2">
        {extrafoci.map((extrafocus) => (
          <div
            key={extrafocus.id}
            className="p-4 rounded-lg border bg-card text-card-foreground"
          >
            {extrafocus.name}
          </div>
        ))}
      </div>
    </div>
  );
};