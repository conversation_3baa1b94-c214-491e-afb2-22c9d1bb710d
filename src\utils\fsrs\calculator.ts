import type { FlashcardResponse } from "@/types/flashcard";
import type { FSRSMetrics } from "./types";
import { FSRS_WEIGHTS as w } from "./parameters";

// Função para calcular as métricas FSRS
export const calculateFSRSMetrics = (
  currentMetrics: FSRSMetrics,
  response: FlashcardResponse
): FSRSMetrics => {
  console.log('🔍 [FSRS] Dados iniciais:', {
    estabilidade: currentMetrics.stability.toFixed(3),
    dificuldade: currentMetrics.difficulty.toFixed(3),
    retrievability: currentMetrics.retrievability.toFixed(4),
    intervalInDays: currentMetrics.intervalInDays,
    ultima_revisao: currentMetrics.lastReviewDate instanceof Date ?
      currentMetrics.lastReviewDate.toISOString() :
      currentMetrics.lastReviewDate,
    pesos_utilizados: {
      w5: w.w5, // Again multiplier (0.2)
      w6: w.w6, // Hard multiplier (0.7)
      w7: w.w7, // Good multiplier (1.0)
      w8: w.w8, // Easy multiplier (1.3)
      w10: w.w10, // Difficulty adjustment (0.2)
      w12: w.w12, // Minimum stability after fail (0.8)
      w13: w.w13, // Forgetting factor (0.5)
      w14: w.w14, // Stability decay (0.3)
      w16: w.w16, // Difficulty penalty (0.1)
      w17: w.w17  // Retrievability boost (1.0)
    }
  });

  const { stability, difficulty } = currentMetrics;

  // Calcula dias desde a última revisão
  const elapsedDays = currentMetrics.lastReviewDate
    ? (new Date().getTime() - new Date(currentMetrics.lastReviewDate).getTime()) / (1000 * 60 * 60 * 24)
    : 0;

  console.log('📅 [FSRS] Cálculo de dias passados:', {
    ultima_revisao: currentMetrics.lastReviewDate,
    dias_passados: elapsedDays.toFixed(2),
    formula: '(data_atual - ultima_revisao) / ms_por_dia'
  });

  // Calcula retrievability usando curva de esquecimento exponencial
  const retrievability = Math.exp(-elapsedDays / Math.max(stability, w.w4));

  console.log('📊 [FSRS] Cálculo de retrievability:', {
    formula: 'exp(-dias_passados / max(estabilidade, w4))',
    valores: {
      dias_passados: elapsedDays.toFixed(2),
      estabilidade: stability.toFixed(3),
      w4: w.w4.toFixed(2)
    },
    calculo: `exp(-${elapsedDays.toFixed(2)} / max(${stability.toFixed(3)}, ${w.w4})) = ${retrievability.toFixed(4)}`
  });

  // Calcula novas métricas baseadas na resposta
  const newMetrics = calculateNewMetrics(response, {
    stability,
    difficulty,
    retrievability,
    elapsedDays
  });

  // Calcula intervalInDays baseado na estabilidade
  const intervalInDays = Math.ceil(newMetrics.stability);

  // Log para verificar valor de intervalInDays antes de continuar
console.log(`🔍 [FSRS] Intervalo em dias após arredondamento da estabilidade:`, {
  stability: newMetrics.stability,
  intervalInDays
});

  // Calcula próxima data de revisão
  const nextReviewDate = new Date();
  nextReviewDate.setDate(nextReviewDate.getDate() + intervalInDays);

  const finalMetrics = {
    ...newMetrics,
    intervalInDays,
    nextReviewDate,
    lastReviewDate: new Date()
  };

  console.log(`✨ [FSRS] Métricas finais para resposta "${response}":`, {
    estabilidade: {
      anterior: stability.toFixed(3),
      nova: finalMetrics.stability.toFixed(3),
      formula: response === 'error' ?
        'max(w12, stability * w13)' :
        'stability * stabilityMultiplier * difficultyFactor * retrievabilityFactor',
      multiplicadores: {
        again: w.w5.toFixed(2), // 0.2
        hard: w.w6.toFixed(2), // 0.7
        medium: w.w7.toFixed(2), // 1.0
        easy: w.w8.toFixed(2) // 1.3
      }
    },
    dificuldade: {
      anterior: difficulty.toFixed(3),
      nova: finalMetrics.difficulty.toFixed(3),
      ajuste: response === 'error' ? `+${w.w16}` :
              response === 'hard' ? `+${w.w10}` :
              response === 'medium' ? '0' :
              `-${w.w10}`,
      formula: 'dificuldade_atual + ajuste_baseado_na_resposta'
    },
    retrievability: {
      calculado: finalMetrics.retrievability.toFixed(4),
      formula: 'exp(-1 / nova_estabilidade)'
    },
    intervalo: {
      dias: finalMetrics.intervalInDays,
      formula: 'ceil(nova_estabilidade)',
      proxima_revisao: nextReviewDate.toISOString()
    }
  });

  return finalMetrics;
};

interface CalculationParams {
  stability: number;
  difficulty: number;
  retrievability: number;
  elapsedDays: number;
}

function calculateNewMetrics(
  response: FlashcardResponse,
  params: CalculationParams
): FSRSMetrics {
  const { stability, difficulty, retrievability, elapsedDays } = params;

  console.log(`🧮 [FSRS] Calculando métricas para "${response}":`, {
    dados_entrada: {
      estabilidade: stability.toFixed(3),
      dificuldade: difficulty.toFixed(3),
      retrievability: retrievability.toFixed(4),
      dias_passados: elapsedDays.toFixed(2)
    }
  });

  // Calcula nova estabilidade
  const stabilityMultiplier = getStabilityMultiplier(response);
  const difficultyFactor = 1 + w.w14 * (1 - difficulty / 10);
  const retrievabilityFactor = 1 + w.w17 * (retrievability - 0.5);

  console.log('🔢 [FSRS] Fatores de cálculo:', {
    multiplicador_estabilidade: {
      valor: stabilityMultiplier.toFixed(3),
      descricao: `Multiplicador para resposta ${response}`
    },
    fator_dificuldade: {
      formula: '1 + w14 * (1 - dificuldade/10)',
      valores: {
        w14: w.w14.toFixed(3),
        dificuldade: difficulty.toFixed(3)
      },
      resultado: difficultyFactor.toFixed(3)
    },
    fator_retrievability: {
      formula: '1 + w17 * (retrievability - 0.5)',
      valores: {
        w17: w.w17.toFixed(3),
        retrievability: retrievability.toFixed(4)
      },
      resultado: retrievabilityFactor.toFixed(3)
    }
  });

  let newStability: number;
  if (response === 'error') {
    newStability = Math.max(w.w12, stability * w.w13);
    console.log('📉 [FSRS] Cálculo de estabilidade para erro:', {
      formula: 'max(w12, stability * w13)',
      valores: {
        w12: w.w12.toFixed(3),
        w13: w.w13.toFixed(3),
        stability: stability.toFixed(3)
      },
      resultado: newStability.toFixed(3)
    });
  } else {
    newStability = stability * stabilityMultiplier * difficultyFactor * retrievabilityFactor;
    console.log('📈 [FSRS] Cálculo de estabilidade:', {
      formula: 'stability * stabilityMultiplier * difficultyFactor * retrievabilityFactor',
      valores: {
        stability: stability.toFixed(3),
        stabilityMultiplier: stabilityMultiplier.toFixed(3),
        difficultyFactor: difficultyFactor.toFixed(3),
        retrievabilityFactor: retrievabilityFactor.toFixed(3)
      },
      resultado: newStability.toFixed(3)
    });
  }

  // Calcula nova dificuldade
  let newDifficulty = difficulty;
  switch (response) {
    case 'error':
      newDifficulty = Math.min(10, difficulty + w.w16);
      break;
    case 'hard':
      newDifficulty = Math.min(10, difficulty + w.w10);
      break;
    case 'medium':
      // Dificuldade permanece a mesma
      break;
    case 'easy':
      newDifficulty = Math.max(1, difficulty - w.w10);
      break;
  }

  console.log('🎯 [FSRS] Ajuste de dificuldade:', {
    resposta: response,
    dificuldade_anterior: difficulty.toFixed(3),
    formula: `${response === 'error' ? 'min(10, difficulty + w16)' :
              response === 'hard' ? 'min(10, difficulty + w10)' :
              response === 'medium' ? 'difficulty' :
              'max(1, difficulty - w10)'}`,
    valores: {
      w10: w.w10.toFixed(3),
      w16: w.w16.toFixed(3)
    },
    resultado: newDifficulty.toFixed(3)
  });

  // Calcula novo retrievability
  const newRetrievability = Math.exp(-1 / newStability);



  return {
    stability: Math.max(w.w4, Math.min(365, newStability)),
    difficulty: Math.max(1, Math.min(10, newDifficulty)),
    retrievability: Math.max(0.1, Math.min(1, newRetrievability)),
    lastReviewDate: new Date(),
    intervalInDays: Math.ceil(newStability),
    nextReviewDate: new Date(Date.now() + Math.ceil(newStability) * 24 * 60 * 60 * 1000)
  };
}

function getStabilityMultiplier(response: FlashcardResponse): number {
  const multiplier = {
    'error': w.w5,  // 0.2
    'hard': w.w6,   // 0.7
    'medium': w.w7, // 1.0
    'easy': 1.15    // Ajustando para 1.15 em vez de 1.3 para evitar intervalos longos
  }[response];

  console.log('📊 [FSRS] Multiplicador de estabilidade:', {
    resposta: response,
    valor: multiplier.toFixed(3),
    descricao: `Peso w${response === 'error' ? '5' : 
                    response === 'hard' ? '6' :
                    response === 'medium' ? '7' : '8'}`
  });

  return multiplier;
}
