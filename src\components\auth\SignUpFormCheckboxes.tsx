import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { UseFormReturn } from "react-hook-form";
import { Link } from "react-router-dom";

interface SignUpFormCheckboxesProps {
  form: UseFormReturn<any>;
}

export const SignUpFormCheckboxes = ({ form }: SignUpFormCheckboxesProps) => {
  return (
    <>
      <FormField
        control={form.control}
        name="isAdult"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>
                Tenho mais de 17 anos
              </FormLabel>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="acceptTerms"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>
                Aceito os <Link to="/terms" className="text-primary hover:underline">termos e condições</Link> e a <Link to="/privacy" className="text-primary hover:underline">política de privacidade</Link>
              </FormLabel>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};