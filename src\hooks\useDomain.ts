
import { useEffect, useState } from 'react';
import { useUserData } from './useUserData';

type UserProfile = {
  id: string;
  preparation_type: string | null;
  specialty: string | null;
};

/**
 * Hook to determine the domain based on user's preparation type and specialty
 * If preparation_type is 'residencia', domain is 'residencia'
 * If preparation_type is 'titulo', domain is the user's specialty (e.g., 'oftalmologia')
 * If preparation_type is 'revalida', domain is 'revalida'
 * Falls back to 'residencia' if user profile isn't available yet
 */
export const useDomain = () => {
  const { profile, isLoading: userDataLoading, isReady: userDataReady } = useUserData();
  const [domain, setDomain] = useState<string>('');
  const [isResidencia, setIsResidencia] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Aguardar dados do usuário carregarem
    if (userDataLoading) {
      return;
    }

    // Se não há perfil, usar padrão
    if (!profile) {
      setDomain('residencia');
      setIsResidencia(true);
      setIsLoading(false);
      return;
    }

    // Determinar domain baseado no preparation_type e specialty
    if (profile.preparation_type === 'residencia') {
      setDomain('residencia');
      setIsResidencia(true);
    } else if (profile.preparation_type === 'revalida') {
      setDomain('revalida');
      setIsResidencia(false);
    } else if (profile.preparation_type === 'titulo' && profile.specialty) {
      setDomain(profile.specialty.toLowerCase());
      setIsResidencia(false);
    } else {
      console.warn('⚠️ [useDomain] Unrecognized preparation_type or missing specialty, using default domain "residencia"');
      setDomain('residencia');
      setIsResidencia(true);
    }

    setIsLoading(false);
  }, [profile, userDataLoading]); // Usar dados do useUserData

  // Log the domain status once after it's determined
  useEffect(() => {
    if (!isLoading) {
    }
  }, [domain, isResidencia, isLoading]);

  return {
    domain,
    isLoading,
    userProfile: profile,
    isResidencia: isResidencia === null ? true : isResidencia, // Default to true if still null
    isReady: !isLoading && !!domain // New flag to indicate the domain is ready to be used
  };
};
