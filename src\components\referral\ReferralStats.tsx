import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Users,
  Gift,
  Share2,
  Trophy,
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertCircle,
  Edit3,
  Sparkles
} from 'lucide-react';
import { useReferralSystem } from '@/hooks/useReferralSystem';
import { ShareButton } from './ShareButton';
import { ReferredUserCard } from './ReferredUserCard';
import { CustomReferralCodeDialog } from './CustomReferralCodeDialog';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const ReferralStats: React.FC = () => {
  const {
    referralData,
    referredUsers,
    isLoadingReferral,
    isLoadingReferred,
    hasReferralCode,
    totalReferrals,
    referralCode,
    createReferralCode,
    updateReferralCode,
    isCreatingCode,
    isUpdatingCode
  } = useReferralSystem();

  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showCustomCodeDialog, setShowCustomCodeDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');

  const handleCreateReferralCode = async () => {
    try {
      await createReferralCode();
      setDialogMessage('Código de referência criado com sucesso!');
      setShowSuccessDialog(true);
    } catch (error: any) {
      setDialogMessage('Erro ao criar código: ' + error.message);
      setShowErrorDialog(true);
    }
  };

  const handleUpdateReferralCode = async (newCode: string): Promise<boolean> => {
    try {
      await updateReferralCode(newCode);
      setDialogMessage('Código atualizado com sucesso!');
      setShowSuccessDialog(true);
      return true;
    } catch (error: any) {
      setDialogMessage('Erro ao atualizar código: ' + error.message);
      setShowErrorDialog(true);
      return false;
    }
  };

  if (isLoadingReferral) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <h3 className="text-xl font-semibold">Sistema de Referência</h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Users className="h-5 w-5 text-blue-500" />
        <h3 className="text-xl font-semibold">Sistema de Referência</h3>
        {totalReferrals > 0 && (
          <Badge variant="secondary" className="ml-auto">
            {totalReferrals} {totalReferrals === 1 ? 'amigo' : 'amigos'}
          </Badge>
        )}
      </div>
        {!hasReferralCode ? (
          <div className="text-center space-y-4">
            <div className="p-6 bg-gradient-to-br from-[#E6F2FF] to-[#FFE6E6] rounded-lg border border-black/20">
              <Gift className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2 text-gray-800">Convide Amigos!</h3>
              <p className="text-sm text-gray-700 mb-4">
                Crie seu código de convite e compartilhe a MedEvo com seus amigos.
                Ajude a construir nossa comunidade de estudos!
              </p>
              <div className="space-y-3">
                <Button
                  onClick={handleCreateReferralCode}
                  disabled={isCreatingCode}
                  className="w-full bg-[#E6F2FF] text-black hover:bg-[#CCE7FF] border-2 border-black/30"
                >
                  {isCreatingCode ? 'Criando...' : 'Gerar Código Aleatório'}
                </Button>

                <div className="flex items-center gap-2">
                  <div className="flex-1 h-px bg-gray-300"></div>
                  <span className="text-xs text-gray-500 px-2">ou</span>
                  <div className="flex-1 h-px bg-gray-300"></div>
                </div>

                <Button
                  onClick={() => setShowCustomCodeDialog(true)}
                  variant="outline"
                  className="w-full border-2 border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Criar Código Personalizado
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Estatísticas principais */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-[#E6F3E6]/50 rounded-lg border border-black/20">
                <div className="text-3xl font-bold text-green-700 mb-1">
                  {totalReferrals}
                </div>
                <div className="text-sm text-gray-700 font-medium">
                  {totalReferrals === 1 ? 'Amigo Convidado' : 'Amigos Convidados'}
                </div>
              </div>

              <div className="text-center p-4 bg-[#E6F2FF]/50 rounded-lg border border-black/20 relative group">
                <div className="text-lg font-bold text-blue-700 mb-1">
                  {referralCode}
                </div>
                <div className="text-sm text-gray-700 font-medium mb-2">
                  Seu Código
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowCustomCodeDialog(true)}
                  className="text-xs h-7 px-2 bg-white/80 hover:bg-white border-blue-300 text-blue-700 hover:text-blue-800"
                >
                  <Edit3 className="h-3 w-3 mr-1" />
                  Personalizar
                </Button>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                <div className="text-lg font-bold text-purple-600 mb-1">
                  {referralData?.created_at ? 
                    formatDistanceToNow(new Date(referralData.created_at), { 
                      addSuffix: true, 
                      locale: ptBR 
                    }) : 'Recente'
                  }
                </div>
                <div className="text-sm text-purple-700">
                  Criado
                </div>
              </div>
            </div>



            {/* Botão de compartilhamento */}
            <div className="flex justify-center">
              <ShareButton
                variant="default"
                size="lg"
                className="w-full md:w-auto"
              />
            </div>

            {/* Lista de amigos convidados */}
            {referredUsers && referredUsers.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  <h4 className="font-semibold text-lg">Amigos que você trouxe:</h4>
                </div>

                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {referredUsers.map((user, index) => (
                    <ReferredUserCard
                      key={user.id}
                      user={user}
                      index={index}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Motivação para compartilhar mais */}
            {totalReferrals < 5 && (
              <div className="p-4 bg-gradient-to-r from-[#FFE4B5]/50 to-[#FEF7CD]/50 rounded-lg border border-black/20">
                <div className="flex items-center gap-2 mb-2">
                  <Gift className="h-5 w-5 text-orange-600" />
                  <span className="font-semibold text-gray-800">
                    Continue compartilhando!
                  </span>
                </div>
                <p className="text-sm text-gray-700">
                  Quanto mais amigos você trouxer, mais forte nossa comunidade fica.
                  Cada novo membro contribui para o crescimento da plataforma!
                </p>
              </div>
            )}

            {/* Conquista especial */}
            {totalReferrals >= 5 && (
              <div className="p-4 bg-gradient-to-r from-[#FFE6E6]/50 to-[#E6F2FF]/50 rounded-lg border border-black/20">
                <div className="flex items-center gap-2 mb-2">
                  <Trophy className="h-5 w-5 text-purple-600" />
                  <span className="font-semibold text-gray-800">
                    🎉 Embaixador da Comunidade!
                  </span>
                </div>
                <p className="text-sm text-gray-700">
                  Parabéns! Você trouxe {totalReferrals} amigos para a MedEvo.
                  Você é um verdadeiro embaixador da nossa comunidade de estudos!
                </p>
              </div>
            )}
          </>
        )}

        {/* Dialog de Sucesso */}
        <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
          <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
            <div className="w-full py-6 bg-gradient-to-br from-green-50 to-emerald-50 border-b border-black/20">
              <div className="flex flex-col items-center text-center gap-3 px-6">
                <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-green-200">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Sucesso!</h2>
              </div>
            </div>
            <div className="p-6 space-y-4">
              <DialogHeader>
                <DialogTitle className="sr-only">Operação realizada com sucesso</DialogTitle>
                <DialogDescription className="text-center text-gray-700">
                  {dialogMessage}
                </DialogDescription>
              </DialogHeader>
              <Button
                onClick={() => setShowSuccessDialog(false)}
                className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl h-12 font-semibold"
              >
                Continuar
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Dialog de Erro */}
        <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
          <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
            <div className="w-full py-6 bg-gradient-to-br from-red-50 to-pink-50 border-b border-black/20">
              <div className="flex flex-col items-center text-center gap-3 px-6">
                <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-red-200">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Atenção</h2>
              </div>
            </div>
            <div className="p-6 space-y-4">
              <DialogHeader>
                <DialogTitle className="sr-only">Erro na operação</DialogTitle>
                <DialogDescription className="text-center text-gray-700">
                  {dialogMessage}
                </DialogDescription>
              </DialogHeader>
              <Button
                onClick={() => setShowErrorDialog(false)}
                className="w-full bg-red-600 hover:bg-red-700 text-white rounded-xl h-12 font-semibold"
              >
                Entendi
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Dialog de Código Personalizado */}
        <CustomReferralCodeDialog
          open={showCustomCodeDialog}
          onOpenChange={setShowCustomCodeDialog}
          currentCode={referralCode}
          onCodeUpdate={handleUpdateReferralCode}
          isUpdating={isUpdatingCode}
        />
    </div>
  );
};
