import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Flashcard, FlashcardResponse } from '@/types/flashcard';
import { toast } from 'sonner';
import type { Json } from '@/integrations/supabase/types/json';

export const useFlashcardResponse = (sessionId: string) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleResponse = async (card: Flashcard, response: FlashcardResponse) => {
    if (isSubmitting) return false;

    setIsSubmitting(true);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) throw new Error("User not authenticated");

      // Update session card
      const { error: sessionError } = await supabase
        .from('flashcards_session_cards')
        .upsert({
          session_id: sessionId,
          card_id: card.id,
          response,
          review_status: 'reviewed',
          last_review_time: new Date().toISOString(),
        }, {
          onConflict: 'session_id,card_id'
        });

      if (sessionError) throw sessionError;

      return true;
    } catch (error: any) {
      console.error("❌ [useFlashcardResponse] Erro ao processar resposta:", error);
      toast.error("Erro ao salvar resposta do flashcard");
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleResponse,
    isSubmitting
  };
};