/**
 * Filtros para suprimir warnings desnecessários no console
 * Especialmente útil para warnings de cookies Cloudflare que não afetam a funcionalidade
 */

// Lista de mensagens que devem ser suprimidas
const SUPPRESSED_MESSAGES = [
  'O cookie "__cf_bm" foi rejeitado por ter domínio inválido',
  'The cookie "__cf_bm" was rejected because it has an invalid domain',
  '<PERSON><PERSON> "__cf_bm" rejected due to invalid domain'
];

// Função original do console.warn
const originalWarn = console.warn;
const originalError = console.error;

/**
 * Filtro personalizado para console.warn
 */
const filteredWarn = (...args: any[]) => {
  const message = args.join(' ');
  
  // Verificar se a mensagem deve ser suprimida
  const shouldSuppress = SUPPRESSED_MESSAGES.some(suppressedMsg => 
    message.includes(suppressedMsg)
  );
  
  if (!shouldSuppress) {
    originalWarn.apply(console, args);
  }
};

/**
 * Filtro personalizado para console.error
 */
const filteredError = (...args: any[]) => {
  const message = args.join(' ');
  
  // Verificar se a mensagem deve ser suprimida
  const shouldSuppress = SUPPRESSED_MESSAGES.some(suppressedMsg => 
    message.includes(suppressedMsg)
  );
  
  if (!shouldSuppress) {
    originalError.apply(console, args);
  }
};

/**
 * Aplicar filtros de console
 * Deve ser chamado na inicialização da aplicação
 */
export const applyConsoleFilters = () => {
  // Apenas aplicar em produção ou quando explicitamente habilitado
  if (import.meta.env.PROD || import.meta.env.VITE_FILTER_CONSOLE === 'true') {
    console.warn = filteredWarn;
    console.error = filteredError;
    

  }
};

/**
 * Remover filtros de console (para debugging)
 */
export const removeConsoleFilters = () => {
  console.warn = originalWarn;
  console.error = originalError;
  

};

/**
 * Verificar se uma mensagem seria suprimida
 */
export const wouldBeSuppressed = (message: string): boolean => {
  return SUPPRESSED_MESSAGES.some(suppressedMsg => 
    message.includes(suppressedMsg)
  );
};

/**
 * Adicionar nova mensagem à lista de supressão
 */
export const addSuppressedMessage = (message: string) => {
  if (!SUPPRESSED_MESSAGES.includes(message)) {
    SUPPRESSED_MESSAGES.push(message);
  }
};

/**
 * Remover mensagem da lista de supressão
 */
export const removeSuppressedMessage = (message: string) => {
  const index = SUPPRESSED_MESSAGES.indexOf(message);
  if (index > -1) {
    SUPPRESSED_MESSAGES.splice(index, 1);
  }
};

/**
 * Obter lista atual de mensagens suprimidas
 */
export const getSuppressedMessages = (): string[] => {
  return [...SUPPRESSED_MESSAGES];
};

/**
 * Log personalizado para desenvolvimento que sempre aparece
 */
export const devLog = (...args: any[]) => {
  if (import.meta.env.DEV) {
    originalWarn.apply(console, ['[DEV]', ...args]);
  }
};

/**
 * Log personalizado para erros críticos que sempre aparecem
 */
export const criticalError = (...args: any[]) => {
  originalError.apply(console, ['[CRITICAL]', ...args]);
};
