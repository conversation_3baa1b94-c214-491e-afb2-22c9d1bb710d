
import React from 'react';
import { useParams } from 'react-router-dom';
import { useResultsData } from '@/components/results/useResultsData';
import { ResultsContainer } from '@/components/results/ResultsContainer';
import Header from '@/components/Header';
import StudyNavBar from "@/components/study/StudyNavBar";
import { Card } from '@/components/ui/card';
import { Trophy, Loader2 } from 'lucide-react';

export const Results = () => {
  const { sessionId } = useParams();
  const { stats, isLoading } = useResultsData(sessionId || null);

  if (isLoading) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className="container mx-auto px-4 pt-16 md:py-8 max-w-4xl">
          <div className="min-h-[60vh] flex flex-col items-center justify-center">
            <div className="rounded-full bg-primary/10 p-4 mb-4">
              <Loader2 className="h-8 w-8 text-primary animate-spin" />
            </div>
            <p className="text-gray-500 text-lg">Carregando resultados...</p>
          </div>
        </div>
      </>
    );
  }

  if (!stats) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className="container mx-auto px-4 pt-16 md:py-8 max-w-4xl">
          <Card className="p-12 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
            <div className="text-center">
              <div className="rounded-full bg-gray-200 p-4 inline-block mb-4">
                <Trophy className="h-8 w-8 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-600 mb-2">Nenhum resultado encontrado</h2>
              <p className="text-gray-500 text-lg">
                Não foi possível encontrar estatísticas para esta sessão de estudos.
              </p>
            </div>
          </Card>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <StudyNavBar className="mb-8" />
      <div className="pt-16 md:pt-0 pb-20 bg-gradient-to-b from-gray-50 to-white">
        <ResultsContainer stats={stats} />
      </div>
    </>
  );
};

export default Results;
