import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FlashcardSessionContent } from "./FlashcardSessionContent";
import { FlashcardSessionComplete } from "./FlashcardSessionComplete";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { toast } from "sonner";
import type { FlashcardWithHierarchy } from "@/types/flashcardCollaborate";

const BATCH_SIZE = 50;

export const FlashcardSession = () => {
  const { sessionId } = useParams();
  const [flashcards, setFlashcards] = useState<FlashcardWithHierarchy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    const loadSessionFlashcards = async () => {
      try {
        if (!sessionId) {
          throw new Error("No session ID provided");
        }

        const { data: session, error: sessionError } = await supabase
          .from("flashcards_sessions")
          .select(`
            *,
            flashcards_session_cards(
              card_id,
              response,
              review_status
            )
          `)
          .eq("id", sessionId)
          .single();

        if (sessionError) throw sessionError;

        setIsCompleted(session.status === "completed");

        if (!session.cards?.length) {
          throw new Error("No cards found in session");
        }

        const answeredCardIds = new Set(
          session.flashcards_session_cards
            .filter(card => card.review_status === 'reviewed')
            .map(card => card.card_id)
        );


        const unansweredCardIds = session.cards.filter(id => !answeredCardIds.has(id));

        let allCards: FlashcardWithHierarchy[] = [];
        
        for (let i = 0; i < unansweredCardIds.length; i += BATCH_SIZE) {
          const batchIds = unansweredCardIds.slice(i, i + BATCH_SIZE);
          
          const { data: batchCards, error: batchError } = await supabase
            .from("flashcards_cards")
            .select(`
              *,
              specialty:flashcards_specialty(*),
              theme:flashcards_theme(*),
              focus:flashcards_focus(*)
            `)
            .in("id", batchIds);

          if (batchError) {
            console.error(`❌ [FlashcardSession] Error fetching batch:`, batchError);
            continue;
          }

          if (batchCards) {
            const formattedBatchCards = batchCards.map(card => ({
              ...card,
              current_state: card.current_state as FlashcardWithHierarchy["current_state"],
              hierarchy: {
                specialty: card.specialty,
                theme: card.theme,
                focus: card.focus
              }
            }));
            allCards = [...allCards, ...formattedBatchCards];
          }
        }

        setFlashcards(allCards);

      } catch (error: any) {
        console.error("❌ [FlashcardSession] Error:", error);
        toast.error("Error loading flashcards", {
          description: error.message
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSessionFlashcards();
  }, [sessionId]);

  if (!sessionId) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in">
        {isCompleted ? (
          <FlashcardSessionComplete sessionId={sessionId} />
        ) : (
          <FlashcardSessionContent 
            flashcards={flashcards}
            isLoading={isLoading}
            sessionId={sessionId}
            onComplete={() => setIsCompleted(true)}
          />
        )}
      </div>
    </div>
  );
};
