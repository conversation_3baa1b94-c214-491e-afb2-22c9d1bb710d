
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { 
  Pill, 
  Book, 
  Brain, 
  Baby, 
  Calculator,
  Construction, 
  Activity,
  Newspaper,
  Skull,
  StickyNote,
  MessageSquare,
  FileText,
  ClipboardList
} from "lucide-react";
import CategoryCard from "@/components/CategoryCard";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AuthDialog from "@/components/auth/AuthDialog";
import { AIDialog } from "./AIDialog";
import { MedUnityBanner } from "@/components/promotions/MedUnityBanner";

const mainCategories = [
  {
    title: "Cálculo de Medicamentos",
    description: "Dosagens automáticas de forma fácil e rápida.",
    icon: Pill,
    color: "bg-amber-200 border-t border-gray-220",
    path: "/medicamentos/painel",
    requiresAuth: false,
    badge: "Automático"
  },
  {
    title: "Condutas e Manejos",
    description: "Protocolos e condutas para prática clínica.",
    icon: FileText,
    color: "bg-emerald-200 border-gray-220",
    path: "/condutas-e-manejos",
    requiresAuth: false,
    badge: "Novo",
    showNewBadge: true
  },
  {
    title: "Puericultura",
    description: "Crescimento, vacinas, DNPM e fórmulas infantis.",
    icon: Baby,
    color: "bg-purple-200 border-gray-220",
    path: "/puericultura",
    requiresAuth: false,
    badge: "Completo e Automático"
  },
  {
    title: "Prescrições",
    description: "Crie e gerencie prescrições personalizadas.",
    icon: Book,
    color: "bg-red-200 border-gray-220",
    path: "/prescriptions",
    requiresAuth: true,
    badge: "Crie suas prescrições"
  },
  {
    title: "Assistente IA",
    description: "Converse com nossa IA ou use o DxBrain para auxílio em diagnósticos.",
    icon: Brain,
    color: "bg-indigo-200 border-gray-220",
    path: "/ai-assistant",
    requiresAuth: true,
    badge: "Auxílio IA",
    isAI: true,
  },
  {
    title: "Calculadoras e Escalas",
    description: "Calculadoras pediátricas de escores para uso diário.",
    icon: Calculator,
    color: "bg-blue-200 border-gray-220",
    path: "/calculadoras",
    requiresAuth: false,
    badge: "Automático"
  },
  {
    title: "Fluxogramas",
    description: "Manejo de urgências e emergências pediátricas.",
    icon: Activity,
    color: "bg-cyan-200 border-gray-220",
    path: "/flowcharts",
    requiresAuth: false,
    badge: "Automático"
  },
  {
    title: "Intoxicações",
    description: "Toxíndromes, antídotos e doses calculadas.",
    icon: Skull,
    color: "bg-rose-200 border-gray-220",
    path: "/poisonings",
    requiresAuth: false,
  },
  {
    title: "Anotações",
    description: "Organize suas anotações de forma prática e rápida, com acesso fácil de qualquer lugar.",
    icon: StickyNote,
    color: "bg-yellow-200 border-gray-220",
    path: "/notes",
    requiresAuth: true,
  },
  {
    title: "CID-10",
    description: "Consulta rápida de códigos CID.",
    icon: Book,
    color: "bg-green-200 border-gray-220",
    path: "/icd",
    requiresAuth: false
  },
  {
    title: "Bot WhatsApp",
    description: "Acesse as dosagens pediátricas 24 horas por dia através do nosso bot.",
    icon: MessageSquare,
    color: "bg-green-200 border-gray-220",
    path: "/whatsapp-bot",
    requiresAuth: false,
    badge: "24/7"
  }
];

export const CategoryList: React.FC = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [pendingPath, setPendingPath] = useState<string | null>(null);
  const [showAIDialog, setShowAIDialog] = useState(false);

  const handleCategoryClick = async (path: string, requiresAuth: boolean, isAI?: boolean) => {
    if (isAI) {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setPendingPath(path);
        setShowAuthDialog(true);
        return;
      }
      setShowAIDialog(true);
      return;
    }

    if (requiresAuth) {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        setPendingPath(path);
        setShowAuthDialog(true);
        return;
      }
    }
    
    navigate(path);
  };

  return (
    <>
      <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-6 max-w-6xl mx-auto">
        {mainCategories.map((category, index) => (
          <div 
            key={index} 
            className="transform transition-all duration-500 hover:scale-[1.02] relative"
            style={{ 
              animationDelay: `${index * 100}ms`,
              animation: 'fade-in-up 0.5s ease-out forwards',
              opacity: 0 
            }}
          >
            {category.showNewBadge && (
              <span className="absolute -top-2 -right-2 bg-destructive text-white text-[10px] px-1.5 py-0.5 rounded-full font-medium animate-subtle-pulse z-10">
                Novo
              </span>
            )}
            <CategoryCard 
              {...category} 
              onClick={() => handleCategoryClick(category.path, category.requiresAuth || false, category.isAI)}
            />
          </div>
        ))}
        
        {/* MedUnity Banner Card - made responsive */}
        <div 
          className="transform transition-all duration-500 hover:scale-[1.02] relative"
          style={{ 
            animationDelay: `${mainCategories.length * 100}ms`,
            animation: 'fade-in-up 0.5s ease-out forwards',
            opacity: 0 
          }}
        >
          <MedUnityBanner />
        </div>
      </div>

      <AIDialog open={showAIDialog} onOpenChange={setShowAIDialog} />

      {showAuthDialog && (
        <AuthDialog 
          defaultOpen={true} 
          hidden={true} 
          onOpenChange={(open) => {
            setShowAuthDialog(open);
            if (!open) {
              setPendingPath(null);
            }
          }} 
        />
      )}
    </>
  );
};
