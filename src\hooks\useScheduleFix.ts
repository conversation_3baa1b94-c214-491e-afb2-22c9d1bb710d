
import { useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { forceWeekBoundaries } from "@/utils/dateUtils";

export const useScheduleFix = () => {
  const { toast } = useToast();

  const fixWeekBoundaries = useCallback(async () => {
    try {
      // Fetch all schedules
      const { data: schedules, error } = await supabase
        .from('study_schedules')
        .select('*')
        .order('week_number', { ascending: true });

      if (error) throw error;

      if (!schedules || schedules.length === 0) {
        return false;
      }

      // Process each schedule
      let fixCount = 0;
      for (const schedule of schedules) {
        const { week_start_date, week_end_date, id } = schedule;

        // Get corrected dates
        const { correctedStartDate, correctedEndDate } = forceWeekBoundaries(week_start_date, week_end_date);

        // Check if dates need correction
        if (week_start_date !== correctedStartDate || week_end_date !== correctedEndDate) {
          // Update the schedule
          const { error: updateError } = await supabase
            .from('study_schedules')
            .update({
              week_start_date: correctedStartDate,
              week_end_date: correctedEndDate
            })
            .eq('id', id);

          if (updateError) {
            continue;
          }

          fixCount++;
        }
      }



      if (fixCount > 0) {
        toast({
          title: "Datas do cronograma corrigidas",
          description: `${fixCount} semanas foram atualizadas para seguir o padrão Domingo-Sábado.`,
        });
      }

      return fixCount > 0;
    } catch (error) {
      toast({
        title: "Erro ao corrigir datas",
        description: "Não foi possível corrigir as datas do cronograma.",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  return { fixWeekBoundaries };
};
