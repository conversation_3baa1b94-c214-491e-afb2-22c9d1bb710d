import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Send, CheckCircle, Lightbulb, Bug, MessageSquare } from 'lucide-react';
import { useFeedbackSubmission } from '@/hooks/useFeedbackSubmission';
import { useAuth } from '@/hooks/useAuth';
import { motion } from 'framer-motion';

type FeedbackType = 'suggestion' | 'bug' | 'compliment' | 'complaint' | 'feature' | 'general';

interface FeedbackCategory {
  id: FeedbackType;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

const feedbackCategories: FeedbackCategory[] = [
  {
    id: 'suggestion',
    title: 'Sugestão',
    description: 'Ideias para melhorar a plataforma',
    icon: <Lightbulb className="h-5 w-5" />,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 border-yellow-200'
  },
  {
    id: 'bug',
    title: 'Problema/Bug',
    description: 'Reportar erros ou problemas técnicos',
    icon: <Bug className="h-5 w-5" />,
    color: 'text-red-600',
    bgColor: 'bg-red-50 border-red-200'
  },
  {
    id: 'compliment',
    title: 'Elogio',
    description: 'Compartilhar experiências positivas',
    icon: <Star className="h-5 w-5" />,
    color: 'text-green-600',
    bgColor: 'bg-green-50 border-green-200'
  },
  {
    id: 'complaint',
    title: 'Reclamação',
    description: 'Reportar problemas ou insatisfações',
    icon: <MessageSquare className="h-5 w-5" />,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 border-orange-200'
  },
  {
    id: 'feature',
    title: 'Nova Funcionalidade',
    description: 'Solicitar novas funcionalidades',
    icon: <CheckCircle className="h-5 w-5" />,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200'
  }
];

export const FeedbackForm = () => {
  const { user } = useAuth();
  const { submitFeedback, isSubmitting, isSuccess } = useFeedbackSubmission();
  
  const [selectedType, setSelectedType] = useState<FeedbackType | null>(null);
  const [rating, setRating] = useState<number>(5);
  const [comment, setComment] = useState('');
  const [email, setEmail] = useState(user?.email || '');
  const [whatsapp, setWhatsapp] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedType || !comment.trim()) {
      return;
    }

    await submitFeedback({
      feedbackType: selectedType,
      rating,
      comment: comment.trim(),
      email: email.trim() || null,
      whatsapp: whatsapp.trim() || null
    });

    // Reset form on success
    if (isSuccess) {
      setSelectedType(null);
      setComment('');
      setWhatsapp('');
      setRating(5);
    }
  };

  if (isSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-8"
      >
        <div className="bg-green-50 border-2 border-green-200 rounded-xl p-6 max-w-md mx-auto">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-green-800 mb-2">
            Feedback Enviado!
          </h3>
          <p className="text-green-700 mb-4">
            Obrigado por compartilhar sua opinião conosco. Sua contribuição é muito importante!
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            Enviar Outro Feedback
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Feedback Type Selection */}
      <div>
        <Label className="text-base font-semibold mb-4 block">
          Que tipo de feedback você gostaria de deixar?
        </Label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {feedbackCategories.map((category) => (
            <Card
              key={category.id}
              className={`cursor-pointer transition-all border-2 ${
                selectedType === category.id
                  ? 'border-black shadow-card-sm transform scale-105'
                  : 'border-gray-200 hover:border-gray-300'
              } ${category.bgColor}`}
              onClick={() => setSelectedType(category.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={category.color}>
                    {category.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-sm mb-1">
                      {category.title}
                    </h4>
                    <p className="text-xs text-gray-600">
                      {category.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {selectedType && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Rating */}
          <div>
            <Label className="text-base font-semibold mb-3 block">
              Como você avalia nossa plataforma? (1-5 estrelas)
            </Label>
            <div className="flex gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  className="transition-colors"
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= rating
                        ? 'text-yellow-400 fill-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Comment */}
          <div>
            <Label htmlFor="comment" className="text-base font-semibold mb-3 block">
              Descreva seu feedback em detalhes
            </Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={getPlaceholderText(selectedType)}
              className="min-h-[120px] border-2 border-gray-200 focus:border-black"
              required
            />
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email" className="text-sm font-medium mb-2 block">
                Email (opcional)
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="border-2 border-gray-200 focus:border-black"
              />
            </div>
            
            <div>
              <Label htmlFor="whatsapp" className="text-sm font-medium mb-2 block">
                WhatsApp (opcional)
              </Label>
              <Input
                id="whatsapp"
                value={whatsapp}
                onChange={(e) => setWhatsapp(e.target.value)}
                placeholder="(11) 99999-9999"
                className="border-2 border-gray-200 focus:border-black"
              />
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isSubmitting || !comment.trim()}
            className="w-full bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Enviando...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Enviar Feedback
              </>
            )}
          </Button>
        </motion.div>
      )}
    </form>
  );
};

const getPlaceholderText = (type: FeedbackType): string => {
  switch (type) {
    case 'suggestion':
      return 'Ex: Seria interessante ter uma funcionalidade que permita...';
    case 'bug':
      return 'Ex: Quando clico em "Iniciar Prática", a página não carrega e aparece...';
    case 'compliment':
      return 'Ex: Adorei a interface da plataforma, especialmente...';
    case 'complaint':
      return 'Ex: Estou tendo dificuldades com... e gostaria que fosse melhorado...';
    case 'feature':
      return 'Ex: Gostaria de uma funcionalidade que permitisse...';
    default:
      return 'Descreva seu feedback aqui...';
  }
};
