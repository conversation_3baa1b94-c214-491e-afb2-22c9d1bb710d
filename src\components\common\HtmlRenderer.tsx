
import React from 'react';
import { cn } from '@/lib/utils';
import { processHtmlContent } from '@/utils/htmlProcessor';

interface HtmlRendererProps {
  htmlContent: string;
  className?: string;
}

export const HtmlRenderer: React.FC<HtmlRendererProps> = ({ 
  htmlContent,
  className 
}) => {
  const cleanedHtml = processHtmlContent(htmlContent);

  return (
    <div
      className={cn(
        "prose max-w-none whitespace-pre-wrap font-sans leading-relaxed",
        className
      )}
    >
      {cleanedHtml.split('\n').map((line, index) => (
        <React.Fragment key={index}>
          {line}
          {index < cleanedHtml.split('\n').length - 1 && <br />}
        </React.Fragment>
      ))}
    </div>
  );
};
