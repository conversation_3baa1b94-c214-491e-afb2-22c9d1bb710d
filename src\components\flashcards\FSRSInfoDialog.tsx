
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Brain, Book } from "lucide-react";

export const FSRSInfoDialog = () => {
  return (
    <Dialog>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
        <DialogTrigger asChild>
          <Button
            variant="hackYellow"
            size="sm"
            className="gap-2 border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
          >
            <Brain className="w-4 h-4" />
            Como funciona o FSRS-5?
          </Button>
        </DialogTrigger>
      </div>
      <DialogContent className="w-[80dvw] max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Brain className="w-6 h-6 text-hackathon-green" />
            Como funciona o FSRS-5?
          </DialogTitle>
          <DialogDescription>
            Entenda como o algoritmo FSRS-5 otimiza seu aprendizado com repetição espaçada inteligente.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Book className="w-5 h-5 text-hackathon-yellow" />
              O que é o FSRS-5?
            </h3>
            <p className="text-gray-600 leading-relaxed">
              O FSRS-5 (Free Spaced Repetition Scheduler) é um algoritmo avançado de repetição espaçada
              que otimiza o momento ideal para revisar cada flashcard. Ele aprende com seu desempenho e
              ajusta os intervalos de revisão para maximizar a retenção de informações.
            </p>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Brain className="w-5 h-5 text-hackathon-red" />
              Benefícios para você
            </h3>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-start gap-2">
                <span className="text-hackathon-green font-bold">•</span>
                <span>
                  <strong>Aprendizado mais eficiente:</strong> Reveja os cards no momento
                  certo, evitando revisões desnecessárias.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-hackathon-green font-bold">•</span>
                <span>
                  <strong>Maior retenção:</strong> Lembre-se das informações por mais
                  tempo com menos esforço.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-hackathon-green font-bold">•</span>
                <span>
                  <strong>Personalização:</strong> O algoritmo se adapta ao seu ritmo e
                  desempenho de estudo.
                </span>
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Book className="w-5 h-5 text-hackathon-green" />
              Disponível na nossa plataforma
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Nosso sistema já utiliza o FSRS-5 para otimizar suas revisões. Você não precisa
              fazer nada — apenas continue estudando e aproveite os benefícios de um
              aprendizado mais inteligente e eficaz!
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
