import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// Cache keys para dados estáticos
export const STATIC_CACHE_KEYS = {
  SPECIALTIES: 'static-specialties',
  THEMES: 'static-themes',
  FOCUSES: 'static-focuses',
  LOCATIONS: 'static-locations',
  INSTITUTIONS: 'static-institutions',
  STUDY_CATEGORIES: 'static-study-categories',
  FLASHCARD_SPECIALTIES: 'static-flashcard-specialties',
  FLASHCARD_THEMES: 'static-flashcard-themes',
  FLASHCARD_FOCUSES: 'static-flashcard-focuses',
  FLASHCARD_EXTRAFOCUSES: 'static-flashcard-extrafocuses',
} as const;

const STATIC_CACHE_CONFIG = {
  staleTime: 60 * 60 * 1000,
  cacheTime: 24 * 60 * 60 * 1000,
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: false,
  refetchInterval: false,
  retry: 1,
};
export const useStaticSpecialties = () => {
  return useQuery({
    queryKey: [STATIC_CACHE_KEYS.SPECIALTIES],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('study_categories')
        .select('*')
        .eq('type', 'specialty')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    ...STATIC_CACHE_CONFIG,
  });
};

export const useStaticThemes = () => {
  return useQuery({
    queryKey: [STATIC_CACHE_KEYS.THEMES],
    queryFn: async () => {
      console.log('📊 [useStaticThemes] Loading themes...');

      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .eq('type', 'theme')
        .order('name');

      if (error) {
        console.error('❌ [useStaticThemes] Error:', error);
        throw error;
      }

      console.log('✅ [useStaticThemes] Themes loaded:', data?.length || 0);
      return data || [];
    },
    ...STATIC_CACHE_CONFIG,
  });
};

export const useStaticFocuses = () => {
  return useQuery({
    queryKey: [STATIC_CACHE_KEYS.FOCUSES],
    queryFn: async () => {
      console.log('📊 [useStaticFocuses] Loading focuses...');

      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .eq('type', 'focus')
        .order('name');

      if (error) {
        console.error('❌ [useStaticFocuses] Error:', error);
        throw error;
      }

      console.log('✅ [useStaticFocuses] Focuses loaded:', data?.length || 0);
      return data || [];
    },
    ...STATIC_CACHE_CONFIG,
  });
};

export const useStaticLocations = () => {
  return useQuery({
    queryKey: [STATIC_CACHE_KEYS.LOCATIONS],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('exam_locations')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    ...STATIC_CACHE_CONFIG,
  });
};

/**
 * Hook para carregar institutions com cache de longa duração
 * Evita múltiplas requisições para dados estáticos
 */
export const useStaticInstitutions = () => {
  return useQuery({
    queryKey: [STATIC_CACHE_KEYS.INSTITUTIONS],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('institutions')
        .select('id, name')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    ...STATIC_CACHE_CONFIG,
  });
};

/**
 * Hook para carregar todas as categorias de estudo de uma vez
 * Mais eficiente que múltiplas requisições separadas
 */
export const useStaticStudyCategories = () => {
  console.log('🔥 [useStaticStudyCategories] HOOK CALLED');

  return useQuery({
    queryKey: ['static-study-categories-all'],
    queryFn: async () => {
      console.log('🔥 [useStaticStudyCategories] QUERY FUNCTION EXECUTING...');

      // Carregar TODAS as categorias (especialidades, temas e focos)
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .order('type, name')
        .limit(2000); // Limite aumentado para incluir todas as categorias

      if (error) {
        console.error('❌ [useStaticStudyCategories] Error:', error);
        throw error;
      }

      // Organizar por tipo para facilitar o uso
      const organized = {
        specialties: data?.filter(item => item.type === 'specialty') || [],
        themes: data?.filter(item => item.type === 'theme') || [],
        focuses: data?.filter(item => item.type === 'focus') || [],
      };

      console.log('✅ [useStaticStudyCategories] Categories loaded:', {
        specialties: organized.specialties.length,
        themes: organized.themes.length,
        focuses: organized.focuses.length,
        total: data?.length || 0
      });

      return organized;
    },
    ...STATIC_CACHE_CONFIG,
  });
};

/**
 * Hook para carregar hierarquia de flashcards de uma vez
 * Substitui múltiplas requisições por uma única
 */
export const useStaticFlashcardHierarchy = () => {
  return useQuery({
    queryKey: ['static-flashcard-hierarchy-all'],
    queryFn: async () => {
      const [
        { data: specialties, error: specialtiesError },
        { data: themes, error: themesError },
        { data: focuses, error: focusesError },
        { data: extrafocuses, error: extrafocusesError }
      ] = await Promise.all([
        supabase.from('flashcards_specialty').select('*').order('name'),
        supabase.from('flashcards_theme').select('*').order('name'),
        supabase.from('flashcards_focus').select('*').order('name'),
        supabase.from('flashcards_extrafocus').select('*').order('name')
      ]);

      if (specialtiesError) throw specialtiesError;
      if (themesError) throw themesError;
      if (focusesError) throw focusesError;
      if (extrafocusesError) throw extrafocusesError;

      const hierarchy = {
        specialties: specialties || [],
        themes: themes || [],
        focuses: focuses || [],
        extrafocuses: extrafocuses || [],
      };

      return hierarchy;
    },
    ...STATIC_CACHE_CONFIG,
  });
};

/**
 * Hook para invalidar cache de dados estáticos
 * Usar apenas quando necessário (ex: após admin fazer alterações)
 */
export const useInvalidateStaticCache = () => {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    Object.values(STATIC_CACHE_KEYS).forEach(key => {
      queryClient.invalidateQueries({ queryKey: [key] });
    });
    queryClient.invalidateQueries({ queryKey: ['static-study-categories-all'] });
    queryClient.invalidateQueries({ queryKey: ['static-flashcard-hierarchy-all'] });
  };

  const invalidateStudyCategories = () => {
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.SPECIALTIES] });
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.THEMES] });
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.FOCUSES] });
    queryClient.invalidateQueries({ queryKey: ['static-study-categories-all'] });
  };

  const invalidateFlashcardHierarchy = () => {
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.FLASHCARD_SPECIALTIES] });
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.FLASHCARD_THEMES] });
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.FLASHCARD_FOCUSES] });
    queryClient.invalidateQueries({ queryKey: [STATIC_CACHE_KEYS.FLASHCARD_EXTRAFOCUSES] });
    queryClient.invalidateQueries({ queryKey: ['static-flashcard-hierarchy-all'] });
  };

  return {
    invalidateAll,
    invalidateStudyCategories,
    invalidateFlashcardHierarchy,
  };
};
