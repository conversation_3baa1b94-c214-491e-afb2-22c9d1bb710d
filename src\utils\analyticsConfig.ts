/**
 * Configurações específicas do Google Analytics para medevo.com.br
 */

export interface AnalyticsConfig {
  measurementId: string;
  domain: string;
  isEnabled: boolean;
  debugMode: boolean;
  cookieSettings: {
    cookie_domain: string;
    cookie_expires: number;
    cookie_flags: string;
  };
}

/**
 * Obter configuração do Google Analytics baseada no ambiente
 */
export const getAnalyticsConfig = (): AnalyticsConfig => {
  const isProduction = import.meta.env.PROD;
  const measurementId = import.meta.env.VITE_GA_MEASUREMENT_ID;
  
  // Verificar se o measurement ID é válido
  const isValidMeasurementId = measurementId && 
    measurementId.startsWith('G-') && 
    !measurementId.includes('XXXXXXXXXX');

  if (isProduction && isValidMeasurementId) {
    return {
      measurementId,
      domain: 'medevo.com.br',
      isEnabled: true,
      debugMode: false,
      cookieSettings: {
        cookie_domain: 'medevo.com.br',
        cookie_expires: 63072000, // 2 anos
        cookie_flags: 'SameSite=Lax;Secure'
      }
    };
  }
  
  // Desenvolvimento ou measurement ID inválido
  return {
    measurementId: measurementId || 'G-XXXXXXXXXX',
    domain: 'localhost',
    isEnabled: false,
    debugMode: true,
    cookieSettings: {
      cookie_domain: 'localhost',
      cookie_expires: 3600, // 1 hora
      cookie_flags: 'SameSite=Lax'
    }
  };
};

/**
 * Inicializar Google Analytics com configurações corretas
 */
export const initializeAnalytics = () => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled) {
    if (import.meta.env.DEV) {
      console.log('📊 Google Analytics desabilitado em desenvolvimento');
    }
    return;
  }

  // Carregar gtag script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${config.measurementId}`;
  document.head.appendChild(script);

  // Configurar gtag
  window.dataLayer = window.dataLayer || [];
  function gtag(...args: any[]) {
    window.dataLayer.push(args);
  }

  gtag('js', new Date());
  gtag('config', config.measurementId, {
    cookie_domain: config.cookieSettings.cookie_domain,
    cookie_expires: config.cookieSettings.cookie_expires,
    cookie_flags: config.cookieSettings.cookie_flags,
    anonymize_ip: true, // LGPD compliance
    allow_google_signals: false, // Desabilitar para privacidade
    send_page_view: true
  });

  // Adicionar gtag ao window para uso global
  (window as any).gtag = gtag;


};

/**
 * Rastrear evento customizado
 */
export const trackEvent = (
  eventName: string, 
  parameters?: Record<string, any>
) => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled || typeof window === 'undefined') {
    return;
  }

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('event', eventName, {
      ...parameters,
      custom_parameter_domain: config.domain
    });
  }
};

/**
 * Rastrear page view
 */
export const trackPageView = (pagePath: string, pageTitle?: string) => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled || typeof window === 'undefined') {
    return;
  }

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('config', config.measurementId, {
      page_path: pagePath,
      page_title: pageTitle,
      cookie_domain: config.cookieSettings.cookie_domain
    });
  }
};

/**
 * Configurar usuário para analytics
 */
export const setAnalyticsUser = (userId: string, properties?: Record<string, any>) => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled || typeof window === 'undefined') {
    return;
  }

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('config', config.measurementId, {
      user_id: userId,
      custom_map: properties
    });
  }
};

/**
 * Limpar dados do usuário (LGPD compliance)
 */
export const clearAnalyticsUser = () => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled || typeof window === 'undefined') {
    return;
  }

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('config', config.measurementId, {
      user_id: null
    });
  }
};

/**
 * Verificar se Analytics está funcionando corretamente
 */
export const validateAnalyticsSetup = (): boolean => {
  const config = getAnalyticsConfig();
  
  if (!config.isEnabled) {
    return false;
  }

  // Verificar se gtag está disponível
  const gtag = (window as any).gtag;
  const dataLayer = (window as any).dataLayer;
  
  const isValid = !!(gtag && dataLayer && Array.isArray(dataLayer));
  
  if (import.meta.env.DEV) {
    console.log('📊 Analytics Validation:', {
      config: config,
      gtagAvailable: !!gtag,
      dataLayerAvailable: !!dataLayer,
      isValid: isValid
    });
  }
  
  return isValid;
};

// Declaração de tipos para TypeScript
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}
