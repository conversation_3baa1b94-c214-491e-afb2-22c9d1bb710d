
import { useState, useEffect, useMemo } from "react";
import { FilterOption } from "@/components/filters/types";
import { useDomain } from "@/hooks/useDomain";
import { useStaticStudyCategories } from "@/hooks/useStaticDataCache";
import { supabase } from "@/integrations/supabase/client";

export const useCategoryData = (categories: FilterOption[], selectedSpecialty: string, selectedTheme: string) => {
  console.log('🔥 [useCategoryData] HOOK CALLED WITH:', {
    categoriesLength: categories.length,
    selectedSpecialty,
    selectedTheme
  });

  const { domain, isResidencia, isReady } = useDomain();
  const { data: staticCategories, isLoading: isLoadingStatic } = useStaticStudyCategories();
  const [domainQuestions, setDomainQuestions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  console.log('🔥 [useCategoryData] HOOK STATE:', {
    domain,
    isReady,
    isLoadingStatic,
    hasStaticCategories: !!staticCategories,
    staticCategoriesKeys: staticCategories ? Object.keys(staticCategories) : 'null'
  });

  useEffect(() => {
    const fetchQuestionsByDomain = async () => {
      if (!domain || !isReady) {
        console.log('🔍 [useCategoryData] Not ready to fetch questions:', { domain, isReady });
        return;
      }

      console.log('🔍 [useCategoryData] Fetching questions for domain:', domain);
      setIsLoading(true);
      try {
        // Fetch only question IDs and category IDs - no joins to avoid multiple requests
        const { data, error } = await supabase
          .from('questions')
          .select('specialty_id, theme_id, focus_id')
          .eq('knowledge_domain', domain);

        if (error) {
          console.error(`❌ [useCategoryData] Error fetching questions by domain: ${error.message}`);
        } else {
          console.log('✅ [useCategoryData] Questions fetched:', data?.length || 0);
          setDomainQuestions(data || []);
        }
      } catch (err) {
        console.error('❌ [useCategoryData] Error in data fetching:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuestionsByDomain();
  }, [domain, isReady]);

  // TEMPORÁRIO: FORÇAR CARREGAMENTO DE TODAS AS ESPECIALIDADES
  const specialties = useMemo(() => {
    if (!staticCategories) {
      console.log('🔥 [useCategoryData] NO STATIC CATEGORIES - returning empty specialties');
      return [];
    }

    console.log('🔥 [useCategoryData] FORCING ALL SPECIALTIES:', staticCategories.specialties.length);

    // TEMPORÁRIO: Mostrar TODAS as especialidades sem filtro
    const allSpecialties = staticCategories.specialties.map(cat => ({
      id: cat.id,
      name: cat.name,
      type: "specialty" as const,
      parentId: cat.parent_id
    }));

    console.log('🔥 [useCategoryData] ALL SPECIALTIES:', allSpecialties.map(s => s.name));
    return allSpecialties;
  }, [staticCategories]);

  // TEMPORÁRIO: FORÇAR CARREGAMENTO DE TODOS OS TEMAS
  const themes = useMemo(() => {
    if (!selectedSpecialty || !staticCategories) {
      console.log('🔥 [useCategoryData] No specialty selected or no static categories for themes');
      return [];
    }

    const selectedSpecialtyObj = specialties.find(s => s.name === selectedSpecialty);
    if (!selectedSpecialtyObj) {
      console.log('🔥 [useCategoryData] Specialty object not found:', selectedSpecialty);
      return [];
    }

    console.log('🔥 [useCategoryData] FORCING ALL THEMES for specialty:', selectedSpecialty);

    // TEMPORÁRIO: Mostrar TODOS os temas da especialidade sem filtro
    const allThemes = staticCategories.themes.filter(cat =>
      cat.parent_id === selectedSpecialtyObj.id
    ).map(cat => ({
      id: cat.id,
      name: cat.name,
      type: "theme" as const,
      parentId: cat.parent_id
    }));

    console.log('🔥 [useCategoryData] ALL THEMES for', selectedSpecialty, ':', allThemes.map(t => t.name));
    return allThemes;
  }, [staticCategories, selectedSpecialty, specialties]);

  // TEMPORÁRIO: FORÇAR CARREGAMENTO DE TODOS OS FOCOS
  const focuses = useMemo(() => {
    if (!selectedTheme || !staticCategories) {
      console.log('🔥 [useCategoryData] No theme selected or no static categories for focuses');
      return [];
    }

    const selectedThemeObj = themes.find(t => t.name === selectedTheme);
    if (!selectedThemeObj) {
      console.log('🔥 [useCategoryData] Theme object not found:', selectedTheme);
      return [];
    }

    console.log('🔥 [useCategoryData] FORCING ALL FOCUSES for theme:', selectedTheme);
    console.log('🔥 [useCategoryData] Theme ID:', selectedThemeObj.id);

    // TEMPORÁRIO: Mostrar TODOS os focos do tema sem qualquer filtro
    const allFocuses = staticCategories.focuses.filter(cat =>
      cat.parent_id === selectedThemeObj.id
    ).map(cat => ({
      id: cat.id,
      name: cat.name,
      type: "focus" as const,
      parentId: cat.parent_id
    }));

    console.log('🔥 [useCategoryData] ALL FOCUSES for', selectedTheme, ':', allFocuses.map(f => f.name));
    console.log('🔥 [useCategoryData] TOTAL FOCUSES COUNT:', allFocuses.length);

    return allFocuses;
  }, [staticCategories, selectedTheme, themes]);

  // Determine if we should show the focus section (not for Oftalmologia)
  const shouldShowFocus = domain !== "oftalmologia";

  return {
    specialties,
    themes,
    focuses,
    shouldShowFocus,
    isLoading,
    domain
  };
};
