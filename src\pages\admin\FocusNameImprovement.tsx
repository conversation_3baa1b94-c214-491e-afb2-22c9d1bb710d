import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Wand2, Refresh<PERSON><PERSON><PERSON>, Check, X, Merge, Copy, AlertTriangle, Search, Trash2, Settings } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { FilterSearchBar } from "@/components/filters/components/FilterSearchBar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

interface Theme {
  id: string;
  name: string;
}

interface Focus {
  id: string;
  name: string;
  questionCount?: number;
}

interface SmallFocus extends Focus {
  questionCount: number;
  similarFocuses: {
    id: string;
    name: string;
    questionCount: number;
    similarity: number;
  }[];
}

interface Improvement {
  id: string;
  focusId: string;
  originalName: string;
  improvedName: string;
  status: 'pending' | 'approved' | 'rejected';
  selected?: boolean;
  editedName?: string;
}

export const FocusNameImprovement = () => {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [focuses, setFocuses] = useState<Focus[]>([]);
  const [smallFocuses, setSmallFocuses] = useState<SmallFocus[]>([]);
  const [improvements, setImprovements] = useState<Improvement[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isFetchingSmallFocuses, setIsFetchingSmallFocuses] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [consolidationTarget, setConsolidationTarget] = useState<string>("");
  const { toast } = useToast();

  const [consolidationDialogOpen, setConsolidationDialogOpen] = useState(false);
  const [selectedMainFocus, setSelectedMainFocus] = useState<Focus | null>(null);
  const [focusesToMerge, setFocusesToMerge] = useState<Focus[]>([]);
  const [newFocusName, setNewFocusName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [smallFocusSearchTerm, setSmallFocusSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("name-improvement");

  const [selectedSmallFocus, setSelectedSmallFocus] = useState<SmallFocus | null>(null);
  const [smallFocusMergeDialogOpen, setSmallFocusMergeDialogOpen] = useState(false);
  const [suggestedMergeName, setSuggestedMergeName] = useState("");
  const [selectedTargetFocus, setSelectedTargetFocus] = useState<{id: string, name: string, questionCount: number} | null>(null);
  const [targetFocusSearchTerm, setTargetFocusSearchTerm] = useState("");
  const [allTargetFocuses, setAllTargetFocuses] = useState<Focus[]>([]);
  const [isLoadingTargetFocuses, setIsLoadingTargetFocuses] = useState(false);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [focusToDelete, setFocusToDelete] = useState<SmallFocus | null>(null);
  const [isDeletingFocus, setIsDeletingFocus] = useState(false);

  const [maxQuestionCount, setMaxQuestionCount] = useState(3);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);

  useEffect(() => {
    const loadThemes = async () => {
      try {
        const { data, error } = await supabase
          .from("study_categories")
          .select("id, name")
          .eq("type", "theme");

        if (error) throw error;
        setThemes(data || []);
      } catch (error: any) {
        toast({
          title: "Erro ao carregar temas",
          description: error.message,
          variant: "destructive",
        });
      }
    };

    loadThemes();
  }, [toast]);

  const loadFocuses = async (themeId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("study_categories")
        .select("id, name")
        .eq("type", "focus")
        .eq("parent_id", themeId);

      if (error) throw error;
      setFocuses(data || []);
      setImprovements([]);
      setSelectAll(false);
      setConsolidationTarget("");

    } catch (error: any) {
      toast({
        title: "Erro ao carregar focos",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadSmallFocuses = async (themeId: string) => {
    setIsFetchingSmallFocuses(true);
    try {
      const { data: focusesWithCounts, error: focusError } = await supabase
        .from("study_categories")
        .select(`
          id,
          name,
          questions:questions!focus_id(count)
        `)
        .eq("type", "focus")
        .eq("parent_id", themeId);

      if (focusError) throw focusError;

      const formattedFocuses = focusesWithCounts.map(focus => ({
        id: focus.id,
        name: focus.name,
        questionCount: focus.questions.length > 0 ? parseInt(focus.questions[0].count) : 0
      }));

      const smallFocusesList = formattedFocuses
        .filter(f => f.questionCount <= maxQuestionCount)
        .sort((a, b) => a.questionCount - b.questionCount);

      const smallFocusesWithSimilarities: SmallFocus[] = [];

      for (const smallFocus of smallFocusesList) {
        let similarFocuses = formattedFocuses
          .filter(f =>
            f.id !== smallFocus.id &&
            f.questionCount > smallFocus.questionCount
          )
          .map(f => ({
            id: f.id,
            name: f.name,
            questionCount: f.questionCount,
            similarity: calculateSimilarity(smallFocus.name, f.name)
          }))
          .filter(f => f.similarity > 0.4)
          .sort((a, b) => b.similarity - a.similarity)
          .slice(0, 5);

        if (similarFocuses.length > 0) {
          smallFocusesWithSimilarities.push({
            ...smallFocus,
            similarFocuses
          });
        } else {
          smallFocusesWithSimilarities.push({
            ...smallFocus,
            similarFocuses: []
          });
        }
      }

      console.log("🔍 Retrieved small focuses:", smallFocusesList.length);
      console.log("🔍 Small focuses with similarities:", smallFocusesWithSimilarities.length);
      setSmallFocuses(smallFocusesWithSimilarities);
      console.log("🔍 Small focuses loaded:", smallFocusesWithSimilarities.length);
    } catch (error: any) {
      console.error("Error loading small focuses:", error);
      toast({
        title: "Erro ao carregar focos pequenos",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsFetchingSmallFocuses(false);
    }
  };

  const loadTargetFocuses = async (themeId: string) => {
    setIsLoadingTargetFocuses(true);
    try {
      const { data: focusesWithCounts, error: focusError } = await supabase
        .from("study_categories")
        .select(`
          id,
          name,
          questions:questions!focus_id(count)
        `)
        .eq("type", "focus")
        .eq("parent_id", themeId);

      if (focusError) throw focusError;

      const formattedFocuses = focusesWithCounts.map(focus => ({
        id: focus.id,
        name: focus.name,
        questionCount: focus.questions.length > 0 ? parseInt(focus.questions[0].count) : 0
      }));

      formattedFocuses.sort((a, b) => b.questionCount - a.questionCount);

      setAllTargetFocuses(formattedFocuses);
    } catch (error: any) {
      console.error("Error loading target focuses:", error);
      toast({
        title: "Erro ao carregar focos alvo",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingTargetFocuses(false);
    }
  };

  const calculateSimilarity = (a: string, b: string): number => {
    const aLower = a.toLowerCase().trim();
    const bLower = b.toLowerCase().trim();

    if (aLower.includes(bLower) || bLower.includes(aLower)) {
      return 0.8;
    }

    const aWords = aLower.split(/\s+/);
    const bWords = bLower.split(/\s+/);

    const commonWords = aWords.filter(word =>
      bWords.some(bWord => bWord === word ||
        (word.length > 3 && bWord.includes(word)) ||
        (bWord.length > 3 && word.includes(bWord))
      )
    ).length;

    return commonWords / Math.max(aWords.length, bWords.length);
  };

  const generateSuggestions = async () => {
    setIsGenerating(true);
    try {
      const focusNames = focuses.map(f => f.name);

      const { data: suggestions, error } = await supabase.functions.invoke(
        'generate-focus-name-suggestions',
        {
          body: { focusNames }
        }
      );

      if (error) throw error;

      const improvementPromises = focuses.map((focus, index) => {
        const improvementData = {
          focus_id: focus.id,
          original_name: focus.name,
          improved_name: suggestions.suggestedNames[index],
          status: 'pending'
        };

        return supabase
          .from('focus_name_improvements')
          .insert(improvementData)
          .select()
          .single();
      });

      const results = await Promise.all(improvementPromises);
      const newImprovements = results
        .filter(result => !result.error)
        .map(result => ({
          id: result.data.id,
          focusId: result.data.focus_id,
          originalName: result.data.original_name,
          improvedName: result.data.improved_name,
          status: result.data.status as 'pending',
          selected: false
        }));

      setImprovements(newImprovements);

      toast({
        title: "Sugestões geradas",
        description: `${newImprovements.length} sugestões foram geradas`,
      });

    } catch (error: any) {
      toast({
        title: "Erro ao gerar sugestões",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleEditName = (improvementId: string, newName: string) => {
    setImprovements(prev =>
      prev.map(imp =>
        imp.id === improvementId
          ? { ...imp, editedName: newName }
          : imp
      )
    );
  };

  const handleApproveOne = async (improvement: Improvement) => {
    try {
      const { error: rpcError } = await supabase.rpc(
        'approve_focus_name_improvement',
        {
          p_improvement_id: improvement.id,
          p_new_name: improvement.editedName || improvement.improvedName
        }
      );

      if (rpcError) throw rpcError;

      setImprovements(prev =>
        prev.map(imp =>
          imp.id === improvement.id
            ? { ...imp, status: 'approved' as const, selected: false }
            : imp
        )
      );

      setFocuses(prev =>
        prev.map(focus => {
          return focus.id === improvement.focusId
            ? { ...focus, name: improvement.editedName || improvement.improvedName }
            : focus;
        })
      );

      toast({
        title: "Nome atualizado",
        description: "O nome foi atualizado com sucesso",
      });

    } catch (error: any) {
      toast({
        title: "Erro ao aprovar sugestão",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleReject = async (improvement: Improvement) => {
    try {
      const { error } = await supabase
        .from('focus_name_improvements')
        .update({ status: 'rejected' })
        .eq('id', improvement.id);

      if (error) throw error;

      setImprovements(prev =>
        prev.map(imp =>
          imp.id === improvement.id
            ? { ...imp, status: 'rejected' as const, selected: false }
            : imp
        )
      );

      toast({
        title: "Sugestão rejeitada",
        description: "A sugestão foi marcada como rejeitada",
      });

    } catch (error: any) {
      toast({
        title: "Erro ao rejeitar sugestão",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setImprovements(prev =>
      prev.map(imp =>
        imp.status === 'pending'
          ? { ...imp, selected: checked }
          : imp
      )
    );
  };

  const handleSelectOne = (improvementId: string, checked: boolean) => {
    setImprovements(prev => {
      const newImprovements = prev.map(imp =>
        imp.id === improvementId
          ? { ...imp, selected: checked }
          : imp
      );

      const allPendingSelected = newImprovements
        .filter(imp => imp.status === 'pending')
        .every(imp => imp.selected);

      setSelectAll(allPendingSelected);

      return newImprovements;
    });
  };

  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTheme = e.target.value;
    setSelectedTheme(newTheme);
    if (newTheme) {
      loadFocuses(newTheme);
      if (activeTab === "small-focuses") {
        loadSmallFocuses(newTheme);
      }
    } else {
      setFocuses([]);
      setImprovements([]);
      setSmallFocuses([]);
      setSelectAll(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === "small-focuses" && selectedTheme) {
      loadSmallFocuses(selectedTheme);
    }
  };

  const handleCopyAllNames = async () => {
    try {
      const names = focuses.map(focus => focus.name).join('\n');
      await navigator.clipboard.writeText(names);

      toast({
        title: "Nomes copiados",
        description: "Todos os nomes foram copiados para a área de transferência",
      });
    } catch (error) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar os nomes",
        variant: "destructive",
      });
    }
  };

  const openConsolidationDialog = (focus: Focus) => {
    setSelectedMainFocus(focus);
    setNewFocusName(focus.name);
    setFocusesToMerge([]);
    setConsolidationDialogOpen(true);
  };

  const openSmallFocusMergeDialog = (smallFocus: SmallFocus) => {
    setSelectedSmallFocus(smallFocus);

    if (smallFocus.similarFocuses.length > 0) {
      setSelectedTargetFocus(smallFocus.similarFocuses[0]);
      setSuggestedMergeName(smallFocus.similarFocuses[0]?.name || "");
    } else {
      setSelectedTargetFocus(null);
      setSuggestedMergeName("");
    }

    loadTargetFocuses(selectedTheme);
    setTargetFocusSearchTerm("");
    setSmallFocusMergeDialogOpen(true);
  };

  const handleSmallFocusMerge = async () => {
    if (!selectedSmallFocus || !selectedTargetFocus) {
      toast({
        title: "Dados incompletos",
        description: "Selecione o foco alvo para consolidação",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      console.log("🔄 Iniciando consolidação do foco pequeno:", selectedSmallFocus.name);
      console.log("🎯 Foco alvo:", selectedTargetFocus.name);
      console.log("🏷️ Nome final:", suggestedMergeName);

      // Verificar e limpar grupos existentes
      const { data: existingGroups, error: checkError } = await supabase
        .from("focus_consolidation_groups")
        .select("id")
        .eq("theme_id", selectedTheme)
        .eq("main_focus_id", selectedTargetFocus.id);

      if (checkError) {
        console.error("❌ Erro ao verificar grupos existentes:", checkError);
        throw checkError;
      }

      if (existingGroups && existingGroups.length > 0) {
        console.log("⚠️ Encontrou grupo existente para este foco alvo. Limpando...");
        const groupIds = existingGroups.map(g => g.id);

        const { error: cleanMappingsError } = await supabase
          .from("focus_consolidation_mappings")
          .delete()
          .in("group_id", groupIds);

        if (cleanMappingsError) {
          console.error("❌ Erro ao limpar mapeamentos:", cleanMappingsError);
          throw cleanMappingsError;
        }

        const { error: cleanGroupsError } = await supabase
          .from("focus_consolidation_groups")
          .delete()
          .in("id", groupIds);

        if (cleanGroupsError) {
          console.error("❌ Erro ao limpar grupos:", cleanGroupsError);
          throw cleanGroupsError;
        }
      }

      // Verificar e limpar existência de grupos com o foco pequeno como principal
      const { data: smallFocusGroups, error: smallFocusCheckError } = await supabase
        .from("focus_consolidation_groups")
        .select("id")
        .eq("theme_id", selectedTheme)
        .eq("main_focus_id", selectedSmallFocus.id);

      if (smallFocusCheckError) {
        console.error("❌ Erro ao verificar grupos do foco pequeno:", smallFocusCheckError);
        throw smallFocusCheckError;
      }

      if (smallFocusGroups && smallFocusGroups.length > 0) {
        console.log("⚠️ Encontrou grupo existente para o foco pequeno. Limpando...");
        const groupIds = smallFocusGroups.map(g => g.id);

        const { error: cleanMappingsError } = await supabase
          .from("focus_consolidation_mappings")
          .delete()
          .in("group_id", groupIds);

        if (cleanMappingsError) {
          console.error("❌ Erro ao limpar mapeamentos:", cleanMappingsError);
          throw cleanMappingsError;
        }

        const { error: cleanGroupsError } = await supabase
          .from("focus_consolidation_groups")
          .delete()
          .in("id", groupIds);

        if (cleanGroupsError) {
          console.error("❌ Erro ao limpar grupos:", cleanGroupsError);
          throw cleanGroupsError;
        }
      }

      // Criar grupo de consolidação
      const { data: groupData, error: groupError } = await supabase
        .from("focus_consolidation_groups")
        .insert({
          theme_id: selectedTheme,
          main_focus_id: selectedTargetFocus.id,
          name: suggestedMergeName.trim(),
          similarity_score: selectedSmallFocus.similarFocuses.find(f => f.id === selectedTargetFocus.id)?.similarity || 0.5,
          status: "pending",
          metadata: {
            similar_focuses: [
              {
                id: selectedSmallFocus.id,
                name: selectedSmallFocus.name,
                question_count: selectedSmallFocus.questionCount,
                similarity: selectedSmallFocus.similarFocuses.find(f => f.id === selectedTargetFocus.id)?.similarity || 0.5
              }
            ],
            total_questions: selectedSmallFocus.questionCount + selectedTargetFocus.questionCount
          }
        })
        .select()
        .single();

      if (groupError) throw groupError;

      console.log("✅ Grupo de consolidação criado:", groupData.id);

      // Criar mapeamento
      const { error: mappingError } = await supabase
        .from("focus_consolidation_mappings")
        .insert({
          group_id: groupData.id,
          focus_id: selectedSmallFocus.id
        });

      if (mappingError) throw mappingError;

      console.log("✅ Mapeamento criado para o grupo");

      // Executar consolidação
      console.log("🔄 Executando consolidação...");
      const { error: consolidationError } = await supabase.rpc(
        "execute_consolidation",
        { p_group_id: groupData.id }
      );

      if (consolidationError) throw consolidationError;

      console.log("✅ Consolidação concluída com sucesso!");

      toast({
        title: "Consolidação concluída",
        description: `"${selectedSmallFocus.name}" foi consolidado com "${selectedTargetFocus.name}" com sucesso`
      });

      await loadSmallFocuses(selectedTheme);
      setSmallFocusMergeDialogOpen(false);

    } catch (error: any) {
      console.error("❌ Erro na consolidação:", error);
      toast({
        title: "Erro ao consolidar focos",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const filteredFocuses = focuses.filter(focus => {
    if (!searchTerm) return true;
    return focus.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const filteredSmallFocuses = smallFocuses.filter(focus => {
    if (!smallFocusSearchTerm) return true;
    return focus.name.toLowerCase().includes(smallFocusSearchTerm.toLowerCase());
  });

  const filteredTargetFocuses = allTargetFocuses
    .filter(focus => {
      if (selectedSmallFocus && focus.id === selectedSmallFocus.id) return false;

      if (targetFocusSearchTerm) {
        return focus.name.toLowerCase().includes(targetFocusSearchTerm.toLowerCase());
      }
      return true;
    })
    .sort((a, b) => b.questionCount - a.questionCount);

  const handleConsolidation = async () => {
    if (!selectedMainFocus || focusesToMerge.length === 0 || !newFocusName.trim()) {
      toast({
        title: "Dados incompletos",
        description: "Selecione os focos e defina o novo nome",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      const { error: improvementsError } = await supabase
        .from("focus_name_improvements")
        .delete()
        .in("focus_id", [selectedMainFocus.id, ...focusesToMerge.map(f => f.id)]);

      if (improvementsError) {
        console.error("Erro ao limpar melhorias:", improvementsError);
        throw improvementsError;
      }

      const { error: mappingsError } = await supabase
        .from("focus_consolidation_mappings")
        .delete()
        .in("group_id", (
          await supabase
            .from("focus_consolidation_groups")
            .select("id")
            .eq("theme_id", selectedTheme)
            .eq("main_focus_id", selectedMainFocus.id)
        ).data?.map(g => g.id) || []);

      if (mappingsError) {
        console.error("Erro ao limpar mapeamentos:", mappingsError);
        throw mappingsError;
      }

      const { error: cleanError } = await supabase
        .from("focus_consolidation_groups")
        .delete()
        .eq("theme_id", selectedTheme)
        .eq("main_focus_id", selectedMainFocus.id);

      if (cleanError) {
        console.error("Erro ao limpar grupos:", cleanError);
        throw cleanError;
      }

      const { data: groupData, error: groupError } = await supabase
        .from("focus_consolidation_groups")
        .insert({
          theme_id: selectedTheme,
          main_focus_id: selectedMainFocus.id,
          name: newFocusName.trim(),
          similarity_score: 1,
          status: "pending",
          metadata: {
            similar_focuses: focusesToMerge.map(f => ({
              id: f.id,
              name: f.name,
              question_count: 0,
              similarity: 1
            }))
          }
        })
        .select()
        .single();

      if (groupError) throw groupError;

      const mappingPromises = focusesToMerge.map(focus =>
        supabase
          .from("focus_consolidation_mappings")
          .insert({
            group_id: groupData.id,
            focus_id: focus.id
          })
      );

      await Promise.all(mappingPromises);

      const { error: consolidationError } = await supabase.rpc(
        "execute_consolidation",
        { p_group_id: groupData.id }
      );

      if (consolidationError) throw consolidationError;

      const { error: updateError } = await supabase
        .from("study_categories")
        .update({ name: newFocusName.trim() })
        .eq("id", selectedMainFocus.id);

      if (updateError) throw updateError;

      toast({
        title: "Consolidação concluída",
        description: "Os focos foram unidos com sucesso"
      });

      await loadFocuses(selectedTheme);
      if (activeTab === "small-focuses") {
        await loadSmallFocuses(selectedTheme);
      }
      setConsolidationDialogOpen(false);

    } catch (error: any) {
      console.error("❌ Erro completo:", error);
      toast({
        title: "Erro ao consolidar focos",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleFocusToMerge = (focus: Focus) => {
    setFocusesToMerge(prev => {
      const exists = prev.some(f => f.id === focus.id);
      if (exists) {
        return prev.filter(f => f.id !== focus.id);
      } else {
        return [...prev, focus];
      }
    });
  };

  const openDeleteDialog = (focus: SmallFocus) => {
    setFocusToDelete(focus);
    setDeleteDialogOpen(true);
  };

  const handleDeleteFocus = async () => {
    if (!focusToDelete) return;

    setIsDeletingFocus(true);
    try {
      console.log(`🗑️ Starting direct deletion for focus: ${focusToDelete.id} "${focusToDelete.name}"`);

      // Check if focus has questions first - this is important since we shouldn't delete focuses with questions
      const { data: questionCount, error: countError } = await supabase
        .from("questions")
        .select("id", { count: "exact" })
        .eq("focus_id", focusToDelete.id);

      if (countError) {
        console.error("❌ Error checking questions count:", countError);
        throw countError;
      }

      if ((questionCount?.length || 0) > 0) {
        toast({
          title: "Não é possível excluir",
          description: `Este foco possui ${questionCount?.length} questões associadas. Consolide-o com outro foco primeiro.`,
          variant: "destructive"
        });
        setDeleteDialogOpen(false);
        setIsDeletingFocus(false);
        return;
      }

      // Execute a direct DELETE query on the study_categories table
      const { error: deleteError } = await supabase
        .from("study_categories")
        .delete()
        .eq("id", focusToDelete.id);

      if (deleteError) {
        console.error("❌ Error deleting focus:", deleteError);
        throw deleteError;
      }

      console.log(`✅ Focus successfully deleted: ${focusToDelete.id}`);

      // Update the UI immediately by removing the deleted focus from the list
      setSmallFocuses(prev => prev.filter(f => f.id !== focusToDelete.id));

      toast({
        title: "Foco excluído",
        description: `O foco "${focusToDelete.name}" foi excluído com sucesso.`
      });

    } catch (error: any) {
      console.error("❌ Error when deleting focus:", error);

      toast({
        title: "Erro ao excluir foco",
        description: `Falha na exclusão: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setDeleteDialogOpen(false);
      setIsDeletingFocus(false);

      // Force a refresh of the small focuses list after deletion attempt
      if (selectedTheme) {
        console.log("🔄 Refreshing small focuses list after deletion...");
        // Small delay to ensure backend has processed everything
        setTimeout(() => {
          loadSmallFocuses(selectedTheme);
        }, 500);
      }
    }
  };

  const handleMaxQuestionCountChange = (value: number[]) => {
    setMaxQuestionCount(value[0]);
  };

  const handleApplySettings = () => {
    if (selectedTheme && activeTab === "small-focuses") {
      loadSmallFocuses(selectedTheme);
    }
    setSettingsDialogOpen(false);
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-6 w-6" />
            Melhoria e Consolidação de Focos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex gap-4">
              <select
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={selectedTheme}
                onChange={handleThemeChange}
              >
                <option value="">Selecione um tema</option>
                {themes.map((theme) => (
                  <option key={theme.id} value={theme.id}>
                    {theme.name}
                  </option>
                ))}
              </select>
              <Button
                onClick={handleCopyAllNames}
                disabled={focuses.length === 0}
                variant="outline"
                className="gap-2"
              >
                <Copy className="h-4 w-4" />
                Copiar Nomes
              </Button>
              <Button
                onClick={generateSuggestions}
                disabled={!selectedTheme || isGenerating || focuses.length === 0 || activeTab !== "name-improvement"}
              >
                {isGenerating ? (
                  <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Wand2 className="h-4 w-4 mr-2" />
                )}
                Gerar Sugestões
              </Button>
            </div>

            {isLoading && <div>Carregando focos...</div>}

            {selectedTheme && (
              <Tabs
                value={activeTab}
                onValueChange={handleTabChange}
                className="space-y-4"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="name-improvement">Melhoria de Nomes</TabsTrigger>
                  <TabsTrigger value="small-focuses">Focos Pequenos</TabsTrigger>
                </TabsList>

                <TabsContent value="name-improvement" className="space-y-4">
                  {focuses.length > 0 && (
                    <div className="space-y-4">
                      <FilterSearchBar
                        placeholder="Buscar focos..."
                        value={searchTerm}
                        onChange={setSearchTerm}
                      />

                      <div className="rounded-md border">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b bg-muted/50">
                              <th className="p-4 text-left">Nome do Foco</th>
                              <th className="p-4 text-center w-[150px]">Ações</th>
                            </tr>
                          </thead>
                          <tbody>
                            {filteredFocuses.map((focus) => (
                              <tr key={focus.id} className="border-b">
                                <td className="p-4">{focus.name}</td>
                                <td className="p-4">
                                  <div className="flex justify-center gap-2">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-8 w-8 p-0 text-blue-600"
                                      onClick={() => openConsolidationDialog(focus)}
                                    >
                                      <Merge className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="small-focuses" className="space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-amber-500" />
                      <span className="text-sm text-muted-foreground">
                        Focos com {maxQuestionCount} ou menos questões que podem ser consolidados
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSettingsDialogOpen(true)}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Configurações
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectedTheme && loadSmallFocuses(selectedTheme)}
                        disabled={isFetchingSmallFocuses || !selectedTheme}
                      >
                        {isFetchingSmallFocuses ? (
                          <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <RefreshCcw className="h-4 w-4 mr-2" />
                        )}
                        Atualizar
                      </Button>
                    </div>
                  </div>

                  {isFetchingSmallFocuses ? (
                    <div className="flex justify-center py-8">
                      <RefreshCcw className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : smallFocuses.length === 0 ? (
                    <div className="text-center py-12 border rounded-md bg-muted/20">
                      <p className="text-muted-foreground">
                        {selectedTheme
                          ? "Não há focos pequenos para consolidar neste tema"
                          : "Selecione um tema para ver focos pequenos"}
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Buscar focos pequenos..."
                          value={smallFocusSearchTerm}
                          onChange={(e) => setSmallFocusSearchTerm(e.target.value)}
                          className="pl-9"
                        />
                      </div>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Foco</TableHead>
                            <TableHead className="w-[100px] text-center">Questões</TableHead>
                            <TableHead>Sugestões</TableHead>
                            <TableHead className="w-[150px] text-right">Ações</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredSmallFocuses.map((focus) => (
                            <TableRow key={focus.id}>
                              <TableCell>{focus.name}</TableCell>
                              <TableCell className="text-center">
                                <Badge variant="outline">{focus.questionCount}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex flex-col gap-1">
                                  {focus.similarFocuses.length > 0 ? (
                                    focus.similarFocuses.slice(0, 2).map(similar => (
                                      <div key={similar.id} className="flex items-center gap-2 text-sm">
                                        <Badge
                                          variant="secondary"
                                          className="font-normal"
                                        >
                                          {Math.round(similar.similarity * 100)}%
                                        </Badge>
                                        {similar.name}
                                        <span className="text-xs text-muted-foreground">
                                          ({similar.questionCount} questões)
                                        </span>
                                      </div>
                                    ))
                                  ) : (
                                    <span className="text-sm text-muted-foreground">
                                      Nenhuma sugestão autom��tica
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-8 w-8 p-0 text-red-600"
                                    onClick={() => openDeleteDialog(focus)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() => openSmallFocusMergeDialog(focus)}
                                  >
                                    Consolidar
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={smallFocusMergeDialogOpen} onOpenChange={setSmallFocusMergeDialogOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Consolidar Foco Pequeno</DialogTitle>
            <DialogDescription>
              Consolidar o foco pequeno "{selectedSmallFocus?.name}" ({selectedSmallFocus?.questionCount} questões) com qualquer outro foco deste tema.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Buscar Foco Alvo</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome..."
                  value={targetFocusSearchTerm}
                  onChange={(e) => setTargetFocusSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Foco Alvo para Consolidação</label>
              <div className="border rounded-md max-h-[200px] overflow-y-auto">
                {isLoadingTargetFocuses ? (
                  <div className="flex justify-center items-center p-4">
                    <RefreshCcw className="h-5 w-5 animate-spin text-primary" />
                  </div>
                ) : filteredTargetFocuses.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground text-sm">
                    Nenhum foco encontrado
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredTargetFocuses.map(focus => (
                      <div
                        key={focus.id}
                        className={`p-3 flex items-center justify-between hover:bg-muted/50 cursor-pointer ${
                          selectedTargetFocus?.id === focus.id ? 'bg-muted' : ''
                        }`}
                        onClick={() => {
                          setSelectedTargetFocus(focus);
                          setSuggestedMergeName(focus.name);
                        }}
                      >
                        <div>
                          <div>{focus.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {focus.questionCount} questões
                          </div>
                        </div>
                        {selectedTargetFocus?.id === focus.id && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Nome a ser Mantido</label>
              <Input
                value={suggestedMergeName}
                onChange={(e) => setSuggestedMergeName(e.target.value)}
              />
            </div>

            <div className="rounded-md border p-4 bg-amber-50">
              <div className="flex gap-2 items-start">
                <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div className="space-y-1">
                  <p className="font-medium text-amber-800">Atenção</p>
                  <p className="text-sm text-amber-700">
                    Ao consolidar, todas as {selectedSmallFocus?.questionCount} questões do foco "{selectedSmallFocus?.name}" serão
                    movidas para o foco selecionado. Esta ação não pode ser desfeita.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setSmallFocusMergeDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSmallFocusMerge}
              disabled={isProcessing || !selectedTargetFocus || !suggestedMergeName.trim()}
            >
              {isProcessing ? (
                <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Merge className="h-4 w-4 mr-2" />
              )}
              Consolidar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={consolidationDialogOpen} onOpenChange={setConsolidationDialogOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Consolidar Focos</DialogTitle>
            <DialogDescription>
              Selecione os focos que serão unidos a "{selectedMainFocus?.name}" e defina o novo nome.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Novo Nome do Foco</label>
              <Input
                value={newFocusName}
                onChange={(e) => setNewFocusName(e.target.value)}
                placeholder="Digite o novo nome do foco"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Focos Disponíveis para União</label>
              <FilterSearchBar
                placeholder="Buscar focos disponíveis..."
                value={searchTerm}
                onChange={setSearchTerm}
              />
              <div className="border rounded-md p-4 space-y-2 max-h-[300px] overflow-y-auto">
                {focuses
                  .filter(f => {
                    if (!searchTerm) return f.id !== selectedMainFocus?.id;
                    return f.id !== selectedMainFocus?.id &&
                           f.name.toLowerCase().includes(searchTerm.toLowerCase());
                  })
                  .map(focus => (
                    <div key={focus.id} className="flex items-center space-x-2">
                      <Checkbox
                        checked={focusesToMerge.some(f => f.id === focus.id)}
                        onCheckedChange={() => toggleFocusToMerge(focus)}
                      />
                      <label className="text-sm">{focus.name}</label>
                    </div>
                  ))
                }
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConsolidationDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleConsolidation}
              disabled={isProcessing || focusesToMerge.length === 0 || !newFocusName.trim()}
            >
              {isProcessing ? (
                <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Merge className="h-4 w-4 mr-2" />
              )}
              Consolidar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Excluir Foco</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o foco "{focusToDelete?.name}"?
              {focusToDelete?.questionCount > 0 && (
                <p className="mt-2 text-red-500">
                  Este foco possui {focusToDelete.questionCount} questões associadas.
                  Você deve consolidá-lo com outro foco antes de excluí-lo.
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeletingFocus}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteFocus}
              disabled={isDeletingFocus || (focusToDelete?.questionCount || 0) > 0}
            >
              {isDeletingFocus ? (
                <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={settingsDialogOpen} onOpenChange={setSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Configurações</DialogTitle>
            <DialogDescription>
              Ajuste as configurações para visualização de focos pequenos.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="max-question-count">Número máximo de questões</Label>
                <span className="text-sm font-medium">{maxQuestionCount}</span>
              </div>
              <Slider
                id="max-question-count"
                min={1}
                max={20}
                step={1}
                value={[maxQuestionCount]}
                onValueChange={handleMaxQuestionCountChange}
                className="w-full"
              />
              <div className="text-xs text-muted-foreground mt-1">
                Mostra focos com até esta quantidade de questões.
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setSettingsDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button onClick={handleApplySettings}>
              Aplicar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FocusNameImprovement;
