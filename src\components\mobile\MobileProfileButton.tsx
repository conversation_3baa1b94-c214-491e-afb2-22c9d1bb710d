import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useUserData } from "@/hooks/useUserData";
import UserProfileDrawer from "./UserProfileDrawer";

interface MobileProfileButtonProps {
  className?: string;
  variant?: "floating" | "inline";
}

const MobileProfileButton = ({ 
  className = "", 
  variant = "floating" 
}: MobileProfileButtonProps) => {
  const [showProfileDrawer, setShowProfileDrawer] = useState(false);
  const { user } = useAuth();
  const { profile } = useUserData();

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (variant === "floating") {
    return (
      <>
        <Button
          onClick={() => setShowProfileDrawer(true)}
          className={`
            fixed bottom-4 left-4 z-40 
            h-12 w-12 rounded-full 
            bg-white border-2 border-black 
            shadow-button hover:translate-y-0.5 hover:shadow-sm 
            transition-all p-0
            ${className}
          `}
        >
          <Avatar className="h-8 w-8 border border-black">
            <AvatarImage
              src={profile?.avatar_url}
              alt={profile?.full_name || user?.email || "Usuário"}
            />
            <AvatarFallback className="bg-hackathon-yellow text-black font-bold text-xs">
              {profile?.full_name ? getInitials(profile.full_name) :
               user?.email ? getInitials(user.email) : "U"}
            </AvatarFallback>
          </Avatar>
        </Button>

        <UserProfileDrawer 
          open={showProfileDrawer} 
          onOpenChange={setShowProfileDrawer} 
        />
      </>
    );
  }

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setShowProfileDrawer(true)}
        className={`
          flex items-center gap-2 
          border-2 border-black bg-white 
          hover:bg-gray-50 hover:translate-y-0.5 
          shadow-button hover:shadow-sm 
          transition-all
          ${className}
        `}
      >
        <Avatar className="h-6 w-6 border border-black">
          <AvatarImage
            src={profile?.avatar_url}
            alt={profile?.full_name || user?.email || "Usuário"}
          />
          <AvatarFallback className="bg-hackathon-yellow text-black font-bold text-xs">
            {profile?.full_name ? getInitials(profile.full_name) :
             user?.email ? getInitials(user.email) : "U"}
          </AvatarFallback>
        </Avatar>
        <span className="text-sm font-medium">
          {profile?.full_name?.split(' ')[0] || user?.email?.split('@')[0] || "Perfil"}
        </span>
        <User className="h-4 w-4" />
      </Button>

      <UserProfileDrawer 
        open={showProfileDrawer} 
        onOpenChange={setShowProfileDrawer} 
      />
    </>
  );
};

export default MobileProfileButton;
