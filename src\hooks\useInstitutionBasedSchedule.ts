import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@supabase/auth-helpers-react';
import { useInstitutionPrevalence, PrevalenceFilters } from './useInstitutionPrevalence';
import type { StudyTopic, AIScheduleOptions } from '@/types/study-schedule';

export interface InstitutionScheduleOptions extends Omit<AIScheduleOptions, 'domain'> {
  institutionIds: string[];
  startYear?: number;
  endYear?: number;
  domain?: string;
  generationMode: 'random' | 'institution_based';
}

export const useInstitutionBasedSchedule = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const user = useUser();
  const { calculatePrevalence } = useInstitutionPrevalence();

  const generateScheduleByInstitution = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    // ✅ LIMPEZA: Remover logs desnecessários

    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    setIsLoading(true);
    setError(null);

    try {
      onProgress?.(10);

      // Se modo aleatório, usar geração padrão
      if (options.generationMode === 'random') {
        // ✅ LIMPEZA: Remover log desnecessário
        return await generateRandomSchedule(options, onProgress);
      }

      // ✅ LIMPEZA: Remover log desnecessário

      // Calcular prevalência para as instituições selecionadas
      const prevalenceFilters: PrevalenceFilters = {
        institutionIds: options.institutionIds,
        startYear: options.startYear,
        endYear: options.endYear,
        domain: options.domain
      };

      console.log('📊 [useInstitutionBasedSchedule] Filtros de prevalência:', prevalenceFilters);

      onProgress?.(30);

      const institutionStats = await calculatePrevalence(prevalenceFilters);

      console.log('📈 [useInstitutionBasedSchedule] Estatísticas calculadas:', {
        institutionsCount: institutionStats.length,
        institutions: institutionStats.map(stat => ({
          institutionId: stat.institutionId,
          institutionName: stat.institutionName,
          totalQuestions: stat.totalQuestions,
          specialtiesCount: stat.specialties.length,
          themesCount: stat.themes.length,
          focusesCount: stat.focuses.length
        }))
      });

      if (institutionStats.length === 0) {
        console.error('❌ [useInstitutionBasedSchedule] Nenhuma estatística encontrada para as instituições selecionadas');
        throw new Error('Nenhuma estatística encontrada para as instituições selecionadas');
      }

      onProgress?.(50);

      // Combinar estatísticas de todas as instituições
      console.log('🔍 [useInstitutionBasedSchedule] Dados antes de combinar:', {
        institutionStatsLength: institutionStats.length,
        firstInstitution: institutionStats[0] ? {
          institution_id: institutionStats[0].institution_id,
          institution_name: institutionStats[0].institution_name,
          total_questions: institutionStats[0].total_questions,
          specialtiesCount: institutionStats[0].specialties?.length,
          themesCount: institutionStats[0].themes?.length,
          focusesCount: institutionStats[0].focuses?.length,
          firstFocus: institutionStats[0].focuses?.[0]
        } : 'NENHUMA'
      });

      const combinedStats = combineInstitutionStats(institutionStats);

      console.log('🔄 [useInstitutionBasedSchedule] Estatísticas combinadas:', {
        totalQuestions: combinedStats.totalQuestions,
        specialtiesCount: combinedStats.specialties.length,
        themesCount: combinedStats.themes.length,
        focusesCount: combinedStats.focuses.length,
        topSpecialties: combinedStats.specialties.slice(0, 3).map(s => ({ name: s.name, count: s.count, percentage: s.percentage })),
        topThemes: combinedStats.themes.slice(0, 3).map(t => ({ name: t.name, count: t.count, percentage: t.percentage })),
        topFocuses: combinedStats.focuses.slice(0, 3).map(f => `${(f.focus_name || 'Sem nome').substring(0, 30)}... (${(f.percentage || 0).toFixed(1)}%)`)
      });

      // Gerar cronograma baseado na prevalência
      const schedule = await generatePrevalenceBasedSchedule(
        combinedStats,
        institutionStats,
        options,
        onProgress
      );

      console.log('✅ [useInstitutionBasedSchedule] Cronograma gerado com sucesso:', {
        totalTopics: schedule.length,
        uniqueSpecialties: new Set(schedule.map(t => t.specialty)).size,
        uniqueThemes: new Set(schedule.map(t => t.theme)).size,
        uniqueFocuses: new Set(schedule.map(t => t.focus)).size
      });

      onProgress?.(100);
      return schedule;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const generateRandomSchedule = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    // Para modo aleatório, usar o sistema padrão existente
    // Retornar array vazio e deixar o sistema padrão lidar com isso
    onProgress?.(100);
    return [];
  };

  const combineInstitutionStats = (institutionStats: any[]) => {
    console.log('📊 [combineInstitutionStats] ===== INICIANDO COMBINAÇÃO =====');
    console.log('📊 [combineInstitutionStats] Recebido:', institutionStats.length, 'instituições');

    try {
      const combinedSpecialties = new Map<string, { specialty_id: string; name: string; count: number; percentage: number }>();
      const combinedThemes = new Map<string, { theme_id: string; name: string; count: number; percentage: number }>();
      const combinedFocuses = new Map<string, { focus_id: string; focus_name: string; specialty_id: string; theme_id: string; count: number; percentage: number }>();

      let totalQuestions = 0;

    // Somar estatísticas de todas as instituições
    institutionStats.forEach((institution, index) => {
      console.log(`📊 [combineInstitutionStats] Processando instituição ${index + 1}:`, {
        institution_id: institution.institution_id,
        institution_name: institution.institution_name,
        total_questions: institution.total_questions,
        specialtiesCount: institution.specialties?.length,
        themesCount: institution.themes?.length,
        focusesCount: institution.focuses?.length
      });

      totalQuestions += institution.total_questions;

      // Processar especialidades
      if (institution.specialties && Array.isArray(institution.specialties)) {
        institution.specialties.forEach((specialty: any) => {
          const key = specialty.specialty_id;
          const current = combinedSpecialties.get(key) || {
            specialty_id: specialty.specialty_id,
            name: specialty.specialty_name,
            count: 0,
            percentage: 0
          };
          combinedSpecialties.set(key, {
            ...current,
            count: current.count + specialty.question_count
          });
        });
      }

      // Processar temas
      if (institution.themes && Array.isArray(institution.themes)) {
        institution.themes.forEach((theme: any) => {
          const key = theme.theme_id;
          const current = combinedThemes.get(key) || {
            theme_id: theme.theme_id,
            name: theme.theme_name,
            count: 0,
            percentage: 0
          };
          combinedThemes.set(key, {
            ...current,
            count: current.count + theme.question_count
          });
        });
      }

      // Processar focos
      if (institution.focuses && Array.isArray(institution.focuses)) {
        console.log(`📊 [combineInstitutionStats] Processando ${institution.focuses.length} focos da instituição ${institution.institution_name}`);

        institution.focuses.forEach((focus: any) => {
          const key = focus.focus_id;
          const current = combinedFocuses.get(key) || {
            focus_id: focus.focus_id,
            focus_name: focus.focus_name || 'Foco sem nome',
            specialty_id: focus.specialty_id || '',
            theme_id: focus.theme_id || '',
            count: 0,
            percentage: 0
          };
          combinedFocuses.set(key, {
            ...current,
            count: current.count + focus.question_count
          });
        });
      }
    });

    // Recalcular percentuais
    combinedSpecialties.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedThemes.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedFocuses.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    // ✅ ORDENAÇÃO POR PREVALÊNCIA: Ordenar por percentual (maior para menor)
    const sortedSpecialties = Array.from(combinedSpecialties.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedThemes = Array.from(combinedThemes.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedFocuses = Array.from(combinedFocuses.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    console.log('📊 [combineInstitutionStats] Ordenação por prevalência concluída:', {
      topSpecialties: sortedSpecialties.slice(0, 3).map(s => ({ name: s.name, percentage: s.percentage.toFixed(2) + '%' })),
      topThemes: sortedThemes.slice(0, 3).map(t => ({ name: t.name, percentage: t.percentage.toFixed(2) + '%' })),
      topFocuses: sortedFocuses.slice(0, 3).map(f => ({ name: (f.name || 'Sem nome').substring(0, 40) + '...', percentage: f.percentage.toFixed(2) + '%' }))
    });

      return {
        specialties: sortedSpecialties,
        themes: sortedThemes,
        focuses: sortedFocuses,
        totalQuestions
      };
    } catch (error) {
      console.error('❌ [combineInstitutionStats] Erro durante combinação:', error);
      throw error;
    }
  };

  const generatePrevalenceBasedSchedule = async (
    combinedStats: any,
    institutionStats: any[],
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    console.log('📅 [generatePrevalenceBasedSchedule] ===== INICIANDO GERAÇÃO =====');

    const schedule: StudyTopic[] = [];
    const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

    // Calcular total de slots disponíveis
    const enabledDays = Object.entries(options.availableDays)
      .filter(([_, config]) => config.enabled);

    // ✅ CORREÇÃO: Expandir períodos baseado na duração do tópico
    const topicDurationMinutes = parseInt(options.topicDuration);
    const expandedEnabledDays = enabledDays.map(([day, config]) => {
      const expandedPeriods = [];

      for (const period of config.periods) {
        const startTime = period.startTime;
        const endTime = period.endTime;

        // Converter para minutos
        const startMinutes = startTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const endMinutes = endTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const totalMinutes = endMinutes - startMinutes;

        // Calcular quantos tópicos cabem neste período
        const topicsInPeriod = Math.floor(totalMinutes / topicDurationMinutes);

        // Período expandido: ${day} ${startTime}-${endTime} = ${topicsInPeriod} slots

        // Criar slots individuais para cada tópico
        for (let i = 0; i < topicsInPeriod; i++) {
          const slotStartMinutes = startMinutes + (i * topicDurationMinutes);
          const slotStartHours = Math.floor(slotStartMinutes / 60);
          const slotStartMins = slotStartMinutes % 60;
          const slotStartTime = `${slotStartHours.toString().padStart(2, '0')}:${slotStartMins.toString().padStart(2, '0')}`;

          expandedPeriods.push({
            startTime: slotStartTime,
            endTime: period.endTime // Manter o endTime original para referência
          });
        }
      }

      return [day, { ...config, periods: expandedPeriods }];
    });

    const totalSlots = expandedEnabledDays.reduce((total, [_, config]) => {
      return total + config.periods.length;
    }, 0) * options.weeksCount;

    console.log('📅 [generatePrevalenceBasedSchedule] Total de slots:', totalSlots);

    onProgress?.(70);

    // Distribuir tópicos baseado na prevalência
    let currentSlot = 0;

    // ✅ CORREÇÃO: Calcular o próximo week_number baseado nas semanas existentes
    const { data: existingWeeks } = await supabase
      .from('study_schedules')
      .select('week_number')
      .eq('user_id', user.id)
      .order('week_number', { ascending: false })
      .limit(1);

    const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
      existingWeeks[0].week_number + 1 : 1;

    console.log('📅 [generatePrevalenceBasedSchedule] Próximo week_number para tópicos:', nextWeekNumber);

    // ✅ NOVO: Calcular disponibilidade de focos vs slots necessários (será atualizado após filtro)
    let availableFocusesCount = combinedStats.focuses.length;
    const slotsNeeded = totalSlots;
    let willRepeatFocuses = availableFocusesCount < slotsNeeded;

    console.log('🎯 [generatePrevalenceBasedSchedule] Focos totais:', availableFocusesCount, '| Slots necessários:', slotsNeeded);

    // ✅ BUSCAR FOCOS JÁ UTILIZADOS para evitar repetição
    console.log('🔍 [generatePrevalenceBasedSchedule] Verificando focos já utilizados...');

    // Buscar todas as semanas do usuário primeiro
    console.log('🔍 [generatePrevalenceBasedSchedule] Buscando semanas do usuário:', user.id);
    const { data: userSchedules, error: schedulesError } = await supabase
      .from('study_schedules')
      .select('id')
      .eq('user_id', user.id);

    if (schedulesError) {
      console.error('⚠️ [generatePrevalenceBasedSchedule] Erro ao buscar semanas:', schedulesError);
    }

    const scheduleIds = userSchedules?.map(s => s.id) || [];
    console.log('📊 [generatePrevalenceBasedSchedule] Semanas encontradas:', scheduleIds.length, 'IDs:', scheduleIds);

    // Buscar focos já utilizados em todas as semanas
    let existingTopics = [];
    let existingError = null;

    if (scheduleIds.length > 0) {
      console.log('🔍 [generatePrevalenceBasedSchedule] Buscando focos em schedule_ids:', scheduleIds);
      const result = await supabase
        .from('study_schedule_items')
        .select('focus_id')
        .in('schedule_id', scheduleIds)
        .not('focus_id', 'is', null);

      existingTopics = result.data || [];
      existingError = result.error;
    } else {
      console.log('📊 [generatePrevalenceBasedSchedule] Nenhuma semana encontrada, pulando busca de focos');
    }

    if (existingError) {
      console.error('⚠️ [generatePrevalenceBasedSchedule] Erro ao buscar focos existentes:', existingError);
    }

    const usedFocusIds = new Set(existingTopics?.map(item => item.focus_id) || []);
    console.log('📊 [generatePrevalenceBasedSchedule] Focos já utilizados:', usedFocusIds.size, 'Lista:', Array.from(usedFocusIds).slice(0, 5));

    // ✅ FILTRAR FOCOS: Remover focos já utilizados da lista
    const availableFocuses = combinedStats.focuses.filter(focus => !usedFocusIds.has(focus.focus_id));
    availableFocusesCount = availableFocuses.length;
    willRepeatFocuses = availableFocusesCount < slotsNeeded;

    console.log('🎯 [generatePrevalenceBasedSchedule] Focos disponíveis após filtro:', availableFocusesCount, 'de', combinedStats.focuses.length, 'total | Repetição:', willRepeatFocuses ? 'SIM' : 'NÃO');

    // ✅ ALGORITMO BASEADO EM FOCOS: Seguir ordem de prevalência dos focos não utilizados
    console.log('🎯 [generatePrevalenceBasedSchedule] Seguindo ordem de prevalência dos focos não utilizados');

    const validTopics = [];

    // Iterar pelos focos disponíveis (não utilizados) ordenados por prevalência
    for (const focus of availableFocuses) {
      if (validTopics.length >= totalSlots) break;

      // ✅ USAR HIERARQUIA REAL: Buscar especialidade e tema reais deste foco
      const realSpecialtyId = focus.specialty_id;
      const realThemeId = focus.theme_id;

      // Buscar objetos completos da especialidade e tema
      const realSpecialty = combinedStats.specialties.find(s => s.specialty_id === realSpecialtyId);
      const realTheme = combinedStats.themes.find(t => t.theme_id === realThemeId);

      if (!realSpecialty || !realTheme) {
        console.log('⚠️ [generatePrevalenceBasedSchedule] Foco sem hierarquia:', focus.focus_name.substring(0, 50) + '...');
        continue;
      }

      // Log apenas os primeiros 3 focos para não sobrecarregar
      if (validTopics.length < 3) {
        console.log('✅ [generatePrevalenceBasedSchedule] Foco válido:', {
          focus: focus.focus_name.substring(0, 40) + '...',
          theme: realTheme.name,
          specialty: realSpecialty.name,
          prevalence: focus.percentage.toFixed(2) + '%'
        });
      }

      validTopics.push({
        specialty: realSpecialty,
        theme: realTheme,
        focus
      });
    }

    // Criar tópicos baseados na ordem de prevalência dos focos
    for (let i = 0; i < validTopics.length && currentSlot < totalSlots; i++) {
      const { specialty, theme, focus } = validTopics[i];

      const dayIndex = currentSlot % expandedEnabledDays.length;
      const [dayName, dayConfig] = expandedEnabledDays[dayIndex];
      const periodIndex = Math.floor(currentSlot / expandedEnabledDays.length) % dayConfig.periods.length;
      const period = dayConfig.periods[periodIndex];

      // Log apenas os primeiros 3 tópicos para não sobrecarregar
      if (currentSlot < 3) {
        console.log('⏰ [generatePrevalenceBasedSchedule] Tópico', currentSlot + 1, ':', {
          focus: focus.focus_name.substring(0, 50) + '...',
          theme: theme.name,
          specialty: specialty.name,
          prevalence: focus.percentage.toFixed(2) + '%',
          horario: `${dayName} ${period.startTime}`
        });
      }

      // ✅ NOVO: Calcular quais instituições contemplam este foco
      const focusInstitutions = institutionStats.filter(institution =>
        institution.focuses.some((f: any) => f.focus_id === focus.focus_id)
      ).map(institution => ({
        id: institution.institution_id,
        name: institution.institution_name,
        relevance: institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0,
        percentage: ((institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0) / institution.total_questions * 100).toFixed(1)
      })).sort((a, b) => b.relevance - a.relevance);

      schedule.push({
        id: crypto.randomUUID(),
        specialty: specialty.name,
        theme: theme.name,
        focus: focus.focus_name,
        specialtyId: specialty.specialty_id,
        themeId: theme.theme_id,
        focusId: focus.focus_id,
        difficulty: 'Médio',
        activity: `Estudo de ${specialty.name} - ${theme.name}`,
        startTime: period.startTime,
        duration: `${options.topicDuration}:00`,
        day: dayName,
        weekNumber: nextWeekNumber,
        is_manual: false,
        // ✅ NOVO: Informações das instituições
        institutions: focusInstitutions,
        focusPrevalence: focus.percentage.toFixed(2)
      });

      currentSlot++;
    }

    // Se ainda há slots disponíveis, usar focos restantes em ordem de prevalência
    if (currentSlot < totalSlots && validTopics.length < totalSlots) {
      console.log('⚠️ [generatePrevalenceBasedSchedule] Focos insuficientes com hierarquia completa. Slots restantes:', totalSlots - currentSlot);
    }

    // Calcular estatísticas de variedade
    const uniqueSpecialties = new Set(schedule.map(topic => topic.specialty));
    const uniqueThemes = new Set(schedule.map(topic => topic.theme));
    const uniqueFocuses = new Set(schedule.map(topic => topic.focus));

    console.log('✅ [generatePrevalenceBasedSchedule] Geração concluída:', {
      totalTopicsGenerated: schedule.length,
      slotsUsed: currentSlot,
      totalSlotsAvailable: totalSlots,
      uniqueSpecialties: uniqueSpecialties.size,
      uniqueThemes: uniqueThemes.size,
      uniqueFocuses: uniqueFocuses.size,
      focusVariety: `${uniqueFocuses.size}/${availableFocusesCount} focos disponíveis utilizados`
    });

    console.log('🎯 [generatePrevalenceBasedSchedule] Focos únicos utilizados:', Array.from(uniqueFocuses).slice(0, 10).map(f => f.substring(0, 40) + '...'));

    onProgress?.(90);

    // ✅ ADICIONAR: Criar semanas e inserir tópicos no banco
    console.log('💾 [generatePrevalenceBasedSchedule] Iniciando criação de semanas e inserção de tópicos...');

    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    // Criar semanas se necessário
    if (options.scheduleOption === "new") {
      console.log('📅 [generatePrevalenceBasedSchedule] Criando', options.weeksCount, 'novas semanas...');

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) throw new Error('User not authenticated');

      // Verificar qual é o próximo número de semana disponível
      const { data: existingWeeks, error: checkError } = await supabase
        .from('study_schedules')
        .select('week_number')
        .eq('user_id', authUser.id)
        .order('week_number', { ascending: false })
        .limit(1);

      if (checkError) {
        console.error('❌ [generatePrevalenceBasedSchedule] Erro ao verificar semanas existentes:', checkError);
        throw new Error(`Erro ao verificar semanas: ${checkError.message}`);
      }

      const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
        existingWeeks[0].week_number + 1 : 1;

      console.log('📅 [generatePrevalenceBasedSchedule] Próximo número de semana:', nextWeekNumber);

      const weeksToCreate = [];
      const today = new Date();
      const startDate = new Date(today);

      for (let i = 0; i < options.weeksCount; i++) {
        const weekStartDate = new Date(startDate);
        weekStartDate.setDate(startDate.getDate() + i * 7);

        const weekEndDate = new Date(weekStartDate);
        weekEndDate.setDate(weekStartDate.getDate() + 6);

        weeksToCreate.push({
          user_id: authUser.id,
          week_number: nextWeekNumber + i,
          week_start_date: weekStartDate.toISOString().split('T')[0],
          week_end_date: weekEndDate.toISOString().split('T')[0],
          status: 'active'
        });
      }

      console.log('📅 [generatePrevalenceBasedSchedule] Semanas a criar:', weeksToCreate.map(w => ({ week_number: w.week_number, start_date: w.week_start_date })));

      const { data: createdWeeks, error: weeksError } = await supabase
        .from('study_schedules')
        .insert(weeksToCreate)
        .select('*');

      if (weeksError) {
        console.error('❌ [generatePrevalenceBasedSchedule] Erro ao criar semanas:', {
          error: weeksError,
          message: weeksError.message,
          details: weeksError.details,
          hint: weeksError.hint,
          code: weeksError.code
        });
        throw new Error(`Erro ao criar semanas: ${weeksError.message}`);
      }

      console.log('✅ [generatePrevalenceBasedSchedule] Semanas criadas:', createdWeeks?.length);
    }

    // Buscar semanas alvo
    const { data: targetSchedules, error: targetSchedulesError } = await supabase
      .from('study_schedules')
      .select('*')
      .eq('user_id', user.id)
      .order('week_number', { ascending: true });

    if (targetSchedulesError) {
      console.error('❌ [generatePrevalenceBasedSchedule] Erro ao buscar semanas:', targetSchedulesError);
      throw new Error(`Erro ao buscar semanas: ${targetSchedulesError.message}`);
    }

    if (!targetSchedules || targetSchedules.length === 0) {
      throw new Error('Nenhuma semana encontrada para inserir os tópicos');
    }

    console.log('📋 [generatePrevalenceBasedSchedule] Semanas encontradas:', targetSchedules.length);

    // Inserir tópicos no banco
    const itemsToInsert = schedule.map(topic => {
      // ✅ CORREÇÃO: Usar a última semana criada (maior week_number) para novos tópicos
      const targetSchedule = targetSchedules.find(s => s.week_number === topic.weekNumber) ||
                             targetSchedules.sort((a, b) => b.week_number - a.week_number)[0];

      console.log('📝 [generatePrevalenceBasedSchedule] Inserindo tópico na semana:', {
        topic_focus: topic.focus.substring(0, 30) + '...',
        target_week_number: topic.weekNumber,
        target_schedule_id: targetSchedule.id,
        target_schedule_week: targetSchedule.week_number
      });

      return {
        schedule_id: targetSchedule.id,
        day_of_week: topic.day.toLowerCase(),
        topic: `${topic.specialty} - ${topic.theme}`,
        specialty_name: topic.specialty,
        specialty_id: topic.specialtyId,
        theme_name: topic.theme,
        theme_id: topic.themeId,
        focus_name: topic.focus,
        focus_id: topic.focusId,
        difficulty: topic.difficulty,
        activity_description: topic.activity,
        start_time: topic.startTime,
        duration: topic.duration,
        type: 'study',
        activity_type: 'study',
        week_number: topic.weekNumber,
        study_status: 'pending',
        // ✅ NOVO: Salvar dados das instituições no metadata
        metadata: {
          institutions: topic.institutions || [],
          focusPrevalence: topic.focusPrevalence,
          isPersonalized: true,
          generationMode: 'institution_based'
        }
      };
    });

    console.log('💾 [generatePrevalenceBasedSchedule] Inserindo', itemsToInsert.length, 'tópicos no banco...');

    const { data: insertedItems, error: insertError } = await supabase
      .from('study_schedule_items')
      .insert(itemsToInsert)
      .select('*');

    if (insertError) {
      console.error('❌ [generatePrevalenceBasedSchedule] Erro ao inserir tópicos:', insertError);
      throw new Error(`Erro ao inserir tópicos: ${insertError.message}`);
    }

    console.log('✅ [generatePrevalenceBasedSchedule] Tópicos inseridos com sucesso:', insertedItems?.length);

    // ✅ NOVO: Adicionar informações de repetição ao schedule para uso posterior
    schedule.forEach(topic => {
      (topic as any).focusRepetitionWarning = willRepeatFocuses;
      (topic as any).availableFocusesCount = availableFocusesCount;
      (topic as any).totalSlotsCount = slotsNeeded;
    });

    return schedule;
  };

  return {
    generateScheduleByInstitution,
    isLoading,
    error
  };
};
