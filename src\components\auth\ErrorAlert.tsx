import { AlertCircle } from "lucide-react";

interface ErrorAlertProps {
  title: string;
  description: string;
  show: boolean;
}

const ErrorAlert = ({ title, description, show }: ErrorAlertProps) => {
  if (!show) return null;

  return (
    <div className="text-sm text-red-700 bg-red-50 p-4 rounded-xl border-2 border-red-200 flex items-start gap-3 mb-4 shadow-sm">
      <div className="p-1 rounded-full bg-red-100">
        <AlertCircle className="h-5 w-5 flex-shrink-0 text-red-600" />
      </div>
      <div className="flex-1">
        <p className="font-semibold text-red-800">{title}</p>
        <p className="text-sm mt-1 text-red-600 leading-relaxed">{description}</p>
      </div>
    </div>
  );
};

export default ErrorAlert;
