import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@supabase/auth-helpers-react';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import type {
  FocusInsight,
  InsightResponse,
  InsightFilters,
  FocusFrequencyData,
  TemperatureCategory
} from '@/types/insights';
import { TEMPERATURE_CONFIGS } from '@/types/insights';

// Cache simples para insights
const insightsCache = new Map<string, { data: InsightResponse; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Hook para buscar e processar insights de focos personalizados
 * Implementa a matriz Temperatura × Frequência para sugestões de estudo
 */
export const useInsights = (excludeFocusIds: string[] = []) => {
  const [insights, setInsights] = useState<InsightResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const user = useUser();
  const { preferences } = useStudyPreferences();

  /**
   * Classifica um foco na matriz temperatura × frequência
   */
  const classifyTemperature = useCallback((
    frequency: number,
    yearsSinceLast: number,
    frequencyThreshold: number = 2,
    recencyThreshold: number = 3
  ): TemperatureCategory => {
    if (frequency <= 1 && yearsSinceLast > recencyThreshold) return 'frio';
    if (frequency <= 1 && yearsSinceLast <= recencyThreshold) return 'morno';
    if (frequency >= frequencyThreshold && yearsSinceLast > recencyThreshold) return 'quente';
    if (frequency >= frequencyThreshold && yearsSinceLast <= recencyThreshold) return 'vulcanico';
    return 'frio';
  }, []);

  /**
   * Gera tooltip e descrição detalhada para um insight
   */
  const generateInsightTexts = useCallback((insight: FocusFrequencyData, yearsRange: number): {
    tooltip: string;
    detailedDescription: string;
  } => {
    const { frequency, years_since_last } = insight;
    const config = TEMPERATURE_CONFIGS[classifyTemperature(frequency, years_since_last)];
    
    const tooltip = frequency <= 1 
      ? `Apareceu apenas ${frequency} vez em ${yearsRange} anos${years_since_last < 999 ? ` e não cai há ${years_since_last} anos` : ''}.`
      : `Caiu ${frequency} vezes em ${yearsRange} anos${years_since_last < 999 ? ` e não aparece há ${years_since_last} anos` : ''}.`;

    const detailedDescription = `
${config.emoji} **${config.label}**
• **Ocorrências:** ${frequency} ${frequency === 1 ? 'vez' : 'vezes'} nos últimos ${yearsRange} anos
• **Última aparição:** ${years_since_last < 999 ? `há ${years_since_last} anos` : 'nunca registrada'}

${config.description}
    `.trim();

    return { tooltip, detailedDescription };
  }, [classifyTemperature]);

  /**
   * Gera chave de cache baseada nos filtros
   */
  const generateCacheKey = useCallback((filters: InsightFilters): string => {
    return JSON.stringify({
      institution_ids: filters.institution_ids.sort(),
      exclude_focus_ids: filters.exclude_focus_ids.sort(),
      years_range: filters.years_range,
      recency_threshold: filters.recency_threshold,
      frequency_threshold: filters.frequency_threshold
    });
  }, []);

  /**
   * Verifica se há dados válidos no cache
   */
  const getCachedInsights = useCallback((cacheKey: string): InsightResponse | null => {
    const cached = insightsCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log('📦 [useInsights] Usando dados do cache');
      return cached.data;
    }
    return null;
  }, []);

  /**
   * Salva dados no cache
   */
  const setCachedInsights = useCallback((cacheKey: string, data: InsightResponse) => {
    insightsCache.set(cacheKey, { data, timestamp: Date.now() });

    // Limpar cache antigo (manter apenas 10 entradas)
    if (insightsCache.size > 10) {
      const oldestKey = Array.from(insightsCache.keys())[0];
      insightsCache.delete(oldestKey);
    }
  }, []);

  /**
   * Busca insights de focos personalizados
   */
  const fetchInsights = useCallback(async (filters?: Partial<InsightFilters>) => {
    if (!user?.id || !preferences?.target_institutions?.length) {
      setInsights(null);
      return;
    }

    const institutionIds = preferences.target_institutions.map(inst => inst.id);

    const finalFilters: InsightFilters = {
      years_range: 5,
      recency_threshold: 3,
      frequency_threshold: 2,
      exclude_focus_ids: excludeFocusIds,
      institution_ids: institutionIds,
      ...filters
    };

    const cacheKey = generateCacheKey(finalFilters);

    // Verificar cache primeiro
    const cachedData = getCachedInsights(cacheKey);
    if (cachedData) {
      setInsights(cachedData);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 [useInsights] Buscando insights com filtros:', finalFilters);

      // Buscar dados da função RPC
      console.log('🔍 [useInsights] Chamando RPC com parâmetros:', {
        p_institution_ids: finalFilters.institution_ids,
        p_exclude_focus_ids: finalFilters.exclude_focus_ids,
        p_years_range: finalFilters.years_range,
        p_recency_threshold: finalFilters.recency_threshold,
        p_frequency_threshold: finalFilters.frequency_threshold
      });

      const { data: rawData, error: rpcError } = await supabase
        .rpc('get_focus_insights', {
          p_institution_ids: finalFilters.institution_ids,
          p_exclude_focus_ids: finalFilters.exclude_focus_ids,
          p_years_range: finalFilters.years_range,
          p_recency_threshold: finalFilters.recency_threshold,
          p_frequency_threshold: finalFilters.frequency_threshold
        });

      if (rpcError) {
        console.error('❌ [useInsights] Erro na RPC:', rpcError);

        // Se a função não existir, retornar resultado vazio em vez de erro
        if (rpcError.message?.includes('function') && rpcError.message?.includes('does not exist')) {
          console.log('⚠️ [useInsights] Função RPC não encontrada, retornando resultado vazio');
          setInsights({
            suggested_focus: null,
            total_analyzed: 0,
            categories_count: { frio: 0, morno: 0, quente: 0, vulcanico: 0 },
            excluded_from_today: excludeFocusIds.length
          });
          return;
        }

        throw new Error(`Erro na RPC: ${rpcError.message}`);
      }

      console.log('📊 [useInsights] Dados brutos da RPC:', rawData);

      if (!rawData || rawData.length === 0) {
        console.log('📊 [useInsights] Nenhum insight encontrado');
        setInsights({
          suggested_focus: null,
          total_analyzed: 0,
          categories_count: { frio: 0, morno: 0, quente: 0, vulcanico: 0 },
          excluded_from_today: excludeFocusIds.length
        });
        return;
      }

      console.log('📊 [useInsights] Dados brutos recebidos:', rawData.length, 'focos');

      // Processar dados e criar insights
      const processedInsights: FocusInsight[] = rawData.map((item: any) => {
        const temperature = item.temperature as TemperatureCategory;
        const config = TEMPERATURE_CONFIGS[temperature];
        const { tooltip, detailedDescription } = generateInsightTexts(item, finalFilters.years_range);

        return {
          focus_id: item.focus_id,
          focus_name: item.focus_name,
          specialty_name: item.specialty_name,
          theme_name: item.theme_name,
          frequency: item.frequency,
          years_since_last: item.years_since_last,
          last_occurrence_year: item.last_occurrence_year,
          total_questions: item.total_questions,
          temperature,
          config,
          tooltip,
          detailedDescription
        };
      });

      // Contar categorias
      const categoriesCount = processedInsights.reduce((acc, insight) => {
        acc[insight.temperature]++;
        return acc;
      }, { frio: 0, morno: 0, quente: 0, vulcanico: 0 });

      // Selecionar foco sugerido (primeiro da lista ordenada por prioridade)
      const suggestedFocus = processedInsights[0] || null;

      const result: InsightResponse = {
        suggested_focus: suggestedFocus,
        total_analyzed: processedInsights.length,
        categories_count: categoriesCount,
        excluded_from_today: excludeFocusIds.length
      };

      console.log('✅ [useInsights] Insights processados:', result);

      // Salvar no cache
      setCachedInsights(cacheKey, result);
      setInsights(result);

    } catch (err: any) {
      console.error('❌ [useInsights] Erro ao buscar insights:', err);
      setError(err.message || 'Erro ao buscar insights');
      setInsights(null);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, preferences?.target_institutions, excludeFocusIds, generateInsightTexts, generateCacheKey, getCachedInsights, setCachedInsights]);

  // Memoizar filtros para evitar re-renders desnecessários
  const memoizedFilters = useMemo(() => ({
    exclude_focus_ids: excludeFocusIds,
    institution_ids: preferences?.target_institutions?.map(inst => inst.id) || []
  }), [excludeFocusIds, preferences?.target_institutions]);

  // Debounce para evitar requests excessivos
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchInsights();
    }, 300); // 300ms de debounce

    return () => clearTimeout(timeoutId);
  }, [fetchInsights]);

  // Limpar cache quando o usuário mudar
  useEffect(() => {
    if (user?.id) {
      insightsCache.clear();
    }
  }, [user?.id]);

  return {
    insights,
    isLoading,
    error,
    refetch: fetchInsights
  };
};
