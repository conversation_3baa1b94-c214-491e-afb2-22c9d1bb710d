import { useState } from 'react';
import { Flashcard } from '@/types/flashcard';

export const useFlashcardState = () => {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  return {
    flashcards,
    setFlashcards,
    currentIndex,
    setCurrentIndex,
    isLoading,
    setIsLoading
  };
};