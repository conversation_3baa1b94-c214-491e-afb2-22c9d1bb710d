import React, { useState, useEffect, useRef, useCallback } from 'react';
import { debounce } from '@/utils/performanceOptimizations';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

/**
 * Componente de lista virtualizada para melhor performance com grandes volumes de dados
 * Renderiza apenas os itens visíveis na tela
 */
export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calcular quais itens estão visíveis
  const visibleRange = React.useMemo(() => {
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );

    const start = Math.max(0, visibleStart - overscan);
    const end = Math.min(items.length - 1, visibleEnd + overscan);

    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // Handler de scroll otimizado com debounce
  const handleScroll = useCallback(
    debounce((event: React.UIEvent<HTMLDivElement>) => {
      const newScrollTop = event.currentTarget.scrollTop;
      setScrollTop(newScrollTop);
      onScroll?.(newScrollTop);
    }, 16), // 60fps
    [onScroll]
  );

  // Itens visíveis para renderizar
  const visibleItems = React.useMemo(() => {
    const items_to_render = [];
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      if (items[i]) {
        items_to_render.push({
          item: items[i],
          index: i
        });
      }
    }
    return items_to_render;
  }, [items, visibleRange]);

  // Altura total da lista
  const totalHeight = items.length * itemHeight;

  // Offset do primeiro item visível
  const offsetY = visibleRange.start * itemHeight;

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      {/* Container total com altura calculada */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* Container dos itens visíveis */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map(({ item, index }) => (
            <div
              key={index}
              style={{
                height: itemHeight,
                overflow: 'hidden'
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Hook para usar com VirtualizedList
 * Fornece funcionalidades adicionais como scroll para item específico
 */
export const useVirtualizedList = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Scroll para um item específico
  const scrollToItem = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    if (!containerRef.current) return;

    let targetScrollTop: number;

    switch (align) {
      case 'center':
        targetScrollTop = (index * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
        break;
      case 'end':
        targetScrollTop = (index * itemHeight) - containerHeight + itemHeight;
        break;
      default: // 'start'
        targetScrollTop = index * itemHeight;
    }

    // Garantir que não ultrapasse os limites
    const maxScrollTop = (items.length * itemHeight) - containerHeight;
    targetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));

    containerRef.current.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    });
  }, [items.length, itemHeight, containerHeight]);

  // Scroll para o topo
  const scrollToTop = useCallback(() => {
    containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Scroll para o final
  const scrollToBottom = useCallback(() => {
    const maxScrollTop = (items.length * itemHeight) - containerHeight;
    containerRef.current?.scrollTo({ top: maxScrollTop, behavior: 'smooth' });
  }, [items.length, itemHeight, containerHeight]);

  // Verificar se um item está visível
  const isItemVisible = useCallback((index: number) => {
    const itemTop = index * itemHeight;
    const itemBottom = itemTop + itemHeight;
    const viewportTop = scrollTop;
    const viewportBottom = scrollTop + containerHeight;

    return itemTop < viewportBottom && itemBottom > viewportTop;
  }, [scrollTop, itemHeight, containerHeight]);

  return {
    containerRef,
    scrollTop,
    setScrollTop,
    scrollToItem,
    scrollToTop,
    scrollToBottom,
    isItemVisible
  };
};

/**
 * Componente de lista virtualizada com paginação automática
 * Carrega mais itens quando o usuário chega perto do final
 */
interface InfiniteVirtualizedListProps<T> extends VirtualizedListProps<T> {
  hasNextPage?: boolean;
  isLoadingNextPage?: boolean;
  onLoadMore?: () => void;
  loadMoreThreshold?: number;
}

export function InfiniteVirtualizedList<T>({
  hasNextPage = false,
  isLoadingNextPage = false,
  onLoadMore,
  loadMoreThreshold = 5,
  ...props
}: InfiniteVirtualizedListProps<T>) {
  const handleScroll = useCallback((scrollTop: number) => {
    props.onScroll?.(scrollTop);

    // Verificar se deve carregar mais itens
    if (hasNextPage && !isLoadingNextPage && onLoadMore) {
      const { items, itemHeight, containerHeight } = props;
      const totalHeight = items.length * itemHeight;
      const scrollBottom = scrollTop + containerHeight;
      const threshold = loadMoreThreshold * itemHeight;

      if (scrollBottom >= totalHeight - threshold) {
        onLoadMore();
      }
    }
  }, [props, hasNextPage, isLoadingNextPage, onLoadMore, loadMoreThreshold]);

  return (
    <VirtualizedList
      {...props}
      onScroll={handleScroll}
    />
  );
}
