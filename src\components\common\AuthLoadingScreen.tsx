import { motion } from "framer-motion";

interface AuthLoadingScreenProps {
  message?: string;
  showLogo?: boolean;
}

/**
 * Tela de loading para verificações de autenticação
 * Mantém a consistência visual da aplicação
 */
export const AuthLoadingScreen = ({ 
  message = "Verificando autenticação...", 
  showLogo = true 
}: AuthLoadingScreenProps) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-[#FEF7CD]">
      <div className="text-center">
        {showLogo && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="relative mx-auto w-fit">
              <div className="bg-white border-2 border-black px-6 py-3 shadow-card-sm">
                <span className="font-bold text-3xl tracking-tight">Med EVO</span>
              </div>
              <div className="absolute -right-3 -top-3">
                <span className="bg-hackathon-red text-white text-xs px-2 py-1 rounded border border-black font-bold">
                  beta
                </span>
              </div>
            </div>
          </motion.div>
        )}
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="mb-6"
        >
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-black border-t-transparent mx-auto"></div>
            <div className="absolute inset-0 rounded-full border-2 border-hackathon-yellow animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-lg font-medium text-gray-700"
        >
          {message}
        </motion.p>
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="mt-4"
        >
          <div className="flex justify-center space-x-1">
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0 }}
              className="w-2 h-2 bg-hackathon-yellow rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
              className="w-2 h-2 bg-hackathon-green rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
              className="w-2 h-2 bg-hackathon-red rounded-full"
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
};
