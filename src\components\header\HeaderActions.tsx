
import { useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { ShieldCheck } from "lucide-react";
import md5 from "md5";
import type { User } from "@supabase/supabase-js";
import { useToast } from "../ui/use-toast";

interface HeaderActionsProps {
  user: User | null;
  profile: any;
  isAdmin: boolean;
  onLogout: () => void;
}

export const HeaderActions = ({ 
  user, 
  profile, 
  isAdmin, 
  onLogout 
}: HeaderActionsProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const getGravatarUrl = (email: string) => {
    const hash = md5(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=mp`;
  };

  const handleLogout = async () => {
    onLogout();
    toast({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
    });
    navigate("/");
  };

  return (
    <div className="flex items-center gap-2 shrink-0">
      {isAdmin && (
        <Button
          onClick={() => navigate("/admin")}
          className="flex items-center gap-2"
          variant="outline"
          size="sm"
        >
          <ShieldCheck className="h-4 w-4" />
          <span className="hidden sm:inline">Painel Admin</span>
        </Button>
      )}

      {user && (
        <DropdownMenu>
          <DropdownMenuTrigger className="focus:outline-none">
            <div className="flex items-center gap-2 p-1.5 rounded-full border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
              <Avatar className="border-2 border-black h-8 w-8">
                <AvatarImage
                  src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                  alt={profile?.full_name || 'User'}
                />
                <AvatarFallback className="bg-hackathon-yellow text-black font-bold">
                  {(profile?.full_name || 'U').charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="hidden sm:block text-sm font-bold">
                {profile?.full_name || 'Usuário'}
              </span>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-white border-2 border-black shadow-card-sm">
            <DropdownMenuLabel className="font-bold">Minha Conta</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigate('/settings')}
              className="cursor-pointer hover:bg-gray-50"
            >
              Configurações
            </DropdownMenuItem>
            {isAdmin && (
              <DropdownMenuItem 
                onClick={() => navigate('/admin')}
                className="cursor-pointer hover:bg-gray-50 flex items-center gap-2"
              >
                <ShieldCheck className="h-4 w-4" />
                Painel Admin
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={handleLogout}
              className="cursor-pointer text-hackathon-red hover:bg-red-50"
            >
              Sair
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};
