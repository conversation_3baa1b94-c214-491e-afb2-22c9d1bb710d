import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

interface TodayStats {
  questionsAnswered: number;
  timeStudied: number; // em minutos
  isLoading: boolean;
  error: string | null;
}

export const useTodayStats = (): TodayStats => {
  const { user } = useAuth();
  const [stats, setStats] = useState<TodayStats>({
    questionsAnswered: 0,
    timeStudied: 0,
    isLoading: true,
    error: null
  });

  useEffect(() => {
    const fetchTodayStats = async () => {
      if (!user?.id) {
        setStats(prev => ({ ...prev, isLoading: false }));
        return;
      }

      try {
        setStats(prev => ({ ...prev, isLoading: true, error: null }));

        // Data de hoje
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        // Buscar questões respondidas hoje
        const { data: answersData, error: answersError } = await supabase
          .from('user_answers')
          .select('id, time_spent, created_at')
          .eq('user_id', user.id)
          .gte('created_at', startOfDay.toISOString())
          .lte('created_at', endOfDay.toISOString());

        if (answersError) {
          console.error('❌ [useTodayStats] Error fetching answers:', answersError);
          throw answersError;
        }

        // Buscar sessões completadas hoje com cálculo de duração real
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('study_sessions')
          .select('total_time_spent, completed_at, started_at')
          .eq('user_id', user.id)
          .eq('status', 'completed')
          .gte('completed_at', startOfDay.toISOString())
          .lte('completed_at', endOfDay.toISOString());

        if (sessionsError) {
          console.error('❌ [useTodayStats] Error fetching sessions:', sessionsError);
          throw sessionsError;
        }



        // Calcular estatísticas
        const questionsAnswered = answersData?.length || 0;

        // Calcular tempo total - total_time_spent está em SEGUNDOS
        let totalTimeMinutes = 0;

        // Se há sessões completadas, usar total_time_spent (que está em segundos)
        if (sessionsData && sessionsData.length > 0) {
          const totalTimeSeconds = sessionsData.reduce((sum, session) => {
            return sum + (session.total_time_spent || 0);
          }, 0);
          totalTimeMinutes = Math.round(totalTimeSeconds / 60); // converter segundos para minutos
        } else {
          // Fallback: usar tempo das respostas individuais se não há sessões
          if (answersData) {
            const timeFromAnswers = answersData.reduce((sum, answer) => {
              return sum + (answer.time_spent || 0);
            }, 0);
            totalTimeMinutes = Math.round(timeFromAnswers / 60); // converter segundos para minutos
          }
        }

        setStats({
          questionsAnswered,
          timeStudied: totalTimeMinutes,
          isLoading: false,
          error: null
        });



      } catch (error: any) {
        setStats({
          questionsAnswered: 0,
          timeStudied: 0,
          isLoading: false,
          error: error.message || 'Erro ao carregar estatísticas'
        });
      }
    };

    fetchTodayStats();
  }, [user?.id]);

  return stats;
};
