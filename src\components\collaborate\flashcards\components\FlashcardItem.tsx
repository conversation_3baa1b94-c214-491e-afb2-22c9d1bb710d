import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trash2, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LikeButtons } from "@/components/flashcards/LikeButtons";
import { supabase } from "@/integrations/supabase/client";
import type { FlashcardWithHierarchy } from "@/types/flashcard";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface FlashcardItemProps {
  card: FlashcardWithHierarchy;
  onDelete: (cardId: string) => void;
  hideStatusBadge?: boolean;
}

export const FlashcardItem = ({ card, onDelete, hideStatusBadge = false }: FlashcardItemProps) => {
  const getStatusBadge = () => {
    switch (card.current_state) {
      case 'reviewing':
        return (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <Badge className="bg-blue-500">Em Revisão</Badge>
              {card.next_review_date && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  Próxima: {format(new Date(card.next_review_date), "dd/MM", { locale: ptBR })}
                </Badge>
              )}
            </div>
            {card.last_review_date && (
              <Badge variant="outline" className="flex items-center gap-1 bg-gray-50">
                <Clock className="w-3 h-3" />
                Última: {format(new Date(card.last_review_date), "dd/MM")}
              </Badge>
            )}
          </div>
        );
      case 'available':
        return (
          <div className="flex gap-2">
            <Badge className="bg-green-500">Aprovado</Badge>
            {card.next_review_date && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {format(new Date(card.next_review_date), "dd 'de' MMMM", { locale: ptBR })}
              </Badge>
            )}
          </div>
        );
      case 'pending':
        return <Badge className="bg-yellow-500">Aguardando Aprovação</Badge>;
      case 'rejected':
        return <Badge className="bg-red-500">Rejeitado</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card key={card.id} className="p-4">
      <div className="space-y-2">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {!hideStatusBadge && getStatusBadge()}
            </div>
            <p className="font-medium">Frente: {card.front}</p>
            <p className="text-gray-600">Verso: {card.back}</p>
            {card.current_state === 'reviewing' && card.total_reviews !== undefined && (
              <div className="mt-2 text-xs text-blue-600">
                <p>Revisões: {card.total_reviews} total / {card.correct_reviews} corretas</p>
              </div>
            )}
            {card.rejection_reason && (
              <p className="text-red-500 mt-2">
                Motivo da rejeição: {card.rejection_reason}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <LikeButtons
              cardId={card.id}
              initialLikes={card.likes || 0}
              initialDislikes={card.dislikes || 0}
              initialLikedBy={card.liked_by || []}
              initialDislikedBy={card.disliked_by || []}
            />
            {card.user_id === (supabase.auth.getUser() as any)?.data?.user?.id && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(card.id)}
                className="text-red-500 hover:text-red-700 hover:bg-red-100"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <div className="text-sm text-gray-500 space-y-1">
          <p>Especialidade: {card.hierarchy.specialty?.name || "Não definida"}</p>
          {card.hierarchy.theme && (
            <p>Tema: {card.hierarchy.theme.name}</p>
          )}
          {card.hierarchy.focus && (
            <p>Foco: {card.hierarchy.focus.name}</p>
          )}
          {card.hierarchy.extraFocus && (
            <p>Extra Foco: {card.hierarchy.extraFocus.name}</p>
          )}
        </div>
      </div>
    </Card>
  );
};
