import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { HelpCircle, Clock, Zap, BookOpen, Timer } from 'lucide-react';

interface TimeExplanationDialogProps {
  children: React.ReactNode;
}

export const TimeExplanationDialog = ({ children }: TimeExplanationDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Entendendo o Tempo por Questão
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Tempo por Questão */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Clock className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-800">Tempo por Questão</h3>
            </div>
            <div className="space-y-2 text-sm text-blue-700">
              <p><strong>O que é:</strong> Tempo médio que você gasta para responder cada questão</p>
              <p><strong>Inclui:</strong> Leitura, análise, reflexão e tomada de decisão</p>
              <p><strong>Como é calculado:</strong> Tempo total de estudo dividido pelo número de questões únicas respondidas</p>
              <div className="mt-3 p-2 bg-blue-100 rounded">
                <p className="font-medium">Interpretação:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li><strong>≤15s:</strong> Muito rápido - cuidado com chutes</li>
                  <li><strong>16-45s:</strong> Ritmo ideal para questões</li>
                  <li><strong>46-90s:</strong> Ritmo normal, pode acelerar</li>
                  <li><strong>&gt;90s:</strong> Muito lento, precisa treinar</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Como melhorar */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <BookOpen className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-800">Como Otimizar seu Tempo</h3>
            </div>
            <div className="space-y-3 text-sm text-green-700">
              <div>
                <strong>Se está muito rápido (≤15s):</strong>
                <ul className="list-disc list-inside text-xs mt-1 ml-2 space-y-1">
                  <li>Leia as questões com mais atenção</li>
                  <li>Analise todas as alternativas antes de responder</li>
                  <li>Reflita sobre o raciocínio por trás da resposta</li>
                </ul>
              </div>
              <div>
                <strong>Se está muito lento (&gt;90s):</strong>
                <ul className="list-disc list-inside text-xs mt-1 ml-2 space-y-1">
                  <li>Pratique mais questões para ganhar agilidade</li>
                  <li>Identifique padrões nas questões</li>
                  <li>Evite reler excessivamente o enunciado</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Dicas importantes */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Timer className="h-5 w-5 text-amber-600" />
              <h3 className="font-semibold text-amber-800">Dicas Importantes</h3>
            </div>
            <div className="space-y-2 text-sm text-amber-700">
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Qualidade &gt; Velocidade:</strong> É melhor responder com precisão do que rapidamente</li>
                <li><strong>Consistência:</strong> Mantenha um ritmo constante durante o estudo</li>
                <li><strong>Contexto:</strong> Questões mais complexas naturalmente levam mais tempo</li>
                <li><strong>Evolução:</strong> Seu tempo tende a melhorar com a prática</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
