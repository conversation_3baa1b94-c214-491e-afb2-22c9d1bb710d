import { supabase } from '@/integrations/supabase/client';

interface QueryLog {
  table: string;
  select: string;
  filters: string[];
  timestamp: number;
  stackTrace: string;
  size?: number;
}

const queryLogs: QueryLog[] = [];

/**
 * Intercepta e registra todas as queries do Supabase para identificar onde estão sendo executadas
 */
export const initializeSupabaseQueryTracker = () => {
  // Interceptar o método from do Supabase
  const originalFrom = supabase.from.bind(supabase);

  // Interceptar o método rpc do Supabase
  const originalRpc = supabase.rpc.bind(supabase);

  supabase.rpc = function(fn: string, args?: any) {
    const rpcCall = originalRpc(fn, args);

    // Capturar stack trace para identificar onde a RPC foi chamada
    const stackTrace = new Error().stack || '';
    const callerInfo = extractCallerInfo(stackTrace);

    // RPC detection logging disabled

    // Interceptar execução da RPC
    const originalThen = rpcCall.then?.bind(rpcCall);
    if (originalThen) {
      rpcCall.then = function(onFulfilled?: any, onRejected?: any) {
        // RPC execution logging disabled

        return originalThen(
          (result: any) => {
            const dataSize = result.data ? JSON.stringify(result.data).length : 0;

            // RPC completion logging disabled

            // Registrar log
            queryLogs.push({
              table: `rpc:${fn}`,
              select: `${fn}(${JSON.stringify(args || {})})`,
              filters: [],
              timestamp: Date.now(),
              stackTrace: callerInfo,
              size: dataSize
            });

            // Performance alerts disabled for production

            if (onFulfilled) return onFulfilled(result);
            return result;
          },
          onRejected
        );
      };
    }

    return rpcCall;
  };

  supabase.from = function(table: string) {
    const query = originalFrom(table);
    
    // Interceptar o método select
    const originalSelect = query.select.bind(query);
    query.select = function(columns?: string, options?: any) {
      const selectQuery = originalSelect(columns, options);
      
      // Capturar stack trace para identificar onde a query foi chamada
      const stackTrace = new Error().stack || '';
      const callerInfo = extractCallerInfo(stackTrace);
      
      // Query detection logging disabled
      
      // Interceptar métodos de filtro comuns
      const methods = ['eq', 'in', 'gte', 'lte', 'order', 'limit', 'single'];
      methods.forEach(method => {
        if (selectQuery[method]) {
          const originalMethod = selectQuery[method].bind(selectQuery);
          selectQuery[method] = function(...args: any[]) {
            // Filter logging disabled
            return originalMethod(...args);
          };
        }
      });
      
      // Interceptar execução da query
      const originalThen = selectQuery.then?.bind(selectQuery);
      if (originalThen) {
        selectQuery.then = function(onFulfilled?: any, onRejected?: any) {
          // Query execution logging disabled
          
          return originalThen(
            (result: any) => {
              const dataSize = result.data ? JSON.stringify(result.data).length : 0;
              
              // Query completion logging disabled
              
              // Registrar log
              queryLogs.push({
                table,
                select: columns || '*',
                filters: [],
                timestamp: Date.now(),
                stackTrace: callerInfo,
                size: dataSize
              });
              
              // Performance alerts disabled for production
              
              if (onFulfilled) return onFulfilled(result);
              return result;
            },
            onRejected
          );
        };
      }
      
      return selectQuery;
    };
    
    return query;
  };
  

};

/**
 * Extrai informações do caller do stack trace
 */
function extractCallerInfo(stackTrace: string): string {
  const lines = stackTrace.split('\n');
  
  // Procurar pela primeira linha que não seja do próprio tracker
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (line && !line.includes('supabaseQueryTracker') && !line.includes('QueryTracker')) {
      // Extrair nome do arquivo e linha
      const match = line.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/);
      if (match) {
        const [, functionName, filePath, lineNumber] = match;
        const fileName = filePath.split('/').pop() || filePath;
        return `${functionName} (${fileName}:${lineNumber})`;
      }
      
      // Fallback para formato mais simples
      const simpleMatch = line.match(/at\s+(.+)/);
      if (simpleMatch) {
        return simpleMatch[1].trim();
      }
    }
  }
  
  return 'Unknown caller';
}

/**
 * Gera relatório de todas as queries executadas
 */
export const generateQueryReport = () => {
  // Report generation disabled for production
  return {};
};

/**
 * Limpa os logs coletados
 */
export const clearQueryLogs = () => {
  queryLogs.length = 0;
  // Log clearing disabled for production
};
