
import { Button } from "@/components/ui/button";
import React from "react";

interface FlashcardAIWizardEditFormProps {
  editingFront: string;
  editingBack: string;
  setEditingFront: (val: string) => void;
  setEditingBack: (val: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export function FlashcardAIWizardEditForm({
  editingFront,
  editingBack,
  setEditingFront,
  setEditingBack,
  onSave,
  onCancel,
}: FlashcardAIWizardEditFormProps) {
  return (
    <div className="space-y-3">
      <div>
        <label className="block font-bold mb-1">Frente</label>
        <textarea
          value={editingFront}
          onChange={e => setEditingFront(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded"
          rows={4}
        />
      </div>
      <div>
        <label className="block font-bold mb-1">Verso</label>
        <textarea
          value={editingBack}
          onChange={e => setEditingBack(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded"
          rows={2}
        />
      </div>
      <div className="flex gap-2 mt-2">
        <Button size="sm" className="bg-green-600 text-white" onClick={onSave}>Salvar</Button>
        <Button size="sm" variant="outline" onClick={onCancel}>Cancelar</Button>
      </div>
    </div>
  );
}
