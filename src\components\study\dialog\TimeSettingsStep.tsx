
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { HelpCircle } from "lucide-react";
import { TimeSettingsStepProps } from "./types";

export const TimeSettingsStep = ({
  startTime,
  setStartTime,
  duration,
  setDuration,
  activity,
  setActivity
}: TimeSettingsStepProps) => {
  return (
    <div className="p-6 space-y-6">
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-2 flex items-start gap-3">
        <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
        <p className="text-sm text-blue-700 break-words">
          Configure o horário e a duração do seu estudo. Recomendamos blocos de 30 minutos a 2 horas para melhor aproveitamento.
        </p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="startTime" className="text-base font-medium flex items-center">
            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">1</span>
            Horário de Início
          </Label>
          <Input
            id="startTime"
            type="time"
            value={startTime}
            onChange={(e) => setStartTime(e.target.value)}
            className="max-w-xs border-2 focus:border-green-500"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="duration" className="text-base font-medium flex items-center">
            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">2</span>
            Duração do Estudo (horas)
          </Label>
          <div className="flex items-center gap-4">
            <Input
              id="duration"
              type="number"
              min="0.5"
              step="0.5"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              className="max-w-[100px] border-2 focus:border-green-500"
            />
            <span className="text-sm text-gray-500">horas</span>
          </div>
          <div className="flex gap-2 mt-2">
            {[0.5, 1, 1.5, 2].map((value) => (
              <Button 
                key={value} 
                type="button"
                variant={duration === String(value) ? "secondary" : "outline"}
                size="sm"
                onClick={() => setDuration(String(value))}
                className="px-3"
              >
                {value}h
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="activity" className="text-base font-medium flex items-center">
            <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-800 text-xs font-bold mr-2">3</span>
            Descrição da Atividade
          </Label>
          <Textarea
            id="activity"
            value={activity}
            onChange={(e) => setActivity(e.target.value)}
            placeholder="Ex: Leitura + Questões"
            className="h-20 border-2 focus:border-green-500"
          />
        </div>
      </div>
    </div>
  );
};

export default TimeSettingsStep;
