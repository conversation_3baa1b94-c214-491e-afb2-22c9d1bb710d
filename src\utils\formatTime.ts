// Helper to handle Brazil timezone (UTC-3)
export const adjustToBrazilTimezone = (date: Date): Date => {
  const newDate = new Date(date);

  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: false
  };

  const formatter = new Intl.DateTimeFormat('en-US', options);
  const parts = formatter.formatToParts(date);

  const year = parseInt(parts.find(part => part.type === 'year')?.value || '0');
  const month = parseInt(parts.find(part => part.type === 'month')?.value || '0') - 1;
  const day = parseInt(parts.find(part => part.type === 'day')?.value || '0');
  const hour = parseInt(parts.find(part => part.type === 'hour')?.value || '0');
  const minute = parseInt(parts.find(part => part.type === 'minute')?.value || '0');
  const second = parseInt(parts.find(part => part.type === 'second')?.value || '0');

  const brazilDate = new Date(year, month, day, hour, minute, second);

  return brazilDate;
};

// Format seconds to mm:ss display
export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
};

// Normalize day name for consistent comparison (removing accents and lowercase)
export const normalizeDayName = (dayName: string): string => {
  return dayName.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
};

/**
 * Format a date range for display
 */
export const formatDateRange = (startDate: string, endDate: string): string => {
  if (!startDate || !endDate) {
    return '';
  }

  try {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const startDay = start.getDate().toString().padStart(2, '0');
    const startMonth = (start.getMonth() + 1).toString().padStart(2, '0');
    const endDay = end.getDate().toString().padStart(2, '0');
    const endMonth = (end.getMonth() + 1).toString().padStart(2, '0');

    const startFormatted = `${startDay}/${startMonth}`;
    const endFormatted = `${endDay}/${endMonth}`;

    const result = `${startFormatted} - ${endFormatted}`;

    console.log(`🔍 [formatDateRange] Formatting date range:`, {
      startDate,
      endDate,
      startDay,
      startMonth,
      endDay,
      endMonth,
      result
    });

    return result;
  } catch (error) {
    console.error('❌ [formatDateRange] Error formatting date range:', error);
    return '';
  }
};

// Parse time duration string to minutes
export const parseDurationToMinutes = (duration: string): number => {
  try {
    if (!duration) return 0;

    // Handle decimal hours format (e.g., "0.5 horas")
    if (duration.includes('hora') || duration.includes('hour')) {
      // First try to extract "X horas e Y minutos" format
      const complexMatch = duration.match(/(\d+)\s*hora[s]?\s*e\s*(\d+)\s*minuto[s]?/i);
      if (complexMatch) {
        const hours = parseInt(complexMatch[1], 10);
        const minutes = parseInt(complexMatch[2], 10);
        return hours * 60 + minutes;
      }

      // Then try to extract decimal or integer hours
      const valueMatch = duration.match(/(\d+(\.\d+)?)/);
      if (valueMatch) {
        const value = parseFloat(valueMatch[1]);
        return Math.round(value * 60); // Convert hours to minutes
      }
    }

    // Handle "X minutos" format
    if (duration.includes('minuto')) {
      const minutesMatch = duration.match(/(\d+)\s*minuto[s]?/i);
      if (minutesMatch) {
        return parseInt(minutesMatch[1], 10);
      }
    }

    // Handle space-separated format (e.g., "2 horas 30 minutos")
    const parts = duration.split(' ');
    if (parts.length > 1) {
      let totalMinutes = 0;
      let currentValue = 0;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i].toLowerCase();

        // If it's a number, store it
        if (/^\d+(\.\d+)?$/.test(part)) {
          currentValue = parseFloat(part);
        }
        // If it's a unit, apply the stored value
        else if (part.includes('hora')) {
          totalMinutes += currentValue * 60;
          currentValue = 0;
        } else if (part.includes('minuto')) {
          totalMinutes += currentValue;
          currentValue = 0;
        }
      }

      return totalMinutes;
    }

    // Handle HH:MM format
    if (duration.includes(':')) {
      const [hours, minutes] = duration.split(':').map(Number);
      return (hours || 0) * 60 + (minutes || 0);
    }

    // Last resort: try to parse as a plain number (assuming hours)
    const numericValue = parseFloat(duration);
    if (!isNaN(numericValue)) {
      return Math.round(numericValue * 60);
    }

    return 0;
  } catch {
    return 0;
  }
};

// Sum array of durations
export const sumDurations = (durations: string[]): string => {
  const totalMinutes = durations.reduce((total, duration) => {
    return total + parseDurationToMinutes(duration);
  }, 0);

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return minutes > 0
    ? `${hours} hora${hours !== 1 ? 's' : ''} e ${minutes} minuto${minutes !== 1 ? 's' : ''}`
    : `${hours} hora${hours !== 1 ? 's' : ''}`;
};
