/**
 * Sistema de Rotação Diária para Insights
 * Alterna insights automaticamente após meia-noite
 */

import type { TemperatureCategory } from '@/types/insights';

/**
 * Gera seed único baseado na data atual (YYYY-MM-DD)
 * Muda automaticamente após meia-noite
 */
export const getDailySeed = (): string => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * Converte string em número hash para usar como seed
 */
export const stringToHash = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
};

/**
 * Gera seed numérico baseado na data e user ID
 * Garante que cada usuário tenha rotação diferente no mesmo dia
 */
export const getDailyNumericSeed = (userId?: string): number => {
  const dateSeed = getDailySeed();
  const userSeed = userId ? `${dateSeed}-${userId}` : dateSeed;
  return stringToHash(userSeed);
};

/**
 * Determina a prioridade de temperatura baseada no dia da semana
 * Balanceamento semanal inteligente
 */
export const getWeeklyTemperaturePriority = (): TemperatureCategory[] => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.
  
  switch (dayOfWeek) {
    case 1: // Segunda-feira - Começar forte
      return ['vulcanico', 'quente', 'morno', 'frio'];
    case 2: // Terça-feira - Manter intensidade
      return ['quente', 'vulcanico', 'morno', 'frio'];
    case 3: // Quarta-feira - Equilibrar
      return ['morno', 'quente', 'vulcanico', 'frio'];
    case 4: // Quinta-feira - Revisar gaps
      return ['frio', 'morno', 'quente', 'vulcanico'];
    case 5: // Sexta-feira - Preparar para prova
      return ['vulcanico', 'quente', 'morno', 'frio'];
    case 6: // Sábado - Revisão geral
      return ['quente', 'morno', 'vulcanico', 'frio'];
    case 0: // Domingo - Descanso ativo
      return ['morno', 'frio', 'quente', 'vulcanico'];
    default:
      return ['vulcanico', 'quente', 'morno', 'frio'];
  }
};

/**
 * Calcula prioridade de ordenação baseada na temperatura e dia da semana
 * Menor número = maior prioridade
 */
export const getTemperaturePriority = (temperature: TemperatureCategory): number => {
  const priorities = getWeeklyTemperaturePriority();
  const index = priorities.indexOf(temperature);
  return index === -1 ? 999 : index;
};

/**
 * Gera parâmetros de ordenação para a query SQL
 * Inclui seed diário e prioridade semanal
 */
export const getDailyOrderingParams = (userId?: string) => {
  const seed = getDailyNumericSeed(userId);
  const priorities = getWeeklyTemperaturePriority();
  const today = new Date();
  
  return {
    seed,
    dayOfWeek: today.getDay(),
    priorities,
    dateSeed: getDailySeed()
  };
};

/**
 * Verifica se é um novo dia (para invalidar cache)
 */
export const isNewDay = (lastAccessDate?: string): boolean => {
  const today = getDailySeed();
  return !lastAccessDate || lastAccessDate !== today;
};

/**
 * Gera chave de cache que inclui a data
 * Garante que cache seja invalidado diariamente
 */
export const getDailyCacheKey = (baseKey: string, userId?: string): string => {
  const dateSeed = getDailySeed();
  const userPart = userId ? `-${userId.slice(0, 8)}` : '';
  return `${baseKey}-${dateSeed}${userPart}`;
};

/**
 * Calcula offset baseado no seed para variar resultados
 * Usado para "embaralhar" a ordem dos insights
 */
export const calculateDailyOffset = (totalItems: number, userId?: string): number => {
  if (totalItems <= 1) return 0;
  
  const seed = getDailyNumericSeed(userId);
  return seed % totalItems;
};

/**
 * Gera descrição do dia da semana para logs
 */
export const getDayDescription = (): string => {
  const today = new Date();
  const dayNames = [
    'Domingo - Descanso Ativo',
    'Segunda - Começar Forte', 
    'Terça - Manter Intensidade',
    'Quarta - Equilibrar',
    'Quinta - Revisar Gaps',
    'Sexta - Preparar Prova',
    'Sábado - Revisão Geral'
  ];
  
  return dayNames[today.getDay()];
};

/**
 * Log detalhado da rotação diária para debug
 */
export const logDailyRotation = (userId?: string) => {
  const params = getDailyOrderingParams(userId);
  const description = getDayDescription();
  
  console.log('🔄 [DailyRotation] Parâmetros do dia:', {
    dateSeed: params.dateSeed,
    seed: params.seed,
    dayOfWeek: params.dayOfWeek,
    description,
    priorities: params.priorities,
    userId: userId?.slice(0, 8) || 'anonymous'
  });
  
  return params;
};
