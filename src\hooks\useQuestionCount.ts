import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';
import { useMemo } from 'react';
import { useDomain } from '@/hooks/useDomain';
import { useDebounce } from '@/hooks/useDebounce';
import { useUser } from '@supabase/auth-helpers-react';

interface UseQuestionCountOptions {
  filters: SelectedFilters;
  debounceMs?: number;
  enabled?: boolean;
}

/**
 * Hook otimizado para buscar apenas a contagem de questões filtradas
 * Não retorna dados completos das questões, apenas o número total
 */
export const useQuestionCount = ({
  filters,
  debounceMs = 300,
  enabled = true
}: UseQuestionCountOptions) => {
  const { domain, isReady } = useDomain();
  const user = useUser();

  // Debounce dos filtros para evitar muitas requisições
  const debouncedFilters = useDebounce(filters, debounceMs);

  const hasFilters = useMemo(() =>
    Object.values(debouncedFilters).some(f =>
      Array.isArray(f) ? f.length > 0 : false
    ),
    [debouncedFilters]
  );

  const result = useQuery({
    queryKey: ['question-count', debouncedFilters, domain, user?.id],
    queryFn: async () => {
      if (!hasFilters || !isReady || !domain) {
        return { total_count: 0 };
      }

      try {
        // Usar função diferente se excludeAnswered estiver ativo
        if (debouncedFilters.excludeAnswered && user?.id) {
          const rpcParams = {
            specialty_ids: debouncedFilters.specialties || [],
            theme_ids: debouncedFilters.themes || [],
            focus_ids: debouncedFilters.focuses || [],
            location_ids: debouncedFilters.locations || [],
            years: (debouncedFilters.years || []).map(Number),
            question_types: debouncedFilters.question_types || [],
            question_formats: debouncedFilters.question_formats || [],
            domain_filter: domain,
            user_id: user.id
          };

          // Usar função que exclui questões acertadas
          const { data, error } = await supabase.rpc('get_filtered_question_count_excluding_answered', rpcParams);

          if (error) {
            return { total_count: 0 };
          }

          return data || { total_count: 0 };
        } else {
          // Usar função normal
          const baseParams = {
            specialty_ids: debouncedFilters.specialties || [],
            theme_ids: debouncedFilters.themes || [],
            focus_ids: debouncedFilters.focuses || [],
            location_ids: debouncedFilters.locations || [],
            years: (debouncedFilters.years || []).map(Number),
            question_types: debouncedFilters.question_types || [],
            question_formats: debouncedFilters.question_formats || [],
            domain_filter: domain
          };

          const { data, error } = await supabase.rpc('get_filtered_question_count', baseParams);

          if (error) {
            return { total_count: 0 };
          }

          return data as { total_count: number };
        }

      } catch (error) {
        return { total_count: 0 };
      }
    },
    enabled: enabled && hasFilters && isReady && !!domain,
    staleTime: 2 * 60 * 1000, // 2 minutes - cache mais curto para contagens
    cacheTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1, // Menos tentativas para não atrasar
    retryDelay: 500
  });

  return {
    ...result,
    count: result.data?.total_count || 0,
    isLoading: result.isLoading,
    isFetching: result.isFetching
  };
};
