import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search } from "lucide-react";
import { SelectedFiltersDisplay } from "./components/SelectedFiltersDisplay";
import { LocationFilter } from "./components/LocationFilter";
import { YearFilter } from "./components/YearFilter";
import { useQuestionMetadata } from "@/hooks/useQuestionMetadata";
import type { SelectedFilters } from "@/types/question";
import { CategoryList } from "./components/CategoryList";
import { useQuestionCount } from "@/hooks/useQuestionCount";

interface AdvancedQuestionFilterProps {
  onApplyFilters: (filters: SelectedFilters) => void;
  selectedFilters: SelectedFilters;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export function AdvancedQuestionFilter({
  onApplyFilters,
  selectedFilters: initialFilters,
  searchTerm,
  setSearchTerm,
}: AdvancedQuestionFilterProps) {
  const [activeTab, setActiveTab] = useState<"specialty" | "location" | "year">("specialty");
  const [localFilters, setLocalFilters] = useState<SelectedFilters>(initialFilters);

  const { data: metadata, isLoading: isLoadingMetadata } = useQuestionMetadata();

  const availableFilters = metadata || {
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: []
  };

  const { count: questionCount, isLoading } = useQuestionCount({
    filters: localFilters,
    enabled: true
  });



  const handleToggleFilter = (id: string) => {
    setLocalFilters(prev => {
      const filterKey = `${activeTab}s` as keyof SelectedFilters;
      const filterArray = [...(prev[filterKey] || [])];
      const index = filterArray.indexOf(id);

      if (index === -1) {
        filterArray.push(id);
      } else {
        filterArray.splice(index, 1);
      }

      return { ...prev, [filterKey]: filterArray };
    });
  };

  const hasActiveFilters = Object.values(localFilters).some(
    filters => filters && filters.length > 0
  );

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Filtrar Questões</h2>
          <Button onClick={() => onApplyFilters(localFilters)}>
            Aplicar Filtros
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Pesquisar..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
          <TabsList className="w-full grid grid-cols-3">
            <TabsTrigger value="specialty">Especialidades</TabsTrigger>
            <TabsTrigger value="location">Instituições</TabsTrigger>
            <TabsTrigger value="year">Anos</TabsTrigger>
          </TabsList>

          <TabsContent value="specialty" className="mt-4">
            <CategoryList
              categories={availableFilters.specialties}
              selectedFilters={localFilters.specialties}
              expandedCategories={[]}
              searchTerm={searchTerm}
              onToggleFilter={handleToggleFilter}
              onToggleExpand={() => {}}
              type="specialty"
              parentFilters={localFilters}
              questionCounts={{ totalCounts: {}, filteredCounts: {} }}
            />
          </TabsContent>

          <TabsContent value="location" className="mt-4">
            <LocationFilter
              locations={availableFilters.locations}
              selectedLocations={localFilters.locations || []}
              onToggleLocation={handleToggleFilter}
              questionCounts={{ totalCounts: {}, filteredCounts: {} }}
              hasActiveFilters={hasActiveFilters}
            />
          </TabsContent>

          <TabsContent value="year" className="mt-4">
            <YearFilter
              selectedYears={localFilters.years || []}
              onToggleYear={handleToggleFilter}
              questionCounts={{ totalCounts: {}, filteredCounts: {} }}
              hasActiveFilters={hasActiveFilters}
              selectedFilters={localFilters}
            />
          </TabsContent>
        </Tabs>

        <SelectedFiltersDisplay
          selectedFilters={localFilters}
          availableFilters={availableFilters}
          onRemoveFilter={(id, type) => {
            setLocalFilters(prev => ({
              ...prev,
              [type]: prev[type].filter(filterId => filterId !== id)
            }));
          }}
        />
      </div>
    </Card>
  );
}