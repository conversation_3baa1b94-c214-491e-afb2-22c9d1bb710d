import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Gift, Heart, Sparkles } from 'lucide-react';
import { cn } from "@/lib/utils";

interface ReferralWelcomeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  referrerName: string;
  referralCode: string;
}

export const ReferralWelcomeDialog: React.FC<ReferralWelcomeDialogProps> = ({
  open,
  onOpenChange,
  referrerName,
  referralCode
}) => {
  const handleContinue = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
        {/* Header com fundo colorido */}
        <div className="w-full py-6 bg-gradient-to-br from-[#E6F2FF] to-[#FFE6E6] border-b border-black/20">
          <div className="flex flex-col items-center text-center gap-3 px-6">
            <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-black/20">
              <Gift className="h-8 w-8 text-blue-600" />
            </div>
            <div className="space-y-1">
              <h2 className="text-xl font-bold text-gray-900">
                🎉 Bem-vindo ao MedEvo!
              </h2>
              <p className="text-sm text-gray-700">
                Você foi convidado por um amigo
              </p>
            </div>
          </div>
        </div>

        {/* Conteúdo principal */}
        <div className="p-6 space-y-6">
          <DialogHeader className="space-y-3">
            <DialogTitle className="sr-only">Bem-vindo via referência</DialogTitle>
            <DialogDescription className="sr-only">
              Você foi convidado para o MedEvo por {referrerName}
            </DialogDescription>
            
            {/* Informações do convite */}
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="text-base font-medium text-gray-800">
                  Convidado por
                </span>
              </div>
              
              <div className="p-4 bg-[#FEF7CD]/50 rounded-xl border border-black/20">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  <span className="text-lg font-bold text-gray-900">
                    {referrerName}
                  </span>
                </div>
                <Badge variant="outline" className="border-black/30">
                  Código: {referralCode}
                </Badge>
              </div>
            </div>
          </DialogHeader>

          {/* Benefícios */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-yellow-500" />
              <span className="font-semibold text-gray-800">
                Você agora faz parte da nossa comunidade!
              </span>
            </div>
            
            <div className="space-y-3 text-sm text-gray-700">
              <div className="flex items-start gap-3 p-3 bg-[#E6F3E6]/30 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>
                  <strong>Acesso completo</strong> a todas as questões e funcionalidades
                </span>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-[#E6F2FF]/30 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>
                  <strong>Estatísticas detalhadas</strong> para acompanhar seu progresso
                </span>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-[#FFE6E6]/30 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>
                  <strong>Flashcards colaborativos</strong> criados pela comunidade
                </span>
              </div>
            </div>
          </div>

          {/* Botão de ação */}
          <div className="pt-4">
            <Button 
              onClick={handleContinue}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-2 border-black/20 rounded-xl h-12 font-semibold"
            >
              Começar a Estudar! 🚀
            </Button>
          </div>

          {/* Mensagem de agradecimento */}
          <div className="text-center p-4 bg-gradient-to-r from-[#FEF7CD]/30 to-white/30 rounded-xl border border-black/10">
            <p className="text-sm text-gray-600">
              Obrigado por se juntar à nossa comunidade de estudos! 
              Juntos, vamos alcançar seus objetivos acadêmicos.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
