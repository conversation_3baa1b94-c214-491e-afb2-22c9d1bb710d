
import { UseFormReturn } from "react-hook-form";
import { DurationSection } from "./form-sections/DurationSection";
import { DaysSection } from "./form-sections/DaysSection";
import { ScheduleOptionsSection } from "./form-sections/ScheduleOptionsSection";
import { InstitutionFilterSection } from "./form-sections/InstitutionFilterSection";
import { ScheduleSummarySection } from "./form-sections/ScheduleSummarySection";
import { ValidationErrors } from "./form-sections/ValidationErrors";
import { SubmitButton } from "./form-sections/SubmitButton";
import type { AIScheduleFormData } from "../types";

interface ScheduleFormProps {
  form: UseFormReturn<AIScheduleFormData>;
  onSubmit: (data: AIScheduleFormData) => void;
  customWeeks: boolean;
  setCustomWeeks: (value: boolean) => void;
  existingWeeks: number[];
  isLoading: boolean;
  validationErrors: string[];
}

export const ScheduleForm = ({
  form,
  onSubmit,
  customWeeks,
  setCustomWeeks,
  existingWeeks,
  isLoading,
  validationErrors
}: ScheduleFormProps) => {
  const { handleSubmit, watch } = form;

  const onFormSubmit = (data: any) => {
    console.log('📝 [ScheduleForm] ===== FORMULÁRIO SUBMETIDO =====');
    console.log('📝 [ScheduleForm] Dados do formulário:', {
      generationMode: data.generationMode,
      institutionIds: data.institutionIds,
      startYear: data.startYear,
      endYear: data.endYear,
      scheduleOption: data.scheduleOption,
      weeksCount: data.weeksCount,
      targetWeek: data.targetWeek,
      topicDuration: data.topicDuration,
      domain: data.domain,
      availableDays: Object.keys(data.availableDays || {}).filter(day => data.availableDays[day]?.enabled)
    });
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      <DurationSection form={form} />
      <DaysSection form={form} />
      <ScheduleOptionsSection
        form={form}
        customWeeks={customWeeks}
        setCustomWeeks={setCustomWeeks}
        existingWeeks={existingWeeks}
      />
      <InstitutionFilterSection form={form} />
      <ScheduleSummarySection form={form} />
      {validationErrors.length > 0 && <ValidationErrors errors={validationErrors} />}

      <div className="pt-4 border-t">
        <SubmitButton isLoading={isLoading} />
      </div>
    </form>
  );
};
