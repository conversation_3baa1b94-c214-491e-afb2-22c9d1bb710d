import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AdminMenu } from "@/components/admin/AdminMenu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Check, AlertTriangle, Brain, ArrowRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface Category {
  id: string;
  name: string;
  type: string;
  parent_id?: string;
}

interface FocusWithCount {
  id: string;
  name: string;
  question_count: number;
  theme_id: string;
  theme_name: string;
  specialty_id: string;
  specialty_name: string;
}

interface ConsolidationSuggestion {
  targetFocusId: string;
  targetFocusName: string;
  confidence: number;
  reasoning: string;
}

interface BatchAnalysisItem {
  focus: FocusWithCount;
  suggestion: ConsolidationSuggestion | null;
  status: 'pending' | 'analyzing' | 'completed' | 'error' | 'approved';
  error?: string;
  approved?: boolean;
  skipped?: boolean;
}

const FocusConsolidationByYear = () => {
  const [selectedYear, setSelectedYear] = useState<string>("");
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [specialties, setSpecialties] = useState<Category[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
  const [themes, setThemes] = useState<Category[]>([]);
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [yearFocuses, setYearFocuses] = useState<FocusWithCount[]>([]);
  const [targetFocuses, setTargetFocuses] = useState<FocusWithCount[]>([]);
  const [selectedFocus, setSelectedFocus] = useState<FocusWithCount | null>(null);
  const [suggestion, setSuggestion] = useState<ConsolidationSuggestion | null>(null);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [consolidating, setConsolidating] = useState(false);

  // Batch processing states
  const [batchAnalysis, setBatchAnalysis] = useState<BatchAnalysisItem[]>([]);
  const [isBatchAnalyzing, setIsBatchAnalyzing] = useState(false);
  const [isBatchConsolidating, setIsBatchConsolidating] = useState(false);
  const [showBatchResults, setShowBatchResults] = useState(false);
  const [batchSize, setBatchSize] = useState<number>(10); // Default batch size
  const [showAlreadyConsolidated, setShowAlreadyConsolidated] = useState(false);
  const [filterByJune2025, setFilterByJune2025] = useState(true); // Ativo por padrão

  // Load available years on component mount
  useEffect(() => {
    loadAvailableYears();
  }, []);

  // Load specialties when year is selected
  useEffect(() => {
    if (selectedYear) {
      loadSpecialties();
    }
  }, [selectedYear]);

  // Load themes when specialty is selected
  useEffect(() => {
    if (selectedSpecialty && selectedYear) {
      loadThemes();
    }
  }, [selectedSpecialty, selectedYear]);

  // Load focuses when theme is selected or filter changes
  useEffect(() => {
    if (selectedTheme && selectedYear) {
      loadFocuses();
    }
  }, [selectedTheme, selectedYear, filterByJune2025]); // Recarregar quando filtro mudar

  const loadAvailableYears = async () => {
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('exam_year')
        .not('exam_year', 'is', null)
        .order('exam_year', { ascending: false });

      if (error) throw error;

      const years = [...new Set(data.map(q => q.exam_year))].filter(Boolean) as number[];
      setAvailableYears(years);
    } catch (error) {
      console.error('Error loading years:', error);
      toast.error('Erro ao carregar anos disponíveis');
    }
  };

  const loadSpecialties = async () => {
    setLoading(true);
    try {
      // Get specialties that have questions in the selected year
      const { data: questionData, error: questionError } = await supabase
        .from('questions')
        .select('specialty_id')
        .eq('exam_year', parseInt(selectedYear))
        .not('specialty_id', 'is', null);

      if (questionError) throw questionError;

      const specialtyIds = [...new Set(questionData.map(q => q.specialty_id))];

      if (specialtyIds.length === 0) {
        setSpecialties([]);
        return;
      }

      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'specialty')
        .in('id', specialtyIds);

      if (error) throw error;
      setSpecialties(data || []);
    } catch (error) {
      console.error('Error loading specialties:', error);
      toast.error('Erro ao carregar especialidades');
    } finally {
      setLoading(false);
    }
  };

  const loadThemes = async () => {
    setLoading(true);
    try {
      // Get themes that have questions in the selected year and specialty
      const { data: questionData, error: questionError } = await supabase
        .from('questions')
        .select('theme_id')
        .eq('exam_year', parseInt(selectedYear))
        .eq('specialty_id', selectedSpecialty)
        .not('theme_id', 'is', null);

      if (questionError) throw questionError;

      const themeIds = [...new Set(questionData.map(q => q.theme_id))];

      if (themeIds.length === 0) {
        setThemes([]);
        return;
      }

      // Get all themes
      const { data: allThemes, error: themesError } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'theme')
        .eq('parent_id', selectedSpecialty)
        .in('id', themeIds);

      if (themesError) throw themesError;

      // SIMPLIFICAR: Mostrar todos os temas que têm questões de 2025
      // A verificação de consolidação será feita depois, no loadFocuses
      setThemes(allThemes || []);
    } catch (error) {
      console.error('Error loading themes:', error);
      toast.error('Erro ao carregar temas');
    } finally {
      setLoading(false);
    }
  };

  const loadFocuses = async () => {
    setLoading(true);
    try {
      // Use direct queries approach (RPC functions don't exist)
      // Load focuses from selected year using direct queries
      // FILTRO: Apenas focos criados em junho de 2025
      const { data: yearQuestions, error: yearError } = await supabase
        .from('questions')
        .select(`
          focus_id,
          study_categories!questions_focus_id_fkey(id, name, parent_id, created_at)
        `)
        .eq('exam_year', parseInt(selectedYear))
        .eq('specialty_id', selectedSpecialty)
        .eq('theme_id', selectedTheme)
        .not('focus_id', 'is', null);

      if (yearError) throw yearError;

      // Group by focus and count questions (INCLUINDO focos inconsistentes)
      // FILTRO: Apenas focos criados em junho de 2025
      const focusMap = new Map();
      yearQuestions.forEach(q => {
        const focus = q.study_categories;
        if (focus) {
          // FILTRO CONDICIONAL: Verificar se o foco foi criado em junho de 2025 (se filtro ativo)
          if (filterByJune2025) {
            const createdAt = new Date(focus.created_at);
            const isJune2025 = createdAt.getFullYear() === 2025 && createdAt.getMonth() === 5; // Mês 5 = junho (0-indexed)

            if (!isJune2025) {
              return; // Pular este foco
            }
          }

          // MUDANÇA: Incluir TODOS os focos, mesmo os inconsistentes
          const key = focus.id;
          if (focusMap.has(key)) {
            focusMap.get(key).question_count++;
          } else {
            focusMap.set(key, {
              id: focus.id,
              name: focus.name,
              question_count: 1,
              theme_id: selectedTheme,
              theme_name: themes.find(t => t.id === selectedTheme)?.name || '',
              specialty_id: selectedSpecialty,
              specialty_name: specialties.find(s => s.id === selectedSpecialty)?.name || '',
              // Adicionar flag para indicar se é inconsistente
              isInconsistent: focus.parent_id !== selectedTheme,
              originalTheme: focus.parent_id,
              created_at: focus.created_at
            });
          }
        }
      });

      const yearFocusData = Array.from(focusMap.values());

      // Load target focuses (excluding the selected year) + focos que já pertencem ao tema correto
      const { data: targetQuestions, error: targetError } = await supabase
        .from('questions')
        .select(`
          focus_id,
          study_categories!questions_focus_id_fkey(id, name, parent_id)
        `)
        .neq('exam_year', parseInt(selectedYear))
        .eq('specialty_id', selectedSpecialty)
        .eq('theme_id', selectedTheme)
        .not('focus_id', 'is', null);

      if (targetError) throw targetError;

      // TAMBÉM buscar focos que já pertencem ao tema correto E têm questões pré-2025

      const { data: correctThemeFocuses, error: correctError } = await supabase
        .from('study_categories')
        .select(`
          id,
          name,
          parent_id,
          questions!questions_focus_id_fkey!inner(exam_year)
        `)
        .eq('type', 'focus')
        .eq('parent_id', selectedTheme)
        .eq('questions.specialty_id', selectedSpecialty)
        .lt('questions.exam_year', parseInt(selectedYear));

      if (correctError) {
        console.error('Erro ao buscar focos do tema:', correctError);
        throw correctError;
      }



      // Group target focuses
      const targetFocusMap = new Map();

      // Adicionar focos de questões históricas
      targetQuestions.forEach(q => {
        const focus = q.study_categories;
        if (focus && focus.parent_id === selectedTheme) {
          const key = focus.id;
          if (targetFocusMap.has(key)) {
            targetFocusMap.get(key).question_count++;
          } else {
            targetFocusMap.set(key, {
              id: focus.id,
              name: focus.name,
              question_count: 1,
              theme_id: selectedTheme,
              theme_name: themes.find(t => t.id === selectedTheme)?.name || '',
              specialty_id: selectedSpecialty,
              specialty_name: specialties.find(s => s.id === selectedSpecialty)?.name || ''
            });
          }
        }
      });

        // Adicionar focos que pertencem ao tema correto E têm questões pré-2025
        correctThemeFocuses.forEach(focus => {
          if (!targetFocusMap.has(focus.id)) {
            // Contar questões pré-2025 para este foco
            const preYearQuestionCount = focus.questions?.length || 0;

            targetFocusMap.set(focus.id, {
              id: focus.id,
              name: focus.name,
              question_count: preYearQuestionCount,
              theme_id: selectedTheme,
              theme_name: themes.find(t => t.id === selectedTheme)?.name || '',
              specialty_id: selectedSpecialty,
              specialty_name: specialties.find(s => s.id === selectedSpecialty)?.name || ''
            });
          }
        });

        // Filtrar apenas focos que realmente têm questões pré-2025
        const targetFocusData = Array.from(targetFocusMap.values())
          .filter(focus => focus.question_count > 0);



        // Verificar quais focos já foram consolidados (só têm questões de 2025)
        const focusesAlreadyConsolidated = new Set();

        // Otimização: verificar todos os focos de uma vez
        const focusIds = yearFocusData.map(f => f.id);
        if (focusIds.length > 0) {
          const { data: preYearQuestions } = await supabase
            .from('questions')
            .select('focus_id')
            .in('focus_id', focusIds)
            .eq('specialty_id', selectedSpecialty)
            .eq('theme_id', selectedTheme)
            .lt('exam_year', parseInt(selectedYear));

          // Focos que têm questões de anos anteriores
          const focusesWithPreYearQuestions = new Set(preYearQuestions?.map(q => q.focus_id) || []);

          // Focos que NÃO têm questões de anos anteriores = possivelmente já consolidados
          yearFocusData.forEach(focus => {
            if (!focusesWithPreYearQuestions.has(focus.id)) {
              focusesAlreadyConsolidated.add(focus.id);
            }
          });
        }

        // MUDANÇA: Incluir TODOS os focos de 2025 que não têm questões históricas suficientes
        // Critério: focos que só têm questões do ano selecionado (2025) e têm poucas questões
        const targetFocusIds = new Set(targetFocusData.map(f => f.id));
        const focusesToConsolidate = yearFocusData.filter(focus => {
          // NOVO: Excluir focos que já foram consolidados (só têm questões de 2025)
          // Só excluir se o usuário não quer ver focos já consolidados
          if (!showAlreadyConsolidated && focusesAlreadyConsolidated.has(focus.id) && focus.question_count > 25) {
            return false;
          }

          // Se é inconsistente, sempre incluir para consolidação
          if (focus.isInconsistent) return true;

          // Se não é inconsistente, incluir se:
          // 1. Não existe nos targets OU
          // 2. Tem poucas questões (≤ 25) e pode ser consolidado
          const hasLowQuestionCount = focus.question_count <= 25;
          const notInTargets = !targetFocusIds.has(focus.id);

          return notInTargets || hasLowQuestionCount;
        });



        // Adicionar flag de "já consolidado" aos focos
        const focusesWithFlags = focusesToConsolidate.map(focus => ({
          ...focus,
          alreadyConsolidated: focusesAlreadyConsolidated.has(focus.id) && focus.question_count > 25
        }));

      setYearFocuses(focusesWithFlags);
      setTargetFocuses(targetFocusData);
    } catch (error) {
      console.error('Error loading focuses:', error);
      toast.error('Erro ao carregar focos');
    } finally {
      setLoading(false);
    }
  };

  const analyzeConsolidation = async (focus: FocusWithCount) => {
    if (targetFocuses.length === 0) {
      toast.error('Não há focos de destino disponíveis para esta especialidade/tema');
      return;
    }

    // Verificar se há targets válidos (excluindo o próprio foco)
    const validTargets = targetFocuses.filter(f => f.id !== focus.id);
    if (validTargets.length === 0) {
      toast.error(`Não há focos de destino válidos para "${focus.name}". O foco não pode ser consolidado para ele mesmo.`);
      return;
    }

    setAnalyzing(true);
    setSelectedFocus(focus);
    setSuggestion(null);

    try {
      // Filtrar targets: excluir o próprio foco E garantir que são focos pré-2025
      const allValidTargets = targetFocuses.filter(f =>
        f.id !== focus.id && f.question_count > 0
      );

      // LIMITAÇÃO: Enviar apenas os 10 melhores targets para evitar sobrecarga da IA
      // Ordenar por número de questões (descendente) e pegar os top 10
      const validTargets = allValidTargets
        .sort((a, b) => b.question_count - a.question_count)
        .slice(0, 10);



      if (validTargets.length === 0) {
        throw new Error(`Não há focos target válidos (pré-${selectedYear}) para consolidação`);
      }

      const { data, error } = await supabase.functions.invoke('analyze-focus-consolidation', {
        body: {
          sourceFocus: {
            id: focus.id,
            name: focus.name,
            specialty: focus.specialty_name,
            theme: focus.theme_name
          },
          targetFocuses: validTargets.map(f => ({
            id: f.id,
            name: f.name,
            questionCount: f.question_count
          }))
        }
      });

      if (error) {
        console.error('❌ Erro detalhado da Edge Function:', error);
        console.error('📊 Dados enviados:', {
          sourceFocus: focus.name,
          targetCount: validTargets.length,
          targets: validTargets.slice(0, 3).map(f => f.name)
        });
        throw error;
      }

      setSuggestion(data);
    } catch (error) {
      console.error('Error analyzing consolidation:', error);
      toast.error('Erro ao analisar consolidação');
    } finally {
      setAnalyzing(false);
    }
  };

  const executeConsolidation = async (focus?: FocusWithCount, suggestionToUse?: ConsolidationSuggestion) => {
    const focusToUse = focus || selectedFocus;
    const suggestionToUseLocal = suggestionToUse || suggestion;

    if (!focusToUse || !suggestionToUseLocal) return false;

    try {
      // Try using RPC function first, fallback to direct queries if it doesn't exist
      try {
        const { data: result, error: rpcError } = await supabase.rpc('consolidate_focus_questions', {
          p_source_focus_id: focusToUse.id,
          p_target_focus_id: suggestionToUseLocal.targetFocusId
        });

        if (rpcError) throw rpcError;

        if (!focus) { // Only show toast for individual consolidations
          toast.success(result.message || `Consolidação executada com sucesso! ${result.questions_updated} questões foram movidas.`);
        }

      } catch (rpcError) {

        // Fallback: Update all questions from source focus to target focus
        // IMPORTANTE: Só atualizar questões do tema/especialidade específicos
        const { error: updateError } = await supabase
          .from('questions')
          .update({
            focus_id: suggestionToUseLocal.targetFocusId,
            updated_at: new Date().toISOString()
          })
          .eq('focus_id', focusToUse.id)
          .eq('theme_id', selectedTheme)
          .eq('specialty_id', selectedSpecialty);

        if (updateError) throw updateError;

        // MUDANÇA: NÃO deletar o foco original para permitir recuperação
        // O foco ficará zerado e poderá ser removido depois via "Deletar Vazias"

        if (!focus) { // Only show toast for individual consolidations
          const message = `Consolidação executada com sucesso! ${focusToUse.question_count} questões foram movidas. Foco original preservado (agora zerado).`;
          toast.success(message);
        }
      }

      if (!focus) { // Only for individual consolidations
        // Reload focuses to reflect changes
        await loadFocuses();
        setSelectedFocus(null);
        setSuggestion(null);
      }

      return true;
    } catch (error) {
      console.error('Error executing consolidation:', error);
      if (!focus) { // Only show toast for individual consolidations
        toast.error('Erro ao executar consolidação');
      }
      return false;
    }
  };

  // Batch analysis functions
  const startBatchAnalysis = async () => {
    if (yearFocuses.length === 0) {
      toast.error('Não há focos para analisar');
      return;
    }

    setIsBatchAnalyzing(true);
    setShowBatchResults(true);

    // Get the focuses to process (limited by batch size)
    const focusesToProcess = yearFocuses.slice(0, batchSize);

    // Initialize batch items
    const initialBatchItems: BatchAnalysisItem[] = focusesToProcess.map(focus => ({
      focus,
      suggestion: null,
      status: 'pending'
    }));

    setBatchAnalysis(initialBatchItems);

    // Process each focus sequentially to avoid overwhelming the API
    for (let i = 0; i < initialBatchItems.length; i++) {
      const item = initialBatchItems[i];

      // Update status to analyzing
      setBatchAnalysis(prev => prev.map((batchItem, index) =>
        index === i ? { ...batchItem, status: 'analyzing' } : batchItem
      ));

      try {
        // Filtrar targets: excluir o próprio foco E garantir que são focos pré-2025
        const allValidTargets = targetFocuses.filter(f =>
          f.id !== item.focus.id && f.question_count > 0
        );

        // LIMITAÇÃO: Enviar apenas os 10 melhores targets para evitar sobrecarga da IA
        const validTargets = allValidTargets
          .sort((a, b) => b.question_count - a.question_count)
          .slice(0, 10);

        if (validTargets.length === 0) {
          throw new Error(`Não há focos target válidos (pré-${selectedYear}) para consolidação`);
        }

        const { data, error } = await supabase.functions.invoke('analyze-focus-consolidation', {
          body: {
            sourceFocus: {
              id: item.focus.id,
              name: item.focus.name,
              specialty: item.focus.specialty_name,
              theme: item.focus.theme_name
            },
            targetFocuses: validTargets.map(f => ({
              id: f.id,
              name: f.name,
              questionCount: f.question_count
            }))
          }
        });

        if (error) throw error;

        // Update with suggestion
        setBatchAnalysis(prev => prev.map((batchItem, index) =>
          index === i ? {
            ...batchItem,
            suggestion: data,
            status: 'completed'
          } : batchItem
        ));

        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`Error analyzing focus ${item.focus.name}:`, error);

        // Update with error
        setBatchAnalysis(prev => prev.map((batchItem, index) =>
          index === i ? {
            ...batchItem,
            status: 'error',
            error: error.message || 'Erro na análise'
          } : batchItem
        ));
      }
    }

    setIsBatchAnalyzing(false);
    toast.success('Análise em lote concluída!');
  };

  // Funções de aprovação manual
  const approveConsolidation = (index: number) => {
    setBatchAnalysis(prev => prev.map((item, i) =>
      i === index ? { ...item, approved: true, skipped: false } : item
    ));
    toast.success(`Consolidação aprovada: ${batchAnalysis[index].focus.name}`);
  };

  const skipConsolidation = (index: number) => {
    setBatchAnalysis(prev => prev.map((item, i) =>
      i === index ? { ...item, skipped: true, approved: false } : item
    ));
    toast.info(`Consolidação pulada: ${batchAnalysis[index].focus.name}`);
  };

  const approveAllRemaining = () => {
    setBatchAnalysis(prev => prev.map(item =>
      item.suggestion && !item.approved && !item.skipped
        ? { ...item, approved: true }
        : item
    ));
    const remainingCount = batchAnalysis.filter(item =>
      item.suggestion && !item.approved && !item.skipped
    ).length;
    toast.success(`${remainingCount} consolidações aprovadas em lote!`);
  };

  const resetApprovals = () => {
    setBatchAnalysis(prev => prev.map(item => ({
      ...item,
      approved: false,
      skipped: false
    })));
    toast.info('Todas as aprovações foram resetadas');
  };

  const approveAllSuggestions = async () => {
    const completedItems = batchAnalysis.filter(item =>
      item.status === 'completed' && item.suggestion && item.approved
    );

    if (completedItems.length === 0) {
      toast.error('Não há consolidações aprovadas para executar. Aprove pelo menos uma consolidação primeiro.');
      return;
    }

    setIsBatchConsolidating(true);
    let successCount = 0;
    let errorCount = 0;

    for (const item of completedItems) {
      // Update status to approved
      setBatchAnalysis(prev => prev.map(batchItem =>
        batchItem.focus.id === item.focus.id ? { ...batchItem, status: 'approved' } : batchItem
      ));

      const success = await executeConsolidation(item.focus, item.suggestion!);

      if (success) {
        successCount++;
      } else {
        errorCount++;
      }

      // Small delay between consolidations
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    setIsBatchConsolidating(false);

    toast.success(`Consolidação em lote concluída! ${successCount} sucessos, ${errorCount} erros.`);

    // Reload focuses and clear batch results
    await loadFocuses();
    setShowBatchResults(false);
    setBatchAnalysis([]);
  };

  const getStatusIcon = (status: BatchAnalysisItem['status']) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'analyzing': return '🔄';
      case 'completed': return '✅';
      case 'error': return '❌';
      case 'approved': return '🎉';
      default: return '⏳';
    }
  };

  const getStatusColor = (status: BatchAnalysisItem['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'analyzing': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      case 'approved': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <>
      <Header />
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-8">Consolidação de Focos por Ano</h1>
        
        <AdminMenu />

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Seleção de Filtros</CardTitle>
            <CardDescription>
              Selecione o ano e a hierarquia para consolidar focos de 2025 para focos pré-existentes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Ano</label>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o ano" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Especialidade</label>
                <Select 
                  value={selectedSpecialty} 
                  onValueChange={setSelectedSpecialty}
                  disabled={!selectedYear}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a especialidade" />
                  </SelectTrigger>
                  <SelectContent>
                    {specialties.map(specialty => (
                      <SelectItem key={specialty.id} value={specialty.id}>
                        {specialty.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Tema</label>
                <Select 
                  value={selectedTheme} 
                  onValueChange={setSelectedTheme}
                  disabled={!selectedSpecialty}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tema" />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map(theme => (
                      <SelectItem key={theme.id} value={theme.id}>
                        {theme.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Toggles de filtro */}
            <div className="pt-4 border-t space-y-3">
              {/* Toggle para filtrar por junho/2025 */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="filterByJune2025"
                  checked={filterByJune2025}
                  onChange={(e) => setFilterByJune2025(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <label htmlFor="filterByJune2025" className="text-sm font-medium">
                  Filtrar apenas focos criados em junho/2025
                </label>
                <span className="text-xs text-muted-foreground">
                  (focos criados especificamente no mês 06/2025)
                </span>
              </div>

              {/* Toggle para mostrar focos já consolidados */}
              {yearFocuses.length > 0 && (
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showAlreadyConsolidated"
                    checked={showAlreadyConsolidated}
                    onChange={(e) => setShowAlreadyConsolidated(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="showAlreadyConsolidated" className="text-sm font-medium">
                    Mostrar focos já consolidados
                  </label>
                  <span className="text-xs text-muted-foreground">
                    (focos que só têm questões de {selectedYear} e podem ter sido consolidados anteriormente)
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {yearFocuses.length > 0 && (
          <div className="space-y-6">
            {/* Batch Processing Controls */}
            <Card>
              <CardHeader>
                <CardTitle>Processamento em Lote</CardTitle>
                <CardDescription>
                  Analise e consolide focos em lotes personalizáveis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Batch Size Selector */}
                  <div className="flex items-center gap-4">
                    <label className="text-sm font-medium min-w-fit">
                      Quantidade por lote:
                    </label>
                    <Select value={batchSize.toString()} onValueChange={(value) => setBatchSize(parseInt(value))}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 focos</SelectItem>
                        <SelectItem value="10">10 focos</SelectItem>
                        <SelectItem value="15">15 focos</SelectItem>
                        <SelectItem value="20">20 focos</SelectItem>
                        <SelectItem value="25">25 focos</SelectItem>
                        <SelectItem value="50">50 focos</SelectItem>
                        <SelectItem value={yearFocuses.length.toString()}>
                          Todos ({yearFocuses.length})
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-sm text-muted-foreground">
                      {Math.min(batchSize, yearFocuses.length)} de {yearFocuses.length} focos serão processados
                    </span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <Button
                      onClick={startBatchAnalysis}
                      disabled={isBatchAnalyzing || yearFocuses.length === 0}
                      className="flex-1"
                    >
                      {isBatchAnalyzing ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Analisando {batchAnalysis.filter(item => item.status === 'analyzing').length}/{Math.min(batchSize, yearFocuses.length)}...
                        </>
                      ) : (
                        <>
                          <Brain className="h-4 w-4 mr-2" />
                          Analisar Lote ({Math.min(batchSize, yearFocuses.length)} focos)
                        </>
                      )}
                    </Button>


                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Batch Results */}
            {showBatchResults && (
              <Card>
                <CardHeader>
                  <CardTitle>Resultados da Análise em Lote</CardTitle>
                  <CardDescription>
                    Sugestões da IA para todos os focos
                  </CardDescription>

                  {/* Botões de Ação em Lote */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      size="sm"
                      onClick={approveAllRemaining}
                      disabled={isBatchConsolidating || batchAnalysis.filter(item => item.suggestion && !item.approved && !item.skipped).length === 0}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      ✅ Aprovar Todos Restantes ({batchAnalysis.filter(item => item.suggestion && !item.approved && !item.skipped).length})
                    </Button>

                    <Button
                      size="sm"
                      onClick={approveAllSuggestions}
                      disabled={isBatchConsolidating || batchAnalysis.filter(item => item.approved).length === 0}
                      variant="outline"
                    >
                      {isBatchConsolidating ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Consolidando...
                        </>
                      ) : (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Executar Aprovados ({batchAnalysis.filter(item => item.approved).length})
                        </>
                      )}
                    </Button>

                    <Button
                      size="sm"
                      onClick={resetApprovals}
                      disabled={isBatchConsolidating || batchAnalysis.filter(item => item.approved || item.skipped).length === 0}
                      variant="destructive"
                    >
                      🔄 Resetar Aprovações
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {batchAnalysis.map((item, index) => (
                      <div key={item.focus.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className={`text-lg ${getStatusColor(item.status)}`}>
                              {getStatusIcon(item.status)}
                            </span>
                            <span className="font-medium">{item.focus.name}</span>
                            <Badge variant="secondary">{item.focus.question_count} questões</Badge>
                          </div>
                          {item.suggestion && (
                            <Badge variant={item.suggestion.confidence >= 0.7 ? "default" : "destructive"}>
                              {Math.round(item.suggestion.confidence * 100)}%
                            </Badge>
                          )}
                        </div>

                        {item.suggestion && (
                          <div className="text-sm text-muted-foreground">
                            <strong>Sugestão:</strong> {item.suggestion.targetFocusName}
                            {item.suggestion.reasoning && (
                              <div className="mt-1">
                                <strong>Motivo:</strong> {item.suggestion.reasoning}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Botões de Aprovação Manual */}
                        {item.suggestion && !item.approved && !item.skipped && (
                          <div className="flex gap-2 mt-3 pt-3 border-t">
                            <Button
                              size="sm"
                              onClick={() => approveConsolidation(index)}
                              disabled={isBatchConsolidating}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              ✅ Aprovar
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => skipConsolidation(index)}
                              disabled={isBatchConsolidating}
                            >
                              ⏭️ Pular
                            </Button>
                          </div>
                        )}

                        {/* Status de Aprovação */}
                        {item.approved && (
                          <div className="flex items-center gap-2 mt-3 pt-3 border-t text-green-600">
                            <span className="text-sm font-medium">✅ Aprovado - Será consolidado</span>
                          </div>
                        )}

                        {item.skipped && (
                          <div className="flex items-center gap-2 mt-3 pt-3 border-t text-gray-500">
                            <span className="text-sm font-medium">⏭️ Pulado - Não será consolidado</span>
                          </div>
                        )}

                        {item.error && (
                          <div className="text-sm text-red-600">
                            <strong>Erro:</strong> {item.error}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Focos do Ano Selecionado */}
              <Card>
                <CardHeader>
                  <CardTitle>Focos de {selectedYear}</CardTitle>
                  <CardDescription>
                    {yearFocuses.length} focos precisam ser consolidados
                    {filterByJune2025 && (
                      <span className="block text-xs mt-1 text-amber-600 font-medium">
                        📅 Filtro ativo: Apenas focos criados em junho/2025
                      </span>
                    )}
                    {!filterByJune2025 && (
                      <span className="block text-xs mt-1 text-blue-600 font-medium">
                        📅 Mostrando todos os focos de {selectedYear}
                      </span>
                    )}
                    {batchSize < yearFocuses.length && (
                      <span className="block text-xs mt-1 text-blue-600">
                        Próximo lote: {Math.min(batchSize, yearFocuses.length)} focos
                      </span>
                    )}
                    <div className="flex gap-4 mt-2 text-xs">
                      <span className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                        Novos focos
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
                        Possivelmente já consolidados
                      </span>
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {yearFocuses.map((focus, index) => (
                      <div
                        key={focus.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedFocus?.id === focus.id
                            ? 'border-blue-500 bg-blue-50'
                            : focus.alreadyConsolidated
                            ? 'border-orange-200 bg-orange-50 hover:border-orange-300'
                            : index < batchSize
                            ? 'border-green-200 bg-green-50 hover:border-green-300'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => analyzeConsolidation(focus)}
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{focus.name}</span>
                            {focus.alreadyConsolidated && (
                              <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-300">
                                Já Consolidado?
                              </Badge>
                            )}
                            {!focus.alreadyConsolidated && index < batchSize && (
                              <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                                Lote #{index + 1}
                              </Badge>
                            )}
                          </div>
                          <Badge variant="secondary">{focus.question_count} questões</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Análise e Sugestão */}
              <Card>
              <CardHeader>
                <CardTitle>Análise de Consolidação</CardTitle>
                <CardDescription>
                  Sugestão da IA para consolidação
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!selectedFocus && (
                  <div className="text-center py-8 text-muted-foreground">
                    Selecione um foco para analisar
                  </div>
                )}

                {analyzing && (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Analisando com IA...</span>
                  </div>
                )}

                {selectedFocus && suggestion && !analyzing && (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium mb-2">Foco Selecionado:</h4>
                      <p className="text-sm">{selectedFocus.name}</p>
                      <Badge variant="secondary" className="mt-1">
                        {selectedFocus.question_count} questões
                      </Badge>
                    </div>

                    <div className="flex items-center justify-center py-2">
                      <ArrowRight className="h-6 w-6 text-blue-500" />
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium mb-2">Sugestão da IA:</h4>
                      <p className="text-sm font-medium">{suggestion.targetFocusName}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline">
                          Confiança: {Math.round(suggestion.confidence * 100)}%
                        </Badge>
                        <Brain className="h-4 w-4 text-blue-500" />
                      </div>
                      {suggestion.reasoning && (
                        <p className="text-xs text-muted-foreground mt-2">
                          {suggestion.reasoning}
                        </p>
                      )}
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          setConsolidating(true);
                          executeConsolidation().finally(() => setConsolidating(false));
                        }}
                        disabled={consolidating}
                        className="flex-1"
                      >
                        {consolidating ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Consolidando...
                          </>
                        ) : (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            Executar Consolidação
                          </>
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setSelectedFocus(null);
                          setSuggestion(null);
                        }}
                      >
                        Cancelar
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            </div>
          </div>
        )}

        {selectedTheme && yearFocuses.length === 0 && !loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-8 text-center space-y-3">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <p className="text-lg font-medium">
                  Não foram encontrados focos para consolidar
                </p>
                <p className="text-muted-foreground">
                  Não existem focos de {selectedYear} que precisem ser consolidados para esta especialidade/tema.
                  <br />
                  {filterByJune2025 ? (
                    <>
                      <strong>Filtro ativo:</strong> Apenas focos criados em junho/2025 são exibidos.
                      <br />
                      Verifique se há focos com poucas questões (≤25) ou focos inconsistentes criados em junho.
                      <br />
                      <em>Dica: Desative o filtro "junho/2025" para ver todos os focos de {selectedYear}.</em>
                    </>
                  ) : (
                    <>
                      Mostrando todos os focos de {selectedYear}.
                      <br />
                      Verifique se há focos com poucas questões (≤25) ou focos inconsistentes.
                    </>
                  )}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </>
  );
};

export default FocusConsolidationByYear;
