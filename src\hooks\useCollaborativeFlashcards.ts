
import { useState, useCallback, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useStaticFlashcardHierarchy } from '@/hooks/useStaticDataCache';

interface FlashcardFilter {
  specialty?: string;
  theme?: string;
  focus?: string;
  showImported?: boolean;
}

export const useCollaborativeFlashcards = () => {
  const [flashcards, setFlashcards] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [importedCardIds, setImportedCardIds] = useState<string[]>([]);
  const requestInProgressRef = useRef(false);
  const lastFilterRef = useRef<{specialty?: string; showImported?: boolean}>({});
  const initialImportCheckDoneRef = useRef(false);

  // Hook para carregar dados da hierarquia
  const { data: hierarchyData } = useStaticFlashcardHierarchy();

  // Função para resolver nomes da hierarquia
  const resolveHierarchyNames = (cards: any[]) => {
    if (!hierarchyData) {
      // Se não temos dados da hierarquia, retorna com "Carregando..."
      return cards.map(card => ({
        ...card,
        hierarchy: {
          specialty: card.specialty_id ? { id: card.specialty_id, name: 'Carregando...' } : undefined,
          theme: card.theme_id ? { id: card.theme_id, name: 'Carregando...' } : undefined,
          focus: card.focus_id ? { id: card.focus_id, name: 'Carregando...' } : undefined,
          extraFocus: card.extrafocus_id ? { id: card.extrafocus_id, name: 'Carregando...' } : undefined
        }
      }));
    }

    return cards.map(card => ({
      ...card,
      hierarchy: {
        specialty: card.specialty_id ? {
          id: card.specialty_id,
          name: hierarchyData.specialties.find(s => s.id === card.specialty_id)?.name || 'Especialidade não encontrada'
        } : undefined,
        theme: card.theme_id ? {
          id: card.theme_id,
          name: hierarchyData.themes.find(t => t.id === card.theme_id)?.name || 'Tema não encontrado'
        } : undefined,
        focus: card.focus_id ? {
          id: card.focus_id,
          name: hierarchyData.focuses.find(f => f.id === card.focus_id)?.name || 'Foco não encontrado'
        } : undefined,
        extraFocus: card.extrafocus_id ? {
          id: card.extrafocus_id,
          name: hierarchyData.extrafocuses.find(e => e.id === card.extrafocus_id)?.name || 'Extra foco não encontrado'
        } : undefined
      }
    }));
  };

  const refreshImportedCards = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      // Skip if we already performed the initial check
      if (!initialImportCheckDoneRef.current || importedCardIds.length === 0) {
        const { data: userCards, error } = await supabase
          .from('flashcards_cards')
          .select('origin_id')
          .eq('user_id', user.id)
          .not('origin_id', 'is', null);

        if (error) throw error;

        if (userCards && userCards.length > 0) {
          const importedIds = userCards.map(card => card.origin_id).filter(Boolean);
          setImportedCardIds(importedIds);
          console.log(`🔄 [useCollaborativeFlashcards] Refreshed imported cards, found ${importedIds.length} imports`);
          initialImportCheckDoneRef.current = true;
          return importedIds;
        }
      }

      return importedCardIds;
    } catch (error: any) {
      console.error('❌ [useCollaborativeFlashcards] Error refreshing imported cards:', error);
      return importedCardIds;
    }
  }, [importedCardIds]);

  // Run initial refresh
  useEffect(() => {
    if (!initialImportCheckDoneRef.current) {
      refreshImportedCards();
    }
  }, [refreshImportedCards]);

  // Recarregar flashcards quando os dados da hierarquia estiverem disponíveis
  useEffect(() => {
    if (hierarchyData && flashcards.length > 0) {
      // Verificar se algum flashcard ainda tem "Carregando..." nos nomes
      const hasLoadingNames = flashcards.some(card =>
        card.hierarchy?.specialty?.name === 'Carregando...' ||
        card.hierarchy?.theme?.name === 'Carregando...' ||
        card.hierarchy?.focus?.name === 'Carregando...' ||
        card.hierarchy?.extraFocus?.name === 'Carregando...'
      );

      if (hasLoadingNames) {
        // Resolver nomes da hierarquia para os flashcards existentes
        const updatedFlashcards = resolveHierarchyNames(flashcards);
        setFlashcards(updatedFlashcards);
      }
    }
  }, [hierarchyData]);

  const refreshImportedCounts = useCallback(async (cardIds: string[]) => {
    if (!cardIds.length) return;

    try {
      // Using direct update instead of RPC for import count
      for (const cardId of cardIds) {
        const { error } = await supabase
          .from('flashcards_cards')
          .update({ import_count: supabase.rpc('increment', { row_id: cardId, table_name: 'flashcards_cards', column_name: 'import_count' }) })
          .eq('id', cardId);

        if (error) {
          console.error('❌ [useCollaborativeFlashcards] Error incrementing import count:', error);
        }
      }
    } catch (error: any) {
      console.error('❌ [useCollaborativeFlashcards] Error updating import counts:', error);
    }
  }, []);

  const loadFlashcards = useCallback(async (
    page: number = 1,
    filters: FlashcardFilter = {},
    sortBy: string = 'recent',
    limit: number = 10
  ) => {
    // Check if we're already loading data with same filters
    const currentFilter = { specialty: filters.specialty, showImported: filters.showImported };
    const isSameFilter = JSON.stringify(currentFilter) === JSON.stringify(lastFilterRef.current);

    // Skip if already loading data with same filters and we have data
    if (requestInProgressRef.current) {
      console.log('🔄 [useCollaborativeFlashcards] Request already in progress, skipping');
      return;
    }

    if (isSameFilter && flashcards.length > 0 && page === 1) {
      console.log('🔄 [useCollaborativeFlashcards] Same filter, using cached data');
      return;
    }

    try {
      lastFilterRef.current = currentFilter;
      requestInProgressRef.current = true;
      setIsLoading(true);

      // ✅ Otimizado: Buscar apenas dados dos flashcards sem joins
      let query = supabase
        .from('flashcards_cards')
        .select(`
          id,
          front,
          back,
          front_image,
          back_image,
          specialty_id,
          theme_id,
          focus_id,
          extrafocus_id,
          is_shared,
          current_state,
          created_at,
          import_count,
          likes,
          dislikes,
          liked_by,
          disliked_by
        `, { count: 'exact' })
        .eq('is_shared', true)
        .eq('current_state', 'available');

      console.log('🔍 [useCollaborativeFlashcards] Query com filtro is_shared=true');

      // Apply filters
      if (filters.specialty) {
        console.log(`🔍 [useCollaborativeFlashcards] Filtrando por especialidade: ${filters.specialty}`);
        query = query.eq('specialty_id', filters.specialty);
      }

      if (filters.theme) {
        console.log(`🔍 [useCollaborativeFlashcards] Filtrando por theme: ${filters.theme}`);
        query = query.eq('theme_id', filters.theme);
      }

      if (filters.focus) {
        console.log(`🔍 [useCollaborativeFlashcards] Filtrando por focus: ${filters.focus}`);
        query = query.eq('focus_id', filters.focus);
      }

      // Sort
      if (sortBy === 'recent') {
        query = query.order('created_at', { ascending: false });
      } else if (sortBy === 'oldest') {
        query = query.order('created_at', { ascending: true });
      } else if (sortBy === 'likes') {
        query = query.order('likes', { ascending: false });
      } else if (sortBy === 'imported') {
        query = query.order('import_count', { ascending: false });
      } else if (sortBy === 'dislikes_desc') {
        query = query.order('dislikes', { ascending: false });
      }

      // Pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      console.log('🔄 [useCollaborativeFlashcards] Executando query para cards compartilhados');
      const { data, error: fetchError, count } = await query;

      if (fetchError) {
        console.error(`❌ [useCollaborativeFlashcards] Error fetching cards:`, fetchError);
        throw fetchError;
      }

      if (count !== null) {
        setTotalCount(count);
        setTotalPages(Math.ceil(count / limit));
      }

      // Get current user ID to handle imported cards
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;
      console.log(`🔍 [useCollaborativeFlashcards] ID do usuário atual: ${userId || 'not logged in'}`);

      // Process the data
      if (data && data.length > 0) {
        console.log(`✅ [useCollaborativeFlashcards] Recebidos ${data.length} cards`);

        // Filter cards to ensure they have is_shared=true (just a double-check)
        const sharedCards = data.filter(card => card.is_shared === true);
        console.log(`✅ [useCollaborativeFlashcards] Cards que realmente têm is_shared=true: ${sharedCards.length}`);

        // For debugging purposes, log the first card's data
        if (sharedCards.length > 0) {
          console.log(`📊 [useCollaborativeFlashcards] Amostra do primeiro card:`, {
            id: sharedCards[0].id,
            is_shared: sharedCards[0].is_shared,
            specialty_id: sharedCards[0].specialty_id,
            theme_id: sharedCards[0].theme_id
          });
        }

        // Check which cards the user has already imported
        if (userId) {
          // Use the cached importedCardIds if available
          if (importedCardIds.length === 0) {
            const cardIds = sharedCards.map(card => card.id);

            if (cardIds.length > 0) {
              const { data: importedCards, error: importedError } = await supabase
                .from('flashcards_cards')
                .select('origin_id')
                .eq('user_id', userId)
                .in('origin_id', cardIds);

              if (importedError) {
                console.error(`❌ [useCollaborativeFlashcards] Error checking imported cards:`, importedError);
              } else if (importedCards && importedCards.length > 0) {
                console.log(`🔍 [useCollaborativeFlashcards] ${importedCards.length} cards já foram importados pelo usuário`);

                // Mark cards that user has already imported
                const importedIds = new Set(importedCards.map(card => card.origin_id));
                sharedCards.forEach(card => {
                  card.isImported = importedIds.has(card.id);
                });

                // Update the imported card IDs state
                setImportedCardIds(prev => [...new Set([...prev, ...Array.from(importedIds)])]);
              }
            }
          } else {
            console.log(`🔍 [useCollaborativeFlashcards] ${importedCardIds.length} cards já foram importados pelo usuário`);

            // Mark cards that user has already imported using cached importedCardIds
            sharedCards.forEach(card => {
              card.isImported = importedCardIds.includes(card.id);
            });
          }
        }

        // Filter out cards that the user has already imported if showImported is false
        let filteredData = sharedCards || [];
        if (!filters.showImported && userId) {
          const beforeCount = filteredData.length;
          filteredData = filteredData.filter(card => !card.isImported);
          console.log(`🔍 [useCollaborativeFlashcards] Cards após filtro de importados: ${beforeCount} -> ${filteredData.length}`);
        }

        // ✅ Resolver nomes da hierarquia usando dados estáticos
        const transformedCards = resolveHierarchyNames(filteredData);

        console.log(`✅ [useCollaborativeFlashcards] Retornando ${transformedCards.length} cards transformados`);

        if (page === 1) {
          setFlashcards(transformedCards);
        } else {
          setFlashcards(prevCards => {
            // Combine with existing cards, avoiding duplicates
            const existingIds = new Set(prevCards.map(c => c.id));
            const newCards = transformedCards.filter(c => !existingIds.has(c.id));
            const combined = [...prevCards, ...newCards];
            console.log(`📊 [useCollaborativeFlashcards] Dados recebidos: {count: ${count}, dataLength: ${data.length}}`);
            return combined;
          });
        }
      } else {

      }

      return data;
    } catch (err: any) {
      setError(err);
      toast.error('Erro ao carregar flashcards', {
        description: err.message
      });
      throw err;
    } finally {
      setIsLoading(false);
      // Reset request flag after a short delay to prevent immediate retriggering
      setTimeout(() => {
        requestInProgressRef.current = false;
      }, 300);
    }
  }, [importedCardIds]);

  /**
   * Function to like or dislike a flashcard
   *
   * @param cardId - ID of the card to like/dislike
   * @param action - Action to perform (like or dislike)
   */
  const likeDislikeCard = useCallback(async (cardId: string, action: 'like' | 'dislike') => {

    try {
      // First get the user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('❌ [likeDislikeCard] User not authenticated:', userError);
        toast.error("Você precisa estar logado para votar");
        return { error: 'User not authenticated' };
      }

      // Get the current card data
      const { data: card, error: cardError } = await supabase
        .from('flashcards_cards')
        .select('likes, dislikes, liked_by, disliked_by, origin_id')
        .eq('id', cardId)
        .single();

      if (cardError) {
        console.error('❌ [likeDislikeCard] Error fetching card:', cardError);
        return { error: cardError };
      }

      // Initialize arrays if they don't exist
      const likedBy = card.liked_by || [];
      const dislikedBy = card.disliked_by || [];

      // Check if user already liked or disliked
      const hasLiked = likedBy.includes(user.id);
      const hasDisliked = dislikedBy.includes(user.id);

      console.log(`🔍 [likeDislikeCard] Card ID: ${cardId}, User ID: ${user.id}`);
      console.log(`🔍 [likeDislikeCard] Current state: likes=${card.likes || 0}, dislikes=${card.dislikes || 0}, hasLiked=${hasLiked}, hasDisliked=${hasDisliked}`);

      // Handle like/dislike logic
      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = card.likes || 0;
      let newDislikes = card.dislikes || 0;

      // Remove existing votes first
      if (action === 'like') {
        if (hasLiked) {
          console.log(`🔍 [likeDislikeCard] User already liked this card, no changes needed`);
          return {
            success: true,
            likes: newLikes,
            dislikes: newDislikes,
            liked_by: newLikedBy,
            disliked_by: newDislikedBy
          };
        }

        // Remove from disliked if present
        if (hasDisliked) {
          newDislikedBy = newDislikedBy.filter(id => id !== user.id);
          newDislikes--;
        }

        // Add new like
        newLikedBy.push(user.id);
        newLikes++;
      } else if (action === 'dislike') {
        if (hasDisliked) {
          console.log(`🔍 [likeDislikeCard] User already disliked this card, no changes needed`);
          return {
            success: true,
            likes: newLikes,
            dislikes: newDislikes,
            liked_by: newLikedBy,
            disliked_by: newDislikedBy
          };
        }

        // Remove from liked if present
        if (hasLiked) {
          newLikedBy = newLikedBy.filter(id => id !== user.id);
          newLikes--;
        }

        // Add new dislike
        newDislikedBy.push(user.id);
        newDislikes++;
      }

      console.log(`🔄 [likeDislikeCard] New state: likes=${newLikes}, dislikes=${newDislikes}`);

      // Update the card in the database
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', cardId);

      if (updateError) {
        console.error('❌ [likeDislikeCard] Error updating card:', updateError);
        return { error: updateError };
      }

      // Update the local state of the card
      setFlashcards(cards =>
        cards.map(c => {
          if (c.id === cardId) {
            return {
              ...c,
              likes: newLikes,
              dislikes: newDislikes,
              liked_by: newLikedBy,
              disliked_by: newDislikedBy
            };
          }
          return c;
        })
      );

      console.log(`✅ [likeDislikeCard] Successfully ${action}d card ${cardId}`);

      // If this is referring to an original card, also update the original card
      if (card.origin_id) {
        await updateOriginalCard(card.origin_id, action, user.id);
      }

      return {
        success: true,
        likes: newLikes,
        dislikes: newDislikes,
        liked_by: newLikedBy,
        disliked_by: newDislikedBy
      };
    } catch (error: any) {
      console.error(`❌ [likeDislikeCard] Error ${action}ing card:`, error);
      return { error };
    }
  }, []);

  // Helper function to update original card
  const updateOriginalCard = async (originId: string, action: 'like' | 'dislike', userId: string) => {
    try {
      console.log(`🔄 [updateOriginalCard] Updating original card ${originId}`);

      // Get the original card data
      const { data: originalCard, error: fetchError } = await supabase
        .from('flashcards_cards')
        .select('liked_by, disliked_by, likes, dislikes')
        .eq('id', originId)
        .single();

      if (fetchError) {
        console.error('❌ [updateOriginalCard] Error fetching original card:', fetchError);
        return;
      }

      // Initialize arrays if they don't exist
      const likedBy = originalCard.liked_by || [];
      const dislikedBy = originalCard.disliked_by || [];

      // Check if user already liked or disliked
      const hasLiked = likedBy.includes(userId);
      const hasDisliked = dislikedBy.includes(userId);

      // Handle like/dislike logic
      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = originalCard.likes || 0;
      let newDislikes = originalCard.dislikes || 0;

      // Remove existing votes first
      if (hasLiked) {
        newLikedBy = newLikedBy.filter(id => id !== userId);
        newLikes--;
      }

      if (hasDisliked) {
        newDislikedBy = newDislikedBy.filter(id => id !== userId);
        newDislikes--;
      }

      // Add new vote based on action
      if (action === 'like') {
        newLikedBy.push(userId);
        newLikes++;
      } else if (action === 'dislike') {
        newDislikedBy.push(userId);
        newDislikes++;
      }

      // Update the original card in the database
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', originId);

      if (updateError) {
        console.error('❌ [updateOriginalCard] Error updating original card:', updateError);
      } else {
        console.log(`✅ [updateOriginalCard] Successfully updated original card ${originId}`);
      }

    } catch (error) {
      console.error('❌ [updateOriginalCard] Error:', error);
    }
  };

  return {
    flashcards,
    isLoading,
    error,
    totalCount,
    totalPages,
    importedCardIds,
    loadFlashcards,
    refreshImportedCards,
    refreshImportedCounts,
    likeDislikeCard
  };
};
