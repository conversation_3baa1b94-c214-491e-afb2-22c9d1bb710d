import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Sparkles,
  Calendar,
  Clock,
  CheckCircle,
  Flame,
  Star,
  Trophy
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { useTodayStats } from '@/hooks/useTodayStats';
import { useInsights } from '@/hooks/useInsights';
import { useStudySchedule } from '@/hooks/study-schedule/useStudySchedule';
import { extractAllScheduledFocusIds } from '@/utils/todayStudiesFilter';
import { useInsightStudy } from '@/hooks/useInsightStudy';
import InsightCard from '@/components/insights/InsightCard';
import { StudyOptionsDialog } from '@/components/study/StudyOptionsDialog';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const UnifiedBanner: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading: streakLoading
  } = useStreakSystem();

  const {
    questionsAnswered,
    timeStudied,
    isLoading: statsLoading
  } = useTodayStats();

  const { weeklySchedule } = useStudySchedule();

  // ✅ NOVO: Extrair focus_ids de hoje + próximas 2 semanas para excluir dos insights
  // TODO: Implementar busca das próximas semanas quando disponível
  const scheduledFocusIds = extractAllScheduledFocusIds(weeklySchedule, []);

  const {
    insights,
    isLoading: insightsLoading
  } = useInsights(scheduledFocusIds);

  // ✅ NOVO: Hook para gerenciar estudos de insights
  const {
    isDialogOpen,
    selectedInsight,
    maxQuestions,
    availableYears,
    availableInstitutions,
    openStudyDialog,
    closeStudyDialog,
    startStudy
  } = useInsightStudy();

  // Atualizar horário a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  // Nome do usuário
  const userName = user?.user_metadata?.name || 
    user?.user_metadata?.full_name || 
    user?.email?.split('@')[0] || 
    'Estudante';

  // Data formatada
  const formattedDate = currentTime.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long' 
  });

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-blue-50 via-white to-orange-50 rounded-xl p-4 border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center justify-between gap-6">
        {/* Left: Greeting & Info */}
        <div className="flex items-center gap-4">
          <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-2 rounded-full border-2 border-blue-300">
            <Sparkles className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-800">
              {getGreeting()}, {userName}! 👋
            </h3>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{formattedDate}</span>
            </div>
          </div>
        </div>

        {/* Center: Stats do Dia e Insights Unificados */}
        <div className="hidden lg:flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm px-3 py-3 flex-1 max-w-5xl">
          {/* Stats do Dia */}
          <div className="flex items-center gap-3 flex-shrink-0">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="text-center">
                <div className="text-sm font-bold text-green-700">
                  {statsLoading ? '...' : questionsAnswered}
                </div>
                <div className="text-xs text-green-600">questões hoje</div>
              </div>
            </div>
            <div className="w-px h-8 bg-gray-200"></div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <div className="text-center">
                <div className="text-sm font-bold text-blue-700">
                  {statsLoading ? '...' : `${timeStudied}min`}
                </div>
                <div className="text-xs text-blue-600">estudado hoje</div>
              </div>
            </div>
          </div>

          {/* Separador */}
          <div className="w-px h-10 bg-gray-300 flex-shrink-0"></div>

          {/* Insights Section */}
          <div className="flex-1 min-w-0">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">
              Insight Diário
            </div>
            <InsightCard
              insight={insights?.suggested_focus || null}
              isLoading={insightsLoading}
              compact={true}
              simplified={true}
              className="border-0 shadow-none bg-transparent p-0"
              onStudyClick={openStudyDialog}
            />
          </div>
        </div>

        {/* Right: Compact Streak */}
        <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-3 border border-orange-200">
          <div className="flex items-center gap-3 mb-2">
            <div className="bg-gradient-to-br from-orange-400 to-orange-600 p-1.5 rounded-full">
              <Flame className="h-4 w-4 text-white" />
            </div>
            <div className="flex items-center gap-2">
              <div>
                <span className="text-base font-bold text-orange-600">
                  {streakLoading ? '...' : currentStreak}
                </span>
                <span className="text-xs text-orange-500 font-medium ml-1">
                  {currentStreak === 1 ? 'dia' : 'dias'}
                </span>
              </div>
              {maxStreak > 0 && (
                <div className="flex items-center gap-1 bg-amber-50 px-1.5 py-0.5 rounded-full border border-amber-200">
                  <Trophy className="h-2.5 w-2.5 text-amber-600" />
                  <span className="text-xs font-bold text-amber-700">{maxStreak}</span>
                </div>
              )}
            </div>
          </div>

          {/* Mini Calendar */}
          <div className="flex items-center justify-between gap-1">
            {weekDays.map((day, index) => (
              <motion.div
                key={day.key}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.03 * index }}
                className="flex flex-col items-center"
              >
                <span className={`text-xs font-bold mb-0.5 ${
                  day.isCurrentDay 
                    ? 'text-blue-600' 
                    : day.isActive 
                      ? 'text-orange-500' 
                      : 'text-gray-500'
                }`}>
                  {day.shortName}
                </span>
                
                <div className={`
                  w-4 h-4 rounded-full flex items-center justify-center transition-all duration-300
                  ${day.isActive
                    ? 'bg-orange-500 text-white'
                    : day.isCurrentDay
                      ? 'bg-blue-200 text-blue-600'
                      : 'bg-gray-200 text-gray-400'
                  }
                `}>
                  {day.isActive ? (
                    <CheckCircle className="h-2 w-2" />
                  ) : day.isCurrentDay ? (
                    <Star className="h-1.5 w-1.5" />
                  ) : null}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* ✅ NOVO: Versão Mobile - Stats e Insights Compactos */}
      <div className="lg:hidden mt-4 space-y-3">
        {/* Stats do Dia Mobile */}
        <div className="flex items-center justify-center gap-4 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm px-4 py-3">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div className="text-center">
              <div className="text-sm font-bold text-green-700">
                {statsLoading ? '...' : questionsAnswered}
              </div>
              <div className="text-xs text-green-600">questões hoje</div>
            </div>
          </div>
          <div className="w-px h-8 bg-gray-200"></div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-600" />
            <div className="text-center">
              <div className="text-sm font-bold text-blue-700">
                {statsLoading ? '...' : `${timeStudied}min`}
              </div>
              <div className="text-xs text-blue-600">estudado hoje</div>
            </div>
          </div>
        </div>

        {/* Insights Mobile */}
        {(insights?.suggested_focus || insightsLoading) && (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm px-4 py-3">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2 text-center">
              Insight Inteligente Diário
            </div>
            <InsightCard
              insight={insights?.suggested_focus || null}
              isLoading={insightsLoading}
              simplified={true}
              className="border-0 shadow-none bg-transparent p-0"
              onStudyClick={openStudyDialog}
            />
          </div>
        )}
      </div>

      {/* ✅ NOVO: Dialog de Estudos do Insight */}
      <StudyOptionsDialog
        open={isDialogOpen}
        onOpenChange={closeStudyDialog}
        maxQuestions={maxQuestions}
        minQuestions={1}
        availableYears={availableYears}
        availableInstitutions={availableInstitutions}
        onStartStudy={startStudy}
        totalTopics={1}
        specialtyId={undefined}
        themeId={undefined}
        focusId={selectedInsight?.focus_id}
      />
    </motion.div>
  );
};

export default UnifiedBanner;
