import { useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook para agrupar requisições similares e executá-las em lote
 * Reduz significativamente o número de requests ao banco
 */

interface BatchRequest {
  id: string;
  resolve: (data: any) => void;
  reject: (error: any) => void;
  params: any;
}

class RequestBatcher {
  private batches: Map<string, BatchRequest[]> = new Map();
  private timeouts: Map<string, NodeJS.Timeout> = new Map();
  private readonly batchDelay = 50; // 50ms para agrupar requests

  /**
   * Adiciona uma requisição ao lote
   */
  addToBatch<T>(
    batchKey: string,
    requestId: string,
    params: any,
    executor: (allParams: any[]) => Promise<T[]>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      // Adicionar à lista de requests do lote
      if (!this.batches.has(batchKey)) {
        this.batches.set(batchKey, []);
      }

      const batch = this.batches.get(batchKey)!;
      batch.push({ id: requestId, resolve, reject, params });

      // Cancelar timeout anterior se existir
      const existingTimeout = this.timeouts.get(batchKey);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Configurar novo timeout para executar o lote
      const timeout = setTimeout(async () => {
        const currentBatch = this.batches.get(batchKey) || [];
        this.batches.delete(batchKey);
        this.timeouts.delete(batchKey);

        if (currentBatch.length === 0) return;

        try {
          // Executar todas as requisições do lote
          const allParams = currentBatch.map(req => req.params);
          const results = await executor(allParams);

          // Resolver cada promise com seu resultado correspondente
          currentBatch.forEach((req, index) => {
            req.resolve(results[index]);
          });
        } catch (error) {
          console.error(`Erro no lote ${batchKey}:`, error);

          // Rejeitar todas as promises do lote
          currentBatch.forEach(req => {
            req.reject(error);
          });
        }
      }, this.batchDelay);

      this.timeouts.set(batchKey, timeout);
    });
  }

  /**
   * Limpa todos os lotes pendentes
   */
  clear() {
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();

    this.batches.forEach(batch => {
      batch.forEach(req => {
        req.reject(new Error('Batch cleared'));
      });
    });
    this.batches.clear();
  }
}

export const useBatchRequests = () => {
  const batcherRef = useRef<RequestBatcher>(new RequestBatcher());

  /**
   * Buscar questões em lote por IDs
   */
  const batchGetQuestions = useCallback(async (questionIds: string[]) => {
    const batchKey = 'questions-by-ids';
    const requestId = questionIds.join(',');

    return batcherRef.current.addToBatch(
      batchKey,
      requestId,
      questionIds,
      async (allQuestionIdArrays: string[][]) => {
        // Combinar todos os IDs únicos
        const allIds = [...new Set(allQuestionIdArrays.flat())];

        // Buscar apenas dados das questões sem joins para evitar múltiplas requisições
        const { data, error } = await supabase
          .from('questions')
          .select('*')
          .in('id', allIds);

        if (error) throw error;

        // Retornar resultados na ordem correta para cada request original
        return allQuestionIdArrays.map(originalIds =>
          originalIds.map(id => data?.find(q => q.id === id)).filter(Boolean)
        );
      }
    );
  }, []);

  /**
   * Buscar flashcards em lote por filtros
   */
  const batchGetFlashcards = useCallback(async (filters: any) => {
    const batchKey = 'flashcards-by-filters';
    const requestId = JSON.stringify(filters);

    return batcherRef.current.addToBatch(
      batchKey,
      requestId,
      filters,
      async (allFilters: any[]) => {
        // ✅ Otimizado: Query sem joins para melhor performance
        const { data, error } = await supabase
          .from('flashcards_cards')
          .select('*')
          .eq('current_state', 'available')
          .order('created_at', { ascending: false })
          .limit(1000);

        if (error) throw error;

        // Filtrar resultados para cada request
        return allFilters.map(filter => {
          return data?.filter(card => {
            if (filter.specialty_id && card.specialty_id !== filter.specialty_id) return false;
            if (filter.theme_id && card.theme_id !== filter.theme_id) return false;
            if (filter.focus_id && card.focus_id !== filter.focus_id) return false;
            return true;
          }) || [];
        });
      }
    );
  }, []);

  /**
   * Buscar estatísticas de usuário em lote
   */
  const batchGetUserStats = useCallback(async (userId: string, dateRange: { start: Date; end: Date }) => {
    const batchKey = 'user-stats';
    const requestId = `${userId}-${dateRange.start.getTime()}-${dateRange.end.getTime()}`;

    return batcherRef.current.addToBatch(
      batchKey,
      requestId,
      { userId, dateRange },
      async (allParams: Array<{ userId: string; dateRange: { start: Date; end: Date } }>) => {
        // Buscar estatísticas para todos os usuários e períodos
        const userIds = [...new Set(allParams.map(p => p.userId))];
        const earliestDate = new Date(Math.min(...allParams.map(p => p.dateRange.start.getTime())));
        const latestDate = new Date(Math.max(...allParams.map(p => p.dateRange.end.getTime())));

        const { data, error } = await supabase
          .from('user_answers')
          .select('user_id, is_correct, created_at, question_id')
          .in('user_id', userIds)
          .gte('created_at', earliestDate.toISOString())
          .lte('created_at', latestDate.toISOString());

        if (error) throw error;

        // Processar resultados para cada request
        return allParams.map(({ userId, dateRange }) => {
          const userAnswers = data?.filter(answer =>
            answer.user_id === userId &&
            new Date(answer.created_at) >= dateRange.start &&
            new Date(answer.created_at) <= dateRange.end
          ) || [];

          return {
            totalAnswers: userAnswers.length,
            correctAnswers: userAnswers.filter(a => a.is_correct).length,
            incorrectAnswers: userAnswers.filter(a => !a.is_correct).length,
            accuracy: userAnswers.length > 0 ?
              (userAnswers.filter(a => a.is_correct).length / userAnswers.length) * 100 : 0
          };
        });
      }
    );
  }, []);

  /**
   * Buscar contagens de questões em lote
   */
  const batchGetQuestionCounts = useCallback(async (filters: any) => {
    const batchKey = 'question-counts';
    const requestId = JSON.stringify(filters);

    return batcherRef.current.addToBatch(
      batchKey,
      requestId,
      filters,
      async (allFilters: any[]) => {
        // Usar RPC otimizada para contagens em lote
        const results = await Promise.all(
          allFilters.map(async (filter) => {
            const { data, error } = await supabase.rpc('get_question_count', {
              specialty_ids: filter.specialties || [],
              theme_ids: filter.themes || [],
              focus_ids: filter.focuses || [],
              location_ids: filter.locations || [],
              years: filter.years || [],
              question_types: filter.question_types || [],
              domain_filter: filter.domain
            });

            if (error) throw error;
            return data || 0;
          })
        );

        return results;
      }
    );
  }, []);

  // Cleanup ao desmontar
  const cleanup = useCallback(() => {
    batcherRef.current.clear();
  }, []);

  return {
    batchGetQuestions,
    batchGetFlashcards,
    batchGetUserStats,
    batchGetQuestionCounts,
    cleanup,
  };
};
