#!/usr/bin/env python3
import PyPDF2
import sys
import os
import re

def extract_prescriptions(file_path):
    prescriptions = []
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)

            for page_num in range(len(pdf_reader.pages)):
                try:
                    text = pdf_reader.pages[page_num].extract_text()
                    if text.strip():
                        lines = text.split('\n')

                        # Look for prescription patterns
                        for i, line in enumerate(lines):
                            line = line.strip()

                            # Pattern 1: Lines that are prescription titles (usually followed by "Rx" and medications)
                            if line and not line.startswith('-') and not line.startswith('•'):
                                # Check next few lines for prescription indicators
                                next_content = []
                                for j in range(i+1, min(i+15, len(lines))):
                                    next_content.append(lines[j].strip())

                                next_text = ' '.join(next_content)

                                # If we find prescription patterns in the following lines
                                if ('Rx' in next_text and 'USO ORAL' in next_text) or \
                                   ('TOMAR' in next_text and ('MG' in next_text or 'ML' in next_text)):

                                    # Clean the title
                                    clean_title = re.sub(r'^[#\-\d\.\s]*', '', line).strip()
                                    clean_title = re.sub(r'###.*$', '', clean_title).strip()

                                    if (clean_title and
                                        len(clean_title) > 5 and
                                        len(clean_title) < 100 and
                                        not any(word in clean_title.lower() for word in ['rx', 'tomar', 'comprimido', 'caixa', 'frasco']) and
                                        clean_title not in [p[0] for p in prescriptions]):

                                        prescriptions.append((clean_title, page_num + 1))

                except Exception as e:
                    print(f"Error reading page {page_num + 1}: {e}")

    except Exception as e:
        print(f"Error reading PDF: {e}")

    return prescriptions

def read_pdf(file_path):
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)

            print(f"PDF Info:")
            print(f"Number of pages: {len(pdf_reader.pages)}")
            print(f"Title: {pdf_reader.metadata.title if pdf_reader.metadata and pdf_reader.metadata.title else 'N/A'}")
            print(f"Author: {pdf_reader.metadata.author if pdf_reader.metadata and pdf_reader.metadata.author else 'N/A'}")
            print(f"Subject: {pdf_reader.metadata.subject if pdf_reader.metadata and pdf_reader.metadata.subject else 'N/A'}")
            print("=" * 80)

            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    text = page.extract_text()
                    if text.strip():
                        print(f"\n--- PAGE {page_num} ---")
                        print(text)
                        full_text += f"\n--- PAGE {page_num} ---\n{text}\n"
                except Exception as e:
                    print(f"Error reading page {page_num}: {e}")

            return full_text

    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

if __name__ == "__main__":
    pdf_path = "TIGERS BOOK OFICIAL.pdf"
    if os.path.exists(pdf_path):
        prescriptions = extract_prescriptions(pdf_path)
        print(f"\nFound {len(prescriptions)} prescriptions:")
        for title, page in prescriptions:
            print(f"- [ ] {title} – TIGERS BOOK OFICIAL.pdf - Página {page}")
    else:
        print(f"File not found: {pdf_path}")
