import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, Sparkles, AlertTriangle, CheckCircle, XCircle, Edit3, Merge, SkipForward } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Category {
  id: string;
  name: string;
}

interface FocusWithCount {
  id: string;
  name: string;
  question_count: number;
  created_at: string;
}

interface OptimizationSuggestion {
  type: 'duplicate' | 'rename' | 'no_change';
  currentFocus: {
    id: string;
    name: string;
    questionCount: number;
  };
  suggestion: string;
  reasoning: string;
  confidence: number;
  duplicateOf?: {
    id: string;
    name: string;
    questionCount: number;
  };
  suggestedName?: string;
}

interface ProcessedFocus {
  focus: FocusWithCount;
  suggestion: OptimizationSuggestion;
  status: 'pending' | 'approved' | 'applied' | 'skipped';
  editedName?: string; // Nome editado pelo usuário para renomeação
  selectedDirection?: 'to' | 'from'; // Para duplicatas
}

// Componente para ações em cada foco
const FocusActionCard = ({
  processedFocus,
  allFocuses,
  onApprove,
  onSkip,
  getSuggestionIcon,
  getSuggestionColor
}: {
  processedFocus: ProcessedFocus;
  allFocuses: FocusWithCount[];
  onApprove: (focusId: string, editedName?: string, direction?: 'to' | 'from') => void;
  onSkip: (focusId: string) => void;
  getSuggestionIcon: (type: string) => React.ReactNode;
  getSuggestionColor: (type: string) => string;
}) => {
  const [editedName, setEditedName] = useState(processedFocus.suggestion.suggestedName || processedFocus.focus.name);

  if (processedFocus.status === 'applied') {
    return (
      <div className="p-4 border border-green-500 bg-green-50 rounded-lg">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <span className="font-medium text-green-800">
            {processedFocus.focus.name} - ✅ Aplicado
          </span>
        </div>
      </div>
    );
  }

  if (processedFocus.status === 'skipped') {
    return (
      <div className="p-4 border border-gray-500 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <SkipForward className="h-5 w-5 text-gray-600" />
          <span className="font-medium text-gray-800">
            {processedFocus.focus.name} - ⏭️ Pulado
          </span>
        </div>
      </div>
    );
  }

  if (processedFocus.status === 'approved') {
    return (
      <div className="p-4 border border-blue-500 bg-blue-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              {processedFocus.focus.name} - 👍 Aprovado
            </span>
            {processedFocus.suggestion.type === 'duplicate' && processedFocus.selectedDirection && (
              <Badge variant="outline" className="text-xs">
                {processedFocus.selectedDirection === 'to' ? 'Mover PARA' : 'Trazer DE'} duplicata
              </Badge>
            )}
            {processedFocus.suggestion.type === 'rename' && processedFocus.editedName && (
              <Badge variant="outline" className="text-xs">
                Renomear para: {processedFocus.editedName}
              </Badge>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Voltar para pending
              onApprove(processedFocus.focus.id); // Isso vai resetar para pending
            }}
          >
            Cancelar
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 border rounded-lg ${getSuggestionColor(processedFocus.suggestion.type)}`}>
      <div className="flex items-start gap-3">
        {getSuggestionIcon(processedFocus.suggestion.type)}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium">{processedFocus.focus.name}</span>
            <span className="text-sm text-muted-foreground">
              ({processedFocus.focus.question_count} questões)
            </span>
            <Badge variant="outline" className="text-xs">
              {processedFocus.suggestion.confidence}% confiança
            </Badge>
          </div>

          <div className="text-sm mb-2">
            <strong>Sugestão:</strong> {processedFocus.suggestion.suggestion}
          </div>
          <div className="text-xs text-muted-foreground mb-4">
            <strong>Motivo:</strong> {processedFocus.suggestion.reasoning}
          </div>

          {processedFocus.suggestion.type === 'duplicate' && processedFocus.suggestion.duplicateOf && (
            <div className="space-y-3">
              <div className="p-3 bg-yellow-100 border border-yellow-300 rounded">
                <p className="text-sm mb-2">
                  <strong>Duplicata encontrada:</strong> {processedFocus.suggestion.duplicateOf.name}
                  <span className="text-muted-foreground ml-1">
                    ({processedFocus.suggestion.duplicateOf.questionCount} questões)
                  </span>
                </p>
                <p className="text-xs text-muted-foreground">
                  Escolha a direção do movimento das questões:
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                <Button
                  onClick={() => onApprove(processedFocus.focus.id, undefined, 'to')}
                  variant="destructive"
                  size="sm"
                >
                  <Merge className="h-4 w-4 mr-2" />
                  Aprovar: Mover PARA
                </Button>
                <Button
                  onClick={() => onApprove(processedFocus.focus.id, undefined, 'from')}
                  variant="outline"
                  size="sm"
                >
                  <Merge className="h-4 w-4 mr-2" />
                  Aprovar: Trazer DE
                </Button>
                <Button
                  onClick={() => onSkip(processedFocus.focus.id)}
                  variant="outline"
                  size="sm"
                >
                  <SkipForward className="h-4 w-4 mr-2" />
                  Pular
                </Button>
              </div>
            </div>
          )}

          {processedFocus.suggestion.type === 'rename' && (
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium mb-2 block">Novo nome (editável):</label>
                <Input
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  placeholder="Digite o novo nome"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => onApprove(processedFocus.focus.id, editedName)}
                  disabled={!editedName.trim() || editedName === processedFocus.focus.name}
                  className="flex-1"
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  Aprovar Renomeação
                </Button>
                <Button
                  variant="outline"
                  onClick={() => onSkip(processedFocus.focus.id)}
                >
                  <SkipForward className="h-4 w-4 mr-2" />
                  Pular
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const FocusOptimization = () => {
  const { toast: toastHook } = useToast();

  // States
  const [specialties, setSpecialties] = useState<Category[]>([]);
  const [themes, setThemes] = useState<Category[]>([]);
  const [focuses, setFocuses] = useState<FocusWithCount[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('');
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Processing states
  const [processedFocuses, setProcessedFocuses] = useState<ProcessedFocus[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [processedFocusIds, setProcessedFocusIds] = useState<Set<string>>(new Set());

  // Load specialties on mount
  useEffect(() => {
    loadSpecialties();
  }, []);

  // Load themes when specialty is selected
  useEffect(() => {
    if (selectedSpecialty) {
      loadThemes();
    }
  }, [selectedSpecialty]);

  // Load focuses when theme is selected
  useEffect(() => {
    if (selectedSpecialty && selectedTheme) {
      loadFocuses();
    }
  }, [selectedSpecialty, selectedTheme]);

  const loadSpecialties = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'specialty')
        .order('name');

      if (error) throw error;
      setSpecialties(data || []);
    } catch (error) {
      console.error('Error loading specialties:', error);
      toast.error('Erro ao carregar especialidades');
    }
  };

  const loadThemes = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name')
        .eq('type', 'theme')
        .eq('parent_id', selectedSpecialty)
        .order('name');

      if (error) throw error;
      setThemes(data || []);
      setSelectedTheme(''); // Reset theme selection
      setFocuses([]); // Reset focuses
      setOptimizationResult(null); // Reset results
    } catch (error) {
      console.error('Error loading themes:', error);
      toast.error('Erro ao carregar temas');
    }
  };

  const loadFocuses = async () => {
    setLoading(true);
    try {
      const { data: focusData, error } = await supabase.rpc('get_categories_with_question_count', {
        p_type: 'focus'
      });

      if (error) throw error;

      // Filter focuses by theme and exclude already processed
      const filteredFocuses = (focusData || []).filter((focus: any) =>
        focus.parent_id === selectedTheme && !processedFocusIds.has(focus.id)
      );

      setFocuses(filteredFocuses);
      setProcessedFocuses([]); // Reset processed focuses when focuses change
    } catch (error) {
      toast.error('Erro ao carregar focos');
    } finally {
      setLoading(false);
    }
  };

  const analyzeAllFocuses = async () => {
    if (focuses.length === 0) {
      toast.error('Nenhum foco encontrado para análise');
      return;
    }

    setIsAnalyzing(true);
    setProcessedFocuses([]);


    toast.success(`Iniciando análise de ${focuses.length} focos...`);

    try {
      const { data, error } = await supabase.functions.invoke('focus-optimizer', {
        body: {
          specialty: specialties.find(s => s.id === selectedSpecialty)?.name,
          theme: themes.find(t => t.id === selectedTheme)?.name,
          allFocuses: focuses.map(f => ({
            id: f.id,
            name: f.name,
            questionCount: f.question_count
          }))
        }
      });

      if (error) throw error;

      console.log(`✅ Análise concluída:`, data);

      // Processar resultados e criar lista de focos processados
      const processedResults: ProcessedFocus[] = data.suggestions
        .filter((suggestion: OptimizationSuggestion) =>
          suggestion.type === 'duplicate' || suggestion.type === 'rename'
        )
        .map((suggestion: OptimizationSuggestion) => {
          const focus = focuses.find(f => f.id === suggestion.currentFocus.id);
          return {
            focus: focus!,
            suggestion,
            status: 'pending' as const
          };
        });

      setProcessedFocuses(processedResults);

      const duplicates = processedResults.filter(p => p.suggestion.type === 'duplicate').length;
      const renames = processedResults.filter(p => p.suggestion.type === 'rename').length;

      toast.success(`Análise concluída! ${duplicates} duplicatas e ${renames} renomeações encontradas.`);

    } catch (error) {
      console.error('❌ Erro na análise:', error);
      toast.error('Erro ao analisar focos');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const approveSuggestion = (focusId: string, editedName?: string, direction?: 'to' | 'from') => {
    setProcessedFocuses(prev => prev.map(p => {
      if (p.focus.id === focusId) {
        // Se já está aprovado, voltar para pending
        if (p.status === 'approved') {
          return { ...p, status: 'pending' };
        }
        // Se está pending, aprovar
        return {
          ...p,
          status: 'approved',
          editedName: editedName || p.editedName,
          selectedDirection: direction || p.selectedDirection
        };
      }
      return p;
    }));

    const focus = focuses.find(f => f.id === focusId);
    const currentStatus = processedFocuses.find(p => p.focus.id === focusId)?.status;

    if (currentStatus === 'approved') {
      toast.info(`Aprovação cancelada: ${focus?.name}`);
    } else {
      toast.success(`Sugestão aprovada: ${focus?.name}`);
    }
  };

  const unapproveAll = () => {
    setProcessedFocuses(prev => prev.map(p => ({ ...p, status: 'pending' })));
    toast.info('Todas as aprovações foram removidas');
  };

  const applyAllApproved = async () => {
    const approvedItems = processedFocuses.filter(p => p.status === 'approved');

    if (approvedItems.length === 0) {
      toast.error('Nenhuma sugestão aprovada para aplicar');
      return;
    }

    console.log(`🚀 Aplicando ${approvedItems.length} sugestões aprovadas...`);

    for (const item of approvedItems) {
      try {
        if (item.suggestion.type === 'duplicate' && item.selectedDirection) {
          if (item.selectedDirection === 'to') {
            await moveQuestions(item.focus.id, item.suggestion.duplicateOf!.id, 'to', false);
          } else {
            await moveQuestions(item.suggestion.duplicateOf!.id, item.focus.id, 'from', false);
          }
        } else if (item.suggestion.type === 'rename' && item.editedName) {
          await renameFocus(item.focus.id, item.editedName, false);
        }

        // Marcar como aplicado
        setProcessedFocuses(prev => prev.map(p =>
          p.focus.id === item.focus.id ? { ...p, status: 'applied' } : p
        ));

      } catch (error) {
        console.error(`❌ Erro ao aplicar sugestão para ${item.focus.name}:`, error);
        toast.error(`Erro ao aplicar: ${item.focus.name}`);
      }
    }

    toast.success(`${approvedItems.length} sugestões aplicadas com sucesso!`);
    console.log(`✅ Todas as ${approvedItems.length} sugestões foram aplicadas`);
  };

  const moveQuestions = async (fromFocusId: string, toFocusId: string, direction: 'to' | 'from', shouldReload = true) => {
    try {
      const sourceFocus = focuses.find(f => f.id === fromFocusId);
      const targetFocus = focuses.find(f => f.id === toFocusId);

      if (!sourceFocus || !targetFocus) {
        throw new Error('Foco não encontrado');
      }

      console.log(`📋 Movendo questões ${direction === 'to' ? 'PARA' : 'DE'}: ${sourceFocus.name} → ${targetFocus.name}`);
      console.log(`📊 Questões a serem movidas: ${sourceFocus.question_count}`);

      // Mover questões
      const { error: updateError } = await supabase
        .from('questions')
        .update({ focus_id: toFocusId })
        .eq('focus_id', fromFocusId);

      if (updateError) throw updateError;

      console.log(`✅ Questões movidas com sucesso!`);
      console.log(`📊 Foco origem (${sourceFocus.name}) agora tem 0 questões`);
      console.log(`📊 Foco destino (${targetFocus.name}) agora tem ${sourceFocus.question_count + targetFocus.question_count} questões`);

      // Marcar como processado
      setProcessedFocusIds(prev => new Set([...prev, fromFocusId]));

      // Atualizar status apenas se não for parte de um lote
      if (shouldReload) {
        setProcessedFocuses(prev => prev.map(p =>
          p.focus.id === fromFocusId ? { ...p, status: 'applied' } : p
        ));
        toast.success(`Questões movidas: ${sourceFocus.name} → ${targetFocus.name} (${sourceFocus.question_count} questões)`);

        // Recarregar focos para atualizar contadores apenas se solicitado
        loadFocuses();
      }

    } catch (error) {
      console.error('❌ Erro ao mover questões:', error);
      toast.error('Erro ao mover questões');
    }
  };

  const renameFocus = async (focusId: string, newName: string, shouldReload = true) => {
    try {
      const focus = focuses.find(f => f.id === focusId);
      if (!focus) {
        throw new Error('Foco não encontrado');
      }

      console.log(`✏️ Renomeando foco: "${focus.name}" → "${newName}"`);

      const { error } = await supabase
        .from('study_categories')
        .update({ name: newName })
        .eq('id', focusId);

      if (error) throw error;

      console.log(`✅ Foco renomeado com sucesso!`);

      // Marcar como processado
      setProcessedFocusIds(prev => new Set([...prev, focusId]));

      // Atualizar status apenas se não for parte de um lote
      if (shouldReload) {
        setProcessedFocuses(prev => prev.map(p =>
          p.focus.id === focusId ? { ...p, status: 'applied' } : p
        ));
        toast.success(`Foco renomeado: "${focus.name}" → "${newName}"`);

        // Recarregar focos para atualizar nomes apenas se solicitado
        loadFocuses();
      }

    } catch (error) {
      console.error('❌ Erro ao renomear foco:', error);
      toast.error('Erro ao renomear foco');
    }
  };

  const skipFocus = (focusId: string) => {
    // Marcar como processado (pulado)
    setProcessedFocusIds(prev => new Set([...prev, focusId]));

    // Atualizar status
    setProcessedFocuses(prev => prev.map(p =>
      p.focus.id === focusId ? { ...p, status: 'skipped' } : p
    ));

    const focus = focuses.find(f => f.id === focusId);
    toast.info(`Foco pulado: ${focus?.name}`);
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'duplicate':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'rename':
        return <Sparkles className="h-4 w-4 text-blue-500" />;
      case 'no_change':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  };

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'duplicate':
        return 'border-red-200 bg-red-50';
      case 'rename':
        return 'border-blue-200 bg-blue-50';
      case 'no_change':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Otimização de Focos</h1>
          <p className="text-muted-foreground">
            Analise e otimize focos para eliminar duplicatas e melhorar a organização
          </p>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Seleção de Categoria</CardTitle>
            <CardDescription>
              Escolha a especialidade e tema para analisar os focos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Especialidade</label>
                <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma especialidade" />
                  </SelectTrigger>
                  <SelectContent>
                    {specialties.map((specialty) => (
                      <SelectItem key={specialty.id} value={specialty.id}>
                        {specialty.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Tema</label>
                <Select 
                  value={selectedTheme} 
                  onValueChange={setSelectedTheme}
                  disabled={!selectedSpecialty}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um tema" />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map((theme) => (
                      <SelectItem key={theme.id} value={theme.id}>
                        {theme.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {selectedSpecialty && selectedTheme && (
              <div className="pt-4 border-t space-y-4">
                <div className="flex items-center justify-between">
                  <Button
                    onClick={analyzeAllFocuses}
                    disabled={isAnalyzing || loading || focuses.length === 0}
                    className="w-full md:w-auto"
                  >
                    {isAnalyzing ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Analisando...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Analisar Todos os Focos ({focuses.length} focos)
                      </>
                    )}
                  </Button>
                </div>

                {processedFocusIds.size > 0 && (
                  <div className="text-sm text-muted-foreground">
                    📊 Focos já processados: {processedFocusIds.size}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Focos Carregados */}
        {focuses.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Focos Encontrados</CardTitle>
              <CardDescription>
                {focuses.length} focos encontrados no tema selecionado
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {focuses.map((focus) => (
                  <div key={focus.id} className="p-2 border rounded text-sm">
                    <div className="font-medium truncate" title={focus.name}>
                      {focus.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {focus.question_count} questões
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Resultados da Análise */}
        {processedFocuses.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Resultados da Análise</CardTitle>
              <CardDescription>
                Focos que precisam de atenção - {processedFocuses.filter(p => p.status === 'applied').length} de {processedFocuses.length} processados
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Resumo */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                <div className="text-center p-3 border rounded">
                  <div className="text-2xl font-bold">{processedFocuses.length}</div>
                  <div className="text-xs text-muted-foreground">Total</div>
                </div>
                <div className="text-center p-3 border rounded">
                  <div className="text-2xl font-bold text-red-600">
                    {processedFocuses.filter(p => p.suggestion.type === 'duplicate').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Duplicatas</div>
                </div>
                <div className="text-center p-3 border rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {processedFocuses.filter(p => p.suggestion.type === 'rename').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Renomes</div>
                </div>
                <div className="text-center p-3 border rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {processedFocuses.filter(p => p.status === 'approved').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Aprovados</div>
                </div>
                <div className="text-center p-3 border rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {processedFocuses.filter(p => p.status === 'applied').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Aplicados</div>
                </div>
              </div>

              {/* Controles de Lote */}
              <div className="flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg border">
                <Button
                  onClick={applyAllApproved}
                  disabled={processedFocuses.filter(p => p.status === 'approved').length === 0}
                  className="flex-1 md:flex-none"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Aplicar Aprovados ({processedFocuses.filter(p => p.status === 'approved').length})
                </Button>

                <Button
                  onClick={unapproveAll}
                  variant="outline"
                  disabled={processedFocuses.filter(p => p.status === 'approved').length === 0}
                  className="flex-1 md:flex-none"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Cancelar Todas Aprovações
                </Button>

                <div className="text-sm text-muted-foreground flex items-center">
                  💡 Aprove as sugestões individualmente e depois clique "Aplicar Aprovados"
                </div>
              </div>

              {/* Lista de Focos */}
              <div className="space-y-4">
                {processedFocuses.map((processedFocus) => (
                  <FocusActionCard
                    key={processedFocus.focus.id}
                    processedFocus={processedFocus}
                    allFocuses={focuses}
                    onApprove={approveSuggestion}
                    onSkip={skipFocus}
                    getSuggestionIcon={getSuggestionIcon}
                    getSuggestionColor={getSuggestionColor}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Estado vazio */}
        {!loading && selectedSpecialty && selectedTheme && focuses.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-8 text-center space-y-3">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <p className="text-lg font-medium">Nenhum foco encontrado</p>
                <p className="text-muted-foreground">
                  Não foram encontrados focos para esta especialidade/tema.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default FocusOptimization;
