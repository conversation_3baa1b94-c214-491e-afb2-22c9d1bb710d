
// Adicione/atualize o hook useQuestionMetadata para utilizar o isReady do useDomain
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { QuestionMetadata } from '@/types/question';
import { useDomain } from '@/hooks/useDomain';

export const useQuestionMetadata = () => {
  const { domain, isReady } = useDomain();

  return useQuery({
    queryKey: ['question-metadata', domain],
    queryFn: async () => {
      // Skip fetching if domain isn't ready
      if (!isReady || !domain) {
        return {
          specialties: [],
          themes: [],
          focuses: [],
          locations: [],
          years: []
        };
      }



      try {
        // Usar RPC original mas otimizar os dados retornados
        const { data, error } = await supabase.rpc('get_questions_metadata', {
          domain_filter: domain
        });

        if (error) {
          throw error;
        }

        if (!data) {
          return {
            specialties: [],
            themes: [],
            focuses: [],
            locations: [],
            years: []
          };
        }

        // OTIMIZAÇÃO: Manter todos os dados mas otimizar estrutura
        const optimizedData = {
          specialties: data.specialties || [],
          themes: (data.themes || []).map(theme => ({
            id: theme.id,
            name: theme.name,
            count: theme.count || 0,
            parent_id: theme.parent_id,
            specialty_id: theme.specialty_id
          })),
          // Manter TODOS os focos (usuário precisa ver todos)
          // Mas otimizar estrutura removendo campos desnecessários
          focuses: (data.focuses || []).map(focus => ({
            id: focus.id,
            name: focus.name,
            count: focus.count || 0,
            theme_id: focus.theme_id,
            parent_id: focus.parent_id
          })),
          // Manter TODOS os locais (usuário precisa ver todos)
          // Mas otimizar estrutura
          locations: (data.locations || []).map(location => ({
            id: location.id,
            name: location.name,
            count: location.count || 0
            // Remover outros campos se existirem
          })),
          years: data.years || []
        };

        return optimizedData as QuestionMetadata;
      } catch (error) {
        throw error;
      }
    },
    enabled: isReady && !!domain,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours - metadados mudam raramente
    cacheTime: 7 * 24 * 60 * 60 * 1000, // 7 days cache time - manter por muito tempo
    refetchOnWindowFocus: false, // Não refetch ao focar na janela
    refetchOnMount: false, // Não refetch ao montar se já tem dados em cache
    refetchOnReconnect: false, // Não refetch ao reconectar
    refetchInterval: false, // Não refetch automaticamente
    refetchIntervalInBackground: false, // Não refetch em background
    retry: 2, // 2 tentativas em caso de erro
    retryOnMount: false, // Não retry ao montar
    // Compressão adicional
    select: (data) => {
      // Garantir que dados estão no formato mais compacto possível
      return data;
    }
  });
};
