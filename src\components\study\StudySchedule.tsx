
import { motion } from "framer-motion";
import { Calendar } from "lucide-react";
import { Card } from "@/components/ui/card";
import type { DaySchedule } from "@/types/study-schedule";
import { WeekCard } from "./schedule/WeekCard";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { sumDurations, parseDurationToMinutes } from "@/utils/formatTime";
import { getCurrentBrazilDate, isDateInRange, logCurrentBrazilTime } from "@/utils/dateUtils";

interface StudyScheduleProps {
  weeklyPlan: DaySchedule[];
  onAddTopic?: (dayOfWeek: string, weekNumber: number, scheduleId: string, source: 'platform' | 'manual') => void;
  onDeleteWeek?: (weekNumber: number) => void;
  onDeleteAllWeeks?: () => void;
  onDeleteTopic?: (topicId: string) => void;
}

export const StudySchedule = ({
  weeklyPlan,
  onAddTopic,
  onDeleteWeek,
  onDeleteAllWeeks,
  onDeleteTopic
}: StudyScheduleProps) => {
  // Group days by week with updated total hours calculations
  const weekGroups = weeklyPlan.reduce((acc, day) => {
    const weekNumber = day.weekNumber || Math.floor(weeklyPlan.indexOf(day) / 7) + 1;
    if (!acc[weekNumber]) {
      acc[weekNumber] = [];
    }

    // Update the day's total hours based on topics
    const dayTotalMinutes = day.topics.reduce((sum, topic) => {
      return sum + parseDurationToMinutes(topic.duration || "0:00");
    }, 0);

    const dayWithCalculatedHours = {
      ...day,
      totalHours: dayTotalMinutes, // Store total minutes (not converted to hours yet)
      calculatedTotalHours: sumDurations(day.topics.map(topic => topic.duration || "0:00"))
    };

    acc[weekNumber].push(dayWithCalculatedHours);
    return acc;
  }, {} as Record<number, DaySchedule[]>);

  // Get the current date in Brazil timezone
  const currentDate = getCurrentBrazilDate();
  const currentDateStr = currentDate.toISOString().split('T')[0];

  const [currentWeekNumber, setCurrentWeekNumber] = useState<number | null>(null);

  useEffect(() => {
    // Sort week numbers and iterate through them in ascending order
    const weekNumbers = Object.keys(weekGroups).map(Number).sort((a, b) => a - b);

    // Find the first week that contains the current date
    let foundCurrentWeek = false;

    for (const weekNumber of weekNumbers) {
      const days = weekGroups[weekNumber];
      if (days.length > 0) {
        const weekStartDate = days[0].weekStartDate;
        const weekEndDate = days[0].weekEndDate;

        // Use explicit date comparison
        const isInWeek = isDateInRange(
          currentDateStr,
          weekStartDate,
          weekEndDate
        );

        if (isInWeek) {
          setCurrentWeekNumber(weekNumber);
          foundCurrentWeek = true;
          break; // Exit the loop once we find the first matching week
        }
      }
    }

    // If no matching week was found but there are weeks, use the first one as current
    if (!foundCurrentWeek && weekNumbers.length > 0) {
      const firstWeekNumber = weekNumbers[0];
      setCurrentWeekNumber(firstWeekNumber);
    }
  }, [weekGroups, currentDateStr]);

  const handleAddTopic = (dayOfWeek: string, weekNumber: number, scheduleId: string, source: 'platform' | 'manual') => {
    if (onAddTopic) {
      onAddTopic(dayOfWeek, weekNumber, scheduleId, source);
    }
  };

  const handleDeleteWeek = (weekNumber: number) => {
    if (onDeleteWeek) {
      onDeleteWeek(weekNumber);
    }
  };

  const handleDeleteAllWeeks = () => {
    if (onDeleteAllWeeks) {
      onDeleteAllWeeks();
    }
  };

  const handleDeleteTopic = (topicId: string) => {
    if (onDeleteTopic) {
      onDeleteTopic(topicId);
    }
  };

  // Get the highest week number for deletion validation
  const weekNumbers = Object.keys(weekGroups).map(Number);
  const highestWeekNumber = weekNumbers.length > 0 ? Math.max(...weekNumbers) : 0;

  // Format hours from minutes
  const formatHours = (minutes: number) => {
    const hours = minutes / 60;
    const formattedValue = hours.toFixed(1);
    return formattedValue.endsWith('.0') ? formattedValue.slice(0, -2) : formattedValue;
  };

  return (
    <Card className="p-4 sm:p-8 bg-[#FEF7CD] backdrop-blur-sm border-2 border-black">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between mb-4 sm:mb-8"
      >
        <div className="flex items-center gap-3">
          <div className="p-2 bg-[#58CC02] rounded-full border-2 border-black">
            <Calendar className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
          </div>
          <div>
            <h2 className="text-xl sm:text-2xl font-black text-gray-900">Cronograma de Estudos</h2>
            <p className="text-gray-700 text-sm sm:text-base font-medium">Organize sua semana de forma eficiente</p>
          </div>
        </div>

        {currentWeekNumber && (
          <Badge className="px-2 sm:px-3 py-0.5 sm:py-1 bg-[#58CC02] text-white border-2 border-black text-xs sm:text-sm font-bold">
            Semana atual: {currentWeekNumber}
          </Badge>
        )}
      </motion.div>

      <div className="space-y-4 sm:space-y-6">
        {Object.entries(weekGroups)
          .sort(([a], [b]) => parseInt(a) - parseInt(b))
          .map(([weekNumber, days]) => {
            // Calcular o total de minutos para a semana e depois converter para horas
            const weekTotalMinutes = days.reduce((sum, day) =>
              sum + day.totalHours, 0
            );



            return (
              <WeekCard
                key={weekNumber}
                weekNumber={parseInt(weekNumber)}
                days={days}
                totalHours={weekTotalMinutes}
                weekStartDate={days[0].weekStartDate}
                weekEndDate={days[0].weekEndDate}
                isCurrentWeek={parseInt(weekNumber) === currentWeekNumber}
                defaultExpanded={parseInt(weekNumber) === currentWeekNumber}
                isHighestWeek={parseInt(weekNumber) === highestWeekNumber}
                onAddTopic={handleAddTopic}
                onDeleteWeek={onDeleteWeek}
                onDeleteAllWeeks={onDeleteAllWeeks}
                onDeleteTopic={handleDeleteTopic}
                currentDate={currentDate}
              />
            );
          })}
      </div>
    </Card>
  );
};
