
import React, { useState, useCallback, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Info } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { useCategoryResolver } from "@/hooks/useCategoryResolver";

interface PreviewCardProps {
  card: any;
  onImport: (cardId: string) => void;
  isImported?: boolean;
}

export const FlashcardGenerationPreview = ({
  card,
  onImport,
  isImported: isImportedProp,
}: PreviewCardProps) => {
  const [isImporting, setIsImporting] = useState(false);
  const [isImported, setIsImported] = useState(isImportedProp || false);
  const [infoOpen, setInfoOpen] = useState(false);
  const [resolvedHierarchy, setResolvedHierarchy] = useState<any>(null);
  const cardIdRef = useRef(card?.id);
  const importedCardIdRef = useRef<string | null>(null);
  const isMobile = useIsMobile();
  const { resolveCategoryName, isReady } = useCategoryResolver();

  // Resolve hierarchy information when category resolver is ready
  useEffect(() => {
    if (isReady && card && (card.specialty_id || card.theme_id || card.focus_id)) {
      const hierarchy = {
        specialty: card.specialty_id ? {
          id: card.specialty_id,
          name: resolveCategoryName(card.specialty_id, 'specialty') || 'N/A'
        } : null,
        theme: card.theme_id ? {
          id: card.theme_id,
          name: resolveCategoryName(card.theme_id, 'theme') || 'N/A'
        } : null,
        focus: card.focus_id ? {
          id: card.focus_id,
          name: resolveCategoryName(card.focus_id, 'focus') || 'N/A'
        } : null
      };
      
      // Only update if hierarchy actually changed
      const hierarchyChanged = !resolvedHierarchy ||
        resolvedHierarchy.specialty?.id !== hierarchy.specialty?.id ||
        resolvedHierarchy.theme?.id !== hierarchy.theme?.id ||
        resolvedHierarchy.focus?.id !== hierarchy.focus?.id;
      
      if (hierarchyChanged) {
        setResolvedHierarchy(hierarchy);
      }
    }
  }, [isReady, card?.specialty_id, card?.theme_id, card?.focus_id, resolveCategoryName, resolvedHierarchy]);

  // Check if card is already imported
  useEffect(() => {
    const checkIfCardIsImported = async () => {
      if (isImported || isImportedProp) {
        setIsImported(true);
        return;
      }

      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        console.log('🔍 [FlashcardGenerationPreview] Checking if card is already imported:', { cardId: card.id });
        
        if (!card.id) return;
        
        const { data: existing, error: checkError } = await supabase
          .from("flashcards_cards")
          .select("id")
          .eq("user_id", user.id)
          .eq("origin_id", card.id)
          .maybeSingle();

        if (checkError && checkError.code !== 'PGRST116') {
          console.error('❌ [FlashcardGenerationPreview] Error checking card import status:', checkError);
          return;
        }

        if (existing) {
          console.log('✅ [FlashcardGenerationPreview] Card is already imported:', { 
            cardId: existing.id, 
            originId: card.id 
          });
          importedCardIdRef.current = existing.id;
          setIsImported(true);
        } else {
          console.log('ℹ️ [FlashcardGenerationPreview] Card not yet imported:', { cardId: card.id });
          setIsImported(false);
        }
      } catch (error) {
        console.error('❌ [FlashcardGenerationPreview] Error checking import status:', error);
      }
    };

    if (card?.id && card.id !== cardIdRef.current) {
      cardIdRef.current = card.id;
      checkIfCardIsImported();
    }
  }, [card.id, isImported, isImportedProp]);

  useEffect(() => {
    if (typeof isImportedProp === "boolean") {
      setIsImported(isImportedProp);
    }
  }, [isImportedProp]);

  // Handle import logic
  const handleImport = useCallback(async () => {
    if (isImported || isImporting) {
      if (importedCardIdRef.current) {
        onImport(importedCardIdRef.current);
        return;
      }
      return;
    }
    
    try {
      setIsImporting(true);
      console.log('🔄 [FlashcardGenerationPreview] Starting import for card:', { 
        id: card.id,
        front: card.front?.substring(0, 30) + '...'
      });

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Você precisa estar autenticado para importar flashcards");
        return;
      }
      console.log('✅ [FlashcardGenerationPreview] User authenticated:', { userId: user.id });

      // Validate required fields
      if (!card.front || !card.back) {
        console.error('❌ [FlashcardGenerationPreview] Missing required fields:', { 
          front: card.front, 
          back: card.back 
        });
        toast.error("Dados do flashcard inválidos - faltam campos obrigatórios");
        return;
      }

      const checkQuery = supabase
        .from("flashcards_cards")
        .select("id, origin_id")
        .eq("user_id", user.id);
      
      if (card.id) {
        checkQuery.eq("origin_id", card.id);
      } else {
        checkQuery.eq("front", card.front).eq("back", card.back);
      }
      
      const { data: existing, error: checkError } = await checkQuery.maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existing) {
        console.log('ℹ️ [FlashcardGenerationPreview] Card already exists:', {
          cardId: existing.id,
          originId: existing.origin_id
        });
        setIsImported(true);
        importedCardIdRef.current = existing.id;
        onImport(existing.id);
        toast.info("Este flashcard já está na sua coleção");
        return;
      }

      // Determine if this is an import from the Available Flashcards Panel
      // If card.id exists, it's a card from the platform (should have is_shared: false)
      // If card.id doesn't exist, it's a generated card (should have is_shared: true)
      const isFromAvailablePanel = !!card.id;

      const baseCardData = {
        user_id: user.id,
        front: card.front || '',
        back: card.back || '',
        front_image: card.front_image || null,
        back_image: card.back_image || null,
        specialty_id: card.specialty_id || (resolvedHierarchy?.specialty ? resolvedHierarchy.specialty.id : null),
        theme_id: card.theme_id || (resolvedHierarchy?.theme ? resolvedHierarchy.theme.id : null),
        focus_id: card.focus_id || (resolvedHierarchy?.focus ? resolvedHierarchy.focus.id : null),
        current_state: "available",
        // Cards imported from available panel should have is_shared: false
        // Cards from AI generation should have is_shared: true
        is_shared: !isFromAvailablePanel,
        origin_id: card.id || null
      };

      console.log('📝 [FlashcardGenerationPreview] Inserting card with data:', {
        ...baseCardData,
        front: baseCardData.front?.substring(0, 30) + '...',
        back: baseCardData.back?.substring(0, 30) + '...',
        is_shared: baseCardData.is_shared
      });

      const { data: newCard, error } = await supabase
        .from("flashcards_cards")
        .insert(baseCardData)
        .select("id")
        .single();

      if (error) throw error;

      if (!card.id) {
        const { error: updateError } = await supabase
          .from("flashcards_cards")
          .update({ origin_id: newCard.id })
          .eq("id", newCard.id);
          
        if (updateError) {
          console.error('⚠️ [FlashcardGenerationPreview] Error setting origin ID:', updateError);
        } else {
          console.log('✅ [FlashcardGenerationPreview] Set card as its own origin:', newCard.id);
        }
      }

      const { data: verifiedCard, error: verifyError } = await supabase
        .from("flashcards_cards")
        .select("id, is_shared, origin_id, front")
        .eq("id", newCard.id)
        .single();
         
      if (verifyError) {
        console.error('⚠️ [FlashcardGenerationPreview] Error verifying inserted card:', verifyError);
      } else {
        console.log('✅ [FlashcardGenerationPreview] Verified inserted card:', {
          id: verifiedCard.id,
          front: verifiedCard.front?.substring(0, 30) + '...',
          is_shared: verifiedCard.is_shared,
          origin_id: verifiedCard.origin_id
        });
         
        // Ensure sharing status matches our expectation based on source
        const expectedSharingStatus = !isFromAvailablePanel;
        if (verifiedCard.is_shared !== expectedSharingStatus) {
          console.warn(`⚠️ [FlashcardGenerationPreview] WARNING: Card sharing status (${verifiedCard.is_shared}) doesn't match expected (${expectedSharingStatus})`);
          
          const { error: updateShareError } = await supabase
            .from("flashcards_cards")
            .update({ is_shared: expectedSharingStatus })
            .eq("id", newCard.id);
            
          if (updateShareError) {
            console.error('❌ [FlashcardGenerationPreview] Failed to update sharing status:', updateShareError);
          } else {
            console.log(`✅ [FlashcardGenerationPreview] Fixed sharing status to ${expectedSharingStatus}`);
          }
        }
         
        if (card.id && verifiedCard.origin_id !== card.id) {
          console.warn('⚠️ [FlashcardGenerationPreview] WARNING: origin_id mismatch!', {
            expected: card.id,
            actual: verifiedCard.origin_id
          });
        }
      }

      importedCardIdRef.current = newCard.id;
      
      setIsImported(true);
      
      onImport(newCard.id);
      toast.success("Flashcard importado com sucesso!");
      console.log('✨ [FlashcardGenerationPreview] Import completed successfully');
      
    } catch (error: any) {
      console.error('❌ [FlashcardGenerationPreview] Import failed:', error);
      toast.error("Erro ao importar flashcard", {
        description: error.message,
      });
    } finally {
      setIsImporting(false);
    }
  }, [card, isImported, isImporting, onImport, resolvedHierarchy]);

  // Function to truncate text for better mobile display
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  // Use resolved hierarchy or fallback to card hierarchy
  const displayHierarchy = resolvedHierarchy || card.hierarchy;

  return (
    <Card className="relative overflow-hidden bg-white rounded-lg border border-gray-200 shadow-sm p-0">
      <Dialog open={infoOpen} onOpenChange={setInfoOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hierarquia do Flashcard</DialogTitle>
          </DialogHeader>
          <div className="space-y-2 text-sm">
            {displayHierarchy?.specialty?.name && (
              <div>
                <span className="font-semibold">Especialidade:</span> {displayHierarchy.specialty.name}
              </div>
            )}
            {displayHierarchy?.theme?.name && (
              <div>
                <span className="font-semibold">Tema:</span> {displayHierarchy.theme.name}
              </div>
            )}
            {displayHierarchy?.focus?.name && (
              <div>
                <span className="font-semibold">Foco:</span> {displayHierarchy.focus.name}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setInfoOpen(false)}>Fechar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex flex-col items-center justify-center px-3 py-3 min-h-[120px] w-full">
        <div className="w-full flex flex-row items-center mb-1 relative" style={{ minHeight: 32 }}>
          <button
            className="bg-white rounded-full p-1 border border-gray-200 hover:bg-blue-50 shadow-sm absolute left-0 z-10"
            style={{ boxShadow: "0 1px 4px rgba(30,50,110,0.04)" }}
            onClick={(e) => {
              e.stopPropagation();
              setInfoOpen(true);
            }}
            type="button"
            aria-label="Ver hierarquia do flashcard"
            tabIndex={0}
          >
            <Info className="h-4 w-4 text-blue-700" />
          </button>
          <div className="flex-1 flex justify-center items-center">
            <span className="text-xs font-bold text-gray-700">Frente</span>
          </div>
        </div>
        <div className="w-full flex flex-col items-center">
          <div className="min-h-[32px] text-sm text-gray-700 font-normal text-center flex items-center justify-center px-1">
            <span className="break-words line-clamp-4">
              {isMobile ? truncateText(card.front || '', 100) : (card.front || '')}
            </span>
          </div>
        </div>
        <div className="border-t border-gray-200 my-1 w-full"></div>
        <div className="w-full flex justify-center items-center mb-1" style={{ minHeight: 24 }}>
          <span className="text-xs font-bold text-blue-700">Verso</span>
        </div>
        <div className="w-full flex flex-col items-center">
          <div className="min-h-[28px] text-sm text-blue-600 font-medium text-center flex items-center justify-center px-1">
            <span className="break-words line-clamp-4">
              {isMobile ? truncateText(card.back || '', 100) : (card.back || '')}
            </span>
          </div>
        </div>
        <Button
          onClick={handleImport}
          disabled={isImporting || isImported}
          className={`w-full mt-2 text-xs ${isImported ? "bg-green-50 text-green-700 hover:bg-green-50" : ""}`}
          variant={isImported ? "outline" : "default"}
          size="sm"
        >
          {isImporting
            ? "Importando..."
            : isImported
            ? "Importado ✓"
            : isMobile ? "Importar" : "Importar para Minha Coleção"}
        </Button>
      </div>
    </Card>
  );
};
