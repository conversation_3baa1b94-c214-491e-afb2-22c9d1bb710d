import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

import { Note, UpdateNoteInput } from "@/types/notes";

export const useNotes = (searchTerm?: string, folderId?: string) => {
  const queryClient = useQueryClient();

  const { data: notes, isLoading } = useQuery({
    queryKey: ['notes', searchTerm, folderId],
    queryFn: async () => {
      let query = supabase
        .from('pedbook_notes')
        .select('*')
        .order('created_at', { ascending: false });

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`);
      }

      if (folderId) {
        query = query.eq('folder_id', folderId);
      }

      const { data: notesData, error: notesError } = await query;

      if (notesError) {
        console.error('❌ [useNotes] Erro ao carregar notas:', notesError);
        throw notesError;
      }

      const notesWithTags = await Promise.all(notesData.map(async (note) => {
        const { data: tagsData } = await supabase
          .from('pedbook_notes_tags_relations')
          .select('tag_id, pedbook_notes_tags(name)')
          .eq('note_id', note.id);

        const tags = tagsData?.map(tag => tag.pedbook_notes_tags.name) || [];
        return { ...note, tags };
      }));

      return notesWithTags as Note[];
    },
  });

  const toggleFavorite = useMutation({
    mutationFn: async (note: Note) => {
      console.log('Attempting to toggle favorite for note:', note.id);
      console.log('Current favorite status:', note.is_favorite);

      const { data, error } = await supabase
        .from('pedbook_notes')
        .update({
          is_favorite: !note.is_favorite,
          updated_at: new Date().toISOString()
        })
        .eq('id', note.id)
        .select()
        .single();

      if (error) {
        console.error('Error toggling favorite:', error);
        throw error;
      }

      console.log('Toggle favorite response:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    onError: (error) => {
      console.error('❌ [useNotes] Error in toggleFavorite mutation:', error);
    },
  });

  const createNote = useMutation({
    mutationFn: async (note: Partial<Note>) => {
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError) {
        throw userError;
      }

      const { data, error } = await supabase
        .from('pedbook_notes')
        .insert([{
          ...note,
          user_id: userData.user.id,
          content: note.content || "",
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
  });

  const updateNote = useMutation({
    mutationFn: async (input: UpdateNoteInput) => {
      const { error } = await supabase
        .from('pedbook_notes')
        .update({
          title: input.title,
          content: input.content,
          folder_id: input.folder_id,
          updated_at: new Date().toISOString()
        })
        .eq('id', input.id);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    onError: (error) => {
      console.error('❌ [useNotes] Error updating note:', error);
    },
  });

  const deleteNote = useMutation({
    mutationFn: async (noteId: string) => {
      const { error } = await supabase
        .from('pedbook_notes')
        .delete()
        .eq('id', noteId);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
  });

  const addTag = useMutation({
    mutationFn: async ({ noteId, tagName }: { noteId: string; tagName: string }) => {
      const { data: existingTag } = await supabase
        .from('pedbook_notes_tags')
        .select('id')
        .eq('name', tagName)
        .single();

      let tagId;
      if (!existingTag) {
        const { data: userData } = await supabase.auth.getUser();
        const { data: newTag, error: tagError } = await supabase
          .from('pedbook_notes_tags')
          .insert([{ name: tagName, user_id: userData.user.id }])
          .select()
          .single();

        if (tagError) {
          throw tagError;
        }
        tagId = newTag.id;
      } else {
        tagId = existingTag.id;
      }

      const { data: existingRelation } = await supabase
        .from('pedbook_notes_tags_relations')
        .select('*')
        .eq('note_id', noteId)
        .eq('tag_id', tagId)
        .single();

      if (!existingRelation) {
        const { error: relationError } = await supabase
          .from('pedbook_notes_tags_relations')
          .insert([{ note_id: noteId, tag_id: tagId }]);

        if (relationError) {
          throw relationError;
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    onError: (error) => {
      console.error('❌ [useNotes] Error adding tag:', error);
    },
  });

  const removeTag = useMutation({
    mutationFn: async ({ noteId, tagName }: { noteId: string; tagName: string }) => {
      const { data: tag, error: tagError } = await supabase
        .from('pedbook_notes_tags')
        .select('id')
        .eq('name', tagName)
        .single();

      if (tagError) {
        console.error('❌ [useNotes] Erro ao encontrar tag:', tagError);
        throw tagError;
      }

      const { error: relationError } = await supabase
        .from('pedbook_notes_tags_relations')
        .delete()
        .eq('note_id', noteId)
        .eq('tag_id', tag.id);

      if (relationError) {
        console.error('❌ [useNotes] Erro ao remover tag da nota:', relationError);
        throw relationError;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
  });

  const deleteAllNotesInFolder = async (folderId: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_notes')
        .delete()
        .eq('folder_id', folderId);

      if (error) {
        throw error;
      }

      queryClient.invalidateQueries({ queryKey: ['notes'] });
    } catch (error) {
      console.error('Error deleting notes:', error);
      throw error;
    }
  };

  return {
    notes,
    isLoading,
    toggleFavorite,
    createNote,
    updateNote,
    deleteNote,
    addTag,
    removeTag,
    deleteAllNotesInFolder,
  };
};
