
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Brain, Target, Clock, Award, BookOpen, BookmarkCheck, ChevronDown, ChevronUp } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import StatCard from "@/components/StatCard";
import type { StudySessionStats } from "@/types/study-session";
import { motion, AnimatePresence } from "framer-motion";

interface SessionSummaryProps {
  stats: StudySessionStats;
  totalQuestions: number;
}

export const SessionSummary = ({ stats, totalQuestions }: SessionSummaryProps) => {
  const [showSpecialties, setShowSpecialties] = useState(false);
  const [showThemes, setShowThemes] = useState(false);
  const [showFocuses, setShowFocuses] = useState(false);
  const [focusPage, setFocusPage] = useState(0);

  const answeredQuestions = (stats.correct_answers || 0) + (stats.incorrect_answers || 0);
  const accuracy = answeredQuestions > 0
    ? Math.round((stats.correct_answers / answeredQuestions) * 100)
    : 0;

  const getProgressColorClass = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500";
    if (percentage >= 60) return "bg-emerald-500";
    if (percentage >= 40) return "bg-yellow-500";
    if (percentage >= 20) return "bg-orange-500";
    return "bg-red-500";
  };

  return (
    <div className="space-y-8">
      {/* Stats Overview - Centered cards with improved layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <StatCard
          title="Total de Questões"
          value={totalQuestions}
          icon={<Brain className="w-6 h-6 text-blue-500" />}
          className="hover:scale-105 transition-transform border border-blue-100"
        />
        <StatCard
          title="Taxa de Acerto"
          value={`${accuracy}%`}
          icon={<Target className="w-6 h-6 text-green-500" />}
          className="hover:scale-105 transition-transform border border-green-100"
        />
        <StatCard
          title="Tempo Médio"
          value={`${Math.round(stats.time_spent / totalQuestions)}s`}
          icon={<Clock className="w-6 h-6 text-purple-500" />}
          className="hover:scale-105 transition-transform border border-purple-100"
        />
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Especialidades */}
        <Card className="hover:shadow-md transition-shadow border border-blue-100">
          <CardHeader className="bg-blue-50/50 border-b border-blue-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-blue-500" />
                <CardTitle className="text-lg text-blue-700">Especialidades</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSpecialties(!showSpecialties)}
                className="h-8 w-8 p-0 hover:bg-blue-100"
              >
                {showSpecialties ? (
                  <ChevronUp className="h-4 w-4 text-blue-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-blue-600" />
                )}
              </Button>
            </div>
          </CardHeader>
          <AnimatePresence>
            {showSpecialties && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                style={{ overflow: "hidden" }}
              >
                <CardContent className="space-y-4 pt-4">
                  {Object.entries(stats.by_specialty || {}).map(([id, data]) => (
                    <div key={id} className="animate-fade-in-up">
                      <div className="flex justify-between text-sm mb-1.5">
                        <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                        <span className="font-medium text-blue-600">
                          {Math.round((data.correct / data.total) * 100)}%
                        </span>
                      </div>
                      <Progress
                        value={(data.correct / data.total) * 100}
                        className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                      />
                    </div>
                  ))}
                  {Object.keys(stats.by_specialty || {}).length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
                  )}
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Temas */}
        <Card className="hover:shadow-md transition-shadow border border-green-100">
          <CardHeader className="bg-green-50/50 border-b border-green-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-green-500" />
                <CardTitle className="text-lg text-green-700">Temas</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowThemes(!showThemes)}
                className="h-8 w-8 p-0 hover:bg-green-100"
              >
                {showThemes ? (
                  <ChevronUp className="h-4 w-4 text-green-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-green-600" />
                )}
              </Button>
            </div>
          </CardHeader>
          <AnimatePresence>
            {showThemes && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                style={{ overflow: "hidden" }}
              >
                <CardContent className="space-y-4 pt-4">
                  {Object.entries(stats.by_theme || {}).map(([id, data]) => (
                    <div key={id} className="animate-fade-in-up">
                      <div className="flex justify-between text-sm mb-1.5">
                        <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                        <span className="font-medium text-green-600">
                          {Math.round((data.correct / data.total) * 100)}%
                        </span>
                      </div>
                      <Progress
                        value={(data.correct / data.total) * 100}
                        className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                      />
                    </div>
                  ))}
                  {Object.keys(stats.by_theme || {}).length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
                  )}
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Focos */}
        <Card className="hover:shadow-md transition-shadow border border-purple-100">
          <CardHeader className="bg-purple-50/50 border-b border-purple-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BookmarkCheck className="h-5 w-5 text-purple-500" />
                <CardTitle className="text-lg text-purple-700">Focos</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFocuses(!showFocuses)}
                className="h-8 w-8 p-0 hover:bg-purple-100"
              >
                {showFocuses ? (
                  <ChevronUp className="h-4 w-4 text-purple-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-purple-600" />
                )}
              </Button>
            </div>
          </CardHeader>
          <AnimatePresence>
            {showFocuses && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                style={{ overflow: "hidden" }}
              >
                <CardContent className="space-y-4 pt-4">
                  {(() => {
                    const focusEntries = Object.entries(stats.by_focus || {});
                    const itemsPerPage = 10;
                    const startIndex = focusPage * itemsPerPage;
                    const endIndex = startIndex + itemsPerPage;
                    const currentPageItems = focusEntries.slice(startIndex, endIndex);
                    const totalPages = Math.ceil(focusEntries.length / itemsPerPage);

                    return (
                      <>
                        {currentPageItems.map(([id, data]) => (
                          <div key={id} className="animate-fade-in-up">
                            <div className="flex justify-between text-sm mb-1.5">
                              <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                              <span className="font-medium text-purple-600">
                                {Math.round((data.correct / data.total) * 100)}%
                              </span>
                            </div>
                            <Progress
                              value={(data.correct / data.total) * 100}
                              className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                            />
                          </div>
                        ))}

                        {focusEntries.length === 0 && (
                          <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
                        )}

                        {/* Paginação */}
                        {totalPages > 1 && (
                          <div className="flex items-center justify-between pt-4 border-t border-purple-100">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setFocusPage(Math.max(0, focusPage - 1))}
                              disabled={focusPage === 0}
                              className="text-purple-600 border-purple-200 hover:bg-purple-50"
                            >
                              Anterior
                            </Button>
                            <span className="text-sm text-purple-600">
                              {focusPage + 1} de {totalPages}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setFocusPage(Math.min(totalPages - 1, focusPage + 1))}
                              disabled={focusPage === totalPages - 1}
                              className="text-purple-600 border-purple-200 hover:bg-purple-50"
                            >
                              Próximo
                            </Button>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </div>
    </div>
  );
};
