
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useDomain } from "@/hooks/useDomain";

interface FilterTabsProps {
  activeTab: "specialty" | "location" | "year" | "question_type";
  onTabChange: (value: string) => void;
}

export const FilterTabs = ({ activeTab, onTabChange }: FilterTabsProps) => {
  const { isResidencia, domain } = useDomain();
  
  const getSpecialtyLabel = () => {
    if (isResidencia || domain === "revalida") return "Especialidades";
    return "Temas";
  };
  
  // Check if we should show location tab
  const shouldShowLocation = domain !== "revalida";
  
  // Check if we should show question_type tab
  const shouldShowQuestionType = !isResidencia && domain !== "revalida";

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      <TabsList className="w-full flex gap-1 bg-muted/20">
        <TabsTrigger 
          value="specialty" 
          className="flex-1 data-[state=active]:bg-white data-[state=active]:shadow-sm"
        >
          {getSpecialtyLabel()}
        </TabsTrigger>
        
        {shouldShowLocation && (
          <TabsTrigger 
            value="location"
            className="flex-1 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            Instituições
          </TabsTrigger>
        )}
        
        <TabsTrigger 
          value="year"
          className="flex-1 data-[state=active]:bg-white data-[state=active]:shadow-sm"
        >
          Anos
        </TabsTrigger>
        
        {shouldShowQuestionType && (
          <TabsTrigger 
            value="question_type"
            className="flex-1 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            Tipo de Prova
          </TabsTrigger>
        )}
      </TabsList>
    </Tabs>
  );
};
