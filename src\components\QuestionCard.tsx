import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAnswerSubmission } from "@/hooks/useAnswerSubmission";
import { QuestionComments } from "@/components/question/QuestionComments";
import { MessageSquare, ChevronLeft, Brain } from "lucide-react";
import { QuestionAlternatives } from "./question/QuestionAlternatives";
import { DiscursiveAnswer } from "./question/DiscursiveAnswer";
import { QuestionFeedback } from "./question/QuestionFeedback";
import { QuestionMetadata } from "./question/QuestionMetadata";
import { QuestionLikeButtons } from "./question/QuestionLikeButtons";
import type { Question, Comment as QuestionComment } from "@/types/question";
import { TextHighlighter } from "@/components/text/TextHighlighter";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { QuestionImages } from "./question/QuestionImages";
import { convertJsonToComments, convertCommentsToJson } from "@/utils/commentHelpers";
import { FormattedContent } from "./question/FormattedContent";
import { FormattedQuestionContent } from "./question/FormattedQuestionContent";
import type { Comment } from "@/types/comments";
import { Json } from "@/integrations/supabase/types/json";
import { useQueryClient } from '@tanstack/react-query';

const drWillMessages = [
  "Dr. Will está analisando sua resposta com cuidado...",
  "Pensando como um professor humanizado...",
  "Consultando literatura médica...",
  "Elaborando explicação detalhada...",
  "Organizando feedback objetivo...",
];

interface QuestionCardProps {
  question: Question;
  selectedAnswer: string | null;
  hasAnswered: boolean;
  onSelectAnswer: (answer: string) => void;
  onSubmitAnswer?: (timeSpent: number) => Promise<void>;
  onNext?: () => void;
  onPrevious?: () => void;
  userId: string;
  sessionId: string;
  isAnswered?: boolean;
  timeSpent: number;
  showFeedback: boolean;
  isLastQuestion?: boolean;
  isFirstQuestion?: boolean;
  allQuestionsAnswered?: boolean;
}

export const QuestionCard = ({
  question,
  selectedAnswer,
  hasAnswered,
  onSelectAnswer,
  onSubmitAnswer,
  onNext,
  onPrevious,
  userId,
  sessionId,
  isAnswered = false,
  timeSpent,
  showFeedback,
  isLastQuestion = false,
  isFirstQuestion = false,
  allQuestionsAnswered = false
}: QuestionCardProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [questionData, setQuestionData] = useState<Question>(question);
  const [discursiveAnswer, setDiscursiveAnswer] = useState("");
  const [internallyAnswered, setInternallyAnswered] = useState(false);
  const [currentQuestionId, setCurrentQuestionId] = useState<string>(question.id);

  const [discursiveAIResult, setDiscursiveAIResult] = useState<{ aiAnswer: string; feedback: string } | null>(null);
  const [loadingAI, setLoadingAI] = useState(false);
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [aiMessageIdx, setAIMessageIdx] = useState(0);

  const { submitAnswer, isSubmitting } = useAnswerSubmission(sessionId, () => {
    setInternallyAnswered(true);
  });

  useEffect(() => {
    if (currentQuestionId !== question.id) {
      // Reset imediato e sincronizado para evitar flash das cores
      setCurrentQuestionId(question.id);
      setQuestionData(question);
      setInternallyAnswered(false);
      setShowComments(false);
      setNewComment("");
      setShowAIAnalysis(false);
      setAIMessageIdx(0);
      if (question.answer_type === 'DISCURSIVE') {
        setDiscursiveAnswer("");
        setDiscursiveAIResult(null);
      }

      // Log para debug
      console.log(`🔄 [QuestionCard] Mudança de questão: ${currentQuestionId} → ${question.id}`);
      console.log(`🔄 [QuestionCard] Reset internallyAnswered para false`);
    } else {
      setQuestionData(question);
    }
  }, [question.id, currentQuestionId, question]);

  useEffect(() => {
    if (!loadingAI) return;
    const interval = setInterval(() => {
      setAIMessageIdx((prev) => (prev + 1) % drWillMessages.length);
    }, 2500);
    return () => clearInterval(interval);
  }, [loadingAI]);

  const handleSubmit = async () => {
    if (isSubmitting) return;

    try {
      let success = false;

      if (question.answer_type === 'DISCURSIVE') {
        if (!discursiveAnswer.trim()) {
          toast({
            title: "Resposta obrigatória",
            description: "Por favor, escreva sua resposta antes de enviar.",
            variant: "destructive"
          });
          return;
        }

        // Verificar se specialty_id existe, pois é NOT NULL na tabela
        if (!question.specialty?.id) {
          toast({
            title: "Erro ao salvar resposta",
            description: "Esta questão não possui uma especialidade associada, o que é necessário para salvar a resposta.",
            variant: "destructive"
          });
          return;
        }

        // ✅ Verificar se já existe resposta para evitar duplicatas
        try {
          const { data: existingAnswer, error: checkError } = await supabase
            .from('user_answers')
            .select('id')
            .eq('user_id', userId)
            .eq('session_id', sessionId)
            .eq('question_id', question.id)
            .maybeSingle();

          if (checkError) {
            // Erro ao verificar resposta existente, continuar com inserção
          } else if (existingAnswer) {
            return; // Já existe resposta
          }
        } catch (error) {
          // Erro ao verificar, continuar com inserção
        }

        const { error } = await supabase
          .from('user_answers')
          .insert({
            user_id: userId,
            question_id: question.id,
            session_id: sessionId,
            text_answer: discursiveAnswer,
            is_correct: false, // ✅ Valor padrão até a análise da IA (questões dissertativas)
            ai_analyzed: false, // ✅ Indica que ainda não foi analisada pela IA
            time_spent: timeSpent,
            specialty_id: question.specialty.id,
            theme_id: question.theme?.id,
            focus_id: question.focus?.id,
            year: question.year || new Date().getFullYear()
          });

        if (error) throw error;

        setInternallyAnswered(true);
        success = true;

        // Invalidar a query de questões acertadas para forçar uma atualização
        queryClient.invalidateQueries({ queryKey: ['correct-questions'] });

        toast({
          title: "Resposta salva",
          description: "Sua resposta discursiva foi registrada com sucesso",
        });
      } else {
        success = await submitAnswer(userId, question, selectedAnswer!, timeSpent);
      }

      if (success && onSubmitAnswer) {
        await onSubmitAnswer(timeSpent);
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Erro ao enviar resposta",
        description: "Houve um erro ao registrar sua resposta. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  const handleGenerateAnalysis = async () => {
    if (loadingAI || !discursiveAnswer.trim()) return;
    setLoadingAI(true);
    setDiscursiveAIResult(null);
    setShowAIAnalysis(true);
    setAIMessageIdx(0);

    try {
      const res = await fetch(
        `https://bxedpdmgvgatjdfxgxij.functions.supabase.co/discursive-ai-analysis`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            specialty: question.specialty?.name || "",
            theme: question.theme?.name || "",
            focus: question.focus?.name || "",
            statement: question.statement,
            userAnswer: discursiveAnswer,
          }),
        }
      );

      if (!res.ok) {
        throw new Error(`API error: ${res.status} - ${(await res.text())}`);
      }

      const data = await res.json();
      setDiscursiveAIResult({
        aiAnswer: data.ai_answer || "",
        feedback: data.feedback || "",
      });
    } catch (error) {
      toast({
        title: "Análise de IA indisponível",
        description: "Não foi possível obter a análise da IA para sua resposta. Tente novamente mais tarde.",
        variant: "destructive"
      });
      setDiscursiveAIResult({
        aiAnswer: "",
        feedback: "Não foi possível obter a análise da IA para esta resposta no momento.",
      });
    } finally {
      setLoadingAI(false);
    }
  };

  const handleNext = () => {
    if (onNext) {
      onNext();
      setShowComments(false);
      setNewComment("");
    }
  };

  const handleAddComment = async (text: string) => {
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('comments')
        .eq('id', question.id)
        .single();

      if (error) throw error;

      const comments = data.comments ?
        (convertJsonToComments(data.comments as unknown as Json) as unknown as QuestionComment[]) :
        [];

      setQuestionData(prev => ({
        ...prev,
        comments: comments
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error fetching updated comments:', error);
    }
  };

  const handleReplyComment = async (commentId: string | number, replyText: string) => {
    try {
      const newReply: QuestionComment = {
        id: crypto.randomUUID(),
        text: replyText,
        user: userId,
        timestamp: new Date().toISOString(),
        likes: 0,
        dislikes: 0,
        likedBy: [],
        dislikedBy: [],
        replies: []
      };

      const currentComments = convertJsonToComments(questionData.comments as unknown as Json) as QuestionComment[];

      const updatedComments = currentComments.map(comment => {
        if (comment.id === commentId) {
          const updatedReplies = Array.isArray(comment.replies) ? [...comment.replies, newReply] : [newReply];
          return {
            ...comment,
            replies: updatedReplies
          };
        }
        return comment;
      });

      const { error } = await supabase
        .from('questions')
        .update({ comments: convertCommentsToJson(updatedComments as unknown as Comment[]) })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error adding reply:', error);
    }
  };

  const handleCommentLike = async (commentId: string | number, isLike: boolean) => {
    try {
      const currentComments = convertJsonToComments(questionData.comments as unknown as Json) as QuestionComment[];
      const updatedComments = currentComments.map(comment => {
        if (comment.id === commentId) {
          const alreadyLiked = comment.likedBy?.includes(userId);
          const alreadyDisliked = comment.dislikedBy?.includes(userId);

          let newLikedBy = Array.isArray(comment.likedBy) ? [...comment.likedBy] : [];
          let newDislikedBy = Array.isArray(comment.dislikedBy) ? [...comment.dislikedBy] : [];
          let newLikes = comment.likes || 0;
          let newDislikes = comment.dislikes || 0;

          if (alreadyLiked) {
            newLikedBy = newLikedBy.filter(id => id !== userId);
            newLikes--;
          }
          if (alreadyDisliked) {
            newDislikedBy = newDislikedBy.filter(id => id !== userId);
            newDislikes--;
          }

          if (isLike && !alreadyLiked) {
            newLikedBy.push(userId);
            newLikes++;
          } else if (!isLike && !alreadyDisliked) {
            newDislikedBy.push(userId);
            newDislikes++;
          }

          return {
            ...comment,
            likes: newLikes,
            dislikes: newDislikes,
            likedBy: newLikedBy,
            dislikedBy: newDislikedBy
          };
        }
        return comment;
      });

      const { error } = await supabase
        .from('questions')
        .update({ comments: convertCommentsToJson(updatedComments as unknown as Comment[]) })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      toast({
        title: "Reação registrada",
        description: isLike ? "Você curtiu este comentário" : "Você não curtiu este comentário"
      });

    } catch (error) {
      toast({
        title: "Erro ao registrar reação",
        description: "Não foi possível registrar sua reação ao comentário",
        variant: "destructive"
      });
    }
  };

  const handleReplyLike = async (commentId: string | number, replyId: string | number, isLike: boolean) => {
    try {
      const currentComments = convertJsonToComments(questionData.comments as unknown as Json) as QuestionComment[];
      const updatedComments = currentComments.map(comment => {
        if (comment.id === commentId) {
          const updatedReplies = (Array.isArray(comment.replies) ? comment.replies : []).map(reply => {
            if (reply.id === replyId) {
              const alreadyLiked = reply.likedBy?.includes(userId);
              const alreadyDisliked = reply.dislikedBy?.includes(userId);

              let newLikedBy = Array.isArray(reply.likedBy) ? [...reply.likedBy] : [];
              let newDislikedBy = Array.isArray(reply.dislikedBy) ? [...reply.dislikedBy] : [];
              let newLikes = reply.likes || 0;
              let newDislikes = reply.dislikes || 0;

              if (alreadyLiked) {
                newLikedBy = newLikedBy.filter(id => id !== userId);
                newLikes--;
              }
              if (alreadyDisliked) {
                newDislikedBy = newDislikedBy.filter(id => id !== userId);
                newDislikes--;
              }

              if (isLike && !alreadyLiked) {
                newLikedBy.push(userId);
                newLikes++;
              } else if (!isLike && !alreadyDisliked) {
                newDislikedBy.push(userId);
                newDislikes++;
              }

              return {
                ...reply,
                likes: newLikes,
                dislikes: newDislikes,
                likedBy: newLikedBy,
                dislikedBy: newDislikedBy
              };
            }
            return reply;
          });

          return {
            ...comment,
            replies: updatedReplies
          };
        }
        return comment;
      });

      const { error } = await supabase
        .from('questions')
        .update({ comments: convertCommentsToJson(updatedComments as unknown as Comment[]) })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      toast({
        title: "Reação registrada",
        description: isLike ? "Você curtiu esta resposta" : "Você não curtiu esta resposta"
      });

    } catch (error) {
      toast({
        title: "Erro ao registrar reação",
        description: "Não foi possível registrar sua reação à resposta",
        variant: "destructive"
      });
    }
  };

  const currentQuestionAnswered = internallyAnswered || hasAnswered || isAnswered;

  return (
    <Card className="bg-white rounded-xl shadow-lg overflow-hidden border-0">
      <div className="p-6 space-y-6">
        <div className="mb-4">
          <QuestionMetadata question={question} />
        </div>

        <div className="prose max-w-none mb-6 bg-gray-50 p-4 rounded-lg" data-highlighter="statement">
          <FormattedQuestionContent content={question.statement} />
        </div>

        {question.images && question.images.length > 0 && (
          <QuestionImages images={question.images} />
        )}

        {question.answer_type === 'DISSERTATIVA' ? (
          <>
            <DiscursiveAnswer
              value={discursiveAnswer}
              onChange={setDiscursiveAnswer}
              onSubmit={handleSubmit}
              hasAnswered={currentQuestionAnswered}
              readOnly={currentQuestionAnswered}
            />

            {currentQuestionAnswered && (
              <div className="space-y-4 mb-6">
                {!showAIAnalysis && (
                  <Button
                    onClick={handleGenerateAnalysis}
                    disabled={loadingAI || !discursiveAnswer.trim()}
                    variant="hackYellow"
                    size="hack"
                    className="mx-auto flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]"
                  >
                    <Brain className="h-4 w-4" />
                    {loadingAI ? "Gerando Análise..." : "Gerar Análise da IA"}
                  </Button>
                )}

                {loadingAI && (
                  <Card className="border-2 border-black bg-hackathon-lightBg shadow-card-light">
                    <div className="flex items-center gap-3 py-4 px-6">
                      <div className="animate-bounce">
                        <Brain className="h-5 w-5 text-hackathon-black" />
                      </div>
                      <p className="text-hackathon-black font-medium animate-fade-in">
                        {drWillMessages[aiMessageIdx]}
                      </p>
                    </div>
                  </Card>
                )}

                {showAIAnalysis && discursiveAIResult && (
                  <Card className="animate-fade-in border-2 border-black shadow-card mt-2">
                    <div className="border-b-2 border-black bg-hackathon-lightBg py-4 px-6 flex items-center gap-2">
                      <Brain className="h-5 w-5 text-hackathon-black" />
                      <span className="font-bold">Comentário do Dr. Will</span>
                    </div>
                    <div className="space-y-4 p-4">
                      {discursiveAIResult.aiAnswer && (
                        <div className="rounded-lg p-4 border-2 border-hackathon-green bg-green-50/50 hover:bg-green-50">
                          <h4 className="font-bold mb-2 flex items-center gap-2">Resposta esperada</h4>
                          <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                            {discursiveAIResult.aiAnswer}
                          </div>
                        </div>
                      )}
                      {discursiveAIResult.feedback && (
                        <div className="rounded-lg p-4 border-2 border-black bg-hackathon-lightBg transition-all">
                          <h4 className="font-bold mb-2 flex items-center gap-2">
                            <Brain className="h-4 w-4 text-hackathon-black" />
                            Feedback do Dr. Will
                          </h4>
                          <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                            {discursiveAIResult.feedback}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                )}
              </div>
            )}

            {showFeedback && currentQuestionAnswered && (
              <div className="mt-6 border-t pt-6 border-gray-100" >
                <QuestionFeedback
                  question={{ ...question, discursiveAnswer }}
                  selectedAnswer={null}
                  onNext={onNext}
                  onPrevious={onPrevious}
                  isLastQuestion={isLastQuestion}
                  isFirstQuestion={isFirstQuestion}
                  discursiveAnswer={discursiveAnswer}
                  sessionId={sessionId}
                  allQuestionsAnswered={allQuestionsAnswered}
                />
              </div>
            )}
          </>
        ) : (
          <>
            <QuestionAlternatives
              key={`${question.id}-${currentQuestionAnswered}`} // Força re-render quando questão ou estado muda
              alternatives={question.alternatives || []}
              selectedAnswer={selectedAnswer}
              setSelectedAnswer={!currentQuestionAnswered ? onSelectAnswer : undefined}
              hasAnswered={currentQuestionAnswered}
              correct_answer={parseInt(question.correct_answer.toString())}
              statistics={question.statistics}
              alternativeComments={question.alternativeComments}
              questionId={question.id}
            />

            {!currentQuestionAnswered && (
              <div className={`flex ${isFirstQuestion ? 'justify-end' : 'justify-between'} gap-4 mt-6`}>
                {!isFirstQuestion && (
                  <Button
                    onClick={onPrevious}
                    variant="outline"
                    className="flex items-center gap-2 rounded-full px-6"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Anterior
                  </Button>
                )}
                <div className={!isFirstQuestion ? "" : ""}>
                  <Button
                    onClick={handleSubmit}
                    disabled={!selectedAnswer || isSubmitting}
                    className="rounded-full px-8 bg-green-500 hover:bg-green-600 text-white"
                  >
                    {isSubmitting ? "Enviando..." : "Verificar"}
                  </Button>
                </div>
              </div>
            )}

            {showFeedback && currentQuestionAnswered && (
              <div className="mt-6 border-t pt-6 border-gray-100">
                <QuestionFeedback
                  question={question}
                  selectedAnswer={selectedAnswer}
                  onNext={onNext}
                  onPrevious={onPrevious}
                  isLastQuestion={isLastQuestion}
                  isFirstQuestion={isFirstQuestion}
                  sessionId={sessionId}
                  allQuestionsAnswered={allQuestionsAnswered}
                />
              </div>
            )}
          </>
        )}

        <div className="flex flex-col md:flex-row items-center justify-center md:justify-between gap-4 mt-6 pt-6 border-t border-gray-100">
          <QuestionLikeButtons
            questionId={question.id}
            userId={userId}
            initialLikes={question.likes || 0}
            initialDislikes={question.dislikes || 0}
            likedBy={question.liked_by || []}
            dislikedBy={question.disliked_by || []}
          />

          {currentQuestionAnswered && (
            <Button
              variant="outline"
              onClick={() => setShowComments(!showComments)}
              className="flex items-center gap-2 rounded-full"
            >
              <MessageSquare className="h-4 w-4" />
              {showComments ? "Ocultar Comentários" : "Ver Comentários"}
            </Button>
          )}
        </div>

        {showComments && currentQuestionAnswered && (
          <div className="animate-fade-in mt-4">
            <QuestionComments
              comments={convertJsonToComments(questionData.comments as unknown as Json) as unknown as Comment[]}
              newComment={newComment}
              setNewComment={setNewComment}
              onAddComment={handleAddComment}
              onReplyComment={handleReplyComment}
              onLikeComment={handleCommentLike}
              onReplyLike={handleReplyLike}
              userId={userId}
              questionId={question.id}
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default QuestionCard;
