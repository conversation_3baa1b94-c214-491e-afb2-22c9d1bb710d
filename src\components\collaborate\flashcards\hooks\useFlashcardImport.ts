
import { useState, useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useFlashcardImport = () => {
  const [importedIndexes, setImportedIndexes] = useState<number[]>([]);
  const [importingIndex, setImportingIndex] = useState<number | null>(null);
  const [importingAll, setImportingAll] = useState(false);
  const [importedCardIds, setImportedCardIds] = useState<string[]>([]);

  // This is needed to refresh the list of imported card IDs
  const refreshImportedCards = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data: userCards, error } = await supabase
        .from('flashcards_cards')
        .select('origin_id')
        .eq('user_id', user.id)
        .not('origin_id', 'is', null);

      if (error) throw error;

      if (userCards && userCards.length > 0) {
        const importedIds = userCards.map(card => card.origin_id).filter(Boolean);
        setImportedCardIds(importedIds);
        return importedIds;
      }
      
      return [];
    } catch (error: any) {
      console.error('Error refreshing imported cards:', error);
      return [];
    }
  }, []);

  // Load imported cards when the hook is initialized
  useCallback(() => {
    refreshImportedCards();
  }, [refreshImportedCards]);

  const importFlashcard = useCallback(async (
    index: number,
    flashcard: any,
    specialty_id: string,
    theme_id?: string,
    focus_id?: string,
    extrafocus_id?: string
  ) => {
    if (importedIndexes.includes(index)) {
      toast.info("Este flashcard já foi importado");
      return;
    }

    setImportingIndex(index);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Usuário não autenticado");
        return;
      }

      // 1. Cria o flashcard
      const { data: insertData, error } = await supabase
        .from('flashcards_cards')
        .insert({
          user_id: user.id,
          front: flashcard.front,
          back: flashcard.back,
          front_image: flashcard.front_image || null,
          back_image: flashcard.back_image || null,
          specialty_id,
          theme_id: theme_id || null,
          focus_id: focus_id || null,
          extrafocus_id: extrafocus_id || null,
          current_state: 'available',
          is_shared: false,
          origin_id: null // Will be set to own ID after insert
        })
        .select('id')
        .single();

      if (error) throw error;

      // 2. Atualiza origin_id para ser igual ao id do card
      if (insertData && insertData.id) {
        const { error: updateError } = await supabase
          .from('flashcards_cards')
          .update({ origin_id: insertData.id })
          .eq('id', insertData.id);
          
        if (updateError) {
          console.error('Error updating origin_id:', updateError);
        }
          
        setImportedIndexes(prev => [...prev, index]);
        toast.success("Flashcard importado com sucesso!");
      }
    } catch (error: any) {
      toast.error("Erro ao importar flashcard", { 
        description: error.message || "Ocorreu um erro durante a importação"
      });
    } finally {
      setImportingIndex(null);
    }
  }, [importedIndexes]);

  const importAllFlashcards = useCallback(async (
    cards: any[],
    specialty_id: string,
    theme_id?: string,
    focus_id?: string,
    extrafocus_id?: string
  ) => {
    if (cards.length === 0) {
      toast.info("Não há flashcards para importar");
      return;
    }

    setImportingAll(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Usuário não autenticado");
        return;
      }

      const importedCards = [];
      const errors = [];
      
      for (let i = 0; i < cards.length; i++) {
        if (importedIndexes.includes(i)) continue;
        
        const card = cards[i];
        
        try {
          // 1. Criar flashcard
          const { data: insertData, error } = await supabase
            .from('flashcards_cards')
            .insert({
              user_id: user.id,
              front: card.front,
              back: card.back,
              front_image: card.front_image || null,
              back_image: card.back_image || null,
              specialty_id,
              theme_id: theme_id || null,
              focus_id: focus_id || null,
              extrafocus_id: extrafocus_id || null,
              current_state: 'available',
              is_shared: false,
              origin_id: null // Will be set to own ID after insert
            })
            .select('id')
            .single();

          if (error) throw error;
          
          // 2. Atualizar origin_id
          if (insertData && insertData.id) {
            const { error: updateError } = await supabase
              .from('flashcards_cards')
              .update({ origin_id: insertData.id })
              .eq('id', insertData.id);
              
            if (updateError) {
              console.error('Error updating origin_id:', updateError);
            }
              
            importedCards.push(i);
          }
        } catch (error) {
          errors.push({ index: i, error });
        }
      }

      if (importedCards.length > 0) {
        setImportedIndexes(prev => [...prev, ...importedCards]);
        toast.success(`${importedCards.length} flashcards importados com sucesso!`);
      }

      if (errors.length > 0) {
        toast.error(`Erro ao importar ${errors.length} flashcards`);
      }
    } catch (error: any) {
      toast.error("Erro ao importar flashcards", { 
        description: error.message || "Ocorreu um erro durante a importação em massa"
      });
    } finally {
      setImportingAll(false);
    }
  }, [importedIndexes]);

  // Function to refresh imported counts - called after batch import operations
  // Note: Using a SQL function that may need to be created on the server side
  const refreshImportedCounts = useCallback(async (cardIds: string[]) => {
    if (!cardIds.length) return;
    
    try {
      // First check if the function exists
      const { data: functionExists, error: checkError } = await supabase
        .rpc('increment_flashcard_import_count', {
          p_card_ids: cardIds
        })
        .catch(() => ({ data: null, error: true })); // Suppress error to handle missing function

      if (checkError) {
        console.log('Function increment_flashcard_import_count may not exist, using alternative approach');
        
        // Alternative approach: Update each card individually
        for (const cardId of cardIds) {
          await supabase.rpc('add_cards_to_user_deck', {
            p_user_id: null, // Will be ignored but needed for parameter
            p_card_ids: [cardId]
          });
        }
      }
    } catch (error: any) {
      console.error('Error updating import counts:', error);
    }
  }, []);

  return {
    importFlashcard,
    importAllFlashcards,
    importedIndexes,
    importingIndex,
    importingAll,
    importedCardIds,
    refreshImportedCards,
    refreshImportedCounts
  };
};
