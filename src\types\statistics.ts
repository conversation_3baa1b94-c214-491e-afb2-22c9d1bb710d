
export interface UserStatistics {
  user_id?: string;
  total_questions: number; // Questões únicas respondidas
  total_answers?: number; // Total de respostas (incluindo repetidas)
  correct_answers: number;
  incorrect_answers: number;
  avg_response_time: number;
  total_study_time: number;
  by_specialty: Record<string, {
    id: string;
    name: string;
    correct: number;
    total: number;
  }>;
  by_theme: Record<string, {
    id: string;
    name: string;
    specialty_id: string;
    correct: number;
    total: number;
  }>;
  by_focus: Record<string, {
    id: string;
    name: string;
    theme_id: string;
    correct: number;
    total: number;
  }>;
  by_year: Record<string, {
    year: number;
    correct: number;
    total: number;
  }>;
  by_institution: Record<string, {
    id: string;
    name: string;
    correct: number;
    total: number;
  }>;
  streak_days: number;
  max_streak: number;
  weekly_stats: {
    current_week: {
      correct: number;
      incorrect: number;
      sessions: number;
    };
    previous_week: {
      correct: number;
      incorrect: number;
      sessions: number;
    };
  };
}
