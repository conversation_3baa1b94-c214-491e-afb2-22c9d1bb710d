import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request: Request): string => {
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }

  return 'https://medevo.com.br'; // fallback
};

const getCorsHeaders = (request: Request) => ({
  'Access-Control-Allow-Origin': getAllowedOrigin(request),
  'Access-Control-Allow-Headers': 'authorization, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
});

serve(async (req: Request) => {
  const corsHeaders = getCorsHeaders(req);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Simple health check
  if (req.method === 'GET') {
    return new Response(
      JSON.stringify({
        status: 'Thinking function is online',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }

  try {
    const requestBody = await req.json();
    const { message, userId, conversationHistory } = requestBody;

    if (!message || typeof message !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Message is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get Gemini API key
    const geminiKey = Deno.env.get("GEMINI_API_KEY")
    if (!geminiKey) {
      throw new Error("Serviço temporariamente indisponível")
    }

    // Get user info for personalization (same logic as dr-will-medevo)
    let userName = 'estudante';
    if (userId) {
      try {
        // Get user profile from Supabase
        const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
        const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';



        if (supabaseUrl && supabaseServiceKey) {
          const supabase = createClient(supabaseUrl, supabaseServiceKey);

          // Try multiple approaches to get user name (same as dr-will-medevo)
          let profile: { full_name?: string } | null = null;

          // First try: profiles table
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('id', userId)
            .single();



          if (profileData && !profileError) {
            profile = profileData;
          } else {
            // Second try: auth.users table (if accessible)
            try {
              const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);


              if (userData?.user?.user_metadata) {
                profile = {
                  full_name: userData.user.user_metadata.full_name || userData.user.user_metadata.name
                };
              }
            } catch (authError) {

            }
          }

          if (profile?.full_name) {
            const firstName = profile.full_name.split(' ')[0];
            userName = firstName;

          }
        }
      } catch (error) {
        console.error('🧠 [THINKING] Error getting user profile:', error);
      }
    }



    // Build conversation context
    let conversationContext = '';
    if (conversationHistory && Array.isArray(conversationHistory) && conversationHistory.length > 0) {
      conversationContext = '\n\nHISTÓRICO DA CONVERSA:\n';
      conversationHistory.slice(-4).forEach((msg) => {
        const role = msg.role === 'user' ? 'Estudante' : 'Dr. Will';
        conversationContext += `${role}: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}\n`;
      });
    }

    // 🎯 PROMPT OTIMIZADO: Mais conciso para evitar MAX_TOKENS
    const thinkingPrompt = `Dr. Will, IA médica da MedEvo. Usuário: ${userName}. Pergunta: "${message.trim()}"${conversationContext}

GERE EXATAMENTE 4 FRASES (2 parágrafos):

Parágrafo 1: 2 frases sobre identificação do escopo + diretriz médica
Parágrafo 2: 2 frases sobre calibração para residência + dica prática

EXEMPLO:
🔍 Analisando questão sobre pneumonia seguindo diretrizes IDSA. Organizando resposta focada em diagnóstico diferencial.

✅ Calibrando para ${userName} com ênfase em critérios de gravidade. Atenção às armadilhas sobre CURB-65 em provas.`



    // Prepare the request for Gemini 2.5 Flash with streaming
    const contents = [
      {
        role: "user",
        parts: [{ text: thinkingPrompt }]
      }
    ];

    const geminiRequestBody = {
      contents,
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 150, // 🎯 REDUZIDO: Thinking deve ser conciso (4 frases = ~100-150 tokens)
        topP: 0.8,
        topK: 40
      },
      safetySettings: [
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" }
      ]
    };



    const geminiResponse = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:streamGenerateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": geminiKey
        },
        body: JSON.stringify(geminiRequestBody)
      }
    )

    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text()
      console.error("API error:", errorText)
      throw new Error("Serviço temporariamente indisponível")
    }



    // Create a ReadableStream for Server-Sent Events
    const stream = new ReadableStream({
      async start(controller) {
        const reader = geminiResponse.body?.getReader()
        if (!reader) {
          controller.error(new Error("No response body"))
          return
        }

        const decoder = new TextDecoder()
        let buffer = ""
        let chunkCount = 0;

        try {
          while (true) {
            const { done, value } = await reader.read()
            chunkCount++;

            if (done) {

              // Send final message to indicate completion
              const finalData = JSON.stringify({ done: true })
              controller.enqueue(new TextEncoder().encode(`data: ${finalData}\n\n`))
              break
            }

            buffer += decoder.decode(value, { stream: true })



            // Process JSON chunks from buffer
            while (buffer.length > 0) {
              try {
                // Find JSON start
                let jsonStart = -1;
                for (let i = 0; i < buffer.length; i++) {
                  if (buffer[i] === '{') {
                    jsonStart = i;
                    break;
                  }
                }

                if (jsonStart === -1) {
                  break;
                }

                // Remove garbage before JSON
                if (jsonStart > 0) {
                  buffer = buffer.substring(jsonStart);
                }

                // Find JSON end
                let jsonEnd = -1;
                let braceCount = 0;
                let inString = false;
                let escaped = false;

                for (let i = 0; i < buffer.length; i++) {
                  const char = buffer[i];

                  if (escaped) {
                    escaped = false;
                    continue;
                  }

                  if (char === '\\') {
                    escaped = true;
                    continue;
                  }

                  if (char === '"') {
                    inString = !inString;
                    continue;
                  }

                  if (!inString) {
                    if (char === '{') {
                      braceCount++;
                    } else if (char === '}') {
                      braceCount--;
                      if (braceCount === 0) {
                        jsonEnd = i;
                        break;
                      }
                    }
                  }
                }

                if (jsonEnd === -1) {
                  break;
                }

                // Extract complete JSON
                const jsonText = buffer.substring(0, jsonEnd + 1);
                buffer = buffer.substring(jsonEnd + 1);

                // Parse the JSON
                const jsonResponse = JSON.parse(jsonText);

                // Check for errors in the response
                if (jsonResponse.error) {

                  const errorData = JSON.stringify({
                    error: "Erro interno do servidor",
                    timestamp: new Date().toISOString()
                  });
                  controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
                  continue;
                }

                // Process candidates
                if (jsonResponse.candidates && Array.isArray(jsonResponse.candidates) && jsonResponse.candidates.length > 0) {
                  const candidate = jsonResponse.candidates[0];

                  if (candidate.content && candidate.content.parts && Array.isArray(candidate.content.parts)) {
                    for (const part of candidate.content.parts) {
                      if (part.text && typeof part.text === 'string' && part.text.length > 0) {



                        // Send the text chunk as SSE
                        const data = JSON.stringify({
                          content: part.text,
                          timestamp: new Date().toISOString()
                        });
                        controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
                      }
                    }
                  }
                }

              } catch (parseError) {

                // If can't parse, wait for more data
                break;
              }
            }
          }
        } catch (error) {

          const errorData = JSON.stringify({
            error: "Erro no streaming do pensamento",
            timestamp: new Date().toISOString()
          })
          controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`))
        } finally {
          controller.close()
        }
      }
    })

    // Return the stream with appropriate headers for SSE
    return new Response(stream, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {


    return new Response(
      JSON.stringify({
        error: 'Erro interno do servidor. Tente novamente em alguns minutos.',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
