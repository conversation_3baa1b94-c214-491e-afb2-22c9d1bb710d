import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2, <PERSON><PERSON>, <PERSON>rk<PERSON>, Target, Info, Zap } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useStudySession } from "@/hooks/useStudySession";
import { ensureUserId } from "@/utils/ensureUserId";
import type { SelectedFilters } from "@/types/question";
import { useDomain } from "@/hooks/useDomain";
import { motion } from "framer-motion";

interface RandomQuestionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filteredQuestions: any[];
  totalQuestionCount: number;
  filters: SelectedFilters;
  domain?: string;
}

const RandomQuestionsDialog = ({
  open,
  onOpenChange,
  filteredQuestions,
  totalQuestionCount,
  filters
}: RandomQuestionsDialogProps) => {
  const { domain } = useDomain();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { createSession } = useStudySession();
  const [isLoading, setIsLoading] = useState(false);
  const [questionCount, setQuestionCount] = useState(10);
  const [sessionTitle, setSessionTitle] = useState("Questões Aleatórias");

  // Maximum number of questions allowed - use totalQuestionCount which reflects the actual filtered count
  const MAX_QUESTIONS = totalQuestionCount > 0
    ? Math.min(50, totalQuestionCount)
    : 50;

  // Adjust questionCount if it exceeds the new maximum
  useEffect(() => {
    if (questionCount > MAX_QUESTIONS) {
      setQuestionCount(Math.min(questionCount, MAX_QUESTIONS));
    }
  }, [MAX_QUESTIONS, questionCount]);

  const handleCreateRandomSession = async () => {
    try {
      setIsLoading(true);

      // If we have filtered questions, use them
      if (filteredQuestions.length > 0) {
        // Randomly select questions
        const shuffled = [...filteredQuestions].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, questionCount);
        const questionIds = selected.map(q => q.id);

        const userId = await ensureUserId();
        const session = await createSession(userId, questionIds, sessionTitle);

        if (!session?.id) {
          throw new Error("Falha ao criar sessão");
        }

        navigate(`/questions/${session.id}`);
      }
      // Otherwise, fetch random questions from the database
      else {
        // Call the stored procedure to get random questions
        const { data, error } = await supabase.rpc('get_random_questions', {
          p_quantity: questionCount,
          p_domain: domain
        });

        if (error) {
          throw error;
        }

        if (!data || data.length === 0) {
          throw new Error("Nenhuma questão encontrada");
        }

        // Extract question IDs from the result
        const questionIds = data.map((q: any) => q.id);

        // Create a study session with these questions
        const userId = await ensureUserId();
        const session = await createSession(userId, questionIds, sessionTitle);

        if (!session?.id) {
          throw new Error("Falha ao criar sessão");
        }

        navigate(`/questions/${session.id}`);
      }
    } catch (error: any) {
      console.error('❌ [RandomQuestionsDialog] Error creating random session:', error);
      toast({
        title: "Erro ao criar sessão aleatória",
        description: error.message || "Ocorreu um erro ao criar a sessão de estudos",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      onOpenChange(false);
    }
  };

  // Log removido para reduzir ruído no console

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="!fixed !left-[50%] !top-[50%] !translate-x-[-50%] !translate-y-[-50%] w-[85dvw] sm:max-w-[500px] max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto relative bg-gradient-to-br from-white to-gray-50 border-2 border-black shadow-2xl"
        style={{
          position: 'fixed !important',
          left: '50% !important',
          top: '50% !important',
          transform: 'translate(-50%, -50%) !important',
          zIndex: 9999
        }}
      >
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center rounded-2xl sm:rounded-xl">
            <div className="flex flex-col items-center gap-3 p-6">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <div className="text-center">
                <p className="font-medium text-gray-900">Criando sua sessão de estudos...</p>
                <p className="text-sm text-gray-600 mt-1">Isso pode levar alguns segundos</p>
              </div>
            </div>
          </div>
        )}

        <DialogHeader className="text-center pb-6 border-b border-gray-200">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center gap-3 mb-3"
          >
            <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full border-2 border-black">
              <Shuffle className="h-6 w-6 text-white" />
            </div>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Questões Aleatórias
            </DialogTitle>
          </motion.div>
          <DialogDescription className="text-base text-gray-600">
            {filteredQuestions.length > 0
              ? `Criar uma sessão personalizada com questões aleatórias dos ${totalQuestionCount} filtrados.`
              : "Criar uma sessão personalizada com questões aleatórias do banco de dados."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-8 py-6">
          {/* Seção do Título */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-3"
          >
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-purple-500" />
              <Label htmlFor="title" className="text-sm font-bold text-gray-700">
                Título da Sessão
              </Label>
            </div>
            <Input
              id="title"
              value={sessionTitle}
              onChange={(e) => setSessionTitle(e.target.value)}
              className="w-full border-2 border-gray-300 focus:border-purple-500 rounded-lg px-4 py-3 text-base font-medium transition-all duration-200 bg-white/80 backdrop-blur-sm"
              placeholder="Ex: Revisão de Cardiologia, Prova Final..."
            />
          </motion.div>
          {/* Seção da Quantidade */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-500" />
              <Label htmlFor="count" className="text-sm font-bold text-gray-700">
                Quantidade de Questões
              </Label>
            </div>

            {/* Card com informações */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-blue-700 font-medium">Questões selecionadas:</span>
                <div className="flex items-center gap-2">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1 rounded-full border-2 border-black font-bold text-lg">
                    {questionCount}
                  </div>
                  <Zap className="h-4 w-4 text-blue-500" />
                </div>
              </div>

              <Slider
                id="count"
                value={[questionCount]}
                min={1}
                max={MAX_QUESTIONS}
                step={1}
                onValueChange={(value) => setQuestionCount(value[0])}
                className="w-full"
              />

              <div className="flex justify-between text-xs text-blue-600 font-medium mt-2">
                <span>Mín: 1</span>
                <span>Máx: {MAX_QUESTIONS}</span>
              </div>
            </div>

            {/* Informação adicional */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 flex items-start gap-2">
              <Info className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-amber-700">
                <p className="font-medium">Dica:</p>
                <p>Para uma sessão rápida, use 5-10 questões. Para estudo intensivo, 20-30 questões.</p>
              </div>
            </div>
          </motion.div>
        </div>
        {/* Botões de Ação */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200"
        >
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto order-2 sm:order-1 border-2 border-gray-300 hover:border-gray-400 font-medium transition-all duration-200"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleCreateRandomSession}
            disabled={isLoading || !sessionTitle.trim()}
            className="w-full sm:w-auto order-1 sm:order-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold border-2 border-black shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Criando Sessão...
              </>
            ) : (
              <>
                <Shuffle className="mr-2 h-4 w-4" />
                Criar Sessão
              </>
            )}
          </Button>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default RandomQuestionsDialog;
