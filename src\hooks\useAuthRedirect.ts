import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useUserData } from './useUserData';

/**
 * Hook para gerenciar redirecionamentos automáticos baseados no estado de autenticação
 * Centraliza a lógica de verificação de perfil e redirecionamento
 */

interface UserProfile {
  preparation_type?: string;
  specialty?: string;
  premium?: boolean;
  premium_requested?: boolean;
  preferences_completed?: boolean;
}

interface AuthRedirectOptions {
  redirectOnAuth?: boolean; // Se deve redirecionar automaticamente quando autenticado
  skipOnboarding?: boolean; // Se deve pular verificação de onboarding
  skipAccessCheck?: boolean; // Se deve pular verificação de acesso
}

export const useAuthRedirect = (options: AuthRedirectOptions = {}) => {
  const { user, loading } = useAuth();
  const userData = useUserData(); // ✅ CORRIGIDO: Pegar objeto completo
  const navigate = useNavigate();
  const [isChecking, setIsChecking] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);

  const {
    redirectOnAuth = true,
    skipOnboarding = false,
    skipAccessCheck = false
  } = options;

  /**
   * Verifica o perfil do usuário e determina o redirecionamento apropriado
   * Agora usa dados centralizados do useUserData
   */
  const checkProfileAndRedirect = (profileData: any): string | null => {
    if (!profileData) {
      return skipOnboarding ? null : '/onboarding';
    }

    setProfile(profileData);

    // Verificar se precisa completar onboarding
    if (!skipOnboarding && !profileData?.hasCompletedOnboarding) {
      return '/onboarding';
    }

    // Verificar se precisa completar preferências de estudo
    if (!skipOnboarding && !profileData?.hasCompletedPreferences) {
      return '/study-preferences';
    }

    // Verificar acesso à plataforma
    if (!skipAccessCheck && !profileData?.isPremium && !profileData?.isPremiumRequested) {
      return '/acesso-restrito';
    }

    // Usuário está completo e tem acesso
    return '/plataformadeestudos';
  };

  /**
   * Executa o redirecionamento automático usando dados centralizados
   */
  useEffect(() => {
    const handleRedirect = () => {
      if (loading || userData.isLoading || !redirectOnAuth) return; // ✅ CORRIGIDO

      if (user && userData && !userData.isLoading) {
        setIsChecking(true);

        try {
          const redirectPath = checkProfileAndRedirect(userData);

          if (redirectPath) {
            navigate(redirectPath, { replace: true });
          }
        } catch (error) {
          // Silent error handling
        } finally {
          setIsChecking(false);
        }
      } else if (!user && !loading) {
        // Usuário não está logado e não está carregando - redirecionar para home
        setProfile(null);
        setIsChecking(false);

        // Se não estiver na página inicial, redirecionar
        if (window.location.pathname !== '/') {
          navigate('/', { replace: true });
        }
      } else {
        setProfile(null);
        setIsChecking(false);
      }
    };

    handleRedirect();
  }, [user, userData, loading, userData.isLoading, navigate, redirectOnAuth, skipOnboarding, skipAccessCheck]); // ✅ CORRIGIDO

  /**
   * Função manual para verificar e redirecionar usando dados centralizados
   */
  const manualRedirect = (): string | null => {
    if (!user || !userData) return null;

    setIsChecking(true);
    try {
      const redirectPath = checkProfileAndRedirect(userData);

      if (redirectPath) {
        navigate(redirectPath, { replace: true });
      }

      return redirectPath;
    } finally {
      setIsChecking(false);
    }
  };

  /**
   * Verifica se o usuário tem acesso à plataforma
   */
  const hasAccess = (): boolean => {
    return !!(userData?.isPremium || userData?.isPremiumRequested); // ✅ CORRIGIDO
  };

  /**
   * Verifica se o usuário completou o onboarding
   */
  const hasCompletedOnboarding = (): boolean => {
    return !!userData?.hasCompletedOnboarding; // ✅ CORRIGIDO
  };

  /**
   * Verifica se o usuário completou as preferências de estudo
   */
  const hasCompletedPreferences = (): boolean => {
    return !!userData?.hasCompletedPreferences; // ✅ CORRIGIDO
  };

  /**
   * Determina qual página o usuário deveria estar
   */
  const getExpectedRoute = (): string => {
    if (!user) return '/';
    if (!hasCompletedOnboarding()) return '/onboarding';
    if (!hasCompletedPreferences()) return '/study-preferences';
    if (!hasAccess()) return '/acesso-restrito';
    return '/plataformadeestudos';
  };

  /**
   * Verifica se o usuário está na rota correta
   */
  const isOnCorrectRoute = (currentPath: string): boolean => {
    const expectedRoute = getExpectedRoute();
    return currentPath === expectedRoute;
  };

  return {
    // Estados
    isChecking: loading || userData.isLoading || isChecking, // ✅ CORRIGIDO
    profile: userData,
    user,

    // Verificações
    hasAccess: hasAccess(),
    hasCompletedOnboarding: hasCompletedOnboarding(),

    // Funções
    manualRedirect,
    checkProfileAndRedirect,
    getExpectedRoute,
    isOnCorrectRoute,

    // Utilitários
    isAuthenticated: !!user,
    isLoading: loading || userData.isLoading, // ✅ CORRIGIDO
  };
};

/**
 * Hook simplificado para redirecionamento automático na página inicial
 */
export const useAutoRedirect = () => {
  return useAuthRedirect({
    redirectOnAuth: true,
    skipOnboarding: false,
    skipAccessCheck: false,
  });
};

/**
 * Hook para verificação de acesso sem redirecionamento automático
 */
export const useAuthCheck = () => {
  return useAuthRedirect({
    redirectOnAuth: false,
    skipOnboarding: false,
    skipAccessCheck: false,
  });
};

/**
 * Hook para páginas que não precisam de verificação de acesso
 */
export const useBasicAuth = () => {
  return useAuthRedirect({
    redirectOnAuth: false,
    skipOnboarding: true,
    skipAccessCheck: true,
  });
};
