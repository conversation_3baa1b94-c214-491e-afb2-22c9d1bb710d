
import React from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON>O<PERSON>, <PERSON>, ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";

interface HeroSectionProps {
  onStartClick: () => void;
}

export function HeroSection({ onStartClick }: HeroSectionProps) {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleStartClick = () => {
    if (user) {
      navigate("/plataformadeestudos");
    } else {
      onStartClick();
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  const featureItems = [
    { icon: <PERSON>, text: "Mais de 1.000 questões" },
    { icon: Book<PERSON><PERSON>, text: "Questões comentadas" },
    { icon: Eye, text: "Especializado em oftalmologia" },
  ];

  return (
    <div className="container mx-auto px-4 py-20 md:py-28 lg:pt-20 lg:pb-28">
      <motion.div
        className="text-center"
        initial="hidden"
        animate="show"
        variants={container}
      >
        <motion.h1 
          className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
          variants={item}
        >
          Estude Oftalmologia com
          <span className="bg-gradient-to-r from-blue-300 to-blue-600 text-transparent bg-clip-text block mt-2">
            Questões Especializadas
          </span>
        </motion.h1>
        
        <motion.p 
          className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto"
          variants={item}
        >
          Prepare-se para sua residência ou atualize seus conhecimentos com questões específicas de oftalmologia.
        </motion.p>
        
        <motion.div variants={item}>
          <Button 
            onClick={handleStartClick}
            size="lg" 
            className="group bg-white hover:bg-white/90 text-primary text-lg px-8 py-7 rounded-full flex items-center gap-2 mx-auto shadow-lg hover:shadow-xl transition-all"
          >
            Comece Agora – É Grátis
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </motion.div>
        
        <motion.div 
          className="flex flex-wrap gap-6 mt-10 justify-center"
          variants={item}
        >
          {featureItems.map((feature, index) => (
            <motion.div 
              key={index} 
              className="flex items-center gap-2 text-white/80 bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm hover:bg-white/20 transition-colors group"
              whileHover={{ scale: 1.05 }}
            >
              <feature.icon className="w-5 h-5 group-hover:text-blue-300 transition-colors" />
              <span>{feature.text}</span>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
}
