import { cn } from "@/lib/utils";

interface LoadingBarProps {
  show: boolean;
  className?: string;
}

const LoadingBar = ({ show, className }: LoadingBarProps) => {
  if (!show) return null;

  return (
    <>
      <style>
        {`
          @keyframes loading-bar {
            0% { width: 0%; margin-left: 0%; }
            50% { width: 75%; margin-left: 25%; }
            100% { width: 0%; margin-left: 100%; }
          }
          .loading-animation {
            animation: loading-bar 2s ease-in-out infinite;
          }
        `}
      </style>
      <div className={cn("w-full h-1 bg-gray-200 rounded-full overflow-hidden", className)}>
        <div className="h-full bg-hackathon-yellow rounded-full loading-animation"></div>
      </div>
    </>
  );
};

export default LoadingBar;
