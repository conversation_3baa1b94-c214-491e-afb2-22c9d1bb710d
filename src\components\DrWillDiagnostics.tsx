// Componente de diagnóstico para Dr. Will
// Mostra métricas em tempo real e status do sistema

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Activity, AlertTriangle, CheckCircle, XCircle, BarChart3, Clock, Database, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { drWillMonitor } from '@/utils/drWillMonitor';
import { logger } from '@/utils/logger';

interface DiagnosticsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DrWillDiagnostics: React.FC<DiagnosticsProps> = ({ isOpen, onClose }) => {
  const [diagnostics, setDiagnostics] = useState<any>(null);
  const [healthStatus, setHealthStatus] = useState<'healthy' | 'warning' | 'critical'>('healthy');

  useEffect(() => {
    if (!isOpen) return;

    const updateDiagnostics = () => {
      const data = drWillMonitor.getDiagnostics();
      const status = drWillMonitor.getHealthStatus();
      setDiagnostics(data);
      setHealthStatus(status);
    };

    // Update immediately
    updateDiagnostics();

    // Update every 2 seconds while open
    const interval = setInterval(updateDiagnostics, 2000);

    return () => clearInterval(interval);
  }, [isOpen]);

  if (!isOpen || !diagnostics) return null;

  const getStatusIcon = () => {
    switch (healthStatus) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (healthStatus) {
      case 'healthy':
        return 'border-green-500 bg-green-50';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50';
      case 'critical':
        return 'border-red-500 bg-red-50';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl border-2 border-black max-w-4xl w-full max-h-[80vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`p-4 border-b-2 border-black ${getStatusColor()}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Activity className="h-6 w-6" />
                <h2 className="text-xl font-bold">Dr. Will Diagnostics</h2>
                {getStatusIcon()}
                <span className="text-sm font-medium capitalize">{healthStatus}</span>
              </div>
              <Button variant="outline" size="sm" onClick={onClose}>
                ✕
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Real-time system monitoring and performance metrics
            </p>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Success Rate</span>
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {diagnostics.performance.successRate}%
                </div>
              </div>

              <div className={`border-2 rounded-lg p-4 ${
                diagnostics.performance.avgDuration > 5000
                  ? 'bg-red-50 border-red-200'
                  : diagnostics.performance.avgDuration > 2000
                    ? 'bg-yellow-50 border-yellow-200'
                    : 'bg-green-50 border-green-200'
              }`}>
                <div className="flex items-center gap-2 mb-2">
                  <Clock className={`h-4 w-4 ${
                    diagnostics.performance.avgDuration > 5000
                      ? 'text-red-600'
                      : diagnostics.performance.avgDuration > 2000
                        ? 'text-yellow-600'
                        : 'text-green-600'
                  }`} />
                  <span className={`text-sm font-medium ${
                    diagnostics.performance.avgDuration > 5000
                      ? 'text-red-800'
                      : diagnostics.performance.avgDuration > 2000
                        ? 'text-yellow-800'
                        : 'text-green-800'
                  }`}>Avg Duration</span>
                </div>
                <div className={`text-2xl font-bold ${
                  diagnostics.performance.avgDuration > 5000
                    ? 'text-red-900'
                    : diagnostics.performance.avgDuration > 2000
                      ? 'text-yellow-900'
                      : 'text-green-900'
                }`}>
                  {diagnostics.performance.avgDuration}ms
                </div>
                {diagnostics.performance.avgDuration > 5000 && (
                  <div className="text-xs text-red-600 mt-1">
                    ⚠️ Edge functions cold start
                  </div>
                )}
              </div>

              <div className="bg-purple-50 border-2 border-purple-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">Operations</span>
                </div>
                <div className="text-2xl font-bold text-purple-900">
                  {diagnostics.performance.totalOperations}
                </div>
              </div>

              <div className="bg-orange-50 border-2 border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-800">Slow Ops</span>
                </div>
                <div className="text-2xl font-bold text-orange-900">
                  {diagnostics.performance.slowOperations}
                </div>
              </div>
            </div>

            {/* Error Information */}
            <div className="bg-gray-50 border-2 border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-bold mb-3 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Error Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Last Minute: <span className="font-bold">{diagnostics.errors.lastMinute}</span></p>
                  <p className="text-sm text-gray-600">Last Hour: <span className="font-bold">{diagnostics.errors.lastHour}</span></p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Recent Errors:</p>
                  {diagnostics.errors.recent.length > 0 ? (
                    <div className="space-y-1">
                      {diagnostics.errors.recent.map((error: any, index: number) => (
                        <div key={index} className="text-xs bg-red-100 border border-red-200 rounded p-2">
                          <p className="font-medium">{error.operation}</p>
                          <p className="text-red-700">{error.error}</p>
                          <p className="text-gray-500">{error.timestamp}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-green-600">No recent errors</p>
                  )}
                </div>
              </div>
            </div>

            {/* Thread Information */}
            <div className="bg-gray-50 border-2 border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-bold mb-3 flex items-center gap-2">
                <Database className="h-5 w-5 text-blue-500" />
                Thread Status
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Active Threads: <span className="font-bold">{diagnostics.threads.active}</span></p>
                  <p className="text-sm text-gray-600">Problematic: <span className="font-bold text-red-600">{diagnostics.threads.problematic.length}</span></p>
                </div>
                <div>
                  {diagnostics.threads.problematic.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">Problematic Threads:</p>
                      <div className="space-y-1">
                        {diagnostics.threads.problematic.map((thread: any, index: number) => (
                          <div key={index} className="text-xs bg-yellow-100 border border-yellow-200 rounded p-2">
                            <p className="font-medium">Thread {thread.id}</p>
                            <p>Load attempts: {thread.loadAttempts}</p>
                            <p>Errors: {thread.errors}</p>
                            <p>Last activity: {thread.lastActivity}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* System Info */}
            <div className="bg-gray-50 border-2 border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-bold mb-3">System Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p><span className="font-medium">Timestamp:</span> {new Date(diagnostics.timestamp).toLocaleString()}</p>
                  <p><span className="font-medium">Environment:</span> {process.env.NODE_ENV}</p>
                </div>
                <div>
                  <p><span className="font-medium">Logger Stats:</span></p>
                  <div className="ml-4 text-xs">
                    {(() => {
                      const stats = logger.getStats();
                      return (
                        <>
                          <p>Total logs: {stats.totalLogs}</p>
                          <p>Last minute: {stats.logsLastMinute}</p>
                          <p>Total errors: {stats.totalErrors}</p>
                          <p>Last error: {stats.lastError}</p>
                        </>
                      );
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
