export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_permissions: {
        Row: {
          action: string
          created_at: string | null
          description: string | null
          id: string
          resource: string
          updated_at: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          description?: string | null
          id?: string
          resource: string
          updated_at?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          description?: string | null
          id?: string
          resource?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      admin_role_permissions: {
        Row: {
          created_at: string | null
          id: string
          permission_id: string
          role_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          permission_id: string
          role_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          permission_id?: string
          role_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_role_permissions_permission_id_fkey"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "admin_permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "admin_role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "admin_roles"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_roles: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_system: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_system?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_system?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      admin_user_permissions: {
        Row: {
          created_at: string | null
          id: string
          resource: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          resource: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          resource?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      admin_user_roles: {
        Row: {
          created_at: string | null
          id: string
          role_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          role_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          role_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "admin_roles"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_terms_acceptance: {
        Row: {
          accepted_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          accepted_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          accepted_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      app_settings: {
        Row: {
          created_at: string | null
          description: string | null
          key: string
          updated_at: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          key: string
          updated_at?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          key?: string
          updated_at?: string | null
          value?: string
        }
        Relationships: []
      }
      cid_categoria: {
        Row: {
          capitulo_id: number | null
          descricao: string
          id: string
          uuid: string
        }
        Insert: {
          capitulo_id?: number | null
          descricao: string
          id: string
          uuid?: string
        }
        Update: {
          capitulo_id?: number | null
          descricao?: string
          id?: string
          uuid?: string
        }
        Relationships: [
          {
            foreignKeyName: "cid_categoria_capitulo_id_fkey"
            columns: ["capitulo_id"]
            isOneToOne: false
            referencedRelation: "temp_capitulos"
            referencedColumns: ["id"]
          },
        ]
      }
      cid_grupo: {
        Row: {
          capitulo_id: number | null
          cat_fim: string
          cat_inicio: string
          descricao: string
          id: number
          uuid: string
        }
        Insert: {
          capitulo_id?: number | null
          cat_fim: string
          cat_inicio: string
          descricao: string
          id: number
          uuid?: string
        }
        Update: {
          capitulo_id?: number | null
          cat_fim?: string
          cat_inicio?: string
          descricao?: string
          id?: number
          uuid?: string
        }
        Relationships: [
          {
            foreignKeyName: "cid_grupo_capitulo_id_fkey"
            columns: ["capitulo_id"]
            isOneToOne: false
            referencedRelation: "temp_capitulos"
            referencedColumns: ["id"]
          },
        ]
      }
      cid_sub_categoria: {
        Row: {
          descricao: string
          id: string
          uuid: string
        }
        Insert: {
          descricao: string
          id: string
          uuid?: string
        }
        Update: {
          descricao?: string
          id?: string
          uuid?: string
        }
        Relationships: []
      }
      exam_institutions: {
        Row: {
          created_at: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      exam_locations: {
        Row: {
          created_at: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      exam_years: {
        Row: {
          created_at: string
          id: string
          year: number
        }
        Insert: {
          created_at?: string
          id?: string
          year: number
        }
        Update: {
          created_at?: string
          id?: string
          year?: number
        }
        Relationships: []
      }
      flashcards_cards: {
        Row: {
          back: string
          back_image: string | null
          correct_reviews: number | null
          created_at: string
          current_state: string
          difficulty: number | null
          disliked_by: string[] | null
          dislikes: number | null
          extrafocus_id: string | null
          focus_id: string | null
          front: string
          front_image: string | null
          id: string
          import_count: number | null
          intervalindays: number | null
          is_shared: boolean
          last_review_date: string | null
          liked_by: string[] | null
          likes: number | null
          next_review_date: string | null
          origin_id: string | null
          rejection_reason: string | null
          retrievability: number | null
          specialty_id: string
          stability: number | null
          theme_id: string | null
          total_reviews: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          back: string
          back_image?: string | null
          correct_reviews?: number | null
          created_at?: string
          current_state?: string
          difficulty?: number | null
          disliked_by?: string[] | null
          dislikes?: number | null
          extrafocus_id?: string | null
          focus_id?: string | null
          front: string
          front_image?: string | null
          id?: string
          import_count?: number | null
          intervalindays?: number | null
          is_shared?: boolean
          last_review_date?: string | null
          liked_by?: string[] | null
          likes?: number | null
          next_review_date?: string | null
          origin_id?: string | null
          rejection_reason?: string | null
          retrievability?: number | null
          specialty_id: string
          stability?: number | null
          theme_id?: string | null
          total_reviews?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          back?: string
          back_image?: string | null
          correct_reviews?: number | null
          created_at?: string
          current_state?: string
          difficulty?: number | null
          disliked_by?: string[] | null
          dislikes?: number | null
          extrafocus_id?: string | null
          focus_id?: string | null
          front?: string
          front_image?: string | null
          id?: string
          import_count?: number | null
          intervalindays?: number | null
          is_shared?: boolean
          last_review_date?: string | null
          liked_by?: string[] | null
          likes?: number | null
          next_review_date?: string | null
          origin_id?: string | null
          rejection_reason?: string | null
          retrievability?: number | null
          specialty_id?: string
          stability?: number | null
          theme_id?: string | null
          total_reviews?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_cards_extrafocus_id_fkey"
            columns: ["extrafocus_id"]
            isOneToOne: false
            referencedRelation: "flashcards_extrafocus"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcards_cards_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "flashcards_focus"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcards_cards_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "flashcards_specialty"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcards_cards_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "flashcards_theme"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards_extrafocus: {
        Row: {
          created_at: string
          focus_id: string
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string
          focus_id: string
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string
          focus_id?: string
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_extrafocus_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "flashcards_focus"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards_focus: {
        Row: {
          created_at: string
          id: string
          name: string
          theme_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          theme_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          theme_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_focus_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "flashcards_theme"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards_reviews: {
        Row: {
          card_id: string
          correct_reviews: number | null
          created_at: string | null
          difficulty: number | null
          id: string
          intervalindays: number | null
          last_review_date: string | null
          next_review_date: string | null
          retrievability: number | null
          stability: number | null
          total_reviews: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          card_id: string
          correct_reviews?: number | null
          created_at?: string | null
          difficulty?: number | null
          id?: string
          intervalindays?: number | null
          last_review_date?: string | null
          next_review_date?: string | null
          retrievability?: number | null
          stability?: number | null
          total_reviews?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          card_id?: string
          correct_reviews?: number | null
          created_at?: string | null
          difficulty?: number | null
          id?: string
          intervalindays?: number | null
          last_review_date?: string | null
          next_review_date?: string | null
          retrievability?: number | null
          stability?: number | null
          total_reviews?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_reviews_card_id_fkey"
            columns: ["card_id"]
            isOneToOne: false
            referencedRelation: "flashcards_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards_session_cards: {
        Row: {
          card_id: string
          created_at: string
          id: string
          last_review_time: string | null
          response: string | null
          response_data: Json | null
          review_status: string
          session_id: string
        }
        Insert: {
          card_id: string
          created_at?: string
          id?: string
          last_review_time?: string | null
          response?: string | null
          response_data?: Json | null
          review_status?: string
          session_id: string
        }
        Update: {
          card_id?: string
          created_at?: string
          id?: string
          last_review_time?: string | null
          response?: string | null
          response_data?: Json | null
          review_status?: string
          session_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_session_cards_card_id_fkey"
            columns: ["card_id"]
            isOneToOne: false
            referencedRelation: "flashcards_cards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcards_session_cards_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "flashcards_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards_sessions: {
        Row: {
          cards: string[] | null
          correct_cards: number
          created_at: string
          end_time: string | null
          filters: Json | null
          id: string
          start_time: string
          status: string
          total_cards: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          cards?: string[] | null
          correct_cards?: number
          created_at?: string
          end_time?: string | null
          filters?: Json | null
          id?: string
          start_time?: string
          status?: string
          total_cards?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          cards?: string[] | null
          correct_cards?: number
          created_at?: string
          end_time?: string | null
          filters?: Json | null
          id?: string
          start_time?: string
          status?: string
          total_cards?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      flashcards_specialty: {
        Row: {
          created_at: string
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: []
      }
      flashcards_theme: {
        Row: {
          created_at: string
          id: string
          name: string
          specialty_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          specialty_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          specialty_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_theme_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "flashcards_specialty"
            referencedColumns: ["id"]
          },
        ]
      }
      focus_consolidation_groups: {
        Row: {
          created_at: string | null
          id: string
          main_focus_id: string
          metadata: Json | null
          name: string
          similarity_score: number
          status: Database["public"]["Enums"]["consolidation_status"] | null
          theme_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          main_focus_id: string
          metadata?: Json | null
          name: string
          similarity_score: number
          status?: Database["public"]["Enums"]["consolidation_status"] | null
          theme_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          main_focus_id?: string
          metadata?: Json | null
          name?: string
          similarity_score?: number
          status?: Database["public"]["Enums"]["consolidation_status"] | null
          theme_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "focus_consolidation_groups_main_focus_id_fkey"
            columns: ["main_focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "focus_consolidation_groups_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      focus_consolidation_mappings: {
        Row: {
          created_at: string | null
          focus_id: string
          group_id: string
          id: string
        }
        Insert: {
          created_at?: string | null
          focus_id: string
          group_id: string
          id?: string
        }
        Update: {
          created_at?: string | null
          focus_id?: string
          group_id?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "focus_consolidation_mappings_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "focus_consolidation_mappings_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "focus_consolidation_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      focus_name_improvements: {
        Row: {
          approved_at: string | null
          created_at: string | null
          focus_id: string
          id: string
          improved_name: string
          metadata: Json | null
          original_name: string
          status: string
          updated_at: string | null
        }
        Insert: {
          approved_at?: string | null
          created_at?: string | null
          focus_id: string
          id?: string
          improved_name: string
          metadata?: Json | null
          original_name: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          approved_at?: string | null
          created_at?: string | null
          focus_id?: string
          id?: string
          improved_name?: string
          metadata?: Json | null
          original_name?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "focus_name_improvements_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      institutions: {
        Row: {
          created_at: string
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      medevo_feedbacks: {
        Row: {
          created_at: string | null
          feedback_type: string
          id: string
          message: string
          question_id: string
          status: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          feedback_type: string
          id?: string
          message: string
          question_id: string
          status?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          feedback_type?: string
          id?: string
          message?: string
          question_id?: string
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "medevo_feedbacks_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_news: {
        Row: {
          category: string | null
          created_at: string | null
          id: string
          image_url: string | null
          link: string | null
          pub_date: string | null
          source: string | null
          summary: string
          title: string
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          link?: string | null
          pub_date?: string | null
          source?: string | null
          summary: string
          title: string
        }
        Update: {
          category?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          link?: string | null
          pub_date?: string | null
          source?: string | null
          summary?: string
          title?: string
        }
        Relationships: []
      }
      medicamentos: {
        Row: {
          alto_custo: boolean
          classe: string
          composicao: string | null
          concentracao: string
          controlado: boolean
          created_at: string
          descricao: string | null
          fabricante: string | null
          forma_farmaceutica_id: string | null
          id: string
          nome_comercial: string
          receituario: string | null
          recomendacoes_ao_paciente: string[] | null
          status: string
          tarja: string | null
          tipo: string | null
          updated_at: string
          uso_adulto: boolean | null
          uso_gestante: string | null
          uso_lactante: string | null
          uso_pediatrico: boolean | null
        }
        Insert: {
          alto_custo?: boolean
          classe: string
          composicao?: string | null
          concentracao: string
          controlado?: boolean
          created_at?: string
          descricao?: string | null
          fabricante?: string | null
          forma_farmaceutica_id?: string | null
          id?: string
          nome_comercial: string
          receituario?: string | null
          recomendacoes_ao_paciente?: string[] | null
          status?: string
          tarja?: string | null
          tipo?: string | null
          updated_at?: string
          uso_adulto?: boolean | null
          uso_gestante?: string | null
          uso_lactante?: string | null
          uso_pediatrico?: boolean | null
        }
        Update: {
          alto_custo?: boolean
          classe?: string
          composicao?: string | null
          concentracao?: string
          controlado?: boolean
          created_at?: string
          descricao?: string | null
          fabricante?: string | null
          forma_farmaceutica_id?: string | null
          id?: string
          nome_comercial?: string
          receituario?: string | null
          recomendacoes_ao_paciente?: string[] | null
          status?: string
          tarja?: string | null
          tipo?: string | null
          updated_at?: string
          uso_adulto?: boolean | null
          uso_gestante?: string | null
          uso_lactante?: string | null
          uso_pediatrico?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "medicamentos_forma_farmaceutica_id_fkey"
            columns: ["forma_farmaceutica_id"]
            isOneToOne: false
            referencedRelation: "medicamentos_forma_farmaceutica"
            referencedColumns: ["id"]
          },
        ]
      }
      medicamentos_classificacao_terapeutica: {
        Row: {
          created_at: string
          id: string
          nome: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          nome: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          nome?: string
          updated_at?: string
        }
        Relationships: []
      }
      medicamentos_classificacoes: {
        Row: {
          classificacao_id: string | null
          created_at: string
          id: string
          medicamento_id: string | null
        }
        Insert: {
          classificacao_id?: string | null
          created_at?: string
          id?: string
          medicamento_id?: string | null
        }
        Update: {
          classificacao_id?: string | null
          created_at?: string
          id?: string
          medicamento_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medicamentos_classificacoes_classificacao_id_fkey"
            columns: ["classificacao_id"]
            isOneToOne: false
            referencedRelation: "medicamentos_classificacao_terapeutica"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medicamentos_classificacoes_medicamento_id_fkey"
            columns: ["medicamento_id"]
            isOneToOne: false
            referencedRelation: "medicamentos"
            referencedColumns: ["id"]
          },
        ]
      }
      medicamentos_forma_farmaceutica: {
        Row: {
          created_at: string
          id: string
          nome: string
          plural: string
          singular: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          nome: string
          plural: string
          singular: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          nome?: string
          plural?: string
          singular?: string
          updated_at?: string
        }
        Relationships: []
      }
      medicamentos_posologia: {
        Row: {
          condicao: string
          created_at: string
          id: string
          medicamento_id: string | null
          sugerido: boolean | null
          texto: string
          texto_html: string | null
          updated_at: string
          variacao: string | null
        }
        Insert: {
          condicao: string
          created_at?: string
          id?: string
          medicamento_id?: string | null
          sugerido?: boolean | null
          texto: string
          texto_html?: string | null
          updated_at?: string
          variacao?: string | null
        }
        Update: {
          condicao?: string
          created_at?: string
          id?: string
          medicamento_id?: string | null
          sugerido?: boolean | null
          texto?: string
          texto_html?: string | null
          updated_at?: string
          variacao?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medicamentos_posologia_medicamento_id_fkey"
            columns: ["medicamento_id"]
            isOneToOne: false
            referencedRelation: "medicamentos"
            referencedColumns: ["id"]
          },
        ]
      }
      medicamentos_principio_ativo: {
        Row: {
          cas: string | null
          created_at: string
          dcb: string | null
          id: string
          nome: string
          updated_at: string
        }
        Insert: {
          cas?: string | null
          created_at?: string
          dcb?: string | null
          id?: string
          nome: string
          updated_at?: string
        }
        Update: {
          cas?: string | null
          created_at?: string
          dcb?: string | null
          id?: string
          nome?: string
          updated_at?: string
        }
        Relationships: []
      }
      medicamentos_principios_ativos: {
        Row: {
          created_at: string
          id: string
          medicamento_id: string | null
          principio_ativo_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          medicamento_id?: string | null
          principio_ativo_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          medicamento_id?: string | null
          principio_ativo_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medicamentos_principios_ativos_medicamento_id_fkey"
            columns: ["medicamento_id"]
            isOneToOne: false
            referencedRelation: "medicamentos"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medicamentos_principios_ativos_principio_ativo_id_fkey"
            columns: ["principio_ativo_id"]
            isOneToOne: false
            referencedRelation: "medicamentos_principio_ativo"
            referencedColumns: ["id"]
          },
        ]
      }
      medicamentos_via_administracao: {
        Row: {
          created_at: string
          id: string
          nome: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          nome: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          nome?: string
          updated_at?: string
        }
        Relationships: []
      }
      medicamentos_vias: {
        Row: {
          created_at: string
          id: string
          medicamento_id: string | null
          via_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          medicamento_id?: string | null
          via_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          medicamento_id?: string | null
          via_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medicamentos_vias_medicamento_id_fkey"
            columns: ["medicamento_id"]
            isOneToOne: false
            referencedRelation: "medicamentos"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medicamentos_vias_via_id_fkey"
            columns: ["via_id"]
            isOneToOne: false
            referencedRelation: "medicamentos_via_administracao"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          address: string | null
          birth_date: string | null
          cpf: string
          created_at: string
          full_name: string
          gender: string | null
          gender_identity: string | null
          id: string
          phone: string
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          birth_date?: string | null
          cpf: string
          created_at?: string
          full_name: string
          gender?: string | null
          gender_identity?: string | null
          id?: string
          phone: string
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          birth_date?: string | null
          cpf?: string
          created_at?: string
          full_name?: string
          gender?: string | null
          gender_identity?: string | null
          id?: string
          phone?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      pedbook_ai_daily_requests: {
        Row: {
          created_at: string | null
          date: string | null
          id: string
          request_count: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          date?: string | null
          id?: string
          request_count?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          date?: string | null
          id?: string
          request_count?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pedbook_blog_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          slug: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          slug: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_blog_comments: {
        Row: {
          content: string
          created_at: string | null
          id: string
          post_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          post_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          post_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_blog_comments_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "pedbook_blog_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_comments_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_comments_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "secure_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_blog_posts: {
        Row: {
          author_id: string
          category_id: string | null
          content: string
          created_at: string | null
          dislikes_count: number | null
          excerpt: string | null
          featured_image: string | null
          id: string
          likes_count: number | null
          meta_description: string | null
          meta_title: string | null
          published: boolean | null
          published_at: string | null
          slug: string
          title: string
          updated_at: string | null
          views_count: number | null
        }
        Insert: {
          author_id: string
          category_id?: string | null
          content: string
          created_at?: string | null
          dislikes_count?: number | null
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          likes_count?: number | null
          meta_description?: string | null
          meta_title?: string | null
          published?: boolean | null
          published_at?: string | null
          slug: string
          title: string
          updated_at?: string | null
          views_count?: number | null
        }
        Update: {
          author_id?: string
          category_id?: string | null
          content?: string
          created_at?: string | null
          dislikes_count?: number | null
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          likes_count?: number | null
          meta_description?: string | null
          meta_title?: string | null
          published?: boolean | null
          published_at?: string | null
          slug?: string
          title?: string
          updated_at?: string | null
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_blog_post_category"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_blog_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "secure_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_blog_posts_tags: {
        Row: {
          post_id: string
          tag_id: string
        }
        Insert: {
          post_id: string
          tag_id: string
        }
        Update: {
          post_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_blog_posts_tags_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "pedbook_blog_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_posts_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "pedbook_blog_tags"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_blog_reactions: {
        Row: {
          created_at: string | null
          id: string
          post_id: string
          reaction_type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id: string
          reaction_type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string
          reaction_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_blog_reactions_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "pedbook_blog_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_reactions_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_blog_reactions_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "secure_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_blog_tags: {
        Row: {
          created_at: string | null
          id: string
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          slug?: string
        }
        Relationships: []
      }
      pedbook_bmi_curves_female: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_bmi_curves_male: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_breastfeeding_medications: {
        Row: {
          additional_info: string | null
          alternativas_seguras: string[] | null
          compatibility_level: string
          created_at: string | null
          efeitos_no_lactente: string | null
          id: string
          name: string
          orientacoes_uso: string | null
          search_text: unknown | null
          section_id: string
          subsection_id: string | null
          updated_at: string | null
          usage_description: string
        }
        Insert: {
          additional_info?: string | null
          alternativas_seguras?: string[] | null
          compatibility_level: string
          created_at?: string | null
          efeitos_no_lactente?: string | null
          id?: string
          name: string
          orientacoes_uso?: string | null
          search_text?: unknown | null
          section_id: string
          subsection_id?: string | null
          updated_at?: string | null
          usage_description: string
        }
        Update: {
          additional_info?: string | null
          alternativas_seguras?: string[] | null
          compatibility_level?: string
          created_at?: string | null
          efeitos_no_lactente?: string | null
          id?: string
          name?: string
          orientacoes_uso?: string | null
          search_text?: unknown | null
          section_id?: string
          subsection_id?: string | null
          updated_at?: string | null
          usage_description?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_breastfeeding_medications_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "pedbook_breastfeeding_sections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_breastfeeding_medications_subsection_id_fkey"
            columns: ["subsection_id"]
            isOneToOne: false
            referencedRelation: "pedbook_breastfeeding_subsections"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_breastfeeding_sections: {
        Row: {
          created_at: string | null
          description: string | null
          display_order: number
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_order?: number
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_order?: number
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_breastfeeding_subsections: {
        Row: {
          created_at: string | null
          description: string | null
          display_order: number
          id: string
          name: string
          nesting_level: number
          parent_subsection_id: string | null
          section_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_order?: number
          id?: string
          name: string
          nesting_level?: number
          parent_subsection_id?: string | null
          section_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_order?: number
          id?: string
          name?: string
          nesting_level?: number
          parent_subsection_id?: string | null
          section_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_breastfeeding_subsections_parent_subsection_id_fkey"
            columns: ["parent_subsection_id"]
            isOneToOne: false
            referencedRelation: "pedbook_breastfeeding_subsections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_breastfeeding_subsections_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "pedbook_breastfeeding_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_chat_history: {
        Row: {
          content: string
          created_at: string | null
          id: string
          metadata: Json | null
          role: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role?: string
          user_id?: string
        }
        Relationships: []
      }
      medevo_chat_history: {
        Row: {
          content: string
          created_at: string | null
          id: string
          metadata: Json | null
          role: string
          thread_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role: string
          thread_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role?: string
          thread_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "medevo_chat_history_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      medevo_chat_threads: {
        Row: {
          created_at: string | null
          id: string
          last_message_at: string | null
          message_count: number | null
          metadata: Json | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          last_message_at?: string | null
          message_count?: number | null
          metadata?: Json | null
          title?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          last_message_at?: string | null
          message_count?: number | null
          metadata?: Json | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "medevo_chat_threads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      pedbook_conducts_categories: {
        Row: {
          color: string | null
          coming_soon: boolean | null
          created_at: string | null
          description: string | null
          display_order: number | null
          icon: string | null
          id: string
          image_url: string | null
          name: string
          slug: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          coming_soon?: boolean | null
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          image_url?: string | null
          name: string
          slug: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          coming_soon?: boolean | null
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          image_url?: string | null
          name?: string
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_conducts_content: {
        Row: {
          content: Json
          created_at: string | null
          id: string
          metadata: Json | null
          published: boolean | null
          title: string
          topic_id: string | null
          updated_at: string | null
        }
        Insert: {
          content: Json
          created_at?: string | null
          id?: string
          metadata?: Json | null
          published?: boolean | null
          title: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: Json
          created_at?: string | null
          id?: string
          metadata?: Json | null
          published?: boolean | null
          title?: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_conducts_content_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "pedbook_conducts_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_content_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "v_conducts_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_conducts_feedback: {
        Row: {
          comment: string | null
          created_at: string | null
          id: string
          rating: Database["public"]["Enums"]["feedback_rating"]
          summary_id: string | null
          summary_title: string | null
          tipo_feedback: string
          user_id: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          id?: string
          rating: Database["public"]["Enums"]["feedback_rating"]
          summary_id?: string | null
          summary_title?: string | null
          tipo_feedback?: string
          user_id?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          id?: string
          rating?: Database["public"]["Enums"]["feedback_rating"]
          summary_id?: string | null
          summary_title?: string | null
          tipo_feedback?: string
          user_id?: string | null
        }
        Relationships: []
      }
      pedbook_conducts_summaries: {
        Row: {
          content: Json
          content_structure: Json | null
          content_type: string
          created_at: string | null
          format_type: string | null
          id: string
          published: boolean | null
          sections: Json | null
          slug: string
          structure: Json | null
          title: string
          topic_id: string | null
          updated_at: string | null
        }
        Insert: {
          content: Json
          content_structure?: Json | null
          content_type?: string
          created_at?: string | null
          format_type?: string | null
          id?: string
          published?: boolean | null
          sections?: Json | null
          slug: string
          structure?: Json | null
          title: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: Json
          content_structure?: Json | null
          content_type?: string
          created_at?: string | null
          format_type?: string | null
          id?: string
          published?: boolean | null
          sections?: Json | null
          slug?: string
          structure?: Json | null
          title?: string
          topic_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_conducts_summaries_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: true
            referencedRelation: "pedbook_conducts_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_summaries_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: true
            referencedRelation: "v_conducts_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_conducts_topics: {
        Row: {
          category_id: string | null
          created_at: string | null
          description: string | null
          display_order: number | null
          icon: string | null
          id: string
          image_url: string | null
          is_subcategory: boolean | null
          name: string
          parent_id: string | null
          slug: string
          updated_at: string | null
        }
        Insert: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_subcategory?: boolean | null
          name: string
          parent_id?: string | null
          slug: string
          updated_at?: string | null
        }
        Update: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_subcategory?: boolean | null
          name?: string
          parent_id?: string | null
          slug?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_conducts_topics_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_conducts_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_topics_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "pedbook_conducts_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_topics_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "v_conducts_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_dnpm_milestones: {
        Row: {
          age_months: number
          age_type: string
          age_years: number | null
          cognition: string | null
          created_at: string | null
          id: string
          image_url: string | null
          language_communication: string | null
          motor_physical: string | null
          social_emotional: string | null
          updated_at: string | null
        }
        Insert: {
          age_months: number
          age_type: string
          age_years?: number | null
          cognition?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          language_communication?: string | null
          motor_physical?: string | null
          social_emotional?: string | null
          updated_at?: string | null
        }
        Update: {
          age_months?: number
          age_type?: string
          age_years?: number | null
          cognition?: string | null
          created_at?: string | null
          id?: string
          image_url?: string | null
          language_communication?: string | null
          motor_physical?: string | null
          social_emotional?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_drug_interaction_medications: {
        Row: {
          active_ingredient: string
          commercial_names: string | null
          created_at: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          active_ingredient: string
          commercial_names?: string | null
          created_at?: string | null
          id?: string
          updated_at?: string | null
        }
        Update: {
          active_ingredient?: string
          commercial_names?: string | null
          created_at?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_feedback_responses: {
        Row: {
          created_at: string | null
          feedback_id: string
          id: string
          message: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          feedback_id: string
          id?: string
          message: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          feedback_id?: string
          id?: string
          message?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_feedback_responses_feedback_id_fkey"
            columns: ["feedback_id"]
            isOneToOne: false
            referencedRelation: "pedbook_feedbacks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_feedback_responses_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_feedback_responses_user_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "secure_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_feedback_types: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_feedbacks: {
        Row: {
          created_at: string | null
          id: string
          message: string
          status: string
          title: string
          type_id: string
          updated_at: string | null
          user_id: string
          whatsapp: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          message: string
          status?: string
          title: string
          type_id: string
          updated_at?: string | null
          user_id: string
          whatsapp?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          message?: string
          status?: string
          title?: string
          type_id?: string
          updated_at?: string | null
          user_id?: string
          whatsapp?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_feedbacks_type_id_fkey"
            columns: ["type_id"]
            isOneToOne: false
            referencedRelation: "pedbook_feedback_types"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_formula_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_formulas: {
        Row: {
          age_range: string
          brand: string
          category_id: string | null
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          ingredients: string | null
          name: string
          nutrients: Json | null
          price: number | null
          updated_at: string | null
        }
        Insert: {
          age_range: string
          brand: string
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          ingredients?: string | null
          name: string
          nutrients?: Json | null
          price?: number | null
          updated_at?: string | null
        }
        Update: {
          age_range?: string
          brand?: string
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          ingredients?: string | null
          name?: string
          nutrients?: Json | null
          price?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_formulas_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_formula_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_growth_curve_metadata: {
        Row: {
          created_at: string | null
          data: Json
          gender: string
          id: string
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          data: Json
          gender: string
          id?: string
          type: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json
          gender?: string
          id?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_growth_curves: {
        Row: {
          created_at: string | null
          description: string | null
          gender: string
          gestational_age: string
          growth_type: string
          id: string
          image_url: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          gender: string
          gestational_age: string
          growth_type: string
          id?: string
          image_url?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          gender?: string
          gestational_age?: string
          growth_type?: string
          id?: string
          image_url?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_growth_percentiles: {
        Row: {
          age_months: number
          created_at: string | null
          curve_id: string | null
          id: string
          l_value: number | null
          m_value: number | null
          percentile: number
          s_value: number | null
          updated_at: string | null
          value: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          curve_id?: string | null
          id?: string
          l_value?: number | null
          m_value?: number | null
          percentile: number
          s_value?: number | null
          updated_at?: string | null
          value: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          curve_id?: string | null
          id?: string
          l_value?: number | null
          m_value?: number | null
          percentile?: number
          s_value?: number | null
          updated_at?: string | null
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_growth_percentiles_curve_id_fkey"
            columns: ["curve_id"]
            isOneToOne: false
            referencedRelation: "pedbook_growth_curves"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_head_circumference_curves_female: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_head_circumference_curves_male: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_height_curves_female: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_height_curves_male: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_icd10_categories: {
        Row: {
          code_range: string
          created_at: string | null
          description: string | null
          id: string
          level: number
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          code_range: string
          created_at?: string | null
          description?: string | null
          id?: string
          level?: number
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          code_range?: string
          created_at?: string | null
          description?: string | null
          id?: string
          level?: number
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_icd10_categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "pedbook_icd10_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_icd10_codes: {
        Row: {
          category_id: string | null
          code_range: string
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          category_id?: string | null
          code_range: string
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          category_id?: string | null
          code_range?: string
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_icd10_codes_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_icd10_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medication_categories: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          icon_url: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon_url?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon_url?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_medication_chat_history: {
        Row: {
          age: number | null
          created_at: string | null
          id: string
          metadata: Json | null
          query: string
          response: Json | null
          user_id: string
          weight: number | null
        }
        Insert: {
          age?: number | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          query: string
          response?: Json | null
          user_id: string
          weight?: number | null
        }
        Update: {
          age?: number | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          query?: string
          response?: Json | null
          user_id?: string
          weight?: number | null
        }
        Relationships: []
      }
      pedbook_medication_dosages: {
        Row: {
          age_group: string
          created_at: string | null
          description: string | null
          dosage_template: string
          explanation: string | null
          id: string
          max_value: number | null
          medication_id: string | null
          multiplier: number | null
          name: string
          summary: string | null
          tabs: string | null
          type: string
          updated_at: string | null
          use_case_id: string | null
        }
        Insert: {
          age_group?: string
          created_at?: string | null
          description?: string | null
          dosage_template: string
          explanation?: string | null
          id?: string
          max_value?: number | null
          medication_id?: string | null
          multiplier?: number | null
          name: string
          summary?: string | null
          tabs?: string | null
          type: string
          updated_at?: string | null
          use_case_id?: string | null
        }
        Update: {
          age_group?: string
          created_at?: string | null
          description?: string | null
          dosage_template?: string
          explanation?: string | null
          id?: string
          max_value?: number | null
          medication_id?: string | null
          multiplier?: number | null
          name?: string
          summary?: string | null
          tabs?: string | null
          type?: string
          updated_at?: string | null
          use_case_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medication_dosages_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_medication_dosages_use_case_id_fkey"
            columns: ["use_case_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medication_use_cases"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medication_instructions: {
        Row: {
          content: string
          created_at: string | null
          format_type: string | null
          id: string
          is_published: boolean
          medication_id: string
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          format_type?: string | null
          id?: string
          is_published?: boolean
          medication_id: string
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          format_type?: string | null
          id?: string
          is_published?: boolean
          medication_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medication_instructions_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medication_presentations: {
        Row: {
          administration_notes: string | null
          concentration: string | null
          created_at: string | null
          dose_calculation_template: string
          form_name: string
          form_type: string
          id: string
          max_dose_per_day: string | null
          max_dose_per_time: string | null
          medication_id: string
          min_age_months: number | null
          min_weight: number | null
          updated_at: string | null
        }
        Insert: {
          administration_notes?: string | null
          concentration?: string | null
          created_at?: string | null
          dose_calculation_template: string
          form_name: string
          form_type: string
          id?: string
          max_dose_per_day?: string | null
          max_dose_per_time?: string | null
          medication_id: string
          min_age_months?: number | null
          min_weight?: number | null
          updated_at?: string | null
        }
        Update: {
          administration_notes?: string | null
          concentration?: string | null
          created_at?: string | null
          dose_calculation_template?: string
          form_name?: string
          form_type?: string
          id?: string
          max_dose_per_day?: string | null
          max_dose_per_time?: string | null
          medication_id?: string
          min_age_months?: number | null
          min_weight?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medication_presentations_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medication_tags: {
        Row: {
          age_weight_ranges: Json | null
          created_at: string | null
          end_month: number | null
          end_weight: number | null
          id: string
          is_user_medication: boolean | null
          max_value: number | null
          medication_id: string | null
          multiplier: number | null
          name: string
          round_result: boolean | null
          start_month: number | null
          start_weight: number | null
          type: string
          updated_at: string | null
        }
        Insert: {
          age_weight_ranges?: Json | null
          created_at?: string | null
          end_month?: number | null
          end_weight?: number | null
          id?: string
          is_user_medication?: boolean | null
          max_value?: number | null
          medication_id?: string | null
          multiplier?: number | null
          name: string
          round_result?: boolean | null
          start_month?: number | null
          start_weight?: number | null
          type?: string
          updated_at?: string | null
        }
        Update: {
          age_weight_ranges?: Json | null
          created_at?: string | null
          end_month?: number | null
          end_weight?: number | null
          id?: string
          is_user_medication?: boolean | null
          max_value?: number | null
          medication_id?: string | null
          multiplier?: number | null
          name?: string
          round_result?: boolean | null
          start_month?: number | null
          start_weight?: number | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medication_tags_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medication_use_cases: {
        Row: {
          created_at: string | null
          description: string | null
          display_order: number
          display_orderr: number | null
          id: string
          medication_id: string | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_order: number
          display_orderr?: number | null
          id?: string
          medication_id?: string | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_order?: number
          display_orderr?: number | null
          id?: string
          medication_id?: string | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medication_use_cases_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medications: {
        Row: {
          brands: string | null
          category_id: string | null
          contraindications: string | null
          created_at: string | null
          description: string | null
          guidelines: string | null
          id: string
          measure_types: string | null
          name: string
          required_measures: string[] | null
          scientific_references: string | null
          slug: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          brands?: string | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description?: string | null
          guidelines?: string | null
          id?: string
          measure_types?: string | null
          name: string
          required_measures?: string[] | null
          scientific_references?: string | null
          slug: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          brands?: string | null
          category_id?: string | null
          contraindications?: string | null
          created_at?: string | null
          description?: string | null
          guidelines?: string | null
          id?: string
          measure_types?: string | null
          name?: string
          required_measures?: string[] | null
          scientific_references?: string | null
          slug?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_medications_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medication_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_medications_feedback: {
        Row: {
          comment: string | null
          created_at: string | null
          id: string
          medication_id: string
          medication_name: string | null
          rating: string
          user_id: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          id?: string
          medication_id: string
          medication_name?: string | null
          rating: string
          user_id?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          id?: string
          medication_id?: string
          medication_name?: string | null
          rating?: string
          user_id?: string | null
        }
        Relationships: []
      }
      pedbook_notes: {
        Row: {
          content: string
          created_at: string | null
          folder_id: string | null
          id: string
          is_favorite: boolean | null
          rich_content: Json | null
          title: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          folder_id?: string | null
          id?: string
          is_favorite?: boolean | null
          rich_content?: Json | null
          title?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          folder_id?: string | null
          id?: string
          is_favorite?: boolean | null
          rich_content?: Json | null
          title?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_notes_folder_id_fkey"
            columns: ["folder_id"]
            isOneToOne: false
            referencedRelation: "pedbook_notes_folders"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_notes_folders: {
        Row: {
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pedbook_notes_tags: {
        Row: {
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pedbook_notes_tags_relations: {
        Row: {
          note_id: string
          tag_id: string
        }
        Insert: {
          note_id: string
          tag_id: string
        }
        Update: {
          note_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_notes_tags_relations_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "pedbook_notes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_notes_tags_relations_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "secure_notes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_notes_tags_relations_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "pedbook_notes_tags"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_patient_vaccination_doses: {
        Row: {
          administered_at: string
          created_at: string | null
          id: string
          patient_vaccination_id: string
          updated_at: string | null
          vaccine_dose_id: string
        }
        Insert: {
          administered_at: string
          created_at?: string | null
          id?: string
          patient_vaccination_id: string
          updated_at?: string | null
          vaccine_dose_id: string
        }
        Update: {
          administered_at?: string
          created_at?: string | null
          id?: string
          patient_vaccination_id?: string
          updated_at?: string | null
          vaccine_dose_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_patient_vaccination_doses_patient_vaccination_id_fkey"
            columns: ["patient_vaccination_id"]
            isOneToOne: false
            referencedRelation: "pedbook_patient_vaccinations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_patient_vaccination_doses_vaccine_dose_id_fkey"
            columns: ["vaccine_dose_id"]
            isOneToOne: false
            referencedRelation: "pedbook_vaccine_doses"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_patient_vaccinations: {
        Row: {
          birth_date: string
          created_at: string | null
          id: string
          patient_name: string | null
          updated_at: string | null
        }
        Insert: {
          birth_date: string
          created_at?: string | null
          id?: string
          patient_name?: string | null
          updated_at?: string | null
        }
        Update: {
          birth_date?: string
          created_at?: string | null
          id?: string
          patient_name?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_prescription_categories: {
        Row: {
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pedbook_prescription_medications: {
        Row: {
          created_at: string | null
          custom_dosage_description: string | null
          custom_dosage_name: string | null
          custom_dosage_notes: string | null
          custom_medication_description: string | null
          custom_medication_name: string | null
          display_order: number
          dosage_calculation_type: string | null
          dosage_description: string | null
          dosage_id: string | null
          dosage_name: string | null
          dosage_notes: string | null
          id: string
          is_custom_medication: boolean | null
          medication_description: string | null
          medication_id: string | null
          medication_name: string | null
          notes: string | null
          prescription_id: string
          quantity: string | null
          section_title: string | null
          tag_data: Json | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          custom_dosage_description?: string | null
          custom_dosage_name?: string | null
          custom_dosage_notes?: string | null
          custom_medication_description?: string | null
          custom_medication_name?: string | null
          display_order: number
          dosage_calculation_type?: string | null
          dosage_description?: string | null
          dosage_id?: string | null
          dosage_name?: string | null
          dosage_notes?: string | null
          id?: string
          is_custom_medication?: boolean | null
          medication_description?: string | null
          medication_id?: string | null
          medication_name?: string | null
          notes?: string | null
          prescription_id: string
          quantity?: string | null
          section_title?: string | null
          tag_data?: Json | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          custom_dosage_description?: string | null
          custom_dosage_name?: string | null
          custom_dosage_notes?: string | null
          custom_medication_description?: string | null
          custom_medication_name?: string | null
          display_order?: number
          dosage_calculation_type?: string | null
          dosage_description?: string | null
          dosage_id?: string | null
          dosage_name?: string | null
          dosage_notes?: string | null
          id?: string
          is_custom_medication?: boolean | null
          medication_description?: string | null
          medication_id?: string | null
          medication_name?: string | null
          notes?: string | null
          prescription_id?: string
          quantity?: string | null
          section_title?: string | null
          tag_data?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_prescription_medications_dosage_id_fkey"
            columns: ["dosage_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medication_dosages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_prescription_medications_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_medications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_prescription_medications_prescription_id_fkey"
            columns: ["prescription_id"]
            isOneToOne: false
            referencedRelation: "pedbook_prescriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_prescription_user_reactions: {
        Row: {
          created_at: string | null
          id: string
          prescription_id: string
          reaction_type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          prescription_id: string
          reaction_type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          prescription_id?: string
          reaction_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_prescription_user_reactions_prescription_id_fkey"
            columns: ["prescription_id"]
            isOneToOne: false
            referencedRelation: "pedbook_prescriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_prescriptions: {
        Row: {
          category_id: string | null
          created_at: string | null
          description: string | null
          dislikes_count: number
          id: string
          is_public: boolean | null
          likes_count: number
          name: string
          notes: string | null
          patient_age: number | null
          patient_weight: number | null
          shared_at: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          dislikes_count?: number
          id?: string
          is_public?: boolean | null
          likes_count?: number
          name: string
          notes?: string | null
          patient_age?: number | null
          patient_weight?: number | null
          shared_at?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          dislikes_count?: number
          id?: string
          is_public?: boolean | null
          likes_count?: number
          name?: string
          notes?: string | null
          patient_age?: number | null
          patient_weight?: number | null
          shared_at?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "secure_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_prescriptions_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_prescription_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_site_instagram_posts: {
        Row: {
          created_at: string | null
          id: string
          link: string
          post_date: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          link: string
          post_date?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          link?: string
          post_date?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_site_settings: {
        Row: {
          created_at: string | null
          id: string
          key: string
          updated_at: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          key: string
          updated_at?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          value?: string
        }
        Relationships: []
      }
      pedbook_text_enhancements: {
        Row: {
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      pedbook_user_medication_dosages: {
        Row: {
          created_at: string | null
          dosage_template: string
          id: string
          medication_id: string
          name: string
          summary: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          dosage_template: string
          id?: string
          medication_id: string
          name: string
          summary?: string | null
          type?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          dosage_template?: string
          id?: string
          medication_id?: string
          name?: string
          summary?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_user_medication_dosages_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "pedbook_user_medications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_user_medication_dosages_medication_id_fkey"
            columns: ["medication_id"]
            isOneToOne: false
            referencedRelation: "secure_user_medications"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_user_medications: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pedbook_vaccine_doses: {
        Row: {
          age_recommendation: string
          created_at: string | null
          description: string | null
          dose_number: number
          dose_type: string
          id: string
          type: string
          updated_at: string | null
          vaccine_id: string
        }
        Insert: {
          age_recommendation: string
          created_at?: string | null
          description?: string | null
          dose_number: number
          dose_type?: string
          id?: string
          type?: string
          updated_at?: string | null
          vaccine_id: string
        }
        Update: {
          age_recommendation?: string
          created_at?: string | null
          description?: string | null
          dose_number?: number
          dose_type?: string
          id?: string
          type?: string
          updated_at?: string | null
          vaccine_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_vaccine_doses_vaccine_id_fkey"
            columns: ["vaccine_id"]
            isOneToOne: false
            referencedRelation: "pedbook_vaccines"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_vaccine_relationships: {
        Row: {
          child_vaccine_id: string
          created_at: string | null
          dose_number: number | null
          dose_type: string | null
          id: string
          parent_vaccine_id: string
          updated_at: string | null
        }
        Insert: {
          child_vaccine_id: string
          created_at?: string | null
          dose_number?: number | null
          dose_type?: string | null
          id?: string
          parent_vaccine_id: string
          updated_at?: string | null
        }
        Update: {
          child_vaccine_id?: string
          created_at?: string | null
          dose_number?: number | null
          dose_type?: string | null
          id?: string
          parent_vaccine_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_vaccine_relationships_child_vaccine_id_fkey"
            columns: ["child_vaccine_id"]
            isOneToOne: false
            referencedRelation: "pedbook_vaccines"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_vaccine_relationships_parent_vaccine_id_fkey"
            columns: ["parent_vaccine_id"]
            isOneToOne: false
            referencedRelation: "pedbook_vaccines"
            referencedColumns: ["id"]
          },
        ]
      }
      pedbook_vaccines: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pedbook_weight_curves_female: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      pedbook_weight_curves_male: {
        Row: {
          age_months: number
          created_at: string | null
          id: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Insert: {
          age_months: number
          created_at?: string | null
          id?: string
          l: number
          m: number
          percentile_1: number
          percentile_15: number
          percentile_25: number
          percentile_3: number
          percentile_5: number
          percentile_50: number
          percentile_75: number
          percentile_85: number
          percentile_95: number
          percentile_97: number
          percentile_99: number
          s: number
        }
        Update: {
          age_months?: number
          created_at?: string | null
          id?: string
          l?: number
          m?: number
          percentile_1?: number
          percentile_15?: number
          percentile_25?: number
          percentile_3?: number
          percentile_5?: number
          percentile_50?: number
          percentile_75?: number
          percentile_85?: number
          percentile_95?: number
          percentile_97?: number
          percentile_99?: number
          s?: number
        }
        Relationships: []
      }
      planner_achievements: {
        Row: {
          achieved_at: string | null
          achievement_type: string
          created_at: string | null
          id: string
          metadata: Json | null
          user_id: string
        }
        Insert: {
          achieved_at?: string | null
          achievement_type: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          user_id: string
        }
        Update: {
          achieved_at?: string | null
          achievement_type?: string
          created_at?: string | null
          id?: string
          metadata?: Json | null
          user_id?: string
        }
        Relationships: []
      }
      planner_events: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          end_time: string
          event_type: string
          focus_id: string | null
          id: string
          is_completed: boolean | null
          metadata: Json | null
          parent_event_id: string | null
          performance_data: Json | null
          revision_type: string | null
          specialty_id: string | null
          start_time: string
          theme_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          end_time: string
          event_type: string
          focus_id?: string | null
          id?: string
          is_completed?: boolean | null
          metadata?: Json | null
          parent_event_id?: string | null
          performance_data?: Json | null
          revision_type?: string | null
          specialty_id?: string | null
          start_time: string
          theme_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          end_time?: string
          event_type?: string
          focus_id?: string | null
          id?: string
          is_completed?: boolean | null
          metadata?: Json | null
          parent_event_id?: string | null
          performance_data?: Json | null
          revision_type?: string | null
          specialty_id?: string | null
          start_time?: string
          theme_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "planner_events_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "planner_events_parent_event_id_fkey"
            columns: ["parent_event_id"]
            isOneToOne: false
            referencedRelation: "planner_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "planner_events_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "planner_events_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      planner_revision_schedule: {
        Row: {
          created_at: string | null
          event_id: string
          id: string
          original_date: string
          performance_rate: number | null
          revision_30_completed: boolean | null
          revision_30_days: string | null
          revision_7_completed: boolean | null
          revision_7_days: string | null
          revision_90_completed: boolean | null
          revision_90_days: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          event_id: string
          id?: string
          original_date: string
          performance_rate?: number | null
          revision_30_completed?: boolean | null
          revision_30_days?: string | null
          revision_7_completed?: boolean | null
          revision_7_days?: string | null
          revision_90_completed?: boolean | null
          revision_90_days?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          event_id?: string
          id?: string
          original_date?: string
          performance_rate?: number | null
          revision_30_completed?: boolean | null
          revision_30_days?: string | null
          revision_7_completed?: boolean | null
          revision_7_days?: string | null
          revision_90_completed?: boolean | null
          revision_90_days?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "planner_revision_schedule_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "planner_events"
            referencedColumns: ["id"]
          },
        ]
      }
      planner_settings: {
        Row: {
          created_at: string | null
          daily_study_goal: number | null
          id: string
          notification_preferences: Json | null
          preferred_study_times: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          daily_study_goal?: number | null
          id?: string
          notification_preferences?: Json | null
          preferred_study_times?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          daily_study_goal?: number | null
          id?: string
          notification_preferences?: Json | null
          preferred_study_times?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      planner_study_items: {
        Row: {
          accuracy_rate: number | null
          completed: boolean | null
          created_at: string | null
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          focus_id: string | null
          id: string
          scheduled_date: string | null
          specialty_id: string | null
          theme_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          accuracy_rate?: number | null
          completed?: boolean | null
          created_at?: string | null
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          focus_id?: string | null
          id?: string
          scheduled_date?: string | null
          specialty_id?: string | null
          theme_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          accuracy_rate?: number | null
          completed?: boolean | null
          created_at?: string | null
          difficulty?: Database["public"]["Enums"]["difficulty_level"]
          focus_id?: string | null
          id?: string
          scheduled_date?: string | null
          specialty_id?: string | null
          theme_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "planner_study_items_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "planner_study_items_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "planner_study_items_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      prescricao_acessos: {
        Row: {
          created_at: string
          data_acesso: string
          id: string
          ip_address: string | null
          prescricao_id: string | null
          tipo_acesso: string
          user_agent: string | null
        }
        Insert: {
          created_at?: string
          data_acesso?: string
          id?: string
          ip_address?: string | null
          prescricao_id?: string | null
          tipo_acesso: string
          user_agent?: string | null
        }
        Update: {
          created_at?: string
          data_acesso?: string
          id?: string
          ip_address?: string | null
          prescricao_id?: string | null
          tipo_acesso?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prescricao_acessos_prescricao_id_fkey"
            columns: ["prescricao_id"]
            isOneToOne: false
            referencedRelation: "prescricoes"
            referencedColumns: ["id"]
          },
        ]
      }
      prescricao_itens: {
        Row: {
          apresentacao: string
          created_at: string
          id: string
          medicamento_id: string | null
          ordem: number
          orientacoes_adicionais: string | null
          posologia: string
          prescricao_id: string | null
          quantidade: number
          unidade_quantidade: string
          updated_at: string
          uso_continuo: boolean | null
        }
        Insert: {
          apresentacao: string
          created_at?: string
          id?: string
          medicamento_id?: string | null
          ordem?: number
          orientacoes_adicionais?: string | null
          posologia: string
          prescricao_id?: string | null
          quantidade: number
          unidade_quantidade: string
          updated_at?: string
          uso_continuo?: boolean | null
        }
        Update: {
          apresentacao?: string
          created_at?: string
          id?: string
          medicamento_id?: string | null
          ordem?: number
          orientacoes_adicionais?: string | null
          posologia?: string
          prescricao_id?: string | null
          quantidade?: number
          unidade_quantidade?: string
          updated_at?: string
          uso_continuo?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "prescricao_itens_prescricao_id_fkey"
            columns: ["prescricao_id"]
            isOneToOne: false
            referencedRelation: "prescricoes"
            referencedColumns: ["id"]
          },
        ]
      }
      prescricoes: {
        Row: {
          codigo_acesso: string
          created_at: string
          data_criacao: string
          data_validade: string | null
          id: string
          medico_id: string | null
          metadata: Json | null
          observacoes: string | null
          paciente_id: string | null
          status: string
          token: string
          updated_at: string
        }
        Insert: {
          codigo_acesso: string
          created_at?: string
          data_criacao?: string
          data_validade?: string | null
          id?: string
          medico_id?: string | null
          metadata?: Json | null
          observacoes?: string | null
          paciente_id?: string | null
          status?: string
          token: string
          updated_at?: string
        }
        Update: {
          codigo_acesso?: string
          created_at?: string
          data_criacao?: string
          data_validade?: string | null
          id?: string
          medico_id?: string | null
          metadata?: Json | null
          observacoes?: string | null
          paciente_id?: string | null
          status?: string
          token?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          birth_date: string | null
          city: string | null
          council_number: string | null
          council_state: string | null
          cpf: string | null
          created_at: string
          formation_area: string
          full_name: string
          graduation_year: string
          id: string
          is_admin: boolean
          is_professional: boolean
          is_student: boolean
          phone: string | null
          premium: boolean | null
          premium_requested: boolean | null
          premium_requested_history: boolean | null
          preparation_type: string | null
          professional_council: string | null
          professional_email: string | null
          registration_number: string | null
          specialty: string | null
          state: string | null
          theme_preference: string | null
          updated_at: string
          username: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          birth_date?: string | null
          city?: string | null
          council_number?: string | null
          council_state?: string | null
          cpf?: string | null
          created_at?: string
          formation_area: string
          full_name: string
          graduation_year: string
          id: string
          is_admin?: boolean
          is_professional?: boolean
          is_student?: boolean
          phone?: string | null
          premium?: boolean | null
          premium_requested?: boolean | null
          premium_requested_history?: boolean | null
          preparation_type?: string | null
          professional_council?: string | null
          professional_email?: string | null
          registration_number?: string | null
          specialty?: string | null
          state?: string | null
          theme_preference?: string | null
          updated_at?: string
          username?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          birth_date?: string | null
          city?: string | null
          council_number?: string | null
          council_state?: string | null
          cpf?: string | null
          created_at?: string
          formation_area?: string
          full_name?: string
          graduation_year?: string
          id?: string
          is_admin?: boolean
          is_professional?: boolean
          is_student?: boolean
          phone?: string | null
          premium?: boolean | null
          premium_requested?: boolean | null
          premium_requested_history?: boolean | null
          preparation_type?: string | null
          professional_council?: string | null
          professional_email?: string | null
          registration_number?: string | null
          specialty?: string | null
          state?: string | null
          theme_preference?: string | null
          updated_at?: string
          username?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      question_institutions: {
        Row: {
          created_at: string
          institution_id: string
          question_id: string
        }
        Insert: {
          created_at?: string
          institution_id: string
          question_id: string
        }
        Update: {
          created_at?: string
          institution_id?: string
          question_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "question_institutions_institution_id_fkey"
            columns: ["institution_id"]
            isOneToOne: false
            referencedRelation: "institutions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "question_institutions_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      question_reports: {
        Row: {
          created_at: string
          id: string
          message: string
          question_id: string
          status: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          message: string
          question_id: string
          status?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          message?: string
          question_id?: string
          status?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "question_reports_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      questions: {
        Row: {
          ai_commentary: Json | null
          alternative_comments: Json | null
          assessment_type: string | null
          comments: Json | null
          content_tags: Json | null
          correct_choice: string
          created_at: string
          disliked_by: string[] | null
          dislikes: number | null
          exam_location: string | null
          exam_year: number | null
          focus_id: string | null
          id: string
          knowledge_domain: string | null
          liked_by: string[] | null
          likes: number | null
          media_attachments: Json | null
          original_statement: string | null
          owner: string | null
          question_content: string
          question_format: Database["public"]["Enums"]["question_type"]
          question_number: number | null
          response_choices: Json | null
          specialty_id: string | null
          statistics: number[] | null
          theme_id: string | null
          updated_at: string
        }
        Insert: {
          ai_commentary?: Json | null
          alternative_comments?: Json | null
          assessment_type?: string | null
          comments?: Json | null
          content_tags?: Json | null
          correct_choice: string
          created_at?: string
          disliked_by?: string[] | null
          dislikes?: number | null
          exam_location?: string | null
          exam_year?: number | null
          focus_id?: string | null
          id?: string
          knowledge_domain?: string | null
          liked_by?: string[] | null
          likes?: number | null
          media_attachments?: Json | null
          original_statement?: string | null
          owner?: string | null
          question_content: string
          question_format?: Database["public"]["Enums"]["question_type"]
          question_number?: number | null
          response_choices?: Json | null
          specialty_id?: string | null
          statistics?: number[] | null
          theme_id?: string | null
          updated_at?: string
        }
        Update: {
          ai_commentary?: Json | null
          alternative_comments?: Json | null
          assessment_type?: string | null
          comments?: Json | null
          content_tags?: Json | null
          correct_choice?: string
          created_at?: string
          disliked_by?: string[] | null
          dislikes?: number | null
          exam_location?: string | null
          exam_year?: number | null
          focus_id?: string | null
          id?: string
          knowledge_domain?: string | null
          liked_by?: string[] | null
          likes?: number | null
          media_attachments?: Json | null
          original_statement?: string | null
          owner?: string | null
          question_content?: string
          question_format?: Database["public"]["Enums"]["question_type"]
          question_number?: number | null
          response_choices?: Json | null
          specialty_id?: string | null
          statistics?: number[] | null
          theme_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "questions_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_location_id_fkey"
            columns: ["exam_location"]
            isOneToOne: false
            referencedRelation: "exam_locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      safeweb_auth: {
        Row: {
          code_verifier: string | null
          created_at: string
          expiration_date: string | null
          id: string
          identifier_ca: string | null
          prescricao_id: string | null
          serial_number: string | null
          state: string | null
          status: string | null
          updated_at: string
        }
        Insert: {
          code_verifier?: string | null
          created_at?: string
          expiration_date?: string | null
          id?: string
          identifier_ca?: string | null
          prescricao_id?: string | null
          serial_number?: string | null
          state?: string | null
          status?: string | null
          updated_at?: string
        }
        Update: {
          code_verifier?: string | null
          created_at?: string
          expiration_date?: string | null
          id?: string
          identifier_ca?: string | null
          prescricao_id?: string | null
          serial_number?: string | null
          state?: string | null
          status?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "safeweb_auth_prescricao_id_fkey"
            columns: ["prescricao_id"]
            isOneToOne: false
            referencedRelation: "prescricoes"
            referencedColumns: ["id"]
          },
        ]
      }
      session_events: {
        Row: {
          created_at: string
          focus_id: string | null
          id: string
          question_id: string
          response: string | null
          response_status: boolean
          response_time: number
          selected_answer: number | null
          session_id: string
          specialty_id: string | null
          theme_id: string | null
        }
        Insert: {
          created_at?: string
          focus_id?: string | null
          id?: string
          question_id: string
          response?: string | null
          response_status: boolean
          response_time: number
          selected_answer?: number | null
          session_id: string
          specialty_id?: string | null
          theme_id?: string | null
        }
        Update: {
          created_at?: string
          focus_id?: string | null
          id?: string
          question_id?: string
          response?: string | null
          response_status?: boolean
          response_time?: number
          selected_answer?: number | null
          session_id?: string
          specialty_id?: string | null
          theme_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "session_events_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_events_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_events_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "study_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_events_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_events_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      site_visitor_feedbacks: {
        Row: {
          comment: string | null
          created_at: string | null
          email: string | null
          id: string
          interested_in_beta: boolean | null
          rating: number
          visitor_id: string
          whatsapp: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          interested_in_beta?: boolean | null
          rating: number
          visitor_id: string
          whatsapp?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          interested_in_beta?: boolean | null
          rating?: number
          visitor_id?: string
          whatsapp?: string | null
        }
        Relationships: []
      }
      study_categories: {
        Row: {
          created_at: string
          id: string
          name: string
          parent_id: string | null
          type: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          parent_id?: string | null
          type?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          parent_id?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      study_performance_metrics: {
        Row: {
          avg_response_time: number | null
          correct_answers: number
          created_at: string | null
          focus_id: string | null
          id: string
          last_study_date: string | null
          specialty_id: string | null
          theme_id: string | null
          total_questions: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          avg_response_time?: number | null
          correct_answers?: number
          created_at?: string | null
          focus_id?: string | null
          id?: string
          last_study_date?: string | null
          specialty_id?: string | null
          theme_id?: string | null
          total_questions?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          avg_response_time?: number | null
          correct_answers?: number
          created_at?: string | null
          focus_id?: string | null
          id?: string
          last_study_date?: string | null
          specialty_id?: string | null
          theme_id?: string | null
          total_questions?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_performance_metrics_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_performance_metrics_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_performance_metrics_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      study_schedule_items: {
        Row: {
          activity_description: string | null
          activity_type: string
          completed: boolean | null
          created_at: string | null
          day_of_week: string
          day_order: number
          difficulty: string
          duration: string
          focus_id: string | null
          focus_name: string
          id: string
          is_manual: boolean | null
          last_revision_date: string | null
          metadata: Json | null
          next_revision_date: string | null
          parent_item_id: string | null
          performance_metrics: Json | null
          priority: number
          question_count: number | null
          question_filters: Json | null
          revision_chain: string[] | null
          revision_number: number | null
          schedule_id: string
          specialty_id: string | null
          specialty_name: string
          start_time: string
          study_status: string | null
          theme_id: string | null
          theme_name: string
          topic: string
          type: string
          updated_at: string | null
          week_number: number
        }
        Insert: {
          activity_description?: string | null
          activity_type: string
          completed?: boolean | null
          created_at?: string | null
          day_of_week: string
          day_order?: number
          difficulty: string
          duration: string
          focus_id?: string | null
          focus_name: string
          id?: string
          is_manual?: boolean | null
          last_revision_date?: string | null
          metadata?: Json | null
          next_revision_date?: string | null
          parent_item_id?: string | null
          performance_metrics?: Json | null
          priority?: number
          question_count?: number | null
          question_filters?: Json | null
          revision_chain?: string[] | null
          revision_number?: number | null
          schedule_id: string
          specialty_id?: string | null
          specialty_name: string
          start_time: string
          study_status?: string | null
          theme_id?: string | null
          theme_name: string
          topic: string
          type: string
          updated_at?: string | null
          week_number?: number
        }
        Update: {
          activity_description?: string | null
          activity_type?: string
          completed?: boolean | null
          created_at?: string | null
          day_of_week?: string
          day_order?: number
          difficulty?: string
          duration?: string
          focus_id?: string | null
          focus_name?: string
          id?: string
          is_manual?: boolean | null
          last_revision_date?: string | null
          metadata?: Json | null
          next_revision_date?: string | null
          parent_item_id?: string | null
          performance_metrics?: Json | null
          priority?: number
          question_count?: number | null
          question_filters?: Json | null
          revision_chain?: string[] | null
          revision_number?: number | null
          schedule_id?: string
          specialty_id?: string | null
          specialty_name?: string
          start_time?: string
          study_status?: string | null
          theme_id?: string | null
          theme_name?: string
          topic?: string
          type?: string
          updated_at?: string | null
          week_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "study_schedule_items_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_schedule_items_parent_item_id_fkey"
            columns: ["parent_item_id"]
            isOneToOne: false
            referencedRelation: "study_schedule_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_schedule_items_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "study_schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_schedule_items_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_schedule_items_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      study_schedules: {
        Row: {
          ai_recommendations: Json | null
          created_at: string | null
          difficulty_metrics: Json | null
          id: string
          performance_data: Json | null
          performance_metrics: Json | null
          status: string
          study_preferences: Json | null
          updated_at: string | null
          user_adjustments: Json | null
          user_id: string
          week_end_date: string
          week_number: number
          week_start_date: string
        }
        Insert: {
          ai_recommendations?: Json | null
          created_at?: string | null
          difficulty_metrics?: Json | null
          id?: string
          performance_data?: Json | null
          performance_metrics?: Json | null
          status?: string
          study_preferences?: Json | null
          updated_at?: string | null
          user_adjustments?: Json | null
          user_id: string
          week_end_date: string
          week_number: number
          week_start_date: string
        }
        Update: {
          ai_recommendations?: Json | null
          created_at?: string | null
          difficulty_metrics?: Json | null
          id?: string
          performance_data?: Json | null
          performance_metrics?: Json | null
          status?: string
          study_preferences?: Json | null
          updated_at?: string | null
          user_adjustments?: Json | null
          user_id?: string
          week_end_date?: string
          week_number?: number
          week_start_date?: string
        }
        Relationships: []
      }
      study_sessions: {
        Row: {
          avg_response_time: number | null
          completed_at: string | null
          current_question_index: number | null
          expires_at: string | null
          focus_id: string | null
          id: string
          is_correct: boolean | null
          knowledge_domain: string | null
          last_activity: string | null
          question_times: Json | null
          questions: string[] | null
          session_start_time: string | null
          specialty_id: string | null
          started_at: string | null
          stats: Json | null
          status: string
          theme_id: string | null
          time_spent: string | null
          title: string | null
          total_correct: number | null
          total_incorrect: number | null
          total_questions: number | null
          total_time: number | null
          total_time_spent: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          avg_response_time?: number | null
          completed_at?: string | null
          current_question_index?: number | null
          expires_at?: string | null
          focus_id?: string | null
          id?: string
          is_correct?: boolean | null
          knowledge_domain?: string | null
          last_activity?: string | null
          question_times?: Json | null
          questions?: string[] | null
          session_start_time?: string | null
          specialty_id?: string | null
          started_at?: string | null
          stats?: Json | null
          status?: string
          theme_id?: string | null
          time_spent?: string | null
          title?: string | null
          total_correct?: number | null
          total_incorrect?: number | null
          total_questions?: number | null
          total_time?: number | null
          total_time_spent?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          avg_response_time?: number | null
          completed_at?: string | null
          current_question_index?: number | null
          expires_at?: string | null
          focus_id?: string | null
          id?: string
          is_correct?: boolean | null
          knowledge_domain?: string | null
          last_activity?: string | null
          question_times?: Json | null
          questions?: string[] | null
          session_start_time?: string | null
          specialty_id?: string | null
          started_at?: string | null
          stats?: Json | null
          status?: string
          theme_id?: string | null
          time_spent?: string | null
          title?: string | null
          total_correct?: number | null
          total_incorrect?: number | null
          total_questions?: number | null
          total_time?: number | null
          total_time_spent?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_sessions_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_sessions_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_sessions_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      temp_capitulos: {
        Row: {
          cat_fim: string
          cat_inicio: string
          descricao: string
          id: number
          uuid: string
        }
        Insert: {
          cat_fim: string
          cat_inicio: string
          descricao: string
          id: number
          uuid?: string
        }
        Update: {
          cat_fim?: string
          cat_inicio?: string
          descricao?: string
          id?: number
          uuid?: string
        }
        Relationships: []
      }
      unified_cids: {
        Row: {
          code: string
          created_at: string | null
          description: string | null
          id: string
          name: string
          parent_code: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          parent_code?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          parent_code?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      user_answers: {
        Row: {
          ai_commentary: Json | null
          created_at: string
          focus_id: string | null
          id: string
          institution_id: string | null
          is_correct: boolean
          location_id: string | null
          question_id: string
          selected_answer: number | null
          session_id: string | null
          specialty_id: string
          text_answer: string | null
          theme_id: string | null
          time_spent: number | null
          user_id: string
          year: number
        }
        Insert: {
          ai_commentary?: Json | null
          created_at?: string
          focus_id?: string | null
          id?: string
          institution_id?: string | null
          is_correct: boolean
          location_id?: string | null
          question_id: string
          selected_answer?: number | null
          session_id?: string | null
          specialty_id: string
          text_answer?: string | null
          theme_id?: string | null
          time_spent?: number | null
          user_id: string
          year: number
        }
        Update: {
          ai_commentary?: Json | null
          created_at?: string
          focus_id?: string | null
          id?: string
          institution_id?: string | null
          is_correct?: boolean
          location_id?: string | null
          question_id?: string
          selected_answer?: number | null
          session_id?: string | null
          specialty_id?: string
          text_answer?: string | null
          theme_id?: string | null
          time_spent?: number | null
          user_id?: string
          year?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_answers_focus_id_fkey"
            columns: ["focus_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_institution_id_fkey"
            columns: ["institution_id"]
            isOneToOne: false
            referencedRelation: "exam_institutions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "exam_locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "study_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_specialty_id_fkey"
            columns: ["specialty_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "study_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string | null
          filter_tutorial_completed: boolean | null
          id: string
          tutorial_completed: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          filter_tutorial_completed?: boolean | null
          id?: string
          tutorial_completed?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          filter_tutorial_completed?: boolean | null
          id?: string
          tutorial_completed?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      cid_capitulo: {
        Row: {
          cat_fim: string | null
          cat_inicio: string | null
          descricao: string | null
          id: number | null
        }
        Relationships: []
      }
      secure_chat_history: {
        Row: {
          content: string | null
          created_at: string | null
          id: string | null
          metadata: Json | null
          role: string | null
          security_message: string | null
          user_id: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string | null
          id?: string | null
          metadata?: Json | null
          role?: string | null
          security_message?: never
          user_id?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string | null
          id?: string | null
          metadata?: Json | null
          role?: string | null
          security_message?: never
          user_id?: string | null
        }
        Relationships: []
      }
      secure_medication_chat_history: {
        Row: {
          age: number | null
          created_at: string | null
          id: string | null
          metadata: Json | null
          query: string | null
          response: Json | null
          security_message: string | null
          user_id: string | null
          weight: number | null
        }
        Insert: {
          age?: number | null
          created_at?: string | null
          id?: string | null
          metadata?: Json | null
          query?: string | null
          response?: Json | null
          security_message?: never
          user_id?: string | null
          weight?: number | null
        }
        Update: {
          age?: number | null
          created_at?: string | null
          id?: string | null
          metadata?: Json | null
          query?: string | null
          response?: Json | null
          security_message?: never
          user_id?: string | null
          weight?: number | null
        }
        Relationships: []
      }
      secure_notes: {
        Row: {
          content: string | null
          created_at: string | null
          folder_id: string | null
          id: string | null
          is_favorite: boolean | null
          rich_content: Json | null
          security_message: string | null
          title: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string | null
          folder_id?: string | null
          id?: string | null
          is_favorite?: boolean | null
          rich_content?: Json | null
          security_message?: never
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string | null
          folder_id?: string | null
          id?: string | null
          is_favorite?: boolean | null
          rich_content?: Json | null
          security_message?: never
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_notes_folder_id_fkey"
            columns: ["folder_id"]
            isOneToOne: false
            referencedRelation: "pedbook_notes_folders"
            referencedColumns: ["id"]
          },
        ]
      }
      secure_patients: {
        Row: {
          address: string | null
          birth_date: string | null
          cpf: string | null
          created_at: string | null
          full_name: string | null
          gender: string | null
          gender_identity: string | null
          id: string | null
          phone: string | null
          security_message: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          address?: string | null
          birth_date?: string | null
          cpf?: string | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          gender_identity?: string | null
          id?: string | null
          phone?: string | null
          security_message?: never
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          address?: string | null
          birth_date?: string | null
          cpf?: string | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          gender_identity?: string | null
          id?: string | null
          phone?: string | null
          security_message?: never
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      secure_profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          birth_date: string | null
          city: string | null
          council_number: string | null
          council_state: string | null
          cpf: string | null
          created_at: string | null
          formation_area: string | null
          full_name: string | null
          graduation_year: string | null
          id: string | null
          is_admin: boolean | null
          is_professional: boolean | null
          is_student: boolean | null
          phone: string | null
          premium: boolean | null
          premium_requested: boolean | null
          premium_requested_history: boolean | null
          preparation_type: string | null
          professional_council: string | null
          professional_email: string | null
          registration_number: string | null
          security_message: string | null
          specialty: string | null
          state: string | null
          theme_preference: string | null
          updated_at: string | null
          username: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          birth_date?: string | null
          city?: string | null
          council_number?: string | null
          council_state?: string | null
          cpf?: string | null
          created_at?: string | null
          formation_area?: string | null
          full_name?: string | null
          graduation_year?: string | null
          id?: string | null
          is_admin?: boolean | null
          is_professional?: boolean | null
          is_student?: boolean | null
          phone?: string | null
          premium?: boolean | null
          premium_requested?: boolean | null
          premium_requested_history?: boolean | null
          preparation_type?: string | null
          professional_council?: string | null
          professional_email?: string | null
          registration_number?: string | null
          security_message?: never
          specialty?: string | null
          state?: string | null
          theme_preference?: string | null
          updated_at?: string | null
          username?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          birth_date?: string | null
          city?: string | null
          council_number?: string | null
          council_state?: string | null
          cpf?: string | null
          created_at?: string | null
          formation_area?: string | null
          full_name?: string | null
          graduation_year?: string | null
          id?: string | null
          is_admin?: boolean | null
          is_professional?: boolean | null
          is_student?: boolean | null
          phone?: string | null
          premium?: boolean | null
          premium_requested?: boolean | null
          premium_requested_history?: boolean | null
          preparation_type?: string | null
          professional_council?: string | null
          professional_email?: string | null
          registration_number?: string | null
          security_message?: never
          specialty?: string | null
          state?: string | null
          theme_preference?: string | null
          updated_at?: string | null
          username?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      secure_user_medications: {
        Row: {
          created_at: string | null
          description: string | null
          id: string | null
          name: string | null
          security_message: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string | null
          name?: string | null
          security_message?: never
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string | null
          name?: string | null
          security_message?: never
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      v_cid_categoria: {
        Row: {
          capitulo_descricao: string | null
          descricao: string | null
          id: string | null
          uuid: string | null
        }
        Relationships: []
      }
      v_cid_grupo: {
        Row: {
          capitulo_descricao: string | null
          capitulo_id: number | null
          cat_fim: string | null
          cat_inicio: string | null
          descricao: string | null
          id: number | null
          uuid: string | null
        }
        Relationships: []
      }
      v_conducts_topics: {
        Row: {
          category_id: string | null
          category_name: string | null
          category_slug: string | null
          description: string | null
          display_order: number | null
          icon: string | null
          id: string | null
          image_url: string | null
          is_subcategory: boolean | null
          name: string | null
          parent_id: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pedbook_conducts_topics_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "pedbook_conducts_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_topics_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "pedbook_conducts_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pedbook_conducts_topics_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "v_conducts_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      vw_drug_interactions: {
        Row: {
          active_ingredient: string | null
          commercial_names: string | null
          created_at: string | null
          id: string | null
          updated_at: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_cards_to_user_deck: {
        Args: { p_user_id: string; p_card_ids: string[] }
        Returns: undefined
      }
      admin_get_all_medications: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          active_ingredient: string
          commercial_names: string
          created_at: string
          updated_at: string
          total_count: number
        }[]
      }
      approve_focus_name_improvement: {
        Args:
          | { p_improvement_id: string }
          | { p_improvement_id: string; p_new_name?: string }
        Returns: undefined
      }
      calculate_difficulty: {
        Args: { accuracy_rate: number }
        Returns: Database["public"]["Enums"]["difficulty_level"]
      }
      calculate_revision_dates: {
        Args: { study_date: string }
        Returns: {
          revision_7: string
          revision_30: string
          revision_90: string
        }[]
      }
      check_and_increment_ai_requests: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      current_user_is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      debug_filters: {
        Args: {
          specialty_ids: string[]
          theme_ids: string[]
          focus_ids: string[]
          location_ids: string[]
          years: number[]
        }
        Returns: Json
      }
      delete_all_user_chat_messages: {
        Args: { user_id_param: string }
        Returns: undefined
      }
      delete_chat_messages_by_thread: {
        Args: { user_id_param: string; thread_id_param: string }
        Returns: undefined
      }
      delete_user_account: {
        Args: { user_id: string }
        Returns: undefined
      }
      document_secure_views: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      execute_consolidation: {
        Args: { p_group_id: string }
        Returns: undefined
      }
      find_similar_focuses: {
        Args: { p_theme_id: string; p_similarity_threshold?: number }
        Returns: {
          main_focus_id: string
          main_focus_name: string
          similar_focuses: Json
          similarity_score: number
          total_questions: number
        }[]
      }
      generate_consolidation_groups: {
        Args: { p_theme_id: string }
        Returns: undefined
      }
      generate_uuid_v5: {
        Args: { namespace: string; name: string }
        Returns: string
      }
      get_available_years: {
        Args: Record<PropertyKey, never>
        Returns: {
          year: number
        }[]
      }
      get_breastfeeding_structure: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_chat_messages_by_thread: {
        Args: { user_id_param: string; thread_id_param: string }
        Returns: {
          content: string
          created_at: string | null
          id: string
          metadata: Json | null
          role: string
          user_id: string
        }[]
      }
      get_chat_threads_simple: {
        Args: { user_id_param: string }
        Returns: {
          thread_id: string
        }[]
      }
      get_filtered_counts: {
        Args: {
          specialty_ids?: string[]
          theme_ids?: string[]
          focus_ids?: string[]
          years?: number[]
        }
        Returns: Json
      }
      get_filtered_flashcards: {
        Args: {
          p_user_id: string
          p_specialties?: string[]
          p_themes?: string[]
          p_focuses?: string[]
          p_extrafocuses?: string[]
          p_limit?: number
        }
        Returns: {
          card_id: string
          specialty_id: string
          theme_id: string
          focus_id: string
          extrafocus_id: string
        }[]
      }
      get_filtered_question_count: {
        Args:
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              domain_filter?: string
            }
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              question_formats?: string[]
              domain_filter?: string
            }
        Returns: Json
      }
      get_filtered_question_count_excluding_answered: {
        Args:
          | {
              p_knowledge_domain: string
              p_user_id: string
              p_specialties?: string[]
              p_themes?: string[]
              p_focuses?: string[]
              p_years?: number[]
              p_institutions?: string[]
              p_exam_locations?: string[]
            }
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              domain_filter?: string
              user_id?: string
            }
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              question_formats?: string[]
              domain_filter?: string
              user_id?: string
            }
        Returns: {
          total_count: number
        }[]
      }
      get_filtered_question_counts: {
        Args: {
          specialty_ids: string[]
          theme_ids: string[]
          focus_ids: string[]
          years: number[]
        }
        Returns: Json
      }
      get_filtered_questions: {
        Args:
          | {
              specialty_ids: string[]
              theme_ids: string[]
              focus_ids: string[]
              location_ids: string[]
              years: number[]
              question_types: string[]
              domain_filter: string
              page_number: number
              items_per_page: number
            }
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              page_number?: number
              items_per_page?: number
              domain_filter?: string
            }
          | {
              specialty_ids?: string[]
              theme_ids?: string[]
              focus_ids?: string[]
              location_ids?: string[]
              years?: number[]
              question_types?: string[]
              question_formats?: string[]
              page_number?: number
              items_per_page?: number
              domain_filter?: string
            }
        Returns: Json
      }
      get_filtered_questions_excluding_answered: {
        Args: {
          specialty_ids?: string[]
          theme_ids?: string[]
          focus_ids?: string[]
          location_ids?: string[]
          years?: number[]
          question_types?: string[]
          question_formats?: string[]
          page_number?: number
          items_per_page?: number
          domain_filter?: string
          user_id?: string
        }
        Returns: Json
      }
      get_final_main_focus: {
        Args: { p_focus_id: string }
        Returns: string
      }
      get_flashcard_counts_by_category: {
        Args: { p_user_id: string }
        Returns: {
          category_id: string
          category_type: string
          card_count: number
        }[]
      }
      get_profile_with_security: {
        Args: { profile_id: string }
        Returns: {
          id: string
          full_name: string
          formation_area: string
          graduation_year: string
          is_student: boolean
          is_professional: boolean
          is_admin: boolean
          created_at: string
          updated_at: string
          avatar_url: string
          professional_email: string
          phone: string
          registration_number: string
          premium: boolean
          theme_preference: string
          preparation_type: string
          specialty: string
          premium_requested: boolean
          premium_requested_history: boolean
          username: string
          birth_date: string
          cpf: string
          professional_council: string
          council_state: string
          council_number: string
          address: string
          city: string
          state: string
          zip_code: string
          security_message: string
        }[]
      }
      get_questions_by_ids: {
        Args: { question_ids: string[]; domain_filter?: string }
        Returns: Json
      }
      get_questions_metadata: {
        Args: Record<PropertyKey, never> | { domain_filter?: string }
        Returns: Json
      }
      get_random_questions: {
        Args: { p_quantity: number; p_domain?: string }
        Returns: {
          id: string
          statement: string
          alternatives: Json
          correct_answer: string
          specialty_id: string
          theme_id: string
          focus_id: string
        }[]
      }
      get_safeweb_code_verifier: {
        Args: { p_prescricao_id: string }
        Returns: string
      }
      get_session_statistics: {
        Args: { p_session_id: string }
        Returns: {
          avg_response_time: number
          total_correct: number
          total_incorrect: number
          by_theme: Json
          by_specialty: Json
          by_focus: Json
        }[]
      }
      get_session_statistics_v2: {
        Args: { p_session_id: string }
        Returns: {
          total_time_spent: number
          avg_response_time: number
          total_correct: number
          total_incorrect: number
          by_theme: Json
          by_specialty: Json
          by_focus: Json
        }[]
      }
      get_thread_details: {
        Args: { user_id_param: string; thread_id_param: string }
        Returns: {
          content: string
          created_at: string
        }[]
      }
      get_user_chat_threads: {
        Args: { user_id_param: string }
        Returns: {
          thread_id: string
          content: string
          created_at: string
        }[]
      }
      handle_blog_reaction: {
        Args: { p_user_id: string; p_post_id: string; p_reaction_type: string }
        Returns: undefined
      }
      handle_premium_request: {
        Args: { user_id: string; action: string }
        Returns: undefined
      }
      handle_prescription_reaction: {
        Args: {
          p_user_id: string
          p_prescription_id: string
          p_reaction_type: string
        }
        Returns: undefined
      }
      has_medication_dosage_access: {
        Args: { medication_id: string }
        Returns: boolean
      }
      has_profile_access: {
        Args: { profile_id: string }
        Returns: boolean
      }
      has_user_access: {
        Args: { record_user_id: string }
        Returns: boolean
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_super_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
      list_consolidation_suggestions: {
        Args: { p_theme_id: string }
        Returns: {
          group_id: string
          main_focus_name: string
          similar_focuses: Json
          similarity_score: number
          total_questions: number
          status: Database["public"]["Enums"]["consolidation_status"]
        }[]
      }
      manage_daily_flashcard_session: {
        Args: { p_user_id: string }
        Returns: string
      }
      reset_user_flashcard_reviews: {
        Args: { p_user_id: string }
        Returns: undefined
      }
      search_breastfeeding_medications: {
        Args: {
          search_query: string
          page_number?: number
          items_per_page?: number
        }
        Returns: Json[]
      }
      search_drug_interactions: {
        Args: { search_term: string }
        Returns: {
          active_ingredient: string
          commercial_names: string | null
          created_at: string | null
          id: string
          updated_at: string | null
        }[]
      }
      security_message: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      store_safeweb_code_verifier: {
        Args: { p_prescricao_id: string; p_code_verifier: string }
        Returns: string
      }
      update_cards_state: {
        Args: { p_card_ids: string[]; p_new_state: string }
        Returns: undefined
      }
      update_session_statistics: {
        Args: { session_id: string }
        Returns: undefined
      }
      user_has_permission: {
        Args: { user_id: string; resource: string; action: string }
        Returns: boolean
      }
      user_has_role: {
        Args: { user_id: string; role_name: string }
        Returns: boolean
      }
    }
    Enums: {
      consolidation_status: "pending" | "approved" | "rejected" | "processing"
      difficulty_level: "Fácil" | "Médio" | "Difícil"
      event_type:
        | "question_session"
        | "theory_review"
        | "quick_review"
        | "mock_exam"
        | "error_review"
        | "flashcards"
      feedback_rating: "excellent" | "good" | "regular" | "poor"
      preparation_type: "residencia" | "titulo"
      question_type:
        | "MULTIPLE_CHOICE"
        | "DISCURSIVE"
        | "TRUE_OR_FALSE"
        | "ALTERNATIVAS"
        | "DISSERTATIVA"
        | "VERDADEIRO_FALSO"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      consolidation_status: ["pending", "approved", "rejected", "processing"],
      difficulty_level: ["Fácil", "Médio", "Difícil"],
      event_type: [
        "question_session",
        "theory_review",
        "quick_review",
        "mock_exam",
        "error_review",
        "flashcards",
      ],
      feedback_rating: ["excellent", "good", "regular", "poor"],
      preparation_type: ["residencia", "titulo"],
      question_type: [
        "MULTIPLE_CHOICE",
        "DISCURSIVE",
        "TRUE_OR_FALSE",
        "ALTERNATIVAS",
        "DISSERTATIVA",
        "VERDADEIRO_FALSO",
      ],
    },
  },
} as const
