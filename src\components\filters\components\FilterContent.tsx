
import React from "react";
import { <PERSON>roll<PERSON>rea } from "@/components/ui/scroll-area";
import { FilterSearchBar } from "./FilterSearchBar";
import { useState, useEffect } from "react";
import { HierarchicalFilterSection } from "./filter-sections/HierarchicalFilterSection";
import { LocationFilterSection } from "./filter-sections/LocationFilterSection";
import { YearFilterSection } from "./filter-sections/YearFilterSection";
import { QuestionTypeFilterSection } from "./filter-sections/QuestionTypeFilterSection";
import { QuestionFormatFilterSection } from "./filter-sections/QuestionFormatFilterSection";
import { Brain, Building2, Calendar, FileText, FileQuestion } from "lucide-react";
import type { FilterOption, SelectedFilters, QuestionMetadata } from '@/types/question';
import { useDomain } from "@/hooks/useDomain";
import { Skeleton } from "@/components/ui/skeleton";

interface FilterContentProps {
  activeTab: string;
  filters: QuestionMetadata;
  selectedFilters: SelectedFilters;
  expandedItems: string[];
  questionCounts: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
  onToggleExpand: (id: string) => void;
  onToggleFilter: (id: string, type: FilterOption['type']) => void;
  searchTerm?: string; // Make searchTerm optional
  isLoading?: boolean;
}

const FilterContentComponent = ({
  activeTab,
  filters,
  selectedFilters,
  expandedItems,
  questionCounts,
  onToggleExpand,
  onToggleFilter,
  searchTerm: externalSearchTerm,
  isLoading = false
}: FilterContentProps) => {
  const [localSearchTerm, setLocalSearchTerm] = useState("");
  const { domain, isResidencia, isReady } = useDomain();

  // Log re-renderizações removido - problema de scroll resolvido

  // Effect for tab changes and domain ready state
  useEffect(() => {
    // Component ready when domain is loaded
  }, [activeTab, domain, isReady]);

  // Use external search term if provided, otherwise use local state
  const searchTerm = externalSearchTerm !== undefined ? externalSearchTerm : localSearchTerm;

  const getTabIcon = () => {
    switch (activeTab) {
      case "specialty":
        return <Brain className="h-5 w-5 text-primary" />;
      case "location":
        return <Building2 className="h-5 w-5 text-primary" />;
      case "year":
        return <Calendar className="h-5 w-5 text-primary" />;
      case "question_type":
        return <FileText className="h-5 w-5 text-primary" />;
      default:
        return null;
    }
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case "specialty":
        if (isResidencia || domain === "revalida") return "Especialidades";
        return "Temas";
      case "location":
        return "Instituições";
      case "year":
        return "Anos";
      case "question_type":
        return "Tipo de Prova";
      default:
        return "";
    }
  };

  const renderLoadingState = () => {
    return (
      <div className="space-y-4 p-2">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4 rounded-sm" />
            <Skeleton className="h-4 w-full" />
          </div>
        ))}
      </div>
    );
  };

  const renderActiveFilter = () => {
    if (isLoading) {
      return renderLoadingState();
    }

    switch (activeTab) {
      case "specialty":
        return (
          <HierarchicalFilterSection
            specialties={filters.specialties}
            themes={filters.themes}
            focuses={filters.focuses}
            selectedFilters={selectedFilters}
            expandedItems={expandedItems}
            questionCounts={questionCounts}
            onToggleExpand={onToggleExpand}
            onToggleFilter={onToggleFilter}
            searchTerm={searchTerm}
            isResidencia={isResidencia || domain === "revalida"}
          />
        );
      case "location":
        return (
          <LocationFilterSection
            locations={filters.locations}
            selectedLocations={selectedFilters.locations}
            onToggleLocation={(id) => onToggleFilter(id, "location")}
            selectedFilters={selectedFilters}
            searchTerm={searchTerm}
          />
        );
      case "year":
        return (
          <YearFilterSection
            years={filters.years}
            selectedYears={selectedFilters.years}
            onToggleYear={(year) => onToggleFilter(year, "year")}
            questionCounts={questionCounts}
            hasActiveFilters={false}
            selectedFilters={selectedFilters}
            searchTerm={searchTerm}
          />
        );
      case "question_type":
        return (
          <QuestionTypeFilterSection
            selectedTypes={selectedFilters.question_types}
            onToggleType={(type) => onToggleFilter(type, "question_type")}
            selectedFilters={selectedFilters}
            searchTerm={searchTerm}
            questionCounts={questionCounts}
          />
        );
      case "question_format":
        return (
          <QuestionFormatFilterSection
            selectedFormats={selectedFilters.question_formats}
            onToggleFormat={(format) => onToggleFilter(format, "question_format")}
            selectedFilters={selectedFilters}
            searchTerm={searchTerm}
            questionCounts={questionCounts}
          />
        );
      default:
        return null;
    }
  };

  // Don't render content until domain is ready
  if (!isReady) {
    return <div className="p-4">Carregando filtros...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Fixed search bar at top */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm pt-1 pb-3">
        <FilterSearchBar
          placeholder={`Pesquisar ${getTabTitle().toLowerCase()}...`}
          value={externalSearchTerm !== undefined ? externalSearchTerm : localSearchTerm}
          onChange={externalSearchTerm !== undefined ? () => {} : setLocalSearchTerm}
        />
      </div>

      {/* ScrollArea with a fixed height */}
      <div className="h-[400px] sm:w-full overflow-hidden">
        <ScrollArea
          className="h-full"
          data-prevent-scroll-reset="true"
          onScroll={(e) => {
            console.log('📜 [FilterContent] ScrollArea scrolled:', {
              scrollTop: e.currentTarget.scrollTop,
              timestamp: new Date().toISOString()
            });
          }}
        >
          <div className="space-y-2 pb-6">
            {renderActiveFilter()}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

// Memoizar o componente para evitar re-renderizações desnecessárias
export const FilterContent = React.memo(FilterContentComponent, (prevProps, nextProps) => {
  // Comparação personalizada para evitar re-renders desnecessários
  return (
    prevProps.activeTab === nextProps.activeTab &&
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.searchTerm === nextProps.searchTerm &&
    JSON.stringify(prevProps.selectedFilters) === JSON.stringify(nextProps.selectedFilters) &&
    JSON.stringify(prevProps.expandedItems) === JSON.stringify(nextProps.expandedItems) &&
    JSON.stringify(prevProps.questionCounts) === JSON.stringify(nextProps.questionCounts) &&
    prevProps.filters === nextProps.filters
  );
});
