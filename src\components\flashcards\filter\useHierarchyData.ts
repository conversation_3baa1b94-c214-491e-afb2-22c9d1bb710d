import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useHierarchyData = () => {
  const [specialties, setSpecialties] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    loadHierarchyData();
  }, []);

  const loadHierarchyData = async () => {
    try {
      const [
        { data: specialtiesData },
        { data: themesData },
        { data: focusesData },
        { data: extrafocusesData }
      ] = await Promise.all([
        supabase.from('flashcards_specialty').select('*'),
        supabase.from('flashcards_theme').select('*'),
        supabase.from('flashcards_focus').select('*'),
        supabase.from('flashcards_extrafocus').select('*')
      ]);

      const { data: { user } } = await supabase.auth.getUser();
      let cardCounts: { [key: string]: number } = {};

      if (user) {
        // Otimizado: Usar função RPC para obter contagens de forma eficiente
        const { data: counts } = await supabase.rpc('get_flashcard_counts_by_category', {
          p_user_id: user.id
        });

        if (counts) {
          counts.forEach((count: any) => {
            cardCounts[count.category_id] = count.card_count;
          });
        }
      }

      // Código de contagem removido - agora usamos a função RPC otimizada

      const hierarchy = specialtiesData?.map(specialty => ({
        id: specialty.id,
        name: specialty.name,
        count: cardCounts[specialty.id] || 0,
        children: themesData
          ?.filter(theme => theme.specialty_id === specialty.id)
          .map(theme => ({
            id: theme.id,
            name: theme.name,
            count: cardCounts[theme.id] || 0,
            children: focusesData
              ?.filter(focus => focus.theme_id === theme.id)
              .map(focus => ({
                id: focus.id,
                name: focus.name,
                count: cardCounts[focus.id] || 0,
                children: extrafocusesData
                  ?.filter(extrafocus => extrafocus.focus_id === focus.id)
                  .map(extrafocus => ({
                    id: extrafocus.id,
                    name: extrafocus.name,
                    count: cardCounts[extrafocus.id] || 0
                  }))
              }))
          }))
      })) || [];

      setSpecialties(hierarchy);
      setIsLoading(false);
    } catch (err: any) {
      setError(err);
      setIsLoading(false);
    }
  };

  return {
    specialties,
    isLoading,
    error
  };
};
