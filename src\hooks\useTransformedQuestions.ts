import { useMemo } from 'react';

/**
 * Função otimizada para processamento de imagens
 * Memoizada para evitar re-processamento
 */
const processQuestionImages = (images: any): string[] => {
  if (!images) return [];

  if (typeof images === 'string') {
    return [images];
  }

  if (Array.isArray(images)) {
    return images.map(img => typeof img === 'string' ? img : '').filter(Boolean);
  }

  // Se for um objeto JSON, tentar extrair URLs
  if (typeof images === 'object' && images !== null) {
    const values = Object.values(images);
    return values.filter(val => typeof val === 'string' && val.length > 0) as string[];
  }

  return [];
};

/**
 * Hook otimizado para transformação de questões
 * Evita re-computação desnecessária da transformação pesada
 */
export const useTransformedQuestions = (questionsData: any[] | null) => {
  return useMemo(() => {
    if (!questionsData?.length) return [];

    const transformedQuestions = questionsData.map((q) => {
      const imagesProcessed = processQuestionImages(q.media_attachments || q.images);

      return {
        id: q.id,
        statement: q.question_content || q.statement,
        question_content: q.question_content || q.statement,
        alternatives: Array.isArray(q.alternatives) && q.alternatives.length > 0
          ? q.alternatives.map((alt) => String(alt))
          : typeof q.alternatives === "object" && q.alternatives !== null
          ? Object.values(q.alternatives).map(String)
          : undefined,
        response_choices: Array.isArray(q.response_choices) && q.response_choices.length > 0
          ? q.response_choices.map((alt) => String(alt))
          : Array.isArray(q.alternatives) && q.alternatives.length > 0
          ? q.alternatives.map((alt) => String(alt))
          : [],
        correct_answer: parseInt(String(q.correct_choice || q.correct_answer)),
        correct_choice: parseInt(String(q.correct_choice || q.correct_answer)),
        answer_type: q.question_format || q.answer_type || 'ALTERNATIVAS',
        question_format: q.question_format || q.answer_type || 'ALTERNATIVAS',
        specialty: q.specialty,
        theme: q.theme,
        focus: q.focus,
        year: q.exam_year || q.year,
        exam_year: q.exam_year || q.year,
        location: q.location,
        institution: q.institutions?.[0]?.institution,
        statistics: Array.isArray(q.statistics) ? q.statistics.map(s => ({
          count: typeof s === 'number' ? s : 0,
          percentage: 0
        })) : [],
        alternativeComments:
          typeof q.alternative_comments === "object" &&
          q.alternative_comments !== null
            ? Object.entries(q.alternative_comments).reduce(
                (acc, [key, value]) => ({
                  ...acc,
                  [parseInt(key)]: String(value),
                }),
                {}
              )
            : {},
        comments: q.comments || [],
        owner: q.owner,
        likes: q.likes || 0,
        dislikes: q.dislikes || 0,
        liked_by: q.liked_by || [],
        disliked_by: q.disliked_by || [],
        created_at: q.created_at,
        domain: q.knowledge_domain || q.domain,
        images: imagesProcessed,
        media_attachments: imagesProcessed,
        question_type: q.assessment_type || q.question_type,
        assessment_type: q.assessment_type || q.question_type,
        question_number: q.question_number,
        ai_commentary: q.ai_commentary
      };
    });

    return transformedQuestions;
  }, [questionsData]);
};
