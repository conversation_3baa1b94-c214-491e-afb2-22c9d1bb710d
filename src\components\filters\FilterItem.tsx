
import { ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useRef, useEffect } from 'react';

interface FilterItemProps {
  item: {
    id: string;
    name: string;
    type: string;
  };
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  questionCount: {
    total: number;
    filtered: number;
  };
  hasChildren: boolean;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, type: string) => void;
  shouldScrollIntoView?: boolean;
}

export const FilterItem = ({
  item,
  level,
  isExpanded,
  isSelected,
  questionCount,
  hasChildren,
  onToggleExpand,
  onToggleSelect,
  shouldScrollIntoView = false
}: FilterItemProps) => {
  const itemRef = useRef<HTMLDivElement>(null);



  // Only scroll into view when component mounts and is selected with shouldScrollIntoView flag
  useEffect(() => {
    if (shouldScrollIntoView && itemRef.current) {
      // Use a timeout to ensure this happens after render, but don't scroll on selection
      setTimeout(() => {
        if (itemRef.current) {
          // Use a more gentle scroll that doesn't reset position completely
          itemRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
          });
        }
      }, 100);
    }
  }, [shouldScrollIntoView]); // Only depends on shouldScrollIntoView flag

  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // Prevent default browser scroll behavior
    onToggleSelect(item.id, item.type);
  };

  const handleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // Prevent default browser scroll behavior
    onToggleExpand(item.id);
  };

  // Get colors based on the hierarchy level
  const getBorderColor = () => {
    switch (level) {
      case 0: return 'border-blue-200'; // Specialties (top level)
      case 1: return 'border-green-200'; // Themes
      case 2: return 'border-amber-200'; // Focuses
      default: return 'border-gray-200';
    }
  };

  const getHoverColor = () => {
    switch (level) {
      case 0: return 'hover:bg-blue-50/60';
      case 1: return 'hover:bg-green-50/60';
      case 2: return 'hover:bg-amber-50/60';
      default: return 'hover:bg-gray-50/60';
    }
  };

  const getSelectedColor = () => {
    switch (level) {
      case 0: return 'bg-blue-50';
      case 1: return 'bg-green-50';
      case 2: return 'bg-amber-50';
      default: return 'bg-[#FEF7CD]';
    }
  };

  const getBadgeColor = () => {
    if (isSelected) return 'bg-[#FF6B00] text-white border-none';

    switch (level) {
      case 0: return 'bg-blue-100 text-blue-700 border-blue-200';
      case 1: return 'bg-green-100 text-green-700 border-green-200';
      case 2: return 'bg-amber-100 text-amber-700 border-amber-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <div
      ref={itemRef}
      className={cn(
        'flex items-center justify-between p-2 rounded-lg border transition-all duration-200',
        getBorderColor(),
        getHoverColor(),
        isSelected && getSelectedColor(),
        // On mobile, reduce left margin to prevent overflow
        level > 0 && 'sm:ml-6 ml-3'
      )}
      data-filter-item="true"
    >
      <div className="flex items-center gap-2 flex-1">
        {/* Espaço reservado para botão de expansão - sempre presente para alinhamento */}
        <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6 hover:bg-transparent"
              onClick={handleExpand}
              type="button"
            >
              <ChevronRight
                className={cn(
                  "h-4 w-4 transition-transform duration-200 text-black",
                  isExpanded && "rotate-90"
                )}
              />
            </Button>
          ) : (
            <div className="w-4 h-4" />
          )}
        </div>

        <div
          className="flex items-center gap-3 flex-1 cursor-pointer"
          onClick={handleSelect}
        >
          <div
            className={cn(
              "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center flex-shrink-0",
              isSelected
                ? "bg-[#FF6B00] border-black text-white"
                : "border-black hover:border-[#FF6B00]",
            )}
          >
            {isSelected && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                className="w-3.5 h-3.5"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            )}
          </div>

          <span className={cn(
            "text-sm transition-colors duration-200 overflow-hidden text-ellipsis",
            isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
          )}>
            {item.name}
          </span>
        </div>
      </div>

      <Badge
        variant="secondary"
        className={cn(
          "min-w-[3rem] justify-center transition-all duration-200",
          getBadgeColor()
        )}
      >
        {questionCount.total}
      </Badge>
    </div>
  );
};
