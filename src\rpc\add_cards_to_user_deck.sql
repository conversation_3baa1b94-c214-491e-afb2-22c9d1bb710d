
CREATE OR REPLACE FUNCTION public.add_cards_to_user_deck(
  p_user_id uuid,
  p_card_ids uuid[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_card_id uuid;
  v_card record;
  v_new_id uuid;
BEGIN
  -- Para cada cartão na lista
  FOREACH v_card_id IN ARRAY p_card_ids
  LOOP
    -- Verifica se o cartão existe e pega seus dados
    SELECT * INTO v_card
    FROM flashcards_cards
    WHERE id = v_card_id AND is_shared = true;
    
    -- Se o cartão for encontrado
    IF FOUND THEN
      -- Gera um novo UUID para o card copiado
      SELECT gen_random_uuid() INTO v_new_id;
      
      -- Insere o cartão no deck do usuário (com novos dados)
      INSERT INTO flashcards_cards (
        id,
        user_id,
        front,
        back,
        front_image,
        back_image,
        specialty_id,
        theme_id,
        focus_id,
        extrafocus_id,
        current_state,
        created_at,
        updated_at,
        is_shared
      ) VALUES (
        v_new_id,                -- novo ID para o card
        p_user_id,               -- ID do usuário atual
        v_card.front,            -- mantém o conteúdo frontal
        v_card.back,             -- mantém o conteúdo traseiro
        v_card.front_image,      -- mantém a imagem frontal, se houver
        v_card.back_image,       -- mantém a imagem traseira, se houver
        v_card.specialty_id,     -- mantém a especialidade
        v_card.theme_id,         -- mantém o tema
        v_card.focus_id,         -- mantém o foco
        v_card.extrafocus_id,    -- mantém o extra foco
        'available',             -- define como disponível por padrão
        NOW(),                   -- timestamp atual para criação
        NOW(),                   -- timestamp atual para atualização
        false                    -- não está compartilhado por padrão
      );
    END IF;
  END LOOP;
END;
$$;
