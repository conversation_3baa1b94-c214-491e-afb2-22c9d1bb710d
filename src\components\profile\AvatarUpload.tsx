
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import { getGravatarUrl } from "@/utils/gravatarUtils";
import { useAuth } from "@/hooks/useAuth";

interface AvatarUploadProps {
  url: string | null;
  email?: string;
  onUpload: (url: string) => void;
  size?: number;
}

export function AvatarUpload({ url, email, onUpload, size = 150 }: AvatarUploadProps) {
  const [uploading, setUploading] = useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const { user } = useAuth();

  const defaultAvatarUrl = email ? getGravatarUrl(email) : null;
  const displayUrl = url || defaultAvatarUrl;

  async function uploadAvatar(event: React.ChangeEvent<HTMLInputElement>) {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('Você precisa selecionar uma imagem para fazer upload.');
      }

      const file = event.target.files[0];
      
      // Convert to WebP
      const webpFile = await convertToWebP(file);
      
      const fileExt = 'webp';
      const fileName = `${user?.id}/avatar_${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, webpFile);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      // Update user's profile with new avatar URL
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ avatar_url: data.publicUrl })
        .eq('id', user?.id);

      if (profileUpdateError) {
        throw profileUpdateError;
      }

      onUpload(data.publicUrl);
      toast.success("Foto de perfil atualizada com sucesso!");
    } catch (error) {
      toast.error('Erro ao fazer upload da imagem');
      console.error(error);
    } finally {
      setUploading(false);
    }
  }

  async function convertToWebP(file: File): Promise<File> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = Math.min(img.width, 800);  // Limit max width
          canvas.height = Math.min(img.height, 800);  // Limit max height
          const ctx = canvas.getContext('2d');
          ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
          
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Conversion to WebP failed'));
              return;
            }
            const webpFile = new File([blob], 'avatar.webp', { type: 'image/webp' });
            resolve(webpFile);
          }, 'image/webp', 0.7);  // 70% quality
        };
        img.onerror = reject;
        img.src = e.target?.result as string;
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <Avatar className="h-32 w-32 border-2 border-black">
        <AvatarImage src={displayUrl || ''} />
        <AvatarFallback>
          {user?.user_metadata?.full_name?.charAt(0) || '?'}
        </AvatarFallback>
      </Avatar>
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          className="gap-2 border-2 border-black" 
          disabled={uploading}
          onClick={handleButtonClick}
        >
          <Upload size={16} />
          {uploading ? 'Enviando...' : 'Alterar foto'}
        </Button>
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept="image/*"
          onChange={uploadAvatar}
          disabled={uploading}
        />
      </div>
    </div>
  );
}
