
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Play, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

import { useAuth } from "@/hooks/useAuth";

export const DailyReviewButton = () => {
  const navigate = useNavigate();


  const { user } = useAuth();

  const { data: cardsCount = 0, isLoading } = useQuery({
    queryKey: ['daily-review-cards', user?.id],
    queryFn: async () => {
      if (!user) throw new Error("User not authenticated");

      const today = new Date().toISOString().split('T')[0];

      const { data: reviews, error } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .lte('next_review_date', today);

      if (error) {
        console.error('❌ [DailyReviewButton] Erro ao buscar reviews:', error);
        throw error;
      }

      return reviews?.length || 0;
    },
    enabled: !!user,
  });

  const startReviewSession = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const today = new Date().toISOString().split('T')[0];

      const { data: cardsToReview, error: cardsError } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .lte('next_review_date', today);

      if (cardsError) {
        console.error('❌ [DailyReviewButton] Erro ao buscar cards:', cardsError);
        throw cardsError;
      }

      const cardIds = cardsToReview?.map(review => review.card_id) || [];

      const { data: session, error: sessionError } = await supabase
        .from('flashcards_sessions')
        .insert({
          user_id: user.id,
          status: 'in_progress',
          total_cards: cardIds.length,
          cards: cardIds,
          correct_cards: 0
        })
        .select()
        .single();

      if (sessionError) {
        console.error('❌ [DailyReviewButton] Erro ao criar sessão:', sessionError);
        throw sessionError;
      }

      if (cardIds.length > 0) {
        const { error: updateError } = await supabase
          .from('flashcards_cards')
          .update({ current_state: 'reviewing' })
          .in('id', cardIds);

        if (updateError) {
          console.error('❌ [DailyReviewButton] Erro ao atualizar estado dos cards:', updateError);
          throw updateError;
        }
      }

      navigate(`/flashcards/session/${session.id}`);
    } catch (error) {
      console.error('❌ [DailyReviewButton] Erro ao iniciar sessão:', error);
      toast({
        title: "Erro ao iniciar sessão de revisão",
        description: "Não foi possível iniciar a sessão de revisão. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // NOVO: função para redirecionar para estudar
  const redirectToStudy = () => {
    navigate("/flashcards/study");
  };

  if (isLoading) return null;

  return (
    <div className="relative bg-white border-2 border-black rounded-xl shadow-card p-6 transform hover:-translate-y-1 transition-all">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {cardsCount > 0 ? (
            <>
              <span className="text-5xl font-black text-hackathon-red">{cardsCount}</span>
              <span className="text-xl font-medium text-gray-700">cards para revisar hoje</span>
            </>
          ) : (
            <span className="text-xl text-gray-600">
              Hoje você tem 0 cards para revisar, que tal iniciar uma nova sessão?
            </span>
          )}
        </div>
        {cardsCount > 0 ? (
          <Button
            onClick={startReviewSession}
            variant="hackRed"
            size="hack"
            className="shadow-card-sm"
          >
            <Play className="w-5 h-5 mr-2" />
            Revisar Agora
          </Button>
        ) : (
          <Button
            variant="hackYellow"
            size="hack"
            className="shadow-card-sm"
            onClick={redirectToStudy}
          >
            <Plus className="w-5 h-5 mr-2" />
            Nova Sessão
          </Button>
        )}
      </div>
    </div>
  );
};
