
export const processHtmlContent = (content: string): string => {
  if (!content) return '';

  // Normalizar as quebras de linha
  let processedContent = content
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n');

  // Converter HTML tags em quebras de linha
  processedContent = processedContent
    .replace(/<div><br\/?><\/div>/g, '\n')
    .replace(/<br\/?>/g, '\n')
    .replace(/<div>/g, '')
    .replace(/<\/div>/g, '\n')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '\n');
  
  // Remover outras tags HTML
  processedContent = processedContent.replace(/<[^>]+>/g, '');

  // Tratar padrões específicos de questões V/F
  processedContent = processedContent
    .replace(/\(\s*\)/g, '( )')
    .replace(/\(\s*V\s*\)/g, '(V)')
    .replace(/\(\s*F\s*\)/g, '(F)');

  // Adicionar quebras de linha apenas após frases completas
  processedContent = processedContent
    // Normalizar espaços múltiplos
    .replace(/\s+/g, ' ')
    
    // Adicionar quebras de linha após pontos que indicam fim de frases
    .replace(/\.\s+([A-Z])/g, '.\n$1')
    
    // Adicionar quebras após dois-pontos seguidos de títulos ou subtítulos
    .replace(/:\s+(Exame físico|Diagnóstico|Exames|Abdome|Ausculta|Sinais vitais|Membros)/g, ':\n$1');

  // Remover espaços no início e fim
  processedContent = processedContent.trim();

  // Preservar quebras de linha duplas e remover quebras extras
  processedContent = processedContent
    .replace(/\n{3,}/g, '\n\n')  // Mais de 2 quebras viram duplas
    .replace(/^\s+|\s+$/g, '');  // Remove espaços no início e fim

  return processedContent;
};
