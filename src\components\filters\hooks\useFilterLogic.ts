import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { SelectedFilters } from "@/types/question";

export const useFilterLogic = (
  selectedFilters: SelectedFilters,
  setSelectedFilters: (filters: SelectedFilters) => void,
  setQuestions: (questions: any[]) => void
) => {
  const { toast } = useToast();
  const [availableFilters, setAvailableFilters] = useState({
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: []
  });
  const [questionCounts, setQuestionCounts] = useState<{[key: string]: number}>({});

  useEffect(() => {
    loadFilters();
    loadQuestionCounts();
  }, []);

  const loadQuestionCounts = async () => {
    try {
      const { data: questions, error } = await supabase
        .from('questions')
        .select('specialty_id, theme_id, focus_id')
        .limit(1000);

      if (error) throw error;

      const counts: {[key: string]: number} = {};

      questions?.forEach(question => {
        if (question.specialty_id) {
          counts[question.specialty_id] = (counts[question.specialty_id] || 0) + 1;
        }
        if (question.theme_id) {
          counts[question.theme_id] = (counts[question.theme_id] || 0) + 1;
        }
        if (question.focus_id) {
          counts[question.focus_id] = (counts[question.focus_id] || 0) + 1;
        }
      });

      setQuestionCounts(counts);
    } catch (error: any) {
      toast({
        title: "Erro ao carregar contagem de questões",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const loadFilters = async () => {
    try {
      const [categoriesResponse, locationsResponse, questionsResponse] = await Promise.all([
        supabase.from('study_categories').select('*').order('name'),
        supabase.from('exam_locations').select('*').order('name'),
        supabase.from('questions').select('year').limit(1000)
      ]);

      if (categoriesResponse.error) throw categoriesResponse.error;
      if (locationsResponse.error) throw locationsResponse.error;
      if (questionsResponse.error) throw questionsResponse.error;

      const specialties = categoriesResponse.data.filter(cat => cat.type === 'specialty');
      const themes = categoriesResponse.data.filter(cat => cat.type === 'theme');
      const focuses = categoriesResponse.data.filter(cat => cat.type === 'focus');
      const uniqueYears = [...new Set(questionsResponse.data.map(q => q.year))];

      setAvailableFilters({
        specialties,
        themes,
        focuses,
        locations: locationsResponse.data,
        years: uniqueYears.sort((a, b) => b - a)
      });
    } catch (error: any) {
      toast({
        title: "Erro ao carregar filtros",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleApplyFilters = async () => {
    try {
      let query = supabase
        .from('questions')
        .select(`
          *,
          specialty:categories!questions_specialty_id_fkey(id, name),
          theme:categories!questions_theme_id_fkey(id, name),
          focus:categories!questions_focus_id_fkey(id, name),
          location:exam_locations(id, name)
        `)
        .limit(100); // Otimizado: reduzido para 100 questões por vez

      if (selectedFilters.specialties.length > 0) {
        query = query.in('specialty_id', selectedFilters.specialties);
      }
      if (selectedFilters.themes.length > 0) {
        query = query.in('theme_id', selectedFilters.themes);
      }
      if (selectedFilters.focuses.length > 0) {
        query = query.in('focus_id', selectedFilters.focuses);
      }
      if (selectedFilters.locations.length > 0) {
        query = query.in('location_id', selectedFilters.locations);
      }
      if (selectedFilters.years.length > 0) {
        query = query.in('year', selectedFilters.years.map(Number));
      }

      const { data, error } = await query;

      if (error) throw error;

      setQuestions(data || []);

      if (!data || data.length === 0) {
        // No questions found with applied filters
      }
    } catch (error: any) {
      // Error fetching questions
    }
  };

  return {
    availableFilters,
    handleApplyFilters,
    questionCounts
  };
};