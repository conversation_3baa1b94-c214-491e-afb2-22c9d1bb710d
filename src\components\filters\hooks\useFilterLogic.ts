import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { SelectedFilters } from "@/types/question";

export const useFilterLogic = (
  selectedFilters: SelectedFilters,
  setSelectedFilters: (filters: SelectedFilters) => void,
  setQuestions: (questions: any[]) => void
) => {
  const { toast } = useToast();
  const [availableFilters, setAvailableFilters] = useState({
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: []
  });
  const [questionCounts, setQuestionCounts] = useState<{[key: string]: number}>({});

  useEffect(() => {
    loadFilters();
    loadQuestionCounts();
  }, []);

  const loadQuestionCounts = async () => {
    try {
      const { data: questions, error } = await supabase
        .from('questions')
        .select('specialty_id, theme_id, focus_id')
        .limit(1000);

      if (error) throw error;

      const counts: {[key: string]: number} = {};

      questions?.forEach(question => {
        if (question.specialty_id) {
          counts[question.specialty_id] = (counts[question.specialty_id] || 0) + 1;
        }
        if (question.theme_id) {
          counts[question.theme_id] = (counts[question.theme_id] || 0) + 1;
        }
        if (question.focus_id) {
          counts[question.focus_id] = (counts[question.focus_id] || 0) + 1;
        }
      });

      setQuestionCounts(counts);
    } catch (error: any) {
      toast({
        title: "Erro ao carregar contagem de questões",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const loadFilters = async () => {
    try {
      const [categoriesResponse, locationsResponse, questionsResponse] = await Promise.all([
        supabase.from('study_categories').select('*').order('name'),
        supabase.from('exam_locations').select('*').order('name'),
        supabase.from('questions').select('year').limit(1000)
      ]);

      if (categoriesResponse.error) throw categoriesResponse.error;
      if (locationsResponse.error) throw locationsResponse.error;
      if (questionsResponse.error) throw questionsResponse.error;

      const specialties = categoriesResponse.data.filter(cat => cat.type === 'specialty');
      const themes = categoriesResponse.data.filter(cat => cat.type === 'theme');
      const focuses = categoriesResponse.data.filter(cat => cat.type === 'focus');
      const uniqueYears = [...new Set(questionsResponse.data.map(q => q.year))];

      setAvailableFilters({
        specialties,
        themes,
        focuses,
        locations: locationsResponse.data,
        years: uniqueYears.sort((a, b) => b - a)
      });
    } catch (error: any) {
      toast({
        title: "Erro ao carregar filtros",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleApplyFilters = async () => {
    try {
      // ✅ OTIMIZAÇÃO AGRESSIVA: Usar RPC em vez de joins pesados
      const rpcParams = {
        specialty_ids: selectedFilters.specialties || [],
        theme_ids: selectedFilters.themes || [],
        focus_ids: selectedFilters.focuses || [],
        location_ids: selectedFilters.locations || [],
        years: (selectedFilters.years || []).map(Number),
        question_types: selectedFilters.question_types || [],
        question_formats: selectedFilters.question_formats || [],
        page_number: 1,
        items_per_page: 100,
        domain_filter: 'residencia' // Assumir residência por padrão
      };

      const { data, error } = await supabase.rpc('get_filtered_questions', rpcParams);

      if (error) {
        throw error;
      }

      setQuestions(data?.questions || []);

      if (!data || data.length === 0) {
        // No questions found with applied filters
      }
    } catch (error: any) {
      // Error fetching questions
    }
  };

  return {
    availableFilters,
    handleApplyFilters,
    questionCounts
  };
};