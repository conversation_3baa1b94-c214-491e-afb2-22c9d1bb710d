import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Target,
  Building2,
  Clock,
  Settings,
  CheckCircle,
  AlertCircle,
  Shuffle
} from 'lucide-react';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import { useNavigate } from 'react-router-dom';

export const UserPreferencesDisplay: React.FC = () => {
  const { preferences, isLoading } = useStudyPreferences();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <Card className="border-2 border-gray-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">Carr<PERSON><PERSON> preferências...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!preferences?.preferences_completed) {
    return (
      <Card className="border-2 border-orange-200 bg-orange-50">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-orange-800">Preferências não configuradas</h3>
                <p className="text-sm text-orange-700">
                  Configure suas preferências para cronogramas mais personalizados ou gere um cronograma aleatório
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {/* Botão de configurar */}
              <Button
                onClick={() => navigate('/settings')}
                size="sm"
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configurar Preferências
              </Button>

              {/* Ou gerar aleatório */}
              <div className="space-y-2">
                <p className="text-xs font-medium text-orange-700 text-center">
                  Ou gere um cronograma sem configurar:
                </p>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-orange-300 text-orange-700 hover:bg-orange-100"
                  onClick={() => {
                    const event = new CustomEvent('generateRandomSchedule');
                    window.dispatchEvent(event);
                  }}
                >
                  <Shuffle className="h-4 w-4 mr-2" />
                  Gerar Cronograma Aleatório
                </Button>

                <p className="text-xs text-orange-600 text-center">
                  Distribui todos os temas de forma equilibrada
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-2 border-green-200 bg-green-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-green-800">
          <CheckCircle className="h-5 w-5" />
          Suas Preferências de Estudo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        
        {/* Especialidade */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-full">
            <Target className="h-4 w-4 text-blue-600" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-700">Especialidade Desejada</p>
            <p className="text-sm text-gray-600">
              {preferences.target_specialty || 'Ainda não definida'}
            </p>
          </div>
        </div>

        {/* Instituições */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-full">
            <Building2 className="h-4 w-4 text-green-600" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-700">Instituições Alvo</p>
            {preferences.target_institutions_unknown ? (
              <p className="text-sm text-gray-600">Qualquer instituição</p>
            ) : preferences.target_institutions.length > 0 ? (
              <div className="flex flex-wrap gap-1 mt-1">
                {preferences.target_institutions.slice(0, 3).map((institution) => (
                  <Badge key={institution.id} variant="secondary" className="text-xs">
                    {institution.name}
                  </Badge>
                ))}
                {preferences.target_institutions.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{preferences.target_institutions.length - 3} mais
                  </Badge>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-600">Nenhuma selecionada</p>
            )}
          </div>
        </div>

        {/* Tempo de Estudo */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 rounded-full">
            <Clock className="h-4 w-4 text-purple-600" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-700">Tempo de Estudo</p>
            <p className="text-sm text-gray-600">
              {preferences.study_months} {preferences.study_months === 1 ? 'mês' : 'meses'}
            </p>
          </div>
        </div>

        {/* Botões de ação */}
        <div className="pt-3 border-t border-green-200 space-y-3">
          {/* Botão de editar */}
          <Button
            onClick={() => navigate('/settings')}
            variant="outline"
            size="sm"
            className="w-full border-green-300 text-green-700 hover:bg-green-100"
          >
            <Settings className="h-4 w-4 mr-2" />
            Editar Preferências
          </Button>

          {/* Informação sobre modos de geração */}
          <div className="space-y-2">
            <p className="text-xs font-medium text-gray-700 text-center">
              Suas preferências serão usadas no cronograma inteligente
            </p>

            <div className="text-xs text-gray-600 space-y-1 bg-blue-50 p-2 rounded-lg border border-blue-200">
              <p><strong>💡 Cronograma Inteligente:</strong> Prioriza temas que mais aparecem nas suas instituições alvo</p>
              <p><strong>🎲 Modo Aleatório:</strong> Disponível nas opções avançadas de geração</p>
            </div>
          </div>
        </div>

        {/* Informação sobre cronograma personalizado */}
        {!preferences.target_institutions_unknown && preferences.target_institutions.length > 0 && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs text-blue-700">
              💡 <strong>Cronograma Inteligente:</strong> Seus cronogramas serão baseados na prevalência 
              de temas nas instituições selecionadas, priorizando o que mais aparece nas provas.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
