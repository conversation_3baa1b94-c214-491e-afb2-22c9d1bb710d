import { useMemo, useCallback, useRef, useEffect, useState } from 'react';

/**
 * Implementação própria de debounce para evitar dependência do lodash
 */
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = { trailing: true }
): T & { cancel: () => void } => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastCallTime: number | null = null;

  const debounced = (...args: Parameters<T>) => {
    const now = Date.now();

    if (options.leading && (!lastCallTime || now - lastCallTime >= delay)) {
      func(...args);
      lastCallTime = now;
      return;
    }

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (options.trailing !== false) {
      timeoutId = setTimeout(() => {
        func(...args);
        lastCallTime = Date.now();
        timeoutId = null;
      }, delay);
    }
  };

  debounced.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    lastCallTime = null;
  };

  return debounced as T & { cancel: () => void };
};

/**
 * Hook para memoização inteligente de cálculos pesados
 */
export const useHeavyComputation = <T, R>(
  computeFn: (data: T) => R,
  data: T,
  dependencies: any[] = []
): R => {
  return useMemo(() => {
    const startTime = performance.now();
    const result = computeFn(data);
    const endTime = performance.now();
    

    
    return result;
  }, [data, ...dependencies]);
};

/**
 * Hook para debounce otimizado com cleanup automático
 */
export const useOptimizedDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = { trailing: true }
): T => {
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  const debouncedFn = useMemo(
    () => debounce(
      (...args: Parameters<T>) => callbackRef.current(...args),
      delay,
      options
    ),
    [delay, options.leading, options.trailing]
  );

  useEffect(() => {
    return () => {
      debouncedFn.cancel();
    };
  }, [debouncedFn]);

  return debouncedFn as T;
};

/**
 * Hook para throttling de eventos de scroll/resize
 */
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());
  
  return useCallback((...args: Parameters<T>) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]) as T;
};

/**
 * Hook para lazy loading de componentes pesados
 */
export const useLazyComponent = <T>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadComponent = useCallback(async () => {
    if (Component || loading) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const module = await importFn();
      setComponent(module.default);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [Component, loading, importFn]);

  return { Component, loading, error, loadComponent };
};

/**
 * Hook para otimização de listas grandes com virtualização
 */
export const useVirtualizedList = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
      .map((item, index) => ({
        item,
        index: visibleRange.startIndex + index,
        top: (visibleRange.startIndex + index) * itemHeight
      }));
  }, [items, visibleRange, itemHeight]);

  const totalHeight = items.length * itemHeight;

  return {
    visibleItems,
    totalHeight,
    setScrollTop,
    visibleRange
  };
};

/**
 * Utilitário para memoização de objetos complexos
 */
export const deepMemoize = <T>(obj: T): T => {
  const cache = new WeakMap();
  
  const memoized = (target: any): any => {
    if (cache.has(target)) {
      return cache.get(target);
    }
    
    if (typeof target !== 'object' || target === null) {
      return target;
    }
    
    const result = Array.isArray(target) ? [] : {};
    cache.set(target, result);
    
    for (const key in target) {
      if (target.hasOwnProperty(key)) {
        result[key] = memoized(target[key]);
      }
    }
    
    return result;
  };
  
  return memoized(obj);
};

/**
 * Hook para monitoramento de performance de componentes
 */
export const useComponentPerformance = (componentName: string) => {
  const renderCount = useRef(0);
  const renderTimes = useRef<number[]>([]);
  const startTime = useRef<number>(0);

  useEffect(() => {
    startTime.current = performance.now();
    renderCount.current++;
  });

  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;
    
    renderTimes.current.push(renderTime);
    
    // Manter apenas os últimos 10 renders
    if (renderTimes.current.length > 10) {
      renderTimes.current = renderTimes.current.slice(-10);
    }
    

  });

  const getAverageRenderTime = useCallback(() => {
    if (renderTimes.current.length === 0) return 0;
    const sum = renderTimes.current.reduce((a, b) => a + b, 0);
    return sum / renderTimes.current.length;
  }, []);

  return {
    renderCount: renderCount.current,
    averageRenderTime: getAverageRenderTime(),
    lastRenderTime: renderTimes.current[renderTimes.current.length - 1] || 0
  };
};

/**
 * Hook para otimização de event listeners
 */
export const useOptimizedEventListener = (
  eventName: string,
  handler: (event: Event) => void,
  element: EventTarget | null = window,
  options: AddEventListenerOptions = {}
) => {
  const savedHandler = useRef(handler);
  savedHandler.current = handler;

  useEffect(() => {
    if (!element) return;

    const eventListener = (event: Event) => savedHandler.current(event);
    
    element.addEventListener(eventName, eventListener, {
      passive: true,
      ...options
    });

    return () => {
      element.removeEventListener(eventName, eventListener);
    };
  }, [eventName, element, options.capture, options.once, options.passive]);
};

/**
 * Utilitário para batch de atualizações de estado
 */
export const batchStateUpdates = (updates: (() => void)[]) => {
  // React 18+ automaticamente faz batch, mas para versões anteriores:
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      updates.forEach(update => update());
    });
  } else {
    setTimeout(() => {
      updates.forEach(update => update());
    }, 0);
  }
};
