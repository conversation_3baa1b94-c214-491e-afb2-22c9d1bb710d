import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FlashcardItem } from "./FlashcardItem";
import type { FlashcardWithHierarchy } from "@/types/flashcard";

interface PendingFlashcardsListProps {
  cards: FlashcardWithHierarchy[];
  onApprove: (cardId: string) => Promise<void>;
  onReject: (cardId: string, reason: string) => Promise<void>;
  onDelete: (cardId: string) => Promise<void>;
}

export const PendingFlashcardsList = ({
  cards,
  onApprove,
  onReject,
  onDelete,
}: PendingFlashcardsListProps) => {
  const [rejectionReason, setRejectionReason] = useState("");

  return (
    <div className="space-y-4">
      {cards.map((card) => (
        <div key={card.id} className="border p-4 rounded-lg space-y-4">
          <FlashcardItem 
            card={card}
            onDelete={onDelete}
          />
          
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="rejectionReason">Motivo da Rejeição</Label>
              <Input
                id="rejectionReason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Informe o motivo caso rejeite o flashcard"
              />
            </div>
            <Button
              onClick={() => onApprove(card.id)}
              className="bg-green-500 hover:bg-green-600"
            >
              Aprovar
            </Button>
            <Button
              onClick={() => {
                onReject(card.id, rejectionReason);
                setRejectionReason("");
              }}
              variant="destructive"
            >
              Rejeitar
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};