
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request: Request): string => {
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }

  return 'https://medevo.com.br'; // fallback
};

const getCorsHeaders = (request: Request) => ({
  "Access-Control-Allow-Origin": getAllowedOrigin(request),
  "Access-Control-Allow-Headers": "authorization, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Credentials": "true",
});

const openAIApiKey = Deno.env.get("OPENAI_API_KEY");

serve(async (req) => {
  const corsHeaders = getCorsHeaders(req);

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { status: 204, headers: corsHeaders });
  }

  try {
    const { specialty, theme, focus, statement, userAnswer } = await req.json();

    console.log("[discursive-ai-analysis] Processing request:", {
      specialty: specialty || "Not provided",
      theme: theme || "Not provided",
      focus: focus || "Not provided",
      statementLength: statement?.length || 0,
      userAnswerLength: userAnswer?.length || 0
    });

    if (!userAnswer || !statement) {
      throw new Error("Missing required data: userAnswer or statement");
    }

    const systemPrompt = `
Você é um especialista em ${specialty || "Medicina"}.
${theme ? `Área específica: ${theme}` : ""}
${focus ? `Foco de estudo: ${focus}` : ""}

Sua tarefa é avaliar a resposta dissertativa do estudante de medicina.

CRITÉRIOS DE AVALIAÇÃO:
- CORRETA: Se a resposta aborda os pontos principais corretos, mesmo que incompleta ou com pequenos erros de detalhamento
- INCORRETA: Apenas se está fundamentalmente errada, completamente fora do tema, ou muito incompleta nos pontos essenciais

INSTRUÇÕES:
1. Forneça uma resposta modelo completa e didática
2. Avalie a resposta do usuário com critério justo e educativo
3. Se a resposta contém os elementos essenciais corretos, marque como CORRETA
4. Foque na correção conceitual, não em detalhes menores

Responda APENAS em formato JSON válido:
{
  "ai_answer": "resposta modelo completa do especialista",
  "feedback": "análise educativa e construtiva da resposta do usuário",
  "is_correct": true/false,
  "score_percentage": 0-100,
  "main_points_covered": ["pontos que o usuário abordou corretamente"],
  "missing_points": ["pontos importantes que faltaram"]
}

IMPORTANTE: Seja justo e educativo. O objetivo é ensinar, não reprovar desnecessariamente.
`;

    const userPrompt = `
Questão:
${statement}

Resposta do usuário:
${userAnswer}

1) Primeiro, apresente sua resposta ideal de especialista.
2) Em seguida, faça uma análise justificada e respeitosa sobre a resposta do usuário, destacando pontos positivos, faltantes e possíveis melhorias, usando de 3 a 8 linhas.
`;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${openAIApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        temperature: 0.2,
        max_tokens: 700,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API error:", errorText);
      throw new Error("Serviço de análise temporariamente indisponível");
    }

    const data = await response.json();
    const aiContent = data.choices?.[0]?.message?.content || "";

    console.log("[discursive-ai-analysis] OpenAI raw response:", aiContent);

    let modelResult;
    try {
      // Tenta extrair o JSON diretamente
      modelResult = JSON.parse(aiContent);
      console.log("[discursive-ai-analysis] JSON parsed successfully:", modelResult);
    } catch (e) {
      console.log("[discursive-ai-analysis] JSON parse failed, using fallback:", e.message);
      // Fallback: tenta extrair campos do texto usando regex
      const answerMatch = aiContent.match(/ai_answer\s*[:=]\s*["'`]?([\s\S]*?)[\n\r]*feedback\s*[:=]/im);
      const feedbackMatch = aiContent.match(/feedback\s*[:=]\s*["'`]?([\s\S]*)$/im);
      modelResult = {
        ai_answer: answerMatch ? answerMatch[1].trim() : "",
        feedback: feedbackMatch ? feedbackMatch[1].trim() : "",
        is_correct: false,
        score_percentage: 0,
        main_points_covered: [],
        missing_points: []
      };
      console.log("[discursive-ai-analysis] Fallback result:", modelResult);
    }

    const finalResult = {
      ai_answer: modelResult.ai_answer || "",
      feedback: modelResult.feedback || "",
      is_correct: modelResult.is_correct || false,
      score_percentage: modelResult.score_percentage || 0,
      main_points_covered: modelResult.main_points_covered || [],
      missing_points: modelResult.missing_points || []
    };

    console.log("[discursive-ai-analysis] Final result being sent:", finalResult);

    return new Response(JSON.stringify(finalResult), {
      headers: { ...corsHeaders, "Content-Type": "application/json" }
    });
  } catch (err) {
    console.error("[discursive-ai-analysis] Error:", err);
    return new Response(JSON.stringify({ error: "Erro interno do servidor. Tente novamente em alguns minutos." }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" }
    });
  }
});
