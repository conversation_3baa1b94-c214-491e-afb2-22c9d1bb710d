import { useMemo } from 'react';
import { useStaticStudyCategories, useStaticLocations } from './useStaticDataCache';

/**
 * Hook para resolver nomes de categorias a partir de IDs
 * Substitui os joins que causavam múltiplas requisições
 */
export const useCategoryResolver = () => {
  const { data: categories, isLoading: categoriesLoading } = useStaticStudyCategories();
  const { data: locations, isLoading: locationsLoading } = useStaticLocations();

  // Criar mapas para lookup rápido
  const categoryMaps = useMemo(() => {
    if (!categories) return null;

    const specialtyMap = new Map();
    const themeMap = new Map();
    const focusMap = new Map();

    categories.specialties.forEach(item => {
      specialtyMap.set(item.id, { id: item.id, name: item.name, type: 'specialty' });
    });

    categories.themes.forEach(item => {
      themeMap.set(item.id, { id: item.id, name: item.name, type: 'theme' });
    });

    categories.focuses.forEach(item => {
      focusMap.set(item.id, { id: item.id, name: item.name, type: 'focus' });
    });

    return { specialtyMap, themeMap, focusMap };
  }, [categories]);

  const locationMap = useMemo(() => {
    if (!locations) return null;

    const map = new Map();
    locations.forEach(location => {
      map.set(location.id, { id: location.id, name: location.name });
    });
    return map;
  }, [locations]);

  /**
   * Resolve uma única questão com dados de categorias
   */
  const resolveQuestion = (question: any) => {
    if (!categoryMaps || !locationMap) return question;

    return {
      ...question,
      specialty: question.specialty_id ? categoryMaps.specialtyMap.get(question.specialty_id) : null,
      theme: question.theme_id ? categoryMaps.themeMap.get(question.theme_id) : null,
      focus: question.focus_id ? categoryMaps.focusMap.get(question.focus_id) : null,
      location: question.exam_location ? locationMap.get(question.exam_location) : null,
    };
  };

  /**
   * Resolve múltiplas questões com dados de categorias
   */
  const resolveQuestions = (questions: any[]) => {
    if (!categoryMaps || !locationMap || !questions) return questions;

    return questions.map(resolveQuestion);
  };

  /**
   * Resolve apenas o nome de uma categoria por ID
   */
  const resolveCategoryName = (categoryId: string, type: 'specialty' | 'theme' | 'focus') => {
    if (!categoryMaps) return null;

    switch (type) {
      case 'specialty':
        return categoryMaps.specialtyMap.get(categoryId)?.name || null;
      case 'theme':
        return categoryMaps.themeMap.get(categoryId)?.name || null;
      case 'focus':
        return categoryMaps.focusMap.get(categoryId)?.name || null;
      default:
        return null;
    }
  };

  /**
   * Resolve nome de local por ID
   */
  const resolveLocationName = (locationId: string) => {
    if (!locationMap) return null;
    return locationMap.get(locationId)?.name || null;
  };

  /**
   * Verifica se os dados estão prontos para uso
   */
  const isReady = !categoriesLoading && !locationsLoading && categoryMaps && locationMap;

  return {
    resolveQuestion,
    resolveQuestions,
    resolveCategoryName,
    resolveLocationName,
    isReady,
    isLoading: categoriesLoading || locationsLoading,
  };
};
