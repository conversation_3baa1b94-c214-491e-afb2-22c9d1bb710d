
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { KeyRound } from "lucide-react";

const formSchema = z.object({
  email: z.string().email("Email inválido"),
});

interface ResetPasswordFormProps {
  onBackToLogin: () => void;
}

const ResetPasswordForm = ({ onBackToLogin }: ResetPasswordFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON>olver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase.auth.resetPasswordForEmail(values.email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;

      toast({
        title: "Email enviado!",
        description: "Verifique sua caixa de entrada para redefinir sua senha.",
      });
      
      onBackToLogin();
    } catch (error) {
      console.error("Error resetting password:", error);
      toast({
        variant: "destructive",
        title: "Erro ao enviar email",
        description: "Verifique o endereço de email e tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 py-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 w-full max-w-sm mx-auto">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm sm:text-base font-medium">Email</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="<EMAIL>" 
                    {...field} 
                    type="email"
                    autoComplete="email"
                    className="w-full text-sm sm:text-base bg-white border-2 border-black focus:border-black transition-all"
                  />
                </FormControl>
                <FormMessage className="text-xs sm:text-sm" />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-4">
            <Button 
              type="submit" 
              className="w-full text-sm sm:text-base bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              disabled={isLoading}
            >
              {isLoading ? "Enviando..." : "Enviar instruções"}
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full flex items-center gap-2 bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              onClick={onBackToLogin}
            >
              <KeyRound className="h-4 w-4" />
              Voltar para o login
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ResetPasswordForm;
