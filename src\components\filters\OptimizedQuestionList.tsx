import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { useOptimizedQuestions, usePrefetchNextPage } from '@/hooks/useOptimizedQuestions';
import { QuestionPagination } from './QuestionPagination';
import type { SelectedFilters } from '@/types/question';

interface OptimizedQuestionListProps {
  filters: SelectedFilters;
  onQuestionSelect?: (question: any) => void;
  pageSize?: number;
}

const QuestionSkeleton = () => (
  <Card className="mb-4">
    <CardContent className="p-6">
      <Skeleton className="h-4 w-3/4 mb-4" />
      <div className="space-y-2">
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-2/3" />
      </div>
      <div className="flex gap-2 mt-4">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </CardContent>
  </Card>
);

const ErrorState = ({ onRetry }: { onRetry: () => void }) => (
  <Card className="mb-4 border-destructive">
    <CardContent className="p-6 text-center">
      <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2">Erro ao carregar questões</h3>
      <p className="text-muted-foreground mb-4">
        Ocorreu um erro ao buscar as questões. Tente novamente.
      </p>
      <Button onClick={onRetry} variant="outline">
        <RefreshCw className="h-4 w-4 mr-2" />
        Tentar novamente
      </Button>
    </CardContent>
  </Card>
);

const EmptyState = () => (
  <Card className="mb-4">
    <CardContent className="p-6 text-center">
      <div className="text-6xl mb-4">🔍</div>
      <h3 className="text-lg font-semibold mb-2">Nenhuma questão encontrada</h3>
      <p className="text-muted-foreground">
        Tente ajustar os filtros para encontrar questões.
      </p>
    </CardContent>
  </Card>
);

export const OptimizedQuestionList: React.FC<OptimizedQuestionListProps> = ({
  filters,
  onQuestionSelect,
  pageSize = 50
}) => {
  const [currentPage, setCurrentPage] = useState(1);

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useOptimizedQuestions({
    filters,
    page: currentPage,
    pageSize,
    enabled: true
  });

  // Prefetch da próxima página
  usePrefetchNextPage(
    filters,
    currentPage,
    pageSize,
    data?.hasNextPage || false
  );

  const totalPages = useMemo(() => {
    if (!data?.totalCount) return 0;
    return Math.ceil(data.totalCount / pageSize);
  }, [data?.totalCount, pageSize]);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    // Scroll suave para o topo da lista
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleQuestionClick = useCallback((question: any) => {
    if (onQuestionSelect) {
      onQuestionSelect(question);
    }
  }, [onQuestionSelect]);

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  if (isError) {
    return <ErrorState onRetry={() => refetch()} />;
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <QuestionSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (!data?.questions?.length) {
    return <EmptyState />;
  }

  return (
    <div className="space-y-4">
      {/* Indicador de carregamento */}
      {isFetching && (
        <div className="flex items-center justify-center py-2">
          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">Atualizando...</span>
        </div>
      )}

      {/* Lista de questões */}
      <div className="space-y-4">
        {data.questions.map((question, index) => (
          <Card
            key={question.id}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => handleQuestionClick(question)}
          >
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-2">
                    Questão {(currentPage - 1) * pageSize + index + 1}
                  </h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    {question.specialty?.name && (
                      <div>Especialidade: {question.specialty.name}</div>
                    )}
                    {question.theme?.name && (
                      <div>Tema: {question.theme.name}</div>
                    )}
                    {question.focus?.name && (
                      <div>Foco: {question.focus.name}</div>
                    )}
                    {(question.exam_year || question.year) && (
                      <div>Ano: {question.exam_year || question.year}</div>
                    )}
                  </div>
                </div>
              </div>

              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: (question.question_content || question.statement)?.substring(0, 200) + '...'
                }}
              />

              <div className="flex gap-2 mt-4">
                <Button size="sm" variant="outline">
                  Ver questão
                </Button>
                {(question.response_choices || question.alternatives)?.length > 0 && (
                  <Button size="sm" variant="ghost">
                    {(question.response_choices || question.alternatives)?.length} alternativas
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Paginação */}
      <QuestionPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalCount={data.totalCount}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        isLoading={isFetching}
      />
    </div>
  );
};
