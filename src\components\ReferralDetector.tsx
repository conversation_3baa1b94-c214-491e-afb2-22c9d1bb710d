import React from 'react';
import { useReferralDetection } from '@/hooks/useReferralDetection';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Users, Gift } from 'lucide-react';

export const ReferralDetector = () => {
  const {
    showSuccessDialog,
    showErrorDialog,
    dialogMessage,
    setShowSuccessDialog,
    setShowErrorDialog
  } = useReferralDetection();

  return (
    <>
      {/* Dialog de Sucesso */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
          {/* Header com fundo verde */}
          <div className="w-full py-6 bg-gradient-to-br from-green-50 to-emerald-50 border-b border-black/20">
            <div className="flex flex-col items-center text-center gap-3 px-6">
              <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-green-200">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">
                Sucesso!
              </h2>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-6 space-y-4">
            <DialogHeader>
              <DialogTitle className="sr-only">Referência processada com sucesso</DialogTitle>
              <DialogDescription className="text-center text-gray-700">
                {dialogMessage}
              </DialogDescription>
            </DialogHeader>

            <Button
              onClick={() => setShowSuccessDialog(false)}
              className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl h-12 font-semibold"
            >
              Continuar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog de Erro */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="w-[90dvw] max-w-md max-h-[85dvh] overflow-y-auto rounded-xl border-2 border-black p-0 gap-0">
          {/* Header com fundo vermelho */}
          <div className="w-full py-6 bg-gradient-to-br from-red-50 to-pink-50 border-b border-black/20">
            <div className="flex flex-col items-center text-center gap-3 px-6">
              <div className="w-16 h-16 bg-white/80 rounded-full flex items-center justify-center border-2 border-red-200">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">
                Atenção
              </h2>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-6 space-y-4">
            <DialogHeader>
              <DialogTitle className="sr-only">Erro no processamento da referência</DialogTitle>
              <DialogDescription className="text-center text-gray-700">
                {dialogMessage}
              </DialogDescription>
            </DialogHeader>

            <Button
              onClick={() => setShowErrorDialog(false)}
              className="w-full bg-red-600 hover:bg-red-700 text-white rounded-xl h-12 font-semibold"
            >
              Entendi
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
