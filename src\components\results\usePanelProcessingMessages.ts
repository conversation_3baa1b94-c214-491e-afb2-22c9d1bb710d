
import { useState } from "react";

export function usePanelProcessingMessages(messages: string[]) {
  const [loadingMessageIndex, setLoadingMessageIndex] = useState(0);
  const [processingInterval, setProcessingInterval] = useState<NodeJS.Timeout | null>(null);

  const startProcessingMessages = () => {
    const interval = setInterval(() => {
      setLoadingMessageIndex((prev) => (prev + 1) % messages.length);
    }, 2500);
    setProcessingInterval(interval);
  };

  const stopProcessingMessages = () => {
    if (processingInterval) {
      clearInterval(processingInterval);
      setProcessingInterval(null);
    }
    setLoadingMessageIndex(0);
  };

  return {
    loadingMessageIndex,
    setLoadingMessageIndex,
    startProcessingMessages,
    stopProcessingMessages,
    loadingMessage: messages[loadingMessageIndex],
  };
}
