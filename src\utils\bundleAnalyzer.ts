import React from 'react';

/**
 * Analisador de bundle para monitoramento de performance
 * Identifica componentes pesados e oportunidades de otimização
 */

interface BundleMetrics {
  componentLoadTimes: Record<string, number>;
  chunkSizes: Record<string, number>;
  memoryUsage: number;
  renderTimes: Record<string, number>;
  cacheHitRates: Record<string, number>;
}

class BundleAnalyzer {
  private metrics: BundleMetrics = {
    componentLoadTimes: {},
    chunkSizes: {},
    memoryUsage: 0,
    renderTimes: {},
    cacheHitRates: {}
  };

  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    if (typeof window === 'undefined') return;

    // Observer para tempos de carregamento
    if ('PerformanceObserver' in window) {
      const loadObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.metrics.componentLoadTimes['initial'] = entry.duration;
          }
        }
      });

      try {
        loadObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(loadObserver);
      } catch (e) {
        console.warn('Performance Observer não suportado');
      }
    }

    // Monitor de memória
    this.startMemoryMonitoring();
  }

  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
      }, 5000);
    }
  }

  /**
   * Marcar início de carregamento de componente
   */
  markComponentStart(componentName: string) {
    if (typeof window === 'undefined') return;
    performance.mark(`${componentName}-start`);
  }

  /**
   * Marcar fim de carregamento de componente
   */
  markComponentEnd(componentName: string) {
    if (typeof window === 'undefined') return;
    
    performance.mark(`${componentName}-end`);
    
    try {
      performance.measure(
        `${componentName}-duration`,
        `${componentName}-start`,
        `${componentName}-end`
      );

      const measure = performance.getEntriesByName(`${componentName}-duration`)[0];
      this.metrics.componentLoadTimes[componentName] = measure.duration;
    } catch (e) {
      // Silently handle performance measurement errors
    }
  }

  /**
   * Registrar tempo de render de componente
   */
  recordRenderTime(componentName: string, duration: number) {
    this.metrics.renderTimes[componentName] = duration;
  }

  /**
   * Registrar hit rate de cache
   */
  recordCacheHitRate(cacheKey: string, hitRate: number) {
    this.metrics.cacheHitRates[cacheKey] = hitRate;
  }

  /**
   * Analisar chunks carregados
   */
  analyzeChunks() {
    if (typeof window === 'undefined') return;

    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach((script) => {
      const src = (script as HTMLScriptElement).src;
      if (src.includes('assets/')) {
        // Estimar tamanho baseado no nome do arquivo (heurística)
        const filename = src.split('/').pop() || '';
        const estimatedSize = this.estimateChunkSize(filename);
        this.metrics.chunkSizes[filename] = estimatedSize;
      }
    });
  }

  private estimateChunkSize(filename: string): number {
    // Heurística baseada em padrões comuns
    if (filename.includes('vendor')) return 500; // KB
    if (filename.includes('react')) return 200;
    if (filename.includes('ui')) return 150;
    if (filename.includes('chart')) return 100;
    return 50; // Tamanho padrão
  }

  /**
   * Gerar relatório de performance
   */
  generateReport(): {
    summary: string;
    recommendations: string[];
    metrics: BundleMetrics;
  } {
    const recommendations: string[] = [];

    // Analisar componentes lentos
    const slowComponents = Object.entries(this.metrics.componentLoadTimes)
      .filter(([_, time]) => time > 100)
      .sort(([_, a], [__, b]) => b - a);

    if (slowComponents.length > 0) {
      recommendations.push(
        `Componentes lentos detectados: ${slowComponents.map(([name]) => name).join(', ')}`
      );
    }

    // Analisar uso de memória
    if (this.metrics.memoryUsage > 100) {
      recommendations.push(
        `Alto uso de memória detectado: ${this.metrics.memoryUsage.toFixed(2)}MB`
      );
    }

    // Analisar cache hit rates
    const lowCacheHits = Object.entries(this.metrics.cacheHitRates)
      .filter(([_, rate]) => rate < 0.7);

    if (lowCacheHits.length > 0) {
      recommendations.push(
        `Cache hit rate baixo em: ${lowCacheHits.map(([key]) => key).join(', ')}`
      );
    }

    const summary = `
Performance Summary:
- Componentes monitorados: ${Object.keys(this.metrics.componentLoadTimes).length}
- Uso de memória: ${this.metrics.memoryUsage.toFixed(2)}MB
- Cache hit rate médio: ${this.getAverageCacheHitRate().toFixed(2)}%
- Componentes com problemas: ${slowComponents.length}
    `.trim();

    return {
      summary,
      recommendations,
      metrics: this.metrics
    };
  }

  private getAverageCacheHitRate(): number {
    const rates = Object.values(this.metrics.cacheHitRates);
    if (rates.length === 0) return 0;
    return (rates.reduce((sum, rate) => sum + rate, 0) / rates.length) * 100;
  }

  /**
   * Limpar observers
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  /**
   * Obter métricas atuais
   */
  getMetrics(): BundleMetrics {
    return { ...this.metrics };
  }
}

// Instância singleton
export const bundleAnalyzer = new BundleAnalyzer();

/**
 * Hook para monitorar performance de componentes
 */
export const useComponentPerformance = (componentName: string) => {
  React.useEffect(() => {
    bundleAnalyzer.markComponentStart(componentName);

    return () => {
      bundleAnalyzer.markComponentEnd(componentName);
    };
  }, [componentName]);

  const recordRender = (duration: number) => {
    bundleAnalyzer.recordRenderTime(componentName, duration);
  };

  return { recordRender };
};

/**
 * Decorator para componentes que precisam de monitoramento
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return React.memo((props: P) => {
    const startTime = performance.now();

    React.useEffect(() => {
      const endTime = performance.now();
      bundleAnalyzer.recordRenderTime(componentName, endTime - startTime);
    });

    return React.createElement(Component, props);
  });
}
