import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import UserProfileCard from "./UserProfileCard";

interface UserProfileDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const UserProfileDrawer = ({ open, onOpenChange }: UserProfileDrawerProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-md border-2 border-black bg-[#FEF7CD] max-h-[90vh] overflow-y-auto p-0 rounded-xl">
        {/* Header */}
        <DialogHeader className="p-4 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-bold text-gray-900">
              <PERSON><PERSON>fil
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0 hover:bg-gray-100 rounded-full"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Fechar</span>
            </Button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="p-4 pt-2">
          <UserProfileCard className="border-0 shadow-none bg-transparent p-0" />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserProfileDrawer;
