import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Controller } from "react-hook-form";
import { ListChecks } from "lucide-react";
import { cn } from "@/lib/utils";

interface ScheduleOptionsSectionProps {
  form: any;
  customWeeks: boolean;
  setCustomWeeks: (value: boolean) => void;
  existingWeeks: number[];
}

export const ScheduleOptionsSection = ({
  form,
  customWeeks,
  setCustomWeeks,
  existingWeeks
}: ScheduleOptionsSectionProps) => {
  const scheduleOption = form.watch('scheduleOption');
  const weeksCount = form.watch('weeksCount');
  const { setValue } = form;
  return (
    <div className="p-6 space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
      <div className="flex items-center gap-2 text-slate-800">
        <div className="p-2 rounded-full bg-purple-100">
          <ListChecks className="w-5 h-5 text-purple-600" />
        </div>
        <h3 className="text-lg font-bold">Opções de cronograma</h3>
      </div>
      
      <RadioGroup 
        value={scheduleOption}
        onValueChange={(value) => {
          setValue("scheduleOption", value);
          if (value === "existing") {
            setCustomWeeks(false);
          }
        }}
        className="space-y-4 mt-3"
      >
        {/* New Weeks Option */}
        <div className={cn(
          "flex items-center space-x-3 border-2 rounded-xl p-5 cursor-pointer transition-colors",
          scheduleOption === "new" 
            ? "border-blue-500 bg-blue-50" 
            : "border-slate-200 hover:bg-slate-50"
        )}>
          <RadioGroupItem value="new" id="option-new" className="text-blue-600" />
          <div className="flex-1">
            <Label htmlFor="option-new" className="text-base font-bold cursor-pointer">
              Criar novas semanas
            </Label>
            <p className="mt-1 text-sm text-slate-600">
              Adiciona novas semanas ao seu cronograma, a partir da última semana existente.
            </p>
          </div>
        </div>

        {/* Existing Week Option */}
        <div className={cn(
          "flex items-center space-x-3 border-2 rounded-xl p-5 cursor-pointer transition-colors",
          existingWeeks.length === 0 && "opacity-60",
          scheduleOption === "existing" 
            ? "border-blue-500 bg-blue-50" 
            : "border-slate-200 hover:bg-slate-50"
        )}>
          <RadioGroupItem 
            value="existing" 
            id="option-existing" 
            disabled={existingWeeks.length === 0}
            className="text-blue-600"
          />
          <div className="flex-1">
            <Label htmlFor="option-existing" className="text-base font-bold cursor-pointer">
              Adicionar à semana existente
            </Label>
            <p className="mt-1 text-sm text-slate-600">
              {existingWeeks.length > 0 
                ? "Adiciona tópicos de estudo a uma semana que já existe no seu cronograma."
                : "Não há semanas existentes no seu cronograma."}
            </p>
            
            {scheduleOption === "existing" && existingWeeks.length > 0 && (
              <div className="mt-4">
                <Label htmlFor="targetWeek" className="text-sm font-bold">Selecione a semana</Label>
                <Controller
                  name="targetWeek"
                  control={form.control}
                  render={({ field }) => (
                    <Select
                      value={field.value?.toString() || ""}
                      onValueChange={(value) => {
                        field.onChange(parseInt(value));
                      }}
                    >
                      <SelectTrigger className="mt-2 border-2 border-slate-300 focus-visible:ring-blue-600/20 bg-white font-medium h-11">
                        <SelectValue placeholder="Selecione uma semana" />
                      </SelectTrigger>
                      <SelectContent
                        className="bg-white shadow-lg border-slate-200 z-[9999] max-h-60"
                        position="popper"
                        sideOffset={4}
                      >
                        {existingWeeks.map(week => (
                          <SelectItem key={week} value={week.toString()} className="font-medium">
                            Semana {week}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </RadioGroup>

      {/* Weeks Count Section */}
      {scheduleOption === "new" && (
        <div className="p-5 mt-4 border-2 rounded-xl bg-white shadow-md border-slate-100">
          <div className="flex items-center justify-between mb-3">
            <div className="space-y-1">
              <Label htmlFor="weeksCount" className="flex items-center gap-2 text-slate-800 font-bold">
                Número de semanas a criar
              </Label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="customWeeks"
                  checked={customWeeks}
                  onChange={(e) => setCustomWeeks(e.target.checked)}
                  className="mr-2 text-blue-600 border-blue-600"
                />
                <Label 
                  htmlFor="customWeeks"
                  className="text-xs text-slate-600 cursor-pointer font-medium"
                >
                  Personalizar quantidade
                </Label>
              </div>
            </div>
            <span className="px-3 py-1 text-sm font-bold text-blue-700 bg-blue-100 rounded-full">
              {weeksCount} semanas
            </span>
          </div>
          
          {customWeeks ? (
            <div className="space-y-2">
              <Input
                id="weeksCount"
                type="number"
                min={1}
                max={50}
                {...form.register('weeksCount', { 
                  valueAsNumber: true,
                  min: 1,
                  max: 50
                })}
                className="max-w-[150px] border-2 border-slate-300 focus:border-blue-600 focus:ring-blue-600/20 h-10 font-medium"
              />
              <p className="text-xs text-slate-500 font-medium">
                Escolha entre 1 e 50 semanas
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mt-3">
              {[1, 4, 8, 12].map(count => (
                <Button
                  key={count}
                  type="button"
                  variant="outline"
                  onClick={() => setValue('weeksCount', count)}
                  className={cn(
                    "h-10 sm:h-11 border-2 font-bold text-xs sm:text-sm whitespace-nowrap",
                    weeksCount === count
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-slate-200"
                  )}
                >
                  {count} {count === 1 ? 'sem' : 'sems'}
                </Button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
