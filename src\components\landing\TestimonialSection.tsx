
import { motion } from "framer-motion";
import { Star } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

export function TestimonialSection() {
  const testimonials = [
    {
      text: "O planner inteligente transformou completamente minha preparação. Consegui uma vaga muito concorrida graças ao método de estudo.",
      author: "<PERSON>",
      role: "R1 em Cirurgia",
      avatar: "/placeholder.svg",
      rating: 5,
      specialty: "Oftalmologia",
    },
    {
      text: "Meu desempenho nas provas melhorou absurdamente! O sistema de revisões espaçadas realmente funciona.",
      author: "<PERSON><PERSON>.",
      role: "R1 em Pediatria",
      avatar: "/placeholder.svg",
      rating: 5,
      specialty: "Pediatria",
    },
    {
      text: "Finalmente consegui organizar meus estudos. O sistema de acompanhamento de progresso é incrível para se manter motivado.",
      author: "<PERSON>",
      role: "Estudante de Medicina",
      avatar: "/placeholder.svg",
      rating: 5,
      specialty: "Oftalmologia",
    },
    {
      text: "Passei na primeira tentativa na residência que sempre sonhei. O banco de questões foi fundamental para meu sucesso!",
      author: "Ana C.",
      role: "R1 em Dermatologia",
      avatar: "/placeholder.svg",
      rating: 5,
      specialty: "Oftalmologia",
    },
  ];

  return (
    <section className="py-20 px-4 bg-primary/5 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-r from-primary/5 to-transparent"></div>
      <div className="absolute -top-12 -right-12 w-40 h-40 bg-yellow-200/20 rounded-full"></div>
      <div className="absolute bottom-0 left-0 w-60 h-32 bg-green-100/30 rounded-full translate-y-1/2 -translate-x-1/3"></div>
      
      {/* Duolingo style pattern */}
      <div className="absolute left-0 top-1/3 h-40 w-6 bg-primary rounded-r-2xl opacity-10"></div>
      <div className="absolute right-0 top-1/2 h-40 w-6 bg-green-400 rounded-l-2xl opacity-10"></div>
      <div className="absolute left-1/3 bottom-0 h-6 w-40 bg-yellow-400 rounded-t-2xl opacity-10"></div>
      
      <div className="container mx-auto relative">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="inline-block mb-6">
            <span className="bg-green-500/10 text-green-600 px-4 py-2 rounded-full text-sm font-medium">
              Depoimentos
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-primary">
            O que dizem nossos alunos aprovados
          </h2>
          <p className="text-lg text-gray-700">
            Histórias reais de alunos que transformaram seus estudos e conquistaram a aprovação
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-12 relative"
        >
          {/* Decorative elements */}
          <div className="absolute left-1/4 -top-6 w-8 h-8 bg-primary/20 rounded-full"></div>
          <div className="absolute right-1/4 -top-10 w-6 h-6 bg-yellow-300/30 rounded-full"></div>
          
          <Carousel className="w-full max-w-5xl mx-auto">
            <CarouselContent>
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/2 p-2">
                  <div className="bg-white rounded-xl shadow-lg p-6 h-full border border-gray-100 flex flex-col relative overflow-hidden group hover:shadow-xl transition-shadow">
                    {/* Corner decoration like Duolingo style */}
                    <div className="absolute -top-10 -right-10 w-20 h-20 bg-primary/5 rounded-full group-hover:scale-125 transition-transform"></div>
                    
                    <div className="flex items-center gap-2 mb-4">
                      <div className="flex gap-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      
                      <div className="ml-2 px-2 py-0.5 bg-primary/5 rounded-full text-xs font-medium text-primary">
                        {testimonial.specialty}
                      </div>
                    </div>
                    <p className="text-gray-700 flex-grow italic mb-6 relative z-10">"{testimonial.text}"</p>
                    <div className="border-t pt-4 flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 border-2 border-primary/20">
                        <img 
                          src={testimonial.avatar} 
                          alt={testimonial.author} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-800">{testimonial.author}</p>
                        <p className="text-sm text-gray-600">{testimonial.role}</p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex justify-center mt-6">
              <div className="flex gap-3">
                <CarouselPrevious className="relative inset-0 translate-y-0 h-8 w-8" />
                <CarouselNext className="relative inset-0 translate-y-0 h-8 w-8" />
              </div>
            </div>
          </Carousel>
        </motion.div>

        <motion.div 
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {/* Progress bar indicator (Duolingo style) */}
          <div className="w-full max-w-xs mx-auto h-2 bg-gray-200 rounded-full mb-8 relative overflow-hidden">
            <div className="absolute top-0 left-0 h-full w-4/5 bg-gradient-to-r from-green-400 to-green-500 rounded-full"></div>
          </div>
          
          <div className="inline-block bg-yellow-100/50 border border-yellow-200/80 px-6 py-3 rounded-lg">
            <p className="text-lg font-medium text-primary">
              Junte-se a milhares de estudantes aprovados em todo o Brasil
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
