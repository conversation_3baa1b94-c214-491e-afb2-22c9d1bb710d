import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { drWillLogger } from '@/utils/logger';
import { drWillMonitor, measureOperation } from '@/utils/drWillMonitor';

// Cache para evitar requisições desnecessárias
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number; // Time to live em ms
}

const cache = new Map<string, CacheEntry>();
const CACHE_TTL = 60000; // 60 segundos para threads (mais agressivo)
const MESSAGES_CACHE_TTL = 30000; // 30 segundos para mensagens

const getCachedData = (key: string): any | null => {
  const entry = cache.get(key);
  if (entry && Date.now() - entry.timestamp < entry.ttl) {
    return entry.data;
  }
  cache.delete(key);
  return null;
};

const setCachedData = (key: string, data: any, ttl: number = CACHE_TTL): void => {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });
};

export interface DrWillMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface DrWillThread {
  id: string;
  title: string;
  lastMessageAt: Date;
  messageCount: number;
  createdAt: Date;
  metadata?: any;
}

export const useDrWillHistory = () => {
  const [threads, setThreads] = useState<DrWillThread[]>([]);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
  const [messages, setMessages] = useState<DrWillMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();



  // Load all threads for the user (com cache para evitar reloads desnecessários)
  const loadThreads = useCallback(async (forceReload = false) => {
    if (!user?.id) return;

    return await measureOperation('loadThreads', async () => {
      const cacheKey = `threads_${user.id}`;

      // Verificar cache primeiro
      if (!forceReload) {
        const cachedThreads = getCachedData(cacheKey);
        if (cachedThreads) {
          drWillLogger.cacheHit(cacheKey);
          setThreads(cachedThreads);
          return;
        }

        // Se já temos threads e não é um reload forçado, não recarregar
        if (threads.length > 0) {

          return;
        }
      }

      try {
        setIsLoading(true);
        drWillLogger.cacheMiss(cacheKey);

        const { data, error } = await supabase
          .from('medevo_chat_threads')
          .select('id, title, last_message_at, message_count, created_at, metadata')
          .eq('user_id', user.id)
          .order('last_message_at', { ascending: false });

        if (error) {
          drWillLogger.error('loading threads', error);
          throw error;
        }

        const formattedThreads: DrWillThread[] = data.map(thread => ({
          id: thread.id,
          title: thread.title,
          lastMessageAt: new Date(thread.last_message_at || thread.created_at),
          messageCount: thread.message_count || 0,
          createdAt: new Date(thread.created_at),
          metadata: thread.metadata
        }));

        setThreads(formattedThreads);

        // Cache the results
        setCachedData(cacheKey, formattedThreads);


      } catch (error) {
        drWillLogger.error('loading threads', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    });
  }, [user?.id, threads.length]);

  // Load messages for a specific thread (otimizado com cache e prevenção de race conditions)
  const loadMessages = useCallback(async (threadId: string): Promise<DrWillMessage[] | null> => {
    if (!user?.id) return null;

    return await measureOperation(`loadMessages_${threadId.slice(0, 8)}`, async () => {
      const cacheKey = `messages_${threadId}_${user.id}`;

      // Verificar cache primeiro (apenas se não é a thread atual para evitar dados desatualizados)
      if (currentThreadId !== threadId) {
        const cachedMessages = getCachedData(cacheKey);
        if (cachedMessages) {
          drWillLogger.cacheHit(cacheKey);
          setMessages(cachedMessages);
          setCurrentThreadId(threadId);
          drWillLogger.threadLoaded(threadId, cachedMessages.length);
          drWillMonitor.updateThreadState(threadId, cachedMessages.length);
          return cachedMessages;
        }
      }

      try {
        setIsLoading(true);
        drWillLogger.cacheMiss(cacheKey);

        const { data, error } = await supabase
          .from('medevo_chat_history')
          .select('*')
          .eq('thread_id', threadId)
          .eq('user_id', user.id)
          .order('created_at', { ascending: true });

        if (error) {
          drWillLogger.error('loading messages', error);
          drWillMonitor.updateThreadState(threadId, 0, error.message);
          throw error;
        }

        const formattedMessages: DrWillMessage[] = data.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.role === 'user',
          timestamp: new Date(msg.created_at)
        }));

        // Sempre atualizar estado local
        setMessages(formattedMessages);
        setCurrentThreadId(threadId);
  

        // Cache the results (com TTL otimizado para mensagens)
        setCachedData(cacheKey, formattedMessages, MESSAGES_CACHE_TTL);

        drWillLogger.threadLoaded(threadId, formattedMessages.length);
        drWillMonitor.updateThreadState(threadId, formattedMessages.length);

        return formattedMessages;
      } catch (error) {
        drWillLogger.error('loading messages', error);
        drWillMonitor.updateThreadState(threadId, 0, error instanceof Error ? error.message : String(error));
        throw error;
      } finally {
        setIsLoading(false);
      }
    });
  }, [user?.id, currentThreadId]);

  // Save a message to the current thread
  const saveMessage = useCallback(async (message: DrWillMessage, threadId?: string) => {
    if (!user?.id) return null;

    return await measureOperation(`saveMessage_${message.isUser ? 'user' : 'ai'}`, async () => {
      const targetThreadId = threadId || currentThreadId;
      if (!targetThreadId) {
        const error = 'No thread ID provided for saving message';
        drWillLogger.error('saving message', error);
        throw new Error(error);
      }

      const { data, error } = await supabase
        .from('medevo_chat_history')
        .insert({
          user_id: user.id,
          thread_id: targetThreadId,
          role: message.isUser ? 'user' : 'assistant',
          content: message.content,
          metadata: {}
        })
        .select()
        .single();

      if (error) {
        drWillLogger.error('saving message', error);
        throw error;
      }

      drWillLogger.messageSaved(data.id, message.isUser);

      // Invalidar cache para garantir dados atualizados
      const threadCacheKey = `threads_${user.id}`;
      const messagesCacheKey = `messages_${targetThreadId}_${user.id}`;
      cache.delete(threadCacheKey);
      cache.delete(messagesCacheKey);

      // Não atualizar mensagens locais aqui - deixar o useDrWillChat gerenciar o estado da UI
      // As mensagens serão sincronizadas quando a thread for carregada

      // OTIMIZAÇÃO: Reload threads em background para não bloquear UI
      loadThreads(true).catch(console.error);

      return data.id;
    });
  }, [user?.id, currentThreadId, loadThreads]);

  // Create a new thread
  const createNewThread = useCallback(async (titleOrFirstMessage?: string, sessionId?: string): Promise<string | null> => {
    if (!user?.id) return null;

    return await measureOperation('createNewThread', async () => {
      const threadId = crypto.randomUUID();

      // Se é uma thread contextual (tem sessionId), usar o título diretamente
      // Se não, tratar como primeira mensagem e truncar se necessário
      let title: string;
      if (sessionId && titleOrFirstMessage) {
        // Thread contextual - usar título completo
        title = titleOrFirstMessage;
      } else {
        // Thread normal - tratar como primeira mensagem
        title = titleOrFirstMessage
          ? (titleOrFirstMessage.length > 50 ? titleOrFirstMessage.substring(0, 50) + '...' : titleOrFirstMessage)
          : 'Nova conversa';
      }

      // Preparar metadata se for thread contextual
      const metadata = sessionId ? { sessionId } : null;

      const { data, error } = await supabase
        .from('medevo_chat_threads')
        .insert({
          id: threadId,
          user_id: user.id,
          title,
          message_count: 0,
          last_message_at: new Date().toISOString(),
          metadata
        })
        .select()
        .single();

      if (error) {
        drWillLogger.error('creating thread', error);
        throw error;
      }

      drWillLogger.threadCreated(threadId, title);
      setCurrentThreadId(threadId);
      setMessages([]);
      await loadThreads(true);
      return threadId;
    });
  }, [user?.id, loadThreads]);

  // Delete a thread and all its messages
  const deleteThread = useCallback(async (threadId: string) => {
    if (!user?.id) return;

    try {
      // Delete messages first (cascade should handle this, but being explicit)
      await supabase
        .from('medevo_chat_history')
        .delete()
        .eq('thread_id', threadId)
        .eq('user_id', user.id);

      // Delete thread
      const { error } = await supabase
        .from('medevo_chat_threads')
        .delete()
        .eq('id', threadId)
        .eq('user_id', user.id);

      if (error) throw error;



      // Update local state
      setThreads(prev => prev.filter(thread => thread.id !== threadId));
      
      if (currentThreadId === threadId) {
        setCurrentThreadId(null);
        setMessages([]);
      }
    } catch (error) {
      console.error('❌ [HISTORY] Error deleting thread:', error);
    }
  }, [user?.id, currentThreadId]);

  // Clear current conversation (start fresh)
  const clearCurrentConversation = useCallback(() => {
    setCurrentThreadId(null);
    setMessages([]);
  }, [currentThreadId, messages.length]);

  // Get conversation history for AI context - SEMPRE do banco de dados para garantir consistência
  const getConversationHistory = useCallback(async (threadId?: string): Promise<Array<{role: 'user' | 'assistant', content: string}>> => {
    const targetThreadId = threadId || currentThreadId;
    if (!targetThreadId || !user?.id) {
      console.warn('⚠️ [HISTORY] No thread ID available for conversation history');
      return [];
    }

    return await measureOperation(`getConversationHistory_${targetThreadId.slice(0, 8)}`, async () => {
      const { data, error } = await supabase
        .from('medevo_chat_history')
        .select('role, content, created_at')
        .eq('thread_id', targetThreadId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: true });

      if (error) {
        drWillLogger.error('getting conversation history', error);
        throw error;
      }

      const history = data.map(msg => ({
        role: msg.role === 'user' ? 'user' as const : 'assistant' as const,
        content: msg.content
      }));

      drWillLogger.contextLoaded(targetThreadId, history.length);
      return history;
    });
  }, [currentThreadId, user?.id]);

  // Find or create contextual thread for a session
  const findOrCreateContextualThread = useCallback(async (sessionTitle: string, sessionId: string): Promise<string | null> => {
    if (!user?.id) return null;



    return await measureOperation('findOrCreateContextualThread', async () => {
      // SEMPRE buscar APENAS por sessionId - cada sessão deve ter sua própria thread
      const { data: existingThreads, error: searchError } = await supabase
        .from('medevo_chat_threads')
        .select('id, title, metadata')
        .eq('user_id', user.id)
        .eq('metadata->>sessionId', sessionId)
        .order('created_at', { ascending: false })
        .limit(1);



      // Se encontrou thread para este sessionId específico, verificar se precisa atualizar o título
      if (!searchError && existingThreads && existingThreads.length > 0) {
        const existingThread = existingThreads[0];


        // Verificar se o título precisa ser atualizado (se ainda tem o formato antigo)
        if (existingThread.title && existingThread.title.includes('- Sessão Contextual')) {

          const enhancedTitle = await createEnhancedSessionTitle(sessionTitle, sessionId);

          // Atualizar o título da thread existente
          const { error: updateError } = await supabase
            .from('medevo_chat_threads')
            .update({ title: enhancedTitle })
            .eq('id', existingThread.id)
            .eq('user_id', user.id);
        }

        return existingThread.id;
      }

      // Se não encontrou, criar nova thread vinculada ao sessionId

      // 🎯 Criar nome mais personalizado e informativo
      const enhancedTitle = await createEnhancedSessionTitle(sessionTitle, sessionId);
      const newThreadId = await createNewThread(enhancedTitle, sessionId);


      return newThreadId;
    });
  }, [user?.id, createNewThread]);

  // 🎯 Função para criar título personalizado da sessão contextual
  const createEnhancedSessionTitle = async (sessionTitle: string, sessionId: string): Promise<string> => {
    try {
      // Buscar informações adicionais da sessão para criar um nome mais descritivo
      // Primeiro buscar dados básicos da sessão
      const { data: sessionData, error } = await supabase
        .from('study_sessions')
        .select(`
          title,
          total_questions,
          specialty_id,
          theme_id,
          focus_id,
          knowledge_domain
        `)
        .eq('id', sessionId)
        .single();

      // Se conseguiu buscar dados básicos, buscar nomes das entidades relacionadas
      let specialtyName = null;
      let themeName = null;
      let focusName = null;

      if (!error && sessionData) {
        // Buscar nome da especialidade se existir
        if (sessionData.specialty_id) {
          const { data: specialtyData } = await supabase
            .from('study_categories')
            .select('name')
            .eq('id', sessionData.specialty_id)
            .eq('type', 'specialty')
            .single();
          specialtyName = specialtyData?.name;
        }

        // Buscar nome do tema se existir
        if (sessionData.theme_id) {
          const { data: themeData } = await supabase
            .from('study_categories')
            .select('name')
            .eq('id', sessionData.theme_id)
            .eq('type', 'theme')
            .single();
          themeName = themeData?.name;
        }

        // Buscar nome do foco se existir
        if (sessionData.focus_id) {
          const { data: focusData } = await supabase
            .from('study_categories')
            .select('name')
            .eq('id', sessionData.focus_id)
            .eq('type', 'focus')
            .single();
          focusName = focusData?.name;
        }
      }

      if (error || !sessionData) {
        // Fallback para o título original se não conseguir buscar dados
        return `📚 ${sessionTitle} - Sessão Contextual`;
      }

      // Construir nome mais informativo
      // 🎯 USAR O TÍTULO DO BANCO DE DADOS SE DISPONÍVEL
      let enhancedName = sessionData.title || sessionTitle;

      // Se o título é genérico, melhorar com informações da sessão
      if (!enhancedName ||
          enhancedName === 'Sessão de Estudos' ||
          enhancedName.toLowerCase().includes('nova sessão') ||
          (enhancedName.toLowerCase().includes('sessão') && enhancedName.length < 20)) {

        const parts: string[] = [];

        // Adicionar especialidade se disponível
        if (specialtyName) {
          parts.push(specialtyName);
        }

        // Adicionar tema se disponível e diferente da especialidade
        if (themeName && themeName !== specialtyName) {
          parts.push(themeName);
        }

        // Adicionar foco se disponível e específico
        if (focusName &&
            focusName !== 'Geral' &&
            focusName !== 'N/A' &&
            focusName !== themeName) {
          parts.push(focusName);
        }

        // Adicionar domínio se disponível
        if (sessionData.knowledge_domain && sessionData.knowledge_domain !== 'general') {
          const domainNames: Record<string, string> = {
            'medico': 'Médico',
            'enfermagem': 'Enfermagem',
            'fisioterapia': 'Fisioterapia',
            'nutricao': 'Nutrição',
            'farmacia': 'Farmácia',
            'odontologia': 'Odontologia'
          };
          const domainName = domainNames[sessionData.knowledge_domain] || sessionData.knowledge_domain;
          parts.push(domainName);
        }

        if (parts.length > 0) {
          enhancedName = parts.join(' • ');
          // Adicionar número de questões para contexto
          enhancedName += ` (${sessionData.total_questions}q)`;
        } else {
          // Se não tem informações específicas, usar título original com número de questões
          enhancedName = `${enhancedName} (${sessionData.total_questions}q)`;
        }
      } else {
        // Se o título já é descritivo, apenas adicionar número de questões
        enhancedName = `${enhancedName} (${sessionData.total_questions}q)`;
      }

      // Limitar tamanho do título para não ficar muito longo
      if (enhancedName.length > 60) {
        enhancedName = enhancedName.substring(0, 57) + '...';
      }

      const finalTitle = `📚 ${enhancedName}`;

      return finalTitle;

    } catch (error) {
      console.warn('⚠️ [useDrWillHistory] Erro ao criar título personalizado:', error);
      // Fallback para o título original
      return `📚 ${sessionTitle} - Sessão Contextual`;
    }
  };

  // Load threads apenas quando necessário (lazy loading)
  // Removido carregamento automático para melhorar performance inicial

  return {
    // State
    threads,
    currentThreadId,
    // ❌ REMOVIDO: messages - estava sobrescrevendo o messages do useDrWillChat
    historyMessages: messages, // ✅ RENOMEADO: para evitar conflito
    historyIsLoading: isLoading, // ✅ RENOMEADO: para evitar conflito

    // Actions
    loadThreads,
    loadMessages,
    saveMessage,
    createNewThread,
    findOrCreateContextualThread,
    deleteThread,
    clearCurrentConversation,

    // Utilities
    getConversationHistory
  };
};
