
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { HierarchySelect } from "./HierarchySelect";
import { useHierarchyData } from "./hooks/useHierarchyData";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface FlashcardImportData {
  frente: string;
  verso: string;
}

export const FlashcardImport = () => {
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const [selectedTheme, setSelectedTheme] = useState("");
  const [selectedFocus, setSelectedFocus] = useState("");
  const [selectedExtraFocus, setSelectedExtraFocus] = useState("");
  const [jsonContent, setJsonContent] = useState("");

  const {
    specialties,
    themes,
    focuses,
    extraFocuses
  } = useHierarchyData(
    selectedSpecialty || undefined,
    selectedTheme || undefined,
    selectedFocus || undefined
  );

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      setJsonContent(e.target?.result as string);
    };
    reader.readAsText(file);
  };

  const handleImport = async () => {
    if (!selectedSpecialty) {
      toast.error("Selecione uma especialidade");
      return;
    }

    if (!jsonContent) {
      toast.error("Nenhum arquivo selecionado");
      return;
    }

    try {
      const flashcards: FlashcardImportData[] = JSON.parse(jsonContent);
      console.log("📥 Iniciando importação de", flashcards.length, "flashcards");

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Usuário não autenticado");
        return;
      }

      let successCount = 0;
      let errorCount = 0;

      for (const card of flashcards) {
        try {
          // 1º: insere flashcard sem origin_id
          const { data: insertData, error } = await supabase
            .from('flashcards_cards')
            .insert({
              user_id: user.id,
              front: card.frente,
              back: card.verso,
              specialty_id: selectedSpecialty,
              theme_id: selectedTheme || null,
              focus_id: selectedFocus || null,
              extrafocus_id: selectedExtraFocus || null,
              front_image: null,
              back_image: null,
              current_state: 'available',
              is_shared: false,
              origin_id: null
            })
            .select('id')
            .single();

          if (error || !insertData) throw error;

          // 2º: atualiza origin_id = id
          await supabase
            .from('flashcards_cards')
            .update({ origin_id: insertData.id })
            .eq('id', insertData.id);

          successCount++;
          console.log("✅ Flashcard importado com sucesso:", card.frente.substring(0, 50));
        } catch (error) {
          console.error("❌ Erro ao importar flashcard:", error);
          errorCount++;
        }
      }

      toast.success(`Importação concluída: ${successCount} sucesso, ${errorCount} erros`);
      setJsonContent("");
    } catch (error) {
      console.error("Erro ao processar JSON:", error);
      toast.error("Erro ao processar o arquivo JSON");
    }
  };

  return (
    <Card className="p-6 space-y-6">
      <h3 className="text-lg font-semibold">Importar Flashcards</h3>

      <div className="grid gap-4">
        <HierarchySelect
          label="Especialidade *"
          value={selectedSpecialty}
          onChange={setSelectedSpecialty}
          options={specialties}
          placeholder="Selecione uma especialidade"
        />

        <HierarchySelect
          label="Tema"
          value={selectedTheme}
          onChange={setSelectedTheme}
          options={themes}
          placeholder="Selecione um tema"
          disabled={!selectedSpecialty}
        />

        <HierarchySelect
          label="Foco"
          value={selectedFocus}
          onChange={setSelectedFocus}
          options={focuses}
          placeholder="Selecione um foco"
          disabled={!selectedTheme}
        />

        <HierarchySelect
          label="Extra Foco"
          value={selectedExtraFocus}
          onChange={setSelectedExtraFocus}
          options={extraFocuses}
          placeholder="Selecione um extra foco"
          disabled={!selectedFocus}
        />

        <div className="space-y-2">
          <Label htmlFor="json-file">Arquivo JSON</Label>
          <input
            id="json-file"
            type="file"
            accept=".json"
            onChange={handleFileUpload}
            className="w-full p-2 border rounded"
          />
        </div>

        <Button 
          onClick={handleImport}
          disabled={!selectedSpecialty || !jsonContent}
          className="w-full"
        >
          Importar Flashcards
        </Button>
      </div>
    </Card>
  );
};
