import React, { useState, useEffect } from 'react';
import { Trophy, Clock, Calendar, BookOpen, Info } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import StudyNavBar from '@/components/study/StudyNavBar';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from '@/components/ui/card';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"

interface RankingUser {
  id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  rank: number;
  score: number;
  questions_answered?: number;
}

const timePeriods = [
  { id: 'all', name: 'Todo Período', icon: <Clock className="h-5 w-5" /> },
  { id: 'week', name: 'Semanal', icon: <Calendar className="h-5 w-5" /> },
  { id: 'day', name: 'Diário', icon: <BookOpen className="h-5 w-5" /> }
];

const QuestionRanking = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [rankingData, setRankingData] = useState<RankingUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchRankingData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
       // console.log('[Ranking] Fetching ranking data...');
        
        let query = supabase.from('user_answers').select('*');
        
        const now = new Date();
        if (activeTab === 'day') {
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
          query = query.gte('created_at', today);
        } else if (activeTab === 'week') {
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
          query = query.gte('created_at', weekAgo);
        }
        
        const { data: userAnswersData, error: userAnswersError } = await query;
          
        if (userAnswersError) throw userAnswersError;
        
       // console.log(`[Ranking] Fetched ${userAnswersData?.length || 0} user answers`);
        
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('id, username, full_name, avatar_url');
          
        if (profilesError) throw profilesError;
        
        // console.log(`[Ranking] Fetched ${profilesData?.length || 0} user profiles`);

        const userStats = new Map<string, Set<string>>();
        
        userAnswersData?.forEach(answer => {
          if (!userStats.has(answer.user_id)) {
            userStats.set(answer.user_id, new Set());
          }
          userStats.get(answer.user_id)!.add(answer.question_id);
        });

        const rankingUsers: RankingUser[] = [];
        
        for (const [userId, questionSet] of userStats.entries()) {
          const profile = profilesData?.find(p => p.id === userId);
          if (!profile) continue;
          
          const questionsAnswered = questionSet.size;
          
          if (questionsAnswered > 0) {
            rankingUsers.push({
              id: userId,
              username: profile.username || 'Anônimo',
              full_name: profile.full_name || 'Usuário',
              avatar_url: profile.avatar_url,
              rank: 0,
              score: questionsAnswered,
              questions_answered: questionsAnswered
            });
          }
        }
        
        const sortedRanking = rankingUsers
          .sort((a, b) => b.score - a.score)
          .map((user, index) => ({
            ...user,
            rank: index + 1
          }));
        
        // console.log(`[Ranking] Final ranking has ${sortedRanking.length} users`);
        setRankingData(sortedRanking);
        
      } catch (error: any) {
        console.error('Error in fetchRankingData:', error);
        setError(error.message || 'Failed to load ranking data');
        toast({
          variant: "destructive",
          title: "Erro ao carregar ranking",
          description: "Não foi possível buscar os dados do ranking.",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchRankingData();
  }, [activeTab, toast]);

  const currentUserRanking = user ? rankingData.find(u => u.id === user.id) : undefined;
  const topThree = rankingData.slice(0, 3);
  const remainingUsers = rankingData.slice(3, 100);

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />
      <StudyNavBar className="mb-8" />

      <div className="container max-w-6xl mx-auto px-4 pt-8 space-y-8 animate-fade-in">
        <div className="relative mb-12">
          <div className="inline-block transform -rotate-2 mb-4">
            <div className="bg-hackathon-red border-2 border-black px-4 py-1 text-white font-bold tracking-wide text-sm shadow-card-sm">
              RANKING
            </div>
          </div>

          <h1 className="text-5xl font-black leading-none mb-4">
            <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
              Ranking de Questões
            </span>
          </h1>

          {currentUserRanking && (
            <Card className="p-6 border-2 border-black bg-white shadow-card-sm my-8">
              <div className="flex items-center gap-4">
                <div className="h-16 w-16 rounded-full bg-hackathon-yellow border-2 border-black flex items-center justify-center text-2xl font-bold">
                  #{currentUserRanking.rank}
                </div>
                <div>
                  <h2 className="text-xl font-bold">Seu Ranking</h2>
                  <p className="text-gray-600">
                    Você está na posição <span className="font-bold">{currentUserRanking.rank}º</span>
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Total de questões respondidas: <span className="font-bold">{currentUserRanking.questions_answered}</span>
                  </p>
                </div>
              </div>
            </Card>
          )}

          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 gap-2 bg-transparent">
              {timePeriods.map(period => (
                <TabsTrigger 
                  key={period.id}
                  value={period.id}
                  className="flex items-center gap-2 border-2 border-black data-[state=active]:bg-hackathon-yellow data-[state=active]:text-black"
                >
                  {period.icon}
                  {period.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {timePeriods.map(period => (
              <TabsContent key={period.id} value={period.id} className="mt-6">
                {topThree.length > 0 && (
                  <div className="mb-10">
                    <div className="flex items-center gap-2 mb-6">
                      <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Top 3 Estudantes</h2>
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Info className="h-5 w-5" />
                          </Button>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-80">
                          <div className="space-y-2">
                            <h4 className="text-sm font-semibold">Como chegar ao Top 3?</h4>
                            <p className="text-sm">
                              Os três primeiros lugares são conquistados pelos estudantes que mais responderam 
                              questões no período selecionado. Mantenha uma rotina constante de estudos para 
                              alcançar as primeiras posições!
                            </p>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </div>
                    <div className="flex flex-col md:flex-row justify-center items-end gap-4 md:gap-8">
                      {topThree[1] && (
                        <div className="flex flex-col items-center">
                          <Avatar className="w-16 h-16 md:w-20 md:h-20 border-2 border-black mb-2">
                            <AvatarImage src={topThree[1].avatar_url} />
                            <AvatarFallback className="bg-hackathon-red text-white">
                              {topThree[1].full_name?.charAt(0) || '2'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="h-32 md:h-40 w-full max-w-28 bg-hackathon-red border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-3 relative shadow-card-sm">
                            <Trophy className="w-8 h-8 text-white absolute top-2 right-2" />
                            <div className="text-white text-lg font-bold">{topThree[1].questions_answered}</div>
                            <div className="text-white text-xs truncate w-full text-center">
                              {topThree[1].full_name}
                            </div>
                            <div className="bg-white text-black font-bold rounded-full w-8 h-8 flex items-center justify-center absolute -top-4">2</div>
                          </div>
                        </div>
                      )}

                      {topThree[0] && (
                        <div className="flex flex-col items-center">
                          <Avatar className="w-20 h-20 md:w-24 md:h-24 border-2 border-black mb-2">
                            <AvatarImage src={topThree[0].avatar_url} />
                            <AvatarFallback className="bg-hackathon-yellow text-black">
                              {topThree[0].full_name?.charAt(0) || '1'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="h-40 md:h-52 w-full max-w-32 bg-hackathon-yellow border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-3 relative shadow-card-sm">
                            <Trophy className="w-10 h-10 text-black absolute top-2 right-2" />
                            <div className="text-black text-2xl font-bold">{topThree[0].questions_answered}</div>
                            <div className="text-black text-sm truncate w-full text-center font-bold">
                              {topThree[0].full_name}
                            </div>
                            <div className="bg-black text-white font-bold rounded-full w-10 h-10 flex items-center justify-center absolute -top-5">1</div>
                          </div>
                        </div>
                      )}

                      {topThree[2] && (
                        <div className="flex flex-col items-center">
                          <Avatar className="w-16 h-16 md:w-20 md:h-20 border-2 border-black mb-2">
                            <AvatarImage src={topThree[2].avatar_url} />
                            <AvatarFallback className="bg-hackathon-green text-black">
                              {topThree[2].full_name?.charAt(0) || '3'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="h-28 md:h-36 w-full max-w-28 bg-hackathon-green border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-3 relative shadow-card-sm">
                            <Trophy className="w-8 h-8 text-black absolute top-2 right-2" />
                            <div className="text-black text-lg font-bold">{topThree[2].questions_answered}</div>
                            <div className="text-black text-xs truncate w-full text-center">
                              {topThree[2].full_name}
                            </div>
                            <div className="bg-white text-black font-bold rounded-full w-8 h-8 flex items-center justify-center absolute -top-4">3</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="bg-white border-2 border-black rounded-lg shadow-card-sm overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-black text-white border-b-0">
                        <TableHead className="w-16 text-white">Pos.</TableHead>
                        <TableHead className="text-white">Estudante</TableHead>
                        <TableHead className="text-right text-white">
                          <div className="flex items-center justify-end gap-2">
                            Questões Respondidas
                            <HoverCard>
                              <HoverCardTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-6 w-6">
                                  <Info className="h-4 w-4 text-white" />
                                </Button>
                              </HoverCardTrigger>
                              <HoverCardContent className="w-80">
                                <div className="space-y-2">
                                  <h4 className="text-sm font-semibold">Contagem de Questões</h4>
                                  <p className="text-sm">
                                    Este número representa o total de questões únicas respondidas por cada estudante 
                                    no período selecionado. Cada questão é contada apenas uma vez, mesmo que tenha 
                                    sido respondida múltiplas vezes.
                                  </p>
                                </div>
                              </HoverCardContent>
                            </HoverCard>
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-6">
                            Carregando ranking...
                          </TableCell>
                        </TableRow>
                      ) : rankingData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-6">
                            Nenhum usuário com pontuação disponível.
                          </TableCell>
                        </TableRow>
                      ) : (
                        remainingUsers.map((user) => (
                          <TableRow 
                            key={user.id}
                            className={user.id === currentUserRanking?.id 
                              ? "bg-hackathon-yellow/20 hover:bg-hackathon-yellow/30" 
                              : ""
                            }
                          >
                            <TableCell className="font-medium">{user.rank}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar className="w-8 h-8 border border-gray-200">
                                  <AvatarImage src={user.avatar_url} />
                                  <AvatarFallback>
                                    {user.full_name?.charAt(0) || '?'}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="font-medium">{user.full_name}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right font-bold">
                              {user.questions_answered}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default QuestionRanking;
