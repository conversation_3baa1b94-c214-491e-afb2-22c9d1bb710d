
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { QuestionStats } from "@/types/question";

interface StudySessionStatsProps {
  session: {
    stats: QuestionStats;
    created_at: string;
  };
}

export const StudySessionStats: React.FC<StudySessionStatsProps> = ({ session }) => {
  const { stats } = session;
  const totalQuestions = stats.correct_answers + stats.incorrect_answers;

  return (
    <Card className="border-2 border-black shadow-card-sm rounded-lg">
      <CardHeader className="border-b-2 border-black">
        <CardTitle className="font-bold">Última Sessão de Estudo</CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <p className="mb-2">Data: <span className="font-bold">{new Date(session.created_at).toLocaleDateString()}</span></p>
        <div className="grid grid-cols-2 gap-2 mt-4">
          <div className="bg-hackathon-yellow/20 p-3 rounded-lg border-2 border-black">
            <p className="text-sm text-gray-700">Total de questões</p>
            <p className="text-xl font-bold">{totalQuestions}</p>
          </div>
          <div className="bg-green-100 p-3 rounded-lg border-2 border-black">
            <p className="text-sm text-gray-700">Respostas corretas</p>
            <p className="text-xl font-bold text-green-600">{stats.correct_answers}</p>
          </div>
          <div className="bg-red-100 p-3 rounded-lg border-2 border-black">
            <p className="text-sm text-gray-700">Respostas incorretas</p>
            <p className="text-xl font-bold text-red-500">{stats.incorrect_answers}</p>
          </div>
          <div className="bg-blue-100 p-3 rounded-lg border-2 border-black">
            <p className="text-sm text-gray-700">Tempo gasto</p>
            <p className="text-xl font-bold text-blue-600">{Math.round(stats.time_spent / 60)} min</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
