// 🎨 FORMATAÇÃO ROBUSTA E TIPADA PARA DR. WILL
// Arquivo dedicado para formatação de mensagens com tipagem expressiva

// ✨ TIPOS E INTERFACES
type ResponseType = "text" | "table" | "mermaid";

interface DrWillPayload {
  type: ResponseType;
  body: string;
  mermaidCode?: string;
}

// 🔍 CACHE REMOVIDO - Agora é gerenciado pelos componentes individuais

// 🧠 FUNÇÃO COMPARTILHADA PARA FORMATAÇÃO DO THINKING MODE
export const formatThinkingContent = (content: string): string => {
  if (!content) return '';

  // Aplicar formatação markdown básica para thinking
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-indigo-800 bg-indigo-50 px-1 rounded">$1</strong>')
    .replace(/\*(.*?)\*/g, '<em class="italic text-purple-700">$1</em>')
    .replace(/\n\n/g, '</p><p class="mb-2 text-gray-700 leading-relaxed">')
    .replace(/\n/g, '<br>')
    .replace(/^(.*)$/, '<p class="mb-2 text-gray-700 leading-relaxed">$1</p>');
};

// 🎯 FORMATAÇÃO OTIMIZADA PARA CONTEÚDO DO BANCO DE DADOS
const formatDatabaseContent = (content: string): string => {
  // 🔧 CORREÇÃO: Remover marcadores de controle
  let cleanContent = content
    .replace(/\[TEXT_RESPONSE\]/g, '')
    .replace(/\[TABLE\]/g, '')
    .replace(/\[MERMAID\]/g, '');

  const lines = cleanContent.split('\n');
  const result: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    if (!trimmedLine) {
      result.push('<div class="mb-2"></div>');
      continue;
    }

    // 🎯 TÍTULOS NUMERADOS (ex: "1. **Definição...**")
    const sectionMatch = trimmedLine.match(/^(\d+)\.\s+\*\*(.+)\*\*:?$/);
    if (sectionMatch) {
      const number = sectionMatch[1];
      const title = sectionMatch[2];
      result.push(`<h3 class="font-bold text-lg text-gray-900 mt-4 mb-2">${number}. <strong class="font-semibold text-gray-900">${title}</strong></h3>`);
      continue;
    }

    // 🎯 SUB-TÍTULOS (ex: "3.1. **Sub-título**")
    const subSectionMatch = trimmedLine.match(/^(\d+\.\d+)\.\s+\*\*(.+)\*\*:?$/);
    if (subSectionMatch) {
      const number = subSectionMatch[1];
      const title = subSectionMatch[2];
      result.push(`<p class="mb-3">${number}. <strong class="font-semibold text-gray-900">${title}</strong></p>`);
      continue;
    }

    // 🎯 DETECTAR NÍVEL DE INDENTAÇÃO CORRETAMENTE
    const leadingSpaces = line.length - line.trimStart().length;

    // 🎯 LISTAS COM DETECÇÃO PRECISA DE ESPAÇOS
    if (trimmedLine.startsWith('*   ')) {
      const content = applyInlineMarkdown(trimmedLine.substring(4)); // Remove "*   "

      if (leadingSpaces === 4) {
        // 4 espaços = Nível 1
        result.push(`<div class="ml-8 mb-1 text-gray-700">◦ ${content}</div>`);
      } else if (leadingSpaces === 8) {
        // 8 espaços = Nível 2
        result.push(`<div class="ml-12 mb-1 text-gray-600">▪ ${content}</div>`);
      } else if (leadingSpaces >= 12) {
        // 12+ espaços = Nível 3
        result.push(`<div class="ml-16 mb-1 text-gray-600">▪ ${content}</div>`);
      } else {
        // Fallback para nível 1
        result.push(`<div class="ml-8 mb-1 text-gray-700">◦ ${content}</div>`);
      }
      continue;
    }

    // 🎯 PARÁGRAFOS NORMAIS
    const formattedContent = applyInlineMarkdown(trimmedLine);
    result.push(`<p class="mb-3">${formattedContent}</p>`);
  }

  return result.join('');
};

// 🎯 FUNÇÃO PRINCIPAL EXPORTADA - VERSÃO LIMPA
export const formatDrWillMessage = (content: string, isStreaming: boolean = false): string => {
  if (!content) return '';

  // 🛡️ PROTEÇÃO: Se já tem HTML, retorna como está
  if (content.includes('<h3 class="font-bold') || content.includes('<div class="ml-') || content.includes('<p class="mb-')) {
    return content;
  }

  // 🛡️ PROTEÇÃO: Conteúdo pequeno quando não está streaming
  if (!isStreaming && content.length < 100) {
    return content;
  }

  // 🎯 FORMATAÇÃO ÚNICA
  return _formatDrWillMessage(content, isStreaming);
};

// 🔧 FUNÇÃO PARA SEPARAR MÚLTIPLOS BLOCOS
const parseMultipleBlocks = (content: string): Array<{type: string, content: string}> => {
  const blocks: Array<{type: string, content: string}> = [];

  // Dividir por marcadores
  const parts = content.split(/(\[(?:TABLE|MERMAID|TEXT)_RESPONSE\])/);

  // Log removido para evitar spam

  let currentType = 'text';
  let currentContent = '';

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];

    if (part === '[TABLE_RESPONSE]') {
      // Salvar bloco anterior se houver conteúdo
      if (currentContent.trim()) {
        blocks.push({ type: currentType, content: currentContent.trim() });
      }
      currentType = 'table';
      currentContent = '';
    } else if (part === '[MERMAID_RESPONSE]') {
      // Salvar bloco anterior se houver conteúdo
      if (currentContent.trim()) {
        blocks.push({ type: currentType, content: currentContent.trim() });
      }
      currentType = 'mermaid';
      currentContent = '';
    } else if (part === '[TEXT_RESPONSE]') {
      // Salvar bloco anterior se houver conteúdo
      if (currentContent.trim()) {
        blocks.push({ type: currentType, content: currentContent.trim() });
      }
      currentType = 'text';
      currentContent = '';
    } else {
      currentContent += part;
    }
  }

  // Adicionar último bloco
  if (currentContent.trim()) {
    blocks.push({ type: currentType, content: currentContent.trim() });
  }

  return blocks;
};

// 🔧 IMPLEMENTAÇÃO INTERNA (REFORMULADA PARA MÚLTIPLOS BLOCOS)
const _formatDrWillMessage = (content: string, isStreaming: boolean): string => {
  // 🎯 VERIFICAR SE HÁ MÚLTIPLOS BLOCOS
  const markers = content.match(/\[(?:TABLE|MERMAID|TEXT)_RESPONSE\]/g) || [];
  const hasMultipleMarkers = markers.length > 1;



  if (hasMultipleMarkers) {
    const blocks = parseMultipleBlocks(content);

    const formattedBlocks = blocks.map((block, index) => {
      switch (block.type) {
        case 'table':
          return formatTableMessage(block.content, isStreaming);
        case 'mermaid':
          return formatMermaidMessage(block.content, isStreaming);
        default:
          return formatTextMessage(block.content);
      }
    });

    return formattedBlocks.join('');
  }

  // 🎯 PROCESSAMENTO SINGLE BLOCK (CÓDIGO ORIGINAL)
  const hasTableMarker = content.includes('[TABLE_RESPONSE]');
  const hasMermaidMarker = content.includes('[MERMAID_RESPONSE]');
  const hasTextMarker = content.includes('[TEXT_RESPONSE]');

  // 🎯 LIMPEZA: Remover marcadores APÓS detecção
  let cleanContent = content
    .replace(/\[MERMAID_RESPONSE\]/g, '')
    .replace(/\[TABLE_RESPONSE\]/g, '')
    .replace(/\[TEXT_RESPONSE\]/g, '')
    .trim();

  const { type, body } = parsePrefix(cleanContent);

  // 🎯 PROTEÇÃO CONTRA STREAMING INCOMPLETO
  if (isStreaming && body.length < 50) {
    return `<p class="mb-3">${applyInlineMarkdown(body)}</p>`;
  }

  const isTable = looksLikeTable(body);
  const isMermaid = looksLikeMermaid(body);

  // 🎯 PRIORIDADE: Usar marcadores explícitos primeiro, depois detecção automática
  if (hasMermaidMarker || type === "mermaid" || isMermaid) {
    return formatMermaidMessage(body, isStreaming);
  }
  if (hasTableMarker || type === "table" || isTable || hasTableInContent(body)) {
    return formatTableMessage(body, isStreaming);
  }
  return formatTextMessage(body);
};

// 🔍 PARSER DE PREFIXO (MELHORADO PARA MÚLTIPLOS TIPOS)
const parsePrefix = (content: string): DrWillPayload => {
  // 🎯 NOVO: Limpar TODOS os marcadores primeiro
  let cleanBody = content
    .replace(/\[MERMAID_RESPONSE\]/g, '')
    .replace(/\[TABLE_RESPONSE\]/g, '')
    .replace(/\[TEXT_RESPONSE\]/g, '')
    .trim();

  // Detectar tipo baseado no conteúdo, não apenas nos marcadores
  if (content.includes("[MERMAID_RESPONSE]") || looksLikeMermaid(content)) {
    // Extrair código Mermaid e texto
    const mermaidMatch = content.match(/```\s*(mindmap|graph|flowchart|sequenceDiagram)[\s\S]*?```/);
    return {
      type: "mermaid",
      body: cleanBody,
      mermaidCode: mermaidMatch ? mermaidMatch[0] : undefined
    };
  }
  if (content.includes("[TABLE_RESPONSE]") || looksLikeTable(content)) {
    return { type: "table", body: cleanBody };
  }

  // Se tem marcador TEXT_RESPONSE ou é texto puro
  return { type: "text", body: cleanBody };
};

// 🔍 DETECTOR DE TABELA (MAIS PRECISO)
const looksLikeTable = (body: string): boolean => {
  // Deve ter pelo menos 2 linhas com | (excluindo separadores)
  const lines = body.split('\n');
  const linesWithPipes = lines.filter(line => {
    const trimmed = line.trim();

    // Deve ter | e pelo menos 3 partes
    if (!trimmed.includes('|') || trimmed.split('|').length < 3) {
      return false;
    }

    // Não deve ser linha separadora
    if (trimmed.match(/^\s*\|[\s\-:]+\|\s*$/) ||
        trimmed.match(/^[\s\-:|]+$/) ||
        trimmed.match(/^\|[\s\-:]+\|$/)) {
      return false;
    }

    // Não deve ter mais de 80% de hífens
    const hyphensCount = (trimmed.match(/-/g) || []).length;
    const totalChars = trimmed.length;
    if (hyphensCount > totalChars * 0.8) {
      return false;
    }

    return true;
  });

  // Precisa ter pelo menos 2 linhas com estrutura de tabela
  if (linesWithPipes.length < 2) {
    return false;
  }

  // Verificar se as linhas têm número similar de colunas
  const columnCounts = linesWithPipes.map(line => line.split('|').length);
  const firstCount = columnCounts[0];
  const similarColumns = columnCounts.filter(count => Math.abs(count - firstCount) <= 1);

  const isTable = similarColumns.length >= linesWithPipes.length * 0.8;

  // Pelo menos 80% das linhas devem ter número similar de colunas
  return isTable;
};

// 🎯 DETECTOR AGRESSIVO DE TABELA EM CONTEÚDO MISTO
const hasTableInContent = (content: string): boolean => {
  const lines = content.split('\n');
  let consecutiveTableLines = 0;
  let maxConsecutiveTableLines = 0;

  for (const line of lines) {
    const trimmed = line.trim();

    // Verificar se é linha de tabela válida
    if (trimmed.includes('|') && trimmed.split('|').length >= 3) {
      // Não deve ser separador
      if (!trimmed.match(/^\s*\|[\s\-:]+\|\s*$/) &&
          !trimmed.match(/^[\s\-:|]+$/) &&
          !trimmed.match(/^\|[\s\-:]+\|$/)) {

        consecutiveTableLines++;
        maxConsecutiveTableLines = Math.max(maxConsecutiveTableLines, consecutiveTableLines);
      } else {
        consecutiveTableLines = 0;
      }
    } else {
      consecutiveTableLines = 0;
    }
  }

  // Se encontrou pelo menos 3 linhas consecutivas de tabela, considerar como tabela
  return maxConsecutiveTableLines >= 3;
};

// ✨ FORMATAÇÃO INLINE MARKDOWN (DRY) - CORRIGIDA PARA PRESERVAR ESTRUTURA
const applyInlineMarkdown = (text: string): string => {
  if (!text) return '';

  let formatted = text;

  // 🔧 CORREÇÃO: Aplicar formatação de negrito com regex mais robusta
  formatted = formatted.replace(/\*\*([^*\n]+?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');

  // Tratar casos onde ** pode estar no início/fim de linha
  formatted = formatted.replace(/^\*\*([^*\n]+?)\*\*/gm, '<strong class="font-semibold text-gray-900">$1</strong>');
  formatted = formatted.replace(/\*\*([^*\n]+?)\*\*$/gm, '<strong class="font-semibold text-gray-900">$1</strong>');

  // 🔧 CORREÇÃO ESPECÍFICA: Detectar padrões que deveriam estar em negrito mas não estão
  formatted = formatted.replace(/(\*\*Suspeita de [^:]+:)/g, '<strong class="font-semibold text-gray-900">$1</strong>');

  // Aplicar formatação de itálico (evitando conflito com negrito)
  formatted = formatted.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em class="italic text-gray-700">$1</em>');

  // Aplicar formatação de código inline
  formatted = formatted.replace(/`([^`\n]+?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>');

  // Limpar apenas espaços excessivos (3 ou mais), preservando estrutura
  formatted = formatted.replace(/\s{3,}/g, ' ');

  return formatted.trim();
};

// 🔧 FUNÇÃO PARA PROCESSAR CONTEÚDO LONGO EM LISTAS - CORRIGIDA PARA QUEBRAR SENTENÇAS
const processLongListContent = (content: string, indentSpaces: number): string[] => {
  const result: string[] = [];

  // 🎯 CORREÇÃO: Detectar se é título de seção (termina com : e tem negrito)
  const isSectionTitle = content.trim().endsWith(':') && content.includes('**');

  // Determinar classes CSS baseado na indentação e tipo de conteúdo
  let marginClass: string;
  let textClass: string;
  let symbol: string;

  if (isSectionTitle) {
    // Títulos de seção sempre no mesmo nível
    marginClass = 'ml-8';
    textClass = 'text-gray-700';
    symbol = '◦';
  } else if (indentSpaces === 0) {
    marginClass = 'ml-4';
    textClass = 'text-gray-800';
    symbol = '•';
  } else if (indentSpaces <= 4) {
    marginClass = 'ml-8';
    textClass = 'text-gray-700';
    symbol = '◦';
  } else {
    // Sub-itens com mais indentação
    const level = Math.floor(indentSpaces / 4);
    marginClass = `ml-${8 + (level - 1) * 4}`;
    textClass = 'text-gray-600';
    symbol = '▪';
  }

  // 🔧 CORREÇÃO: Quebrar por sentenças que terminam com **.
  const sentences = content.split(/(\*\*[^*]+\*\*\.)/);
  let currentSentence = '';
  let isFirstSentence = true;

  for (let i = 0; i < sentences.length; i++) {
    const part = sentences[i].trim();

    if (!part) continue;

    // Se termina com **.  é uma sentença completa
    if (part.endsWith('**.')) {
      currentSentence += part;
      const processedContent = applyInlineMarkdown(currentSentence);

      if (isFirstSentence) {
        result.push(`<div class="${marginClass} mb-1 ${textClass}">${symbol} ${processedContent}</div>`);
        isFirstSentence = false;
      } else {
        result.push(`<div class="${marginClass} mb-1 ${textClass}">${processedContent}</div>`);
      }

      currentSentence = '';
    } else {
      currentSentence += part + ' ';
    }
  }

  // Adicionar conteúdo restante se existir
  if (currentSentence.trim()) {
    const processedContent = applyInlineMarkdown(currentSentence.trim());

    if (isFirstSentence) {
      result.push(`<div class="${marginClass} mb-1 ${textClass}">${symbol} ${processedContent}</div>`);
    } else {
      result.push(`<div class="${marginClass} mb-1 ${textClass}">${processedContent}</div>`);
    }
  }

  // Se não conseguiu quebrar, retornar como uma única div
  if (result.length === 0) {
    const processedContent = applyInlineMarkdown(content);
    result.push(`<div class="${marginClass} mb-1 ${textClass}">${symbol} ${processedContent}</div>`);
  }

  return result;
};

// 📝 FORMATAÇÃO DE TEXTO (CORRIGIDA PARA ESTRUTURA HIERÁRQUICA)
const formatTextMessage = (content: string): string => {
  if (!content || content.trim().length === 0) {
    return '';
  }

  // Logs removidos para limpeza - apenas log essencial no final

  // 1. Limpeza mínima - apenas normalizar quebras de linha
  const cleanContent = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n').trim();

  // 2. Proteção contra conteúdo muito pequeno ou incompleto
  if (cleanContent.length < 20) {
    return `<p class="mb-3">${applyInlineMarkdown(cleanContent)}</p>`;
  }

  // 3. Dividir em blocos por quebras duplas para preservar parágrafos
  const blocks = cleanContent.split(/\n{2,}/);
  const result: string[] = [];

  for (const block of blocks) {
    if (!block.trim()) continue;

    const lines = block.split('\n');
    const blockResult: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Logs removidos para limpeza

      // Linha vazia
      if (trimmedLine === '') {
        continue;
      }

      // 🎯 PROTEÇÃO: Evitar processar linhas que parecem truncadas
      if (trimmedLine.length < 3 && !trimmedLine.match(/^\d+\.$/)) {
        continue;
      }

      // 🎯 TÍTULOS NUMERADOS COMO SEÇÕES (ex: "1. **Aspectos Críticos**")
      const sectionMatch = trimmedLine.match(/^(\d+)\.\s+\*\*(.+)\*\*$/);
      if (sectionMatch) {
        const number = sectionMatch[1];
        const title = applyInlineMarkdown(sectionMatch[2]);
        blockResult.push(`<h3 class="font-bold text-lg text-gray-900 mt-6 mb-3">${number}. ${title}</h3>`);
        continue;
      }

      // 🎯 CORREÇÃO: Headers tradicionais (####, ###, ##) - PRIORIDADE ALTA
      if (trimmedLine.startsWith('#### ')) {
        const headerText = applyInlineMarkdown(trimmedLine.substring(5));
        blockResult.push(`<h4 class="text-base font-semibold text-gray-800 mt-4 mb-3">${headerText}</h4>`);
        continue;
      }

      if (trimmedLine.startsWith('### ')) {
        const headerText = applyInlineMarkdown(trimmedLine.substring(4));
        blockResult.push(`<h3 class="font-bold text-lg text-gray-900 mt-6 mb-3">${headerText}</h3>`);
        continue;
      }

      if (trimmedLine.startsWith('## ')) {
        const headerText = applyInlineMarkdown(trimmedLine.substring(3));
        blockResult.push(`<h2 class="font-bold text-xl text-gray-900 mt-6 mb-4">${headerText}</h2>`);
        continue;
      }

      // 🎯 LISTAS COM HIERARQUIA OTIMIZADA (espaçamento reduzido)
      const listMatch = line.match(/^(\s*)\*\s+(.+)$/);
      if (listMatch) {
        const indentSpaces = listMatch[1].length;
        const content = listMatch[2];



        // 🎯 HIERARQUIA FINAL - REDUZINDO MAIS 1 ESPAÇO:
        // 4 espaços → ml-6 (nível 1, máximo reduzido)
        // 8 espaços → ml-10 (nível 2, máximo reduzido)
        // 12+ espaços → ml-14 (nível 3, máximo reduzido)
        let marginClass = 'ml-4'; // padrão para casos não mapeados
        let textColor = 'text-gray-600';
        let symbol = '▪';

        if (indentSpaces === 4) {
          marginClass = 'ml-6';  // Reduzido ao máximo
        } else if (indentSpaces === 8) {
          marginClass = 'ml-10'; // Reduzido ao máximo
        } else if (indentSpaces >= 12) {
          marginClass = 'ml-14'; // Reduzido ao máximo
        }

        const formattedContent = applyInlineMarkdown(content);
        blockResult.push(`<div class="${marginClass} mb-1 ${textColor}">${symbol} ${formattedContent}</div>`);
        continue;
      }

      // 🎯 LISTAS NUMERADAS (incluindo com negrito)
      const numberedMatch = trimmedLine.match(/^(\d+)\.\s+(.+)$/);
      if (numberedMatch) {
        const number = numberedMatch[1];
        const content = applyInlineMarkdown(numberedMatch[2]);

        // Se tem negrito, é um item importante
        if (numberedMatch[2].includes('**')) {
          blockResult.push(`<div class="ml-4 mb-3"><span class="font-bold text-blue-700">${number}.</span> ${content}</div>`);
          // Log removido para limpeza
        } else {
          blockResult.push(`<div class="ml-4 mb-2"><span class="font-semibold text-blue-600">${number}.</span> ${content}</div>`);
        }
        continue;
      }



      // 🔧 CORREÇÃO: Detectar se é uma linha que deveria ser lista mas não foi detectada
      // Isso acontece quando há conteúdo após ":" que deveria ser uma sublista
      const shouldBeListItem = trimmedLine.includes('**') && trimmedLine.endsWith(':');

      if (shouldBeListItem) {
        // Tratar como item de lista sem símbolo
        const formattedContent = applyInlineMarkdown(trimmedLine);
        blockResult.push(`<div class="ml-8 mb-1 text-gray-700">${formattedContent}</div>`);
      } else {
        // Parágrafo normal - verificar se não é continuação de lista
        const formattedContent = applyInlineMarkdown(trimmedLine);

        // Se a linha anterior era uma lista e esta linha não tem formatação especial,
        // pode ser continuação da lista
        if (blockResult.length > 0 &&
            blockResult[blockResult.length - 1].includes('ml-') &&
            !trimmedLine.match(/^[*\d]/) &&
            trimmedLine.length < 200) {
          // Adicionar como continuação da lista anterior
          blockResult.push(`<div class="ml-8 mb-1 text-gray-600">${formattedContent}</div>`);
        } else {
          // Parágrafo normal
          blockResult.push(`<p class="mb-3">${formattedContent}</p>`);
        }
      }
    }

    // Adicionar resultado do bloco
    if (blockResult.length > 0) {
      result.push(blockResult.join(''));
    }
  }

  // 3. Juntar blocos com espaçamento adequado - CORRIGIDO para preservar estrutura
  const finalResult = result.join('<div class="mb-2"></div>');

  // 4. Validação final: se resultado está muito pequeno, usar formatação simples
  if (finalResult.length < 50 && cleanContent.length > 20) {
    return `<p class="mb-3">${applyInlineMarkdown(cleanContent)}</p>`;
  }

  // 5. CORREÇÃO: Limpeza final - remover divs vazias e espaçamentos desnecessários
  let correctedResult = finalResult
    .replace(/<div class="mb-1"><\/div>/g, '') // Remover divs vazias
    .replace(/(<\/strong>)(\s*)([A-Z])/g, '$1 $3') // Espaço após negrito
    .replace(/(\.)(\s*)(\*\*)/g, '$1 $3') // Espaço após ponto antes de negrito
    .replace(/\s{2,}/g, ' '); // Normalizar espaços múltiplos

  // 🎯 LOGS REMOVIDOS - Focando apenas em thread creation

  return correctedResult;
};

// 📊 FORMATAÇÃO DE TABELAS (MELHORADA - SEPARA TEXTO E TABELA)
const formatTableMessage = (content: string, isStreaming: boolean): string => {
  // 1. Separar texto antes da tabela, tabela, e texto depois da tabela
  const { beforeTable, tableContent, afterTable } = extractTableParts(content);

  const result: string[] = [];

  // 2. Processar texto antes da tabela
  if (beforeTable.trim()) {
    const formattedBefore = formatTextMessage(beforeTable);
    result.push(formattedBefore);
  }

  // 3. Processar tabela
  if (tableContent.length > 0) {
    if (isStreaming) {
      result.push(formatTableLoading());
    } else {
      result.push(formatTableCompact(tableContent));
    }
  }

  // 4. Processar texto depois da tabela
  if (afterTable.trim()) {
    const formattedAfter = formatTextMessage(afterTable);
    result.push(formattedAfter);
  }

  return result.join('');
};

// 🔍 FUNÇÃO PARA EXTRAIR PARTES DA TABELA (CORRIGIDA)
const extractTableParts = (content: string): { beforeTable: string, tableContent: string[], afterTable: string } => {
  const lines = content.split('\n');
  const tableLines: string[] = [];
  let beforeTable = '';
  let afterTable = '';
  let tableStartIndex = -1;
  let tableEndIndex = -1;

  // Encontrar início e fim da tabela
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Verificar se é uma linha de tabela válida
    if (line.includes('|') && line.split('|').length >= 3) {
      // Não deve ser linha separadora (só hífens, dois pontos e espaços)
      if (!line.match(/^\s*\|[\s\-:]+\|\s*$/) &&
          !line.match(/^[\s\-:|]+$/) &&
          !line.match(/^\|[\s\-:]+\|$/)) {

        if (tableStartIndex === -1) {
          tableStartIndex = i;
        }
        tableEndIndex = i;
        tableLines.push(line);
      }
    } else if (tableStartIndex !== -1 && line === '') {
      // Linha vazia pode estar dentro da tabela, continuar
      continue;
    } else if (tableStartIndex !== -1 && tableLines.length > 0) {
      // Encontrou texto após a tabela, parar
      break;
    }
  }

  // Separar conteúdo
  if (tableStartIndex !== -1) {
    beforeTable = lines.slice(0, tableStartIndex).join('\n');
    afterTable = lines.slice(tableEndIndex + 1).join('\n');
  } else {
    beforeTable = content;
  }

  return { beforeTable, tableContent: tableLines, afterTable };
};

// Loading de tabela simples
const formatTableLoading = (): string => {
  return `
    <div class="mb-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-3">
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-white bg-opacity-20 rounded animate-pulse"></div>
          <span class="text-white font-semibold text-sm">Montando Tabela...</span>
        </div>
      </div>
      <div class="p-3">
        <div class="flex items-center justify-center py-4">
          <div class="flex gap-1">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
        </div>
      </div>
    </div>
  `;
};

// 🔧 FUNÇÃO PARA CONSOLIDAR CÉLULAS MULTI-LINHA
const consolidateMultiLineTableCells = (tableLines: string[]): string[] => {
  if (tableLines.length === 0) return [];

  const consolidatedLines: string[] = [];
  let currentLine = '';
  let expectedColumns = 0;

  for (let i = 0; i < tableLines.length; i++) {
    const line = tableLines[i].trim();
    const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');

    // Se é a primeira linha, determinar número de colunas esperado
    if (i === 0) {
      expectedColumns = cells.length;
      consolidatedLines.push(line);
      continue;
    }

    // Se a linha tem o número correto de colunas, é uma nova linha
    if (cells.length >= expectedColumns) {
      // Salvar linha anterior se existir
      if (currentLine) {
        consolidatedLines.push(currentLine);
      }
      // Iniciar nova linha
      currentLine = line;
    } else if (cells.length > 0 && currentLine) {
      // Esta é uma continuação da linha anterior
      // Encontrar a célula que está sendo continuada
      const currentCells = currentLine.split('|').map(cell => cell.trim());

      // Adicionar o conteúdo à última célula não vazia
      for (let j = currentCells.length - 1; j >= 0; j--) {
        if (currentCells[j] && currentCells[j] !== '') {
          currentCells[j] += ' ' + cells.join(' ');
          break;
        }
      }

      // Reconstruir a linha
      currentLine = '| ' + currentCells.join(' | ') + ' |';
    }
  }

  // Adicionar última linha se existir
  if (currentLine) {
    consolidatedLines.push(currentLine);
  }

  return consolidatedLines;
};

// Tabela compacta simples (MELHORADA - APENAS DADOS DA TABELA)
const formatTableCompact = (tableLines: string[]): string => {
  // Filtrar apenas linhas que são realmente de tabela (não separadores)
  const contentLines = tableLines.filter(line => {
    const trimmed = line.trim();

    // Deve ter | e pelo menos 3 partes
    if (!trimmed.includes('|') || trimmed.split('|').length < 3) {
      return false;
    }

    // Não deve ser linha separadora (só hífens, dois pontos e espaços)
    if (trimmed.match(/^\s*\|[\s\-:]+\|\s*$/) ||
        trimmed.match(/^[\s\-:|]+$/) ||
        trimmed.match(/^\|[\s\-:]+\|$/)) {
      return false;
    }

    // Não deve ter mais de 80% de hífens
    const hyphensCount = (trimmed.match(/-/g) || []).length;
    const totalChars = trimmed.length;
    if (hyphensCount > totalChars * 0.8) {
      return false;
    }

    return true;
  });

  if (contentLines.length === 0) return '';

  // 🔧 NOVO: Consolidar células multi-linha antes de processar
  const consolidatedLines = consolidateMultiLineTableCells(contentLines);

  const rows = consolidatedLines.map(line => {
    return line.split('|')
      .map(cell => {
        const trimmed = cell.trim();
        // Aplicar formatação markdown nas células
        return applyInlineMarkdown(trimmed);
      })
      .filter(cell => cell !== '' && !cell.match(/^[\s\-:]+$/));
  }).filter(row => row.length >= 2);

  if (rows.length === 0) return '';

  // Remover linhas que não são dados de tabela (ex: texto solto)
  const cleanRows = rows.filter(row => {
    // Se a primeira célula não parece ser um cabeçalho ou dado estruturado, remover
    const firstCell = row[0];
    return firstCell && firstCell.length < 200; // Evitar texto longo que não é tabela
  });

  if (cleanRows.length === 0) return '';

  const tableId = `table-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;



  // 🎯 UNIFICADO: Usar sempre openTableDialog
  const functionName = 'openTableDialog';
  const dataId = `table-data-${tableId}`;

  return `
    <div class="mb-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-3">
        <div class="flex items-center justify-between">
          <span class="text-white font-semibold">📊 Tabela Completa</span>
          <button onclick="${functionName}('${tableId}')" class="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100">
            Ver Completa
          </button>
        </div>
      </div>
      <div class="p-3">
        <div class="text-center text-gray-600 text-sm">
          ${Math.max(0, cleanRows.length - 1)} linhas de dados • Clique para expandir
        </div>
      </div>
      <script type="application/json" id="${dataId}">
        ${JSON.stringify(cleanRows)}
      </script>
    </div>
  `;
};

// 🧠 DETECÇÃO DE CONTEÚDO MERMAID (MELHORADA)
const looksLikeMermaid = (content: string): boolean => {
  const cleanContent = content.toLowerCase().trim();

  // Verificar palavras-chave do Mermaid
  const mermaidKeywords = [
    'mindmap',
    'graph',
    'flowchart',
    'sequencediagram',
    'classDiagram',
    'stateDiagram',
    'journey',
    'gantt'
  ];

  const hasMermaidKeywords = mermaidKeywords.some(keyword =>
    cleanContent.includes(keyword.toLowerCase())
  );

  // Verificar sintaxe específica do mindmap
  const hasMindmapSyntax = /root\s*\(\([^)]+\)\)/.test(cleanContent);

  // Verificar se usuário pediu mapa mental ou fluxograma
  const userRequestedMermaid = cleanContent.includes('mapa mental') ||
                               cleanContent.includes('mindmap') ||
                               cleanContent.includes('fluxograma') ||
                               cleanContent.includes('diagrama') ||
                               cleanContent.includes('esquema visual');

  return hasMermaidKeywords || hasMindmapSyntax || userRequestedMermaid;
};

// 🛡️ FUNÇÃO ULTRA-ROBUSTA PARA LIMPAR E CORRIGIR CÓDIGO MERMAID
const cleanMermaidCode = (code: string): string => {
  let cleaned = code;

  // 1. Substituir caracteres HTML entities
  cleaned = cleaned
    .replace(/&gt;/g, '>')
    .replace(/&lt;/g, '<')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");

  // 2. Remover caracteres especiais problemáticos (acentos)
  cleaned = cleaned
    .replace(/ç/g, 'c')
    .replace(/ã/g, 'a')
    .replace(/õ/g, 'o')
    .replace(/á/g, 'a')
    .replace(/é/g, 'e')
    .replace(/í/g, 'i')
    .replace(/ó/g, 'o')
    .replace(/ú/g, 'u')
    .replace(/â/g, 'a')
    .replace(/ê/g, 'e')
    .replace(/ô/g, 'o')
    .replace(/ü/g, 'u')
    .replace(/Ç/g, 'C')
    .replace(/Ã/g, 'A')
    .replace(/Õ/g, 'O')
    .replace(/Á/g, 'A')
    .replace(/É/g, 'E')
    .replace(/Í/g, 'I')
    .replace(/Ó/g, 'O')
    .replace(/Ú/g, 'U')
    .replace(/Â/g, 'A')
    .replace(/Ê/g, 'E')
    .replace(/Ô/g, 'O');

  // 3. Processar linha por linha para corrigir estrutura
  const lines = cleaned.split('\n');
  const processedLines = [];
  let foundFirstMindmap = false;

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    const trimmedLine = line.trim();

    // Se encontrar um segundo "mindmap", parar
    if (trimmedLine === 'mindmap' && foundFirstMindmap) {
      break;
    }

    if (trimmedLine === 'mindmap') {
      foundFirstMindmap = true;
      processedLines.push('mindmap');
      continue;
    }

    // Pular linhas vazias
    if (!trimmedLine) {
      continue;
    }

    // Detectar nível de indentação
    const indentMatch = line.match(/^(\s*)/);
    const currentIndent = indentMatch ? indentMatch[1].length : 0;

    // Normalizar indentação (usar 2 espaços por nível)
    const level = Math.floor(currentIndent / 2);
    const normalizedIndent = '  '.repeat(level);

    // Limpar texto do nó
    let nodeText = trimmedLine;

    // Remover caracteres problemáticos adicionais
    nodeText = nodeText
      .replace(/[()]/g, '') // Remover parênteses exceto no root
      .replace(/:/g, '') // Remover dois pontos
      .replace(/;/g, '') // Remover ponto e vírgula
      .replace(/,/g, '') // Remover vírgulas
      .replace(/\//g, ' ou ') // Substituir barras
      .replace(/\+/g, ' mais ') // Substituir +
      .replace(/-{2,}/g, ' ') // Remover múltiplos hífens
      .replace(/\s+/g, ' ') // Normalizar espaços
      .trim();

    // Se for root, manter formato especial
    if (nodeText.toLowerCase().includes('root') || (level === 0 && i === 1)) {
      // Extrair o texto principal do root, removendo a palavra "root"
      let rootText = nodeText;

      // Remover "root" do início se existir
      rootText = rootText.replace(/^root\s*/i, '');

      // Extrair texto entre parênteses se existir
      const rootMatch = rootText.match(/\(\((.+?)\)\)/) || rootText.match(/(.+)/);
      const cleanRootText = rootMatch ? rootMatch[1] || rootMatch[0] : 'Topico Principal';

      // Limpar texto final
      const finalText = cleanRootText.replace(/[()]/g, '').trim();
      processedLines.push(`  root((${finalText}))`);
    } else if (nodeText && nodeText.length > 0) {
      // Limitar tamanho do texto
      if (nodeText.length > 30) {
        nodeText = nodeText.substring(0, 27) + '...';
      }

      // Garantir que não há espaços extras
      const cleanNodeText = nodeText.replace(/\s+/g, ' ').trim();
      if (cleanNodeText) {
        processedLines.push(normalizedIndent + cleanNodeText);
      }
    }
  }

  // 4. Garantir estrutura mínima válida
  if (processedLines.length < 2) {
    return `mindmap
  root((Mapa Mental))
    Topico 1
    Topico 2
    Topico 3`;
  }

  // 5. Limitar número total de linhas (aumentado para 50 linhas)
  if (processedLines.length > 50) {
    // Em vez de truncar, vamos manter os nós mais importantes
    const importantLines = [];
    let currentLevel = 0;

    for (const line of processedLines) {
      const indentLevel = Math.floor((line.match(/^(\s*)/)?.[1]?.length || 0) / 2);

      // Manter root e primeiros 2 níveis sempre
      if (indentLevel <= 2) {
        importantLines.push(line);
        currentLevel = indentLevel;
      } else if (importantLines.length < 45) {
        // Adicionar alguns do 3º nível se houver espaço
        importantLines.push(line);
      }
    }

    processedLines.splice(0, processedLines.length, ...importantLines);
  }

  // 6. Validação final - garantir que não há linhas problemáticas
  const finalLines = [];
  for (let i = 0; i < processedLines.length; i++) {
    const line = processedLines[i];

    // Pular linhas vazias ou com apenas espaços
    if (!line.trim()) continue;

    // Se for a primeira linha, deve ser 'mindmap'
    if (i === 0 && line.trim() !== 'mindmap') {
      finalLines.push('mindmap');
      if (line.trim() !== 'mindmap') {
        finalLines.push(line);
      }
    } else {
      finalLines.push(line);
    }
  }

  // 7. Garantir estrutura mínima
  if (finalLines.length < 3) {
    return `mindmap
  root((Mapa Mental))
    Topico 1
    Topico 2
    Topico 3`;
  }

  return finalLines.join('\n');
};

// 🎯 FUNÇÃO PARA MOSTRAR LOADING DO MERMAID (SIMPLES)
const formatMermaidLoading = (diagramInfo: { icon: string; title: string }): string => {
  return `
    <div class="mb-3 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-300 rounded-lg p-3">
      <div class="flex items-center justify-between">
        <span class="font-medium text-gray-700">${diagramInfo.icon} ${diagramInfo.title}</span>
        <button
          disabled
          class="bg-gray-400 text-white px-3 py-1.5 rounded-md text-sm font-medium cursor-not-allowed flex items-center gap-1.5"
        >
          <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
          Criando...
        </button>
      </div>
    </div>`;
};

// 🎯 FUNÇÃO PARA DETECTAR TIPO DE DIAGRAMA
const detectDiagramType = (content: string): { type: string; icon: string; title: string } => {
  const lowerContent = content.toLowerCase();

  if (lowerContent.includes('fluxograma') || lowerContent.includes('flowchart')) {
    return {
      type: 'fluxograma',
      icon: '📊',
      title: 'Fluxograma Interativo'
    };
  }

  if (lowerContent.includes('organograma')) {
    return {
      type: 'organograma',
      icon: '🏢',
      title: 'Organograma Interativo'
    };
  }

  if (lowerContent.includes('diagrama')) {
    return {
      type: 'diagrama',
      icon: '📈',
      title: 'Diagrama Interativo'
    };
  }

  if (lowerContent.includes('esquema')) {
    return {
      type: 'esquema',
      icon: '🗂️',
      title: 'Esquema Interativo'
    };
  }

  // Default: mapa mental
  return {
    type: 'mapa mental',
    icon: '🧠',
    title: 'Mapa Mental Interativo'
  };
};

// 🎨 FORMATAÇÃO DE MENSAGEM MERMAID
const formatMermaidMessage = (content: string, isStreaming: boolean = false): string => {
  if (!content || content.trim().length === 0) {
    return '';
  }

  // Detectar tipo de diagrama
  const diagramInfo = detectDiagramType(content);

  // Durante streaming, mostrar loading
  if (isStreaming && content.length < 100) {
    return `
      <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center gap-2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span class="text-blue-700 font-medium">Gerando ${diagramInfo.type}...</span>
        </div>
      </div>
    `;
  }

  // Extrair código Mermaid do conteúdo
  const mermaidMatch = content.match(/```\s*(mindmap|graph|flowchart|sequenceDiagram)[\s\S]*?```/);
  let mermaidCode = '';
  let textContent = content;

  if (mermaidMatch) {
    mermaidCode = mermaidMatch[0]
      .replace(/```\s*(mindmap|graph|flowchart|sequenceDiagram)/, '')
      .replace(/```$/, '')
      .trim();
    textContent = content.replace(mermaidMatch[0], '').trim();
  } else {
    // Tentar extrair Mermaid sem marcadores de código
    const lines = content.split('\n');
    const mermaidLines: string[] = [];
    const textLines: string[] = [];
    let inMermaid = false;

    for (const line of lines) {
      const trimmedLine = line.trim().toLowerCase();

      if (trimmedLine.startsWith('mindmap') ||
          trimmedLine.startsWith('graph') ||
          trimmedLine.startsWith('flowchart')) {
        inMermaid = true;
        mermaidLines.push(line);
      } else if (inMermaid && (trimmedLine === '' || line.startsWith('  ') || line.startsWith('\t'))) {
        mermaidLines.push(line);
      } else {
        inMermaid = false;
        textLines.push(line);
      }
    }

    if (mermaidLines.length > 0) {
      mermaidCode = mermaidLines.join('\n').trim();
      textContent = textLines.join('\n').trim();
    }
  }

  // Remover blocos ```mermaid vazios do texto
  textContent = textContent
    .replace(/```mermaid\s*```/g, '')
    .replace(/```mermaid\s*\n\s*```/g, '')
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Limpar quebras de linha excessivas
    .trim();

  // 🛡️ LIMPEZA E VALIDAÇÃO DO CÓDIGO MERMAID
  if (mermaidCode) {
    mermaidCode = cleanMermaidCode(mermaidCode);
  }

  const mermaidId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  let result = '';

  // Adicionar texto explicativo se existir
  if (textContent && textContent.length > 0) {
    result += formatTextMessage(textContent);
  }

  // Adicionar Mermaid se existir
  if (mermaidCode && mermaidCode.length > 0) {
    // 🎯 Verificar se está em streaming (código incompleto ou muito pequeno)
    const isStreamingMermaid = isStreaming || mermaidCode.length < 30;

    if (isStreamingMermaid) {
      // Mostrar estado de loading
      result += `
        <div class="mb-3 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-3">
          <div class="flex items-center justify-between gap-3">
            <span class="font-medium text-gray-800 flex-1 min-w-0">${diagramInfo.icon} ${diagramInfo.title}</span>
            <button
              disabled
              class="bg-gray-400 text-white px-3 py-1.5 rounded-md text-sm font-medium cursor-not-allowed flex items-center gap-1.5 whitespace-nowrap flex-shrink-0"
            >
              <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              Criando...
            </button>
          </div>
        </div>
      `;
    } else {
      // Mostrar botão expandir normal
      result += `
        <div class="mb-3 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-3">
          <div class="flex items-center justify-between gap-3">
            <span class="font-medium text-gray-800 flex-1 min-w-0">${diagramInfo.icon} ${diagramInfo.title}</span>
            <button
              onclick="expandMermaidDiagram('${mermaidId}')"
              class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 whitespace-nowrap flex-shrink-0"
            >
              Expandir
            </button>
          </div>
          <!-- Hidden element for modal data -->
          <div id="${mermaidId}" style="display: none;"></div>
          <script type="application/json" id="${mermaidId}-code">
            ${JSON.stringify(mermaidCode)}
          </script>
        </div>
      `;
    }

    // Não adicionar mais scripts - usar apenas o modal React
  }

  return result;
};
